# 骰子游戏服务器端算法：收分放分线机制说明文档

## 一、文档目的
本说明文档旨在规范百人场游戏中收分线与放分线的动态调节算法逻辑，以便开发与运维人员在不同玩家活跃阶段合理配置参数，从而实现游戏盈利模型的动态平衡与用户体验的最优化。

## 二、核心概念与术语

| 术语 | 定义 |
|------|------|
| 中心线（Center Line） | 反映当前玩家活跃度或奖池趋势的权重基准线，收分/放分线以其为基础波动 |
| 放分线（Release Line） | 中心线以上的控制线，系统开始倾向于让玩家赢 |
| 收分线（Collect Line） | 中心线以下的控制线，系统开始倾向于让玩家输 |
| 绝对放分线 | 中心线加浮动值得出的线，达到后触发强力放分机制 |
| 前置放分线 | 基于绝对放分线提前设定的缓释线，用于预警和逐步触发放分 |
| 前置放分权重值 | 1-1000中的一个数字，当库存达到前置放分线的时候，随机产生一个数，如果产生的这个数字小于这个放分权重值时，就执行放分，否则执行随机操作 |
| 绝对收分线 | 中心线减浮动值得出的线，达到后触发强力收分机制 |
| 前置收分线 | 基于绝对收分线提前设定的缓释线，用于预警和逐步触发收分 |
| 前置收分权重值 | 1-1000中的一个数字，当库存达到前置收分线的时候，随机产生一个数，如果产生的这个数字小于这个收分权重值时，就执行收分，否则执行随机操作 |
| 浮动值（Floating Value） | 中心线 × 比例计算后的值，限制在最大最小之间 |
| 明税 | 玩家赢钱时系统直接抽取的税收 |
| 暗税 | 系统从明税中按比例提取的额外库存，控制中心线变化，可为负 |
| 当前库存 | 所有玩家的总输赢值的总和，用当前库存来跟上面的收放分线来确认当局是放分、收分还是随机 |

## 三、中心线机制说明
中心线初始值默认为0，游戏每局结算时，根据本局“暗税”结果对中心线进行更新：
- 暗税 = 明税 × 暗税比例（单位：千分比）
- 中心线 = 上一局中心线 + 本局暗税

**说明**：暗税比例可以为负值。如果配置为负值（如 -10），代表中心线将下降（因为中心线加上一个负值），此时系统逐步向放分倾斜，库存释放速度加快。配置为正值时，中心线将逐步上升，被固化不释放的库存越来越多，系统逐步偏向收分。

## 四、配置项说明

### 1. 放分线配置

| 配置项 | 说明 |
|--------|------|
| release_line_max | 放分线浮动最大值 |
| release_line_min | 放分线浮动最小值 |
| release_line_ratio | 放分线浮动比例（如 0.2 即 20%） |
| pre_release_line_ratio | 前置放分线比例（如 0.7 即 70%） |

### 2. 收分线配置

| 配置项 | 说明 |
|--------|------|
| collect_line_max | 收分线浮动最大值 |
| collect_line_min | 收分线浮动最小值 |
| collect_line_ratio | 收分线浮动比例 |
| pre_collect_line_ratio | 前置收分线比例 |

### 3. 暗税配置
- **配置项**：dark_tax_ratio
- **说明**：按千分比设置暗税比例。
  - 10：代表明税的 1%
  - 100：代表明税的 10%
  - -10：代表明税的 -1%，即中心线将上升

### 4. 黑桃、红心、梅花、方块、皇冠、小皇冠权重配置
配置每局黑桃、红心、梅花、方块、皇冠、小皇冠的比例，这个比例的配置可以考虑改了以后立即生效，默认比例权重都是100，目的如下：
1. 在极端情况下（例如出现倍投玩家），针对性杀玩家

### 5. 收放分控制
- **随机**：不做控制，按照上面的花色权重配置出结果
- **收分**：首先随机出现结果，比值后计算本局库存是增加还是减少，如果库存增加，则满足收分，不做处理，如果库存减少，则重新随机结果，直到达到收分结果再发出，达到让库存增加或持平的目的
- **放分**：首先随机出现结果，比值后计算本局库存是增加还是减少，如果库存减少，则满足放分，不做处理，如果库存增加，直到达到放分结果再发出，达到让库存减少或持平的目的

### 6. Jackpot处理：
同时出现6个花色相同的概率是0.00013（万分之1.3），由于出现概率低，且赢奖倍数特别高（100倍），我们可以按照Jackpot方式来处理，不计入库存处理，也就是每局下注额拿出固定比例（这个比例可以配置，可以是暗税的比例或下注流水的比例）的金额进入Jackpot奖池，如果当出现6个花色相同的情况，并且有玩家下注，并且计算每种花色如果中奖，会给出多少奖励，算出最少的奖励花色，如果这个花色的奖励低于累计的Jackpot奖池金额，那就按照这个花色改变骰子的所有花色，让玩家中奖，如果Jackpot奖池金额不足以支付奖励，那就将6个骰子的花色都改成玩家未下注的花色，如果这种情况下，6个花色玩家都下注了，但是Jackpot奖池金额不足以支付奖金，就重新生成随机骰子花色，按照前面的收放分机制来处理

## 五、计算逻辑与流程

### 1. 放分线计算逻辑：
① 计算浮动值：
floating_value = clamp(C × release_line_ratio, release_line_min, release_line_max)
② 绝对放分线 = C + floating_value
③ 前置放分线 = C + (floating_value × pre_release_line_ratio)

### 2. 收分线计算逻辑：
① 计算浮动值：
floating_value = clamp(C × collect_line_ratio, collect_line_min, collect_line_max)
② 绝对收分线 = C - floating_value
③ 前置收分线 = C - (floating_value × pre_collect_line_ratio)

## 六、示例分析
**配置**：
- release_line_max = 30000
- release_line_min = 5000
- release_line_ratio = 0.2
- pre_release_line_ratio = 0.7

**示例 1**：中心线 = 20000
- floating_value = 20000 × 0.2 = 4000 → 小于最小值 → 取5000
- absolute_release_line = 20000 + 5000 = 25000
- pre_release_line = 20000 + (5000 × 0.7) = 23500

**示例 2**：中心线 = 1000000
- floating_value = 1000000 × 0.2 = 200000 → 超出最大值 → 取30000
- absolute_release_line = 1000000 + 30000 = 1030000
- pre_release_line = 1000000 + (30000 × 0.7) = 1021000

## 七、可视化建议
建议在后台监控系统中，绘制当前库存值、中心线、收分线、放分线的实时曲线图，以便于运维人员判断当前阶段系统盈利状态。也可用柱状图显示放分/收分段用户盈亏比例。