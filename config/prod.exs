import Config

# Note we also include the path to a cache manifest
# containing the digested version of static files. This
# manifest is generated by the `mix assets.deploy` task,
# which you should run after static files are built and
# before starting your production server.
config :cypridina, CypridinaWeb.Endpoint, cache_static_manifest: "priv/static/cache_manifest.json"

# Configures Swoosh API Client
config :swoosh, api_client: Swoosh.ApiClient.Req

# Disable Swoosh Local Memory Storage
config :swoosh, local: false

# 生产环境严格禁用模拟支付
config :cypridina, :use_mock_payment, true

# Do not print debug messages in production
config :logger, level: :info

# 生产环境支付系统日志配置
config :logger, :payment_logger,
  level: :info,
  path: "logs/payment.log",
  format: "[$time] [$level] [PAYMENT] $message\n"

# Runtime production configuration, including reading
# of environment variables, is done on config/runtime.exs.
