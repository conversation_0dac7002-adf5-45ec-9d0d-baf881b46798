# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :elixir, :compiler_options, [
  # 生产环境移除调试信息
  all_warnings: false,
  debug_info: Mix.env() == :dev,
  profile: :time,
  parallel: true,
  verbose: true,
  ignore_module_conflict: false,
  no_warn_undefined: :all
]

# 项目模式配置
# 从环境变量读取项目模式，默认为 teen
project_mode = System.get_env("PROJECT_MODE")

config :cypridina, :project_mode, project_mode

# 服务器基础URL配置
config :cypridina, :base_url, System.get_env("BASE_URL", "http://*************:4001")

config :ash,
  allow_forbidden_field_for_relationships_by_default?: true,
  include_embedded_source_by_default?: false,
  show_keysets_for_all_actions?: false,
  default_page_type: :keyset,
  policies: [no_filter_static_forbidden_reads?: false, log_policy_breakdowns: true],
  keep_read_action_loads_when_loading?: false,
  default_actions_require_atomic?: true,
  read_action_after_action_hooks_in_order?: true,
  bulk_actions_default_to_errors?: true,
  utc_datetime_type: :utc_datetime_usec,
  known_types: [AshMoney.Types.Money],
  custom_types: [money: AshMoney.Types.Money]

# 启用 Ash PubSub 调试，方便查看通知日志
config :ash, :pub_sub, debug?: true

config :spark,
  formatter: [
    remove_parens?: true,
    "Ash.Resource": [
      section_order: [
        :account,
        :balance,
        :transfer,
        :authentication,
        :tokens,
        :admin,
        :postgres,
        :resource,
        :code_interface,
        :actions,
        :policies,
        :pub_sub,
        :preparations,
        :changes,
        :validations,
        :multitenancy,
        :attributes,
        :relationships,
        :calculations,
        :aggregates,
        :identities
      ]
    ],
    "Ash.Domain": [
      section_order: [:admin, :resources, :policies, :authorization, :domain, :execution]
    ]
  ]

# 根据项目模式配置不同的 ash_domains
base_domains = [
  Cypridina.Accounts,
  Cypridina.Ledger,
  Cypridina.Communications
]

teen_domains = [
  Teen.CustomerService,
  Teen.PaymentSystem,
  Teen.BanSystem,
  Teen.GameManagement,
  Teen.ActivitySystem,
  Teen.Statistics,
  Teen.SystemSettings,
  Teen.PromotionSystem,
  Teen.UserSystem,
  Teen.ShopSystem,
  Teen.VipSystem,
  Teen.RobotManagement
]

race_domains = [
  RacingGame
]

ash_domains =
  case project_mode do
    "teen" -> base_domains ++ teen_domains
    "race" -> base_domains ++ race_domains
    "self" -> base_domains
    _ -> []
  end

config :cypridina,
  ecto_repos: [Cypridina.Repo],
  generators: [timestamp_type: :utc_datetime],
  ash_domains: base_domains ++ teen_domains ++ race_domains

# Configures the endpoint
config :cypridina, CypridinaWeb.Endpoint,
  url: [host: System.get_env("PHX_HOST") || "localhost"],
  check_origin: false,
  adapter: Bandit.PhoenixAdapter,
  render_errors: [
    formats: [html: CypridinaWeb.ErrorHTML, json: CypridinaWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: Cypridina.PubSub,
  live_view: [signing_salt: "XT6i4QNZ"]

# Configures the mailer
#
# By default it uses the "Local" adapter which stores the emails
# locally. You can see the emails in your browser, at "/dev/mailbox".
#
# For production it's recommended to configure a different adapter
# at the `config/runtime.exs`.
config :cypridina, Cypridina.Mailer, adapter: Swoosh.Adapters.Local

# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  cypridina: [
    args:
      ~w(js/app.js --bundle --target=es2022 --outdir=../priv/static/assets/js --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "4.0.9",
  cypridina: [
    args: ~w(
      --input=assets/css/app.css
      --output=priv/static/assets/css/app.css
    ),
    cd: Path.expand("..", __DIR__)
  ]

# Configures Elixir's Logger
config :logger, :default_formatter,
  format: "[$time][$metadata] $message\n",
  metadata: [
    :error_code,
    :tid,
    :turn,
    :round,
    :room_id,
    :area,
    :amount,
    :updated_bets,
    :payouts,
    :max_payout,
    :label,
    :game_id
  ],
  utc_log: false

# colors: [enabled: false]  # 禁用颜色输出，避免ANSI转义序列写入日志文件

config :logger, :default_handler,
  config: [
    file: ~c"logs/system.log",
    filesync_repeat_interval: 5000,
    file_check: 5000,
    max_no_bytes: 10_000_000,
    max_no_files: 5,
    compress_on_rotate: true
  ],
  filters: [
    {:room_filter, {&Cypridina.RoomSystem.RoomLogFilter.global_log_filter/2, []}}
  ],
  filter_default: :log

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

config :elixir, :time_zone_database, Tzdata.TimeZoneDatabase

config :cypridina, :teen,
  # 赛马游戏API接口
  DataCryptKey: "12sldakj~@!#!@ew",
  DataMD5CrypKey: "6b4b9ef284f0c357eb6211055a96caba"

config :cypridina, Oban,
  # log: :debug,
  notifier: Oban.Notifiers.PG,
  repo: Cypridina.Repo,
  plugins: [
    # 保留一天的作业历史
    {Oban.Plugins.Pruner, max_age: 60 * 60 * 24},
    {Oban.Plugins.Cron,
     crontab: [
       #  {"* * * * *", RacingGameScheduler, max_attempts: 1}
     ]}
  ],
  engine: Oban.Engines.Basic,
  queues: [
    default: 10,
    vip_updates: 5,
    activity_updates: 5,
    luck_updates: 3
  ]

# 启用 CLDR 配置
config :ex_cldr, default_backend: Cypridina.Cldr

# 配置 ex_money
config :ex_money,
  default_cldr_backend: Cypridina.Cldr,
  auto_start_exchange_rate_service: false

# 自定义货币现在通过Cldr.Currency服务定义，见lib/cypridina/custom_currencies.ex

# 短信服务配置
config :cypridina, :sms,
  api_url: "https://xunmaicloud.com/admin-api/business/sms-send-batch/send",
  app_key: System.get_env("SMS_APP_KEY") || "CpBFSvjCWjU8eGM1ln",
  secret_key: System.get_env("SMS_SECRET_KEY") || "jNErzLjqs8"

# 支付服务配置
config :cypridina, :payment,
  merchant_id: System.get_env("PAYMENT_MERCHANT_ID") || "10236",
  secret_key: System.get_env("PAYMENT_SECRET_KEY") || "ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM"

config :gettext, :default_locale, "zh"

# MinIO/S3 配置
config :ex_aws,
  region: "local"

config :ex_aws, :s3,
  region: "local",
  scheme: "http://",
  bucket: "cypridina",
  port: 9000

# Waffle 配置
config :waffle,
  storage: Waffle.Storage.S3,
  bucket: "cypridina",
  asset_host: "http://minio:9000/cypridina"

# Waffle S3 配置
config :ex_aws,
  access_key_id: System.get_env("MINIO_ROOT_USER") || "minioadmin",
  secret_access_key: System.get_env("MINIO_ROOT_PASSWORD") || "minioadmin",
  region: "local"

# config :error_tracker,
#   repo: Cypridina.Repo,
#   otp_app: :cypridina,
#   enabled: false

config :backpex,
  pubsub_server: Cypridina.PubSub,
  translator_function: {CypridinaWeb.CoreComponents, :translate_backpex},
  error_translator_function: {CypridinaWeb.CoreComponents, :translate_error}

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
import_config "games.exs"
