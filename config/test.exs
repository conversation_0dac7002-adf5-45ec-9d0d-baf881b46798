import Config
config :cyprid<PERSON>, token_signing_secret: "jo/7Oj7zzD4FhzOHm2VabbgHmLaNXm5/"
config :cypridina, env: :test
config :bcrypt_elixir, log_rounds: 1
config :cyp<PERSON><PERSON>, <PERSON><PERSON>, testing: :manual

# Configure your database
#
# The MIX_TEST_PARTITION environment variable can be used
# to provide built-in test partitioning in CI environment.
# Run `mix help test` for more information.
config :cypridina, Cypridina.Repo,
  username: "postgres",
  password: "zhp19940805",
  hostname: "localhost",
  database: "cypridina_test#{System.get_env("MIX_TEST_PARTITION")}",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: System.schedulers_online() * 2

# We don't run a server during test. If one is required,
# you can enable the server option below.
config :cyp<PERSON><PERSON>, CypridinaWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "W3NTey4gJf4gttvDYXOLc9ekojnkX6TCxfMghX2g7B33ujNKq4HuqtylqWZj4x4u",
  server: false

# In test we don't send emails
config :cypridina, Cypridina.Mailer, adapter: Swoosh.Adapters.Test

# Disable swoosh api client as it is only required for production adapters
config :swoosh, :api_client, false

# Print only warnings and errors during test
config :logger, level: :warning

# Initialize plugs at runtime for faster test compilation
config :phoenix, :plug_init_mode, :runtime

# Enable helpful, but potentially expensive runtime checks
config :phoenix_live_view,
  enable_expensive_runtime_checks: true
