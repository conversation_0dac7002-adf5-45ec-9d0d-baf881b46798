#!/bin/bash

# Heroicons Purge 修复脚本
# 此脚本自动修复 Tailwind CSS 删除 Heroicons 图标的问题

set -e

echo "🔧 开始修复 Heroicons Purge 问题..."

# 检查当前目录
if [ ! -f "mix.exs" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查 Heroicons 依赖
if [ ! -d "deps/heroicons" ]; then
    echo "❌ 错误: 未找到 heroicons 依赖，请先安装"
    echo "   运行: mix deps.get"
    exit 1
fi

echo "✅ 环境检查通过"

# 步骤 1: 生成图标安全列表
echo "📝 步骤 1: 生成图标安全列表..."
if [ -f "assets/js/generate_heroicons_safelist.js" ]; then
    node assets/js/generate_heroicons_safelist.js
    echo "✅ 图标安全列表生成完成"
else
    echo "❌ 错误: 未找到 generate_heroicons_safelist.js 脚本"
    exit 1
fi

# 步骤 2: 生成强制包含的 CSS
echo "🎨 步骤 2: 生成强制包含的图标 CSS..."
if [ -f "assets/js/generate_heroicons_css.js" ]; then
    node assets/js/generate_heroicons_css.js
    echo "✅ 图标 CSS 生成完成"
else
    echo "❌ 错误: 未找到 generate_heroicons_css.js 脚本"
    exit 1
fi

# 步骤 3: 检查 CSS 配置
echo "🔍 步骤 3: 检查 CSS 配置..."
if grep -q "heroicons-force.css" assets/css/app.css; then
    echo "✅ CSS 配置已正确设置"
else
    echo "⚠️  警告: CSS 配置可能需要手动更新"
    echo "   请确保在 assets/css/app.css 中包含:"
    echo "   @import \"./heroicons-force.css\";"
fi

# 步骤 4: 重新编译 Tailwind CSS
echo "🔄 步骤 4: 重新编译 Tailwind CSS..."
mix tailwind cypridina
echo "✅ Tailwind CSS 编译完成"

# 步骤 5: 验证修复结果
echo "🧪 步骤 5: 验证修复结果..."

# 检查生成的 CSS 文件
if [ -f "priv/static/assets/css/app.css" ]; then
    css_size=$(wc -c < "priv/static/assets/css/app.css")
    echo "✅ CSS 文件大小: $((css_size / 1024)) KB"
    
    # 检查是否包含图标样式
    if grep -q "hero-user" "priv/static/assets/css/app.css"; then
        echo "✅ CSS 文件包含图标样式"
    else
        echo "⚠️  警告: CSS 文件可能不包含图标样式"
    fi
else
    echo "❌ 错误: 未找到编译后的 CSS 文件"
    exit 1
fi

# 统计信息
echo ""
echo "📊 修复统计信息:"

if [ -f "assets/heroicons-safelist.js" ]; then
    icon_count=$(grep -c "hero-" assets/heroicons-safelist.js || echo "0")
    echo "   📋 安全列表图标数量: $icon_count"
fi

if [ -f "assets/css/heroicons-force.css" ]; then
    force_css_size=$(wc -c < "assets/css/heroicons-force.css")
    echo "   🎨 强制 CSS 文件大小: $((force_css_size / 1024)) KB"
fi

if [ -f "assets/css/heroicons-safelist.css" ]; then
    safelist_css_size=$(wc -c < "assets/css/heroicons-safelist.css")
    echo "   🛡️  保护 CSS 文件大小: $((safelist_css_size / 1024)) KB"
fi

echo ""
echo "🎉 Heroicons Purge 问题修复完成！"
echo ""
echo "📋 修复内容:"
echo "   ✅ 生成了完整的图标安全列表"
echo "   ✅ 创建了强制包含的 CSS 文件"
echo "   ✅ 重新编译了 Tailwind CSS"
echo "   ✅ 所有 Heroicons 图标现在都被保护"
echo ""
echo "🧪 测试建议:"
echo "   1. 打开 test_icons_fixed.html 查看图标显示"
echo "   2. 在浏览器开发者工具中检查图标样式"
echo "   3. 确认所有图标都正常显示"
echo ""
echo "🔄 如需重新修复，请再次运行此脚本"

# 创建测试报告
echo "📝 生成测试报告..."
cat > heroicons_fix_report.md << EOF
# Heroicons Purge 修复报告

## 修复时间
$(date)

## 修复内容
- ✅ 生成图标安全列表: $([ -f "assets/heroicons-safelist.js" ] && echo "完成" || echo "失败")
- ✅ 生成强制 CSS: $([ -f "assets/css/heroicons-force.css" ] && echo "完成" || echo "失败")
- ✅ 重新编译 CSS: $([ -f "priv/static/assets/css/app.css" ] && echo "完成" || echo "失败")

## 文件统计
- 图标安全列表: $([ -f "assets/heroicons-safelist.js" ] && wc -l < "assets/heroicons-safelist.js" || echo "0") 行
- 强制 CSS 文件: $([ -f "assets/css/heroicons-force.css" ] && wc -l < "assets/css/heroicons-force.css" || echo "0") 行
- 最终 CSS 大小: $([ -f "priv/static/assets/css/app.css" ] && echo "$(($(wc -c < "priv/static/assets/css/app.css") / 1024)) KB" || echo "未知")

## 测试方法
1. 打开 \`test_icons_fixed.html\` 查看图标显示
2. 检查浏览器开发者工具中的图标样式
3. 确认所有图标变体都正常工作

## 维护说明
- 如果添加新图标，运行 \`./scripts/fix_heroicons_purge.sh\` 重新生成
- 定期检查 CSS 文件大小，确保图标样式被正确包含
- 如果遇到问题，检查 Tailwind 配置和插件设置
EOF

echo "✅ 测试报告已生成: heroicons_fix_report.md"
echo ""
echo "🚀 修复完成！现在所有 Heroicons 图标都不会被 Tailwind purge 删除了！"
