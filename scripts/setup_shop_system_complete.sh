#!/bin/bash

# 商店系统完整初始化脚本
# 使用方法: ./scripts/setup_shop_system_complete.sh

echo "🚀 开始完整初始化商店系统..."

# 检查是否在项目根目录
if [ ! -f "mix.exs" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 1. 生成数据库迁移
echo "📋 生成数据库迁移文件..."
mix ash.codegen --name create_shop_system_tables

if [ $? -eq 0 ]; then
    echo "✅ 数据库迁移文件生成成功"
else
    echo "❌ 数据库迁移文件生成失败"
    exit 1
fi

# 2. 运行数据库迁移
echo "🔄 运行数据库迁移..."
mix ecto.migrate

if [ $? -eq 0 ]; then
    echo "✅ 数据库迁移完成"
else
    echo "❌ 数据库迁移失败"
    exit 1
fi

# 3. 运行种子文件
echo "🌱 运行种子文件..."
mix run priv/repo/seeds/shop_system_seeds.exs

if [ $? -eq 0 ]; then
    echo "✅ 种子文件执行完成"
else
    echo "❌ 种子文件执行失败"
    exit 1
fi

# 4. 编译项目
echo "🔨 编译项目..."
mix compile

if [ $? -eq 0 ]; then
    echo "✅ 项目编译完成"
else
    echo "❌ 项目编译失败"
    exit 1
fi

echo ""
echo "🎉 商店系统初始化完成！"
echo ""
echo "📋 系统功能:"
echo "  • 商品管理后台: http://localhost:4000/admin/products"
echo "  • 商品模板管理: http://localhost:4000/admin/product-templates"
echo "  • 购买记录管理: http://localhost:4000/admin/user-purchases"
echo ""
echo "🔗 API接口:"
echo "  • 获取商品列表: GET /api/shop/products"
echo "  • 获取商品详情: GET /api/shop/products/:id"
echo "  • 创建购买订单: POST /api/shop/purchase"
echo "  • 支付回调: POST /api/shop/payment_callback"
echo "  • 购买历史: GET /api/shop/purchases"
echo ""
echo "🚀 启动开发服务器:"
echo "  mix phx.server"
echo ""
echo "✨ 现在你的支付系统可以销售具体的商品了！"
