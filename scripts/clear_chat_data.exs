#!/usr/bin/env elixir

# 清理所有聊天数据的脚本
# 使用方法: mix run scripts/clear_chat_data.exs

defmodule ClearChatData do
  @moduledoc """
  清理数据库中所有聊天相关数据的脚本
  
  包括：
  1. 客服聊天数据 (customer_chats)
  2. 通用聊天系统数据 (chat_messages, chat_sessions, chat_participants, message_read_receipts)
  """

  require Logger
  alias Cypridina.Repo

  def run do
    Logger.info("🗑️  开始清理所有聊天数据...")
    
    # 确认操作
    if confirm_deletion() do
      clear_all_chat_data()
    else
      Logger.info("❌ 操作已取消")
    end
  end

  defp confirm_deletion do
    IO.puts("\n⚠️  警告：此操作将删除数据库中的所有聊天数据！")
    IO.puts("包括：")
    IO.puts("  - 客服聊天记录 (customer_chats)")
    IO.puts("  - 聊天会话 (chat_sessions)")
    IO.puts("  - 聊天消息 (chat_messages)")
    IO.puts("  - 聊天参与者 (chat_participants)")
    IO.puts("  - 消息已读回执 (message_read_receipts)")
    IO.puts("\n此操作不可逆！")
    
    answer = IO.gets("确定要继续吗？(输入 'yes' 确认): ") |> String.trim()
    answer == "yes"
  end

  defp clear_all_chat_data do
    Repo.transaction(fn ->
      try do
        # 1. 清理客服聊天数据
        clear_customer_service_chats()
        
        # 2. 清理通用聊天系统数据
        clear_general_chat_data()
        
        Logger.info("✅ 所有聊天数据清理完成！")
      rescue
        error ->
          Logger.error("❌ 清理过程中发生错误: #{inspect(error)}")
          Repo.rollback(error)
      end
    end)
  end

  defp clear_customer_service_chats do
    Logger.info("🧹 清理客服聊天数据...")
    
    # 获取记录数量
    count_query = "SELECT COUNT(*) FROM customer_chats"
    {:ok, %{rows: [[count]]}} = Repo.query(count_query)
    
    if count > 0 do
      Logger.info("📊 找到 #{count} 条客服聊天记录")
      
      # 删除所有客服聊天记录
      delete_query = "DELETE FROM customer_chats"
      {:ok, %{num_rows: deleted_count}} = Repo.query(delete_query)
      
      Logger.info("✅ 已删除 #{deleted_count} 条客服聊天记录")
    else
      Logger.info("ℹ️  没有找到客服聊天记录")
    end
  end

  defp clear_general_chat_data do
    Logger.info("🧹 清理通用聊天系统数据...")
    
    # 按照外键依赖顺序删除
    tables = [
      {"message_read_receipts", "消息已读回执"},
      {"chat_messages", "聊天消息"},
      {"chat_participants", "聊天参与者"},
      {"chat_sessions", "聊天会话"}
    ]
    
    Enum.each(tables, fn {table, description} ->
      clear_table(table, description)
    end)
  end

  defp clear_table(table_name, description) do
    # 检查表是否存在
    check_query = """
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = $1
    )
    """
    
    case Repo.query(check_query, [table_name]) do
      {:ok, %{rows: [[true]]}} ->
        # 表存在，继续删除
        count_query = "SELECT COUNT(*) FROM #{table_name}"
        
        case Repo.query(count_query) do
          {:ok, %{rows: [[count]]}} ->
            if count > 0 do
              Logger.info("📊 找到 #{count} 条#{description}记录")
              
              delete_query = "DELETE FROM #{table_name}"
              {:ok, %{num_rows: deleted_count}} = Repo.query(delete_query)
              
              Logger.info("✅ 已删除 #{deleted_count} 条#{description}记录")
            else
              Logger.info("ℹ️  没有找到#{description}记录")
            end
          
          {:error, reason} ->
            Logger.warn("⚠️  无法查询表 #{table_name}: #{inspect(reason)}")
        end
      
      {:ok, %{rows: [[false]]}} ->
        Logger.info("ℹ️  表 #{table_name} 不存在，跳过")
      
      {:error, reason} ->
        Logger.warn("⚠️  检查表 #{table_name} 时出错: #{inspect(reason)}")
    end
  end

  # 使用Ash资源进行清理的替代方法
  def clear_with_ash do
    Logger.info("🗑️  使用Ash资源清理聊天数据...")
    
    try do
      # 清理客服聊天数据
      clear_customer_chats_with_ash()
      
      # 清理通用聊天数据
      clear_general_chat_with_ash()
      
      Logger.info("✅ Ash清理完成！")
    rescue
      error ->
        Logger.error("❌ Ash清理过程中发生错误: #{inspect(error)}")
    end
  end

  defp clear_customer_chats_with_ash do
    alias Teen.CustomerService.CustomerChat
    
    Logger.info("🧹 使用Ash清理客服聊天记录...")
    
    case CustomerChat |> Ash.read() do
      {:ok, chats} ->
        count = length(chats)
        Logger.info("📊 找到 #{count} 条客服聊天记录")
        
        if count > 0 do
          Enum.each(chats, fn chat ->
            case CustomerChat.destroy(chat) do
              :ok -> :ok
              {:error, reason} ->
                Logger.warn("⚠️  删除聊天记录 #{chat.id} 失败: #{inspect(reason)}")
            end
          end)
          
          Logger.info("✅ 客服聊天记录清理完成")
        end
      
      {:error, reason} ->
        Logger.error("❌ 读取客服聊天记录失败: #{inspect(reason)}")
    end
  end

  defp clear_general_chat_with_ash do
    # 这里可以添加通用聊天系统的Ash资源清理
    # 如果需要的话
    Logger.info("ℹ️  通用聊天系统Ash清理暂未实现")
  end

  # 重置自增ID序列（如果需要的话）
  def reset_sequences do
    Logger.info("🔄 重置序列...")
    
    sequences = [
      "customer_chats_id_seq",
      "chat_sessions_id_seq", 
      "chat_messages_id_seq",
      "chat_participants_id_seq",
      "message_read_receipts_id_seq"
    ]
    
    Enum.each(sequences, fn seq_name ->
      reset_query = "SELECT setval('#{seq_name}', 1, false)"
      
      case Repo.query(reset_query) do
        {:ok, _} ->
          Logger.info("✅ 序列 #{seq_name} 已重置")
        
        {:error, %{postgres: %{code: :undefined_table}}} ->
          Logger.info("ℹ️  序列 #{seq_name} 不存在，跳过")
        
        {:error, reason} ->
          Logger.warn("⚠️  重置序列 #{seq_name} 失败: #{inspect(reason)}")
      end
    end)
  end

  # 显示清理后的统计信息
  def show_stats do
    Logger.info("📊 清理后统计信息:")
    
    tables = [
      {"customer_chats", "客服聊天记录"},
      {"chat_sessions", "聊天会话"},
      {"chat_messages", "聊天消息"},
      {"chat_participants", "聊天参与者"},
      {"message_read_receipts", "消息已读回执"}
    ]
    
    Enum.each(tables, fn {table, description} ->
      count_query = "SELECT COUNT(*) FROM #{table}"
      
      case Repo.query(count_query) do
        {:ok, %{rows: [[count]]}} ->
          Logger.info("  #{description}: #{count} 条记录")
        
        {:error, %{postgres: %{code: :undefined_table}}} ->
          Logger.info("  #{description}: 表不存在")
        
        {:error, reason} ->
          Logger.warn("  #{description}: 查询失败 - #{inspect(reason)}")
      end
    end)
  end
end

# 执行清理
case System.argv() do
  ["--ash"] ->
    ClearChatData.clear_with_ash()
  ["--stats"] ->
    ClearChatData.show_stats()
  ["--reset-seq"] ->
    ClearChatData.reset_sequences()
  _ ->
    ClearChatData.run()
    ClearChatData.show_stats()
end
