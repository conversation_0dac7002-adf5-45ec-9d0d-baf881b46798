#!/usr/bin/env elixir

# Heroicons CSS 优化脚本
# 此脚本将优化 heroicons-force.css 文件，减少重复代码并提高性能

defmodule HeroiconsOptimizer do
  @moduledoc """
  优化 Heroicons CSS 文件的工具模块
  """

  def optimize_file(input_path, output_path) do
    IO.puts("开始优化 Heroicons CSS 文件...")
    IO.puts("输入文件: #{input_path}")
    IO.puts("输出文件: #{output_path}")

    content = File.read!(input_path)
    optimized_content = optimize_content(content)

    File.write!(output_path, optimized_content)

    original_size = byte_size(content)
    optimized_size = byte_size(optimized_content)
    reduction = original_size - optimized_size
    percentage = Float.round(reduction / original_size * 100, 2)

    IO.puts("优化完成!")
    IO.puts("原始大小: #{format_bytes(original_size)}")
    IO.puts("优化后大小: #{format_bytes(optimized_size)}")
    IO.puts("减少: #{format_bytes(reduction)} (#{percentage}%)")
  end

  defp optimize_content(content) do
    # 提取所有图标定义 - 处理两种格式
    icon_pattern = ~r/\.hero-([a-z0-9-]+)\s*\{[^}]+\}/s

    icons = Regex.scan(icon_pattern, content)
    |> Enum.map(&extract_icon_data/1)
    |> Enum.filter(& &1)

    IO.puts("找到 #{length(icons)} 个图标定义")

    # 生成优化后的 CSS
    generate_optimized_css(icons)
  end

  defp extract_icon_data([full_match]) do
    # 提取图标名称
    case Regex.run(~r/\.hero-([a-z0-9-]+)/, full_match) do
      [_, name] ->
        # 尝试提取 SVG URL - 支持两种格式
        svg_url = case Regex.run(~r/--hero-icon:\s*url\('([^']+)'\)/, full_match) do
          [_, url] -> url
          _ ->
            case Regex.run(~r/--hero-[^:]+:\s*url\('([^']+)'\)/, full_match) do
              [_, url] -> url
              _ -> nil
            end
        end

        if svg_url, do: {name, svg_url}, else: nil
      _ -> nil
    end
  end

  defp generate_optimized_css(icons) do
    header = """
    /*
     * 优化的 Heroicons CSS 定义
     * 包含所有图标的完整 CSS 类定义，经过优化以减少重复代码
     * 生成时间: #{DateTime.utc_now() |> DateTime.to_iso8601()}
     * 图标数量: #{length(icons)}
     */

    /* ===== 基础样式定义 ===== */

    /* 所有 Heroicon 图标的基础样式 */
    [class^="hero-"],
    [class*=" hero-"] {
      -webkit-mask-repeat: no-repeat;
      mask-repeat: no-repeat;
      background-color: currentColor;
      vertical-align: middle;
      display: inline-block;
      width: 1.5rem;
      height: 1.5rem;
    }

    /* ===== 尺寸变体 ===== */

    /* 小尺寸图标 (1rem) */
    .hero-sm,
    [class^="hero-"].hero-sm,
    [class*=" hero-"].hero-sm {
      width: 1rem;
      height: 1rem;
    }

    /* 大尺寸图标 (2rem) */
    .hero-lg,
    [class^="hero-"].hero-lg,
    [class*=" hero-"].hero-lg {
      width: 2rem;
      height: 2rem;
    }

    /* 超大尺寸图标 (2.5rem) */
    .hero-xl,
    [class^="hero-"].hero-xl,
    [class*=" hero-"].hero-xl {
      width: 2.5rem;
      height: 2.5rem;
    }

    /* ===== 实用工具类 ===== */

    /* 图标旋转 */
    .hero-rotate-90 { transform: rotate(90deg); }
    .hero-rotate-180 { transform: rotate(180deg); }
    .hero-rotate-270 { transform: rotate(270deg); }

    /* 图标翻转 */
    .hero-flip-x { transform: scaleX(-1); }
    .hero-flip-y { transform: scaleY(-1); }

    /* 图标对齐 */
    .hero-align-top { vertical-align: top; }
    .hero-align-bottom { vertical-align: bottom; }
    .hero-align-text-top { vertical-align: text-top; }
    .hero-align-text-bottom { vertical-align: text-bottom; }

    /* 图标颜色变体 */
    .hero-text-primary { color: var(--color-primary, #3b82f6); }
    .hero-text-secondary { color: var(--color-secondary, #6b7280); }
    .hero-text-success { color: var(--color-success, #10b981); }
    .hero-text-warning { color: var(--color-warning, #f59e0b); }
    .hero-text-error { color: var(--color-error, #ef4444); }

    /* 响应式尺寸 */
    @media (min-width: 640px) {
      .sm\\:hero-sm { width: 1rem; height: 1rem; }
      .sm\\:hero-lg { width: 2rem; height: 2rem; }
      .sm\\:hero-xl { width: 2.5rem; height: 2.5rem; }
    }

    @media (min-width: 768px) {
      .md\\:hero-sm { width: 1rem; height: 1rem; }
      .md\\:hero-lg { width: 2rem; height: 2rem; }
      .md\\:hero-xl { width: 2.5rem; height: 2.5rem; }
    }

    @media (min-width: 1024px) {
      .lg\\:hero-sm { width: 1rem; height: 1rem; }
      .lg\\:hero-lg { width: 2rem; height: 2rem; }
      .lg\\:hero-xl { width: 2.5rem; height: 2.5rem; }
    }

    /* ===== 图标定义 ===== */

    """

    icon_definitions = icons
    |> Enum.map(fn {name, svg_url} ->
      ".hero-#{name} {\n  --hero-icon: url('#{svg_url}');\n  -webkit-mask: var(--hero-icon);\n  mask: var(--hero-icon);\n}"
    end)
    |> Enum.join("\n\n")

    footer = """

    /* ===== 动画效果 ===== */

    /* 悬停效果 */
    .hero-hover-scale:hover {
      transform: scale(1.1);
      transition: transform 0.2s ease-in-out;
    }

    .hero-hover-rotate:hover {
      transform: rotate(15deg);
      transition: transform 0.2s ease-in-out;
    }

    /* 加载动画 */
    .hero-spin {
      animation: hero-spin 1s linear infinite;
    }

    @keyframes hero-spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    /* 脉冲动画 */
    .hero-pulse {
      animation: hero-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes hero-pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    /* 弹跳动画 */
    .hero-bounce {
      animation: hero-bounce 1s infinite;
    }

    @keyframes hero-bounce {
      0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
      }
      50% {
        transform: none;
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
      }
    }
    """

    header <> icon_definitions <> footer
  end

  defp format_bytes(bytes) when bytes >= 1_048_576 do
    "#{Float.round(bytes / 1_048_576, 2)} MB"
  end

  defp format_bytes(bytes) when bytes >= 1024 do
    "#{Float.round(bytes / 1024, 2)} KB"
  end

  defp format_bytes(bytes) do
    "#{bytes} bytes"
  end
end

# 运行优化
case System.argv() do
  [input_path, output_path] ->
    HeroiconsOptimizer.optimize_file(input_path, output_path)

  [input_path] ->
    output_path = String.replace(input_path, ".css", "-optimized.css")
    HeroiconsOptimizer.optimize_file(input_path, output_path)

  _ ->
    IO.puts("用法: elixir optimize_heroicons.exs <input_file> [output_file]")
    IO.puts("示例: elixir optimize_heroicons.exs assets/css/heroicons-force.css")
end
