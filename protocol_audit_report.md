# Cypridina服务端SC协议审计报告

## 一、Protocol.ts中定义的SC协议清单

### 1. RegLogin模块 (MainProto: 0)
- SC_NORMAL_REG_P (1) - 普通注册响应
- SC_LOGIN_P (3) - 登录响应  
- SC_ONLINE_P (5) - 发送上线信息
- SC_CHECK_REGINFO_P (7) - 校验注册信息响应
- SC_GET_RANDOM_NICKNAME_P (9) - 返回随机昵称
- SC_OHTER_LOGIN_P (10) - 账号在别处登录通知(被挤下线)
- SC_LOGIN_OTHER_P (11) - 账号在别处登录通知(挤掉其他设备)
- SC_SERVER_STOP_P (12) - 服务器停机维护通知
- SC_UPDATE_SAVE_RANDOM_P (13) - 更新保存密码随机数
- SC_FULLCONNECT_ATTACK_P (14) - 全连接攻击断开通知
- SC_WEB_KILL_P (15) - 被踢下线
- SC_GAMESERVER_LOGIN_P (17) - 游戏服务器登录响应
- SC_GAMESERVER_ONLIEN_P (18) - 游戏服务器上线通知
- SC_HALL_SERVER_VERSION_P (20) - 大厅版本号
- SC_GAME_SERVER_VERSION_P (21) - 游戏服务版本号
- SC_REQUEST_REG_PHONECODE_P (30) - 手机验证码请求响应
- SC_PHONECODE_REG_P (32) - 手机注册响应
- SC_CHECK_CONNECT_P (37) - 检测连接响应
- SC_REQUEST_VERCODE_P (42) - 请求验证码响应
- SC_VERCODE_HALL_RESULT_P (44) - 大厅验证码结果
- SC_VERCODE_GAME_RESULT_P (45) - 游戏验证码结果
- DC_REQUEST_SYSTEM_STATUS_P (50) - 返回系统配置
- SC_REQUEST_GAMEVERSIONS_P (52) - 服务器下发游戏版本号列表

### 2. BaseInfo模块 (MainProto: 6)  
- SC_SET_NICKNAME_RESULT_P (1) - 设置昵称结果返回
- SC_SET_NICKNAME_P (2) - 昵称修改成功通知
- SC_CHANGE_PSW_RESULT_P (6) - 修改密码结果
- SC_SET_LOTTERY_P (7) - 奖券改变
- SC_CHANGE_PSW_CHECK_P (9) - 验证修改后密码有效性
- SC_SET_SPECPHONE_P (11) - 设置特殊手机号
- SC_FRIEND_P (13) - 朋友圈
- SC_SET_ROBOT_LEVEL_P (14) - 设置机器人等级
- SC_CHANGE_LOTTERY_P (15) - 变更奖券
- DC_SET_SEX_P (17) - 设置性别返回

### 3. Money模块 (MainProto: 7)
- SC_SET_MONEY_P (0) - 金钱改变
- SC_SET_WALLETMONEY_P (1) - 钱包改变  
- SC_SAVE_MONEY_RESULT_P (3) - 存钱结果
- SC_GET_MONEY_RESULT_P (5) - 取钱结果
- SC_SET_GAME_MONEY_P (7) - 游戏中金钱改变
- SC_SET_GAME_WALLETMONEY_P (8) - 游戏中钱包改变
- SC_SET_HONOR_VALUE_P (9) - 荣誉点改变
- SC_SET_DIAMONDS_VALUE_P (10) - 钻石改变
- SC_DIAMONDS_CHANGE_MONEY_P (12) - 钻石兑换金币结果
- SC_DIAMONDS_CHANGE_VIP_P (14) - 钻石兑换会员结果
- SC_DIAMONDS_TRANS_MONEY_CONFING_P (16) - 钻石兑换金币配置
- SC_DIAMONDS_TRANS_VIP_CONFING_P (18) - 钻石兑换会员配置
- SC_RMB_TRANS_DIAMONDS_CONFING_P (20) - 人民币换钻石配置
- SC_RMB_TRANS_DIAMONDS_CONFING_IOS_P (22) - 人民币换钻石配置_IOS
- SC_TRANSFER_MONEY_P (24) - 玩家转账结果
- DS_BIND_PICKUP_P (26) - 绑定提取号返回
- DS_BANK_PASSWORD_P (28) - 修改银行密码返回
- DS_MONEY_CHANG_RMB_P (30) - 游戏币兑换现金返回
- DC_SEND_MSG_GUEST_SERVER_P (32) - 发送消息给客服返回
- DS_BIND_BANK_P (34) - 绑定银行卡返回
- SC_REQUEST_ALMS_RESULT_P (38) - 查询救济金结果
- SC_GET_ALMS_RESULT_P (36) - 领取救济金结果
- SC_COMPLAINT_RESULT_P (48) - 投诉结果
- SC_PAY (50) - 请求支付返回
- SC_VIP_PAY_LIST_P (52) - 请求VIP支付列表返回

### 4. FindPsw模块 (MainProto: 1)
- SC_FINDPSW_P (1) - 找回密码
- SC_FINDPSW_REQUEST_CODE_RESULT_P (3) - 请求手机验证码结果
- SC_FINDPSW_CKECK_P (6) - 验证结果
- SC_FINDPSW_SET_NEW_PSW_RESULT_P (8) - 设置新密码结果

### 5. NoticeManager模块 (MainProto: 15)
- SC_NOTICE_P (0) - 系统公告推送
- SC_SEND_NOTICE_P (2) - 请求发送公告结果
- SC_REQUEST_NOTICE_NEED_P (4) - 请求发送公告所需
- DC_REQUEST_SYSTEM_NOTICE_P (6) - 请求系统公告内容返回

### 6. Rank模块 (MainProto: 40)
- SC_RANK_DATA (1) - 后端返回排行榜信息
- DC_RANK_LIST (3) - dbserver返回排行榜信息
- SC_SELF_RANK_DATA_P (5) - 玩家自己的排行数据返回
- DC_SELF_RANK_DATA (7) - dbserver返回自己的排行数据

### 7. MailManager模块 (MainProto: 14)
- SC_REQUEST_MAIL_INFO_P (1) - 邮件详情返回
- SC_ADD_MAIL_P (4) - 新邮件推送通知
- SC_REQUEST_MAILLIST_P (6) - 邮件列表返回
- SC_REQUEST_NEW_MAIL_COUNT_P (8) - 新邮件数量返回

### 8. Game模块 (MainProto: 4)
- SC_ADD_GAMELIST_P (0) - 添加游戏到列表
- SC_DEL_GAMELIST_P (1) - 从列表删除游戏
- SC_ROOM_INFO_P (2) - 房间数据
- SC_ROOM_SET_PLAYER_STATE_P (4) - 设置玩家状态(广播)
- SC_ROOM_SET_STATE_P (5) - 设置房间状态(广播)
- SC_ROOM_CHAT_P (7) - 聊天(广播)
- SC_ROOM_RESET_COIN_P (8) - 重置金币
- SC_ROOM_ZANLI_SUCCESS_P (9) - 暂离成功
- SC_ROOM_ZANLI_COMBACK_SUCCESS_P (11) - 玩家请求暂离回来成功
- SC_ROOM_PLAYER_ENTER_P (12) - 玩家进入房间(广播)
- SC_ROOM_WATCH_ENTER_P (13) - 观看者进入房间(广播)
- SC_ROOM_PLAYER_QUIT_P (14) - 玩家离开房间(广播)
- SC_ROOM_WATCH_QUIT_P (15) - 观看者离开房间(广播)
- SC_ROOM_DEL_P (16) - 同意退出房间
- SC_ROOM_PREPARE_TIMEOUT_P (17) - 准备超时踢出
- SC_ROOM_DEL_PLAYER_P (18) - 游戏结束踢出
- SC_ROOM_DEL_WATCH_P (19) - 观看者被踢出
- SC_GAME_PLAYER_NUM_P (35) - 游戏玩家人数
- SC_UPDATE_GAME_LIST_P (36) - 更新游戏列表
- SC_SELECT_GAME_P (38) - 选择游戏结果
- SC_WATCH_P (42) - 观看结果
- SC_HUANZHUO_P (44) - 换桌结果
- SC_MODE1_ENTER_P (47) - 模式1进入结果
- SC_MODE1_ROBOT_FAILD_P (48) - 机器人进入失败
- SC_MODE2_DATA_P (50) - 模式2数据
- SC_MODE2_ADD_PLAYER_P (51) - 模式2增加玩家
- SC_MODE2_DEL_PLAYER_P (52) - 模式2删除玩家
- SC_MODE2_ROOM_STATE_P (53) - 模式2房间状态
- SC_MODE2_DATA_CREAT_ROOM_P (54) - 模式2创建房间
- SC_MODE2_DATA_CLEAR_ROOM_P (55) - 模式2清空房间
- SC_MODE2_CREATE_ROOM_P (57) - 模式2创建房间结果
- SC_MODE2_ENTER_ROOM_P (59) - 模式2进入房间结果
- SC_MODE3_ENTER_P (63) - 模式3进入
- SC_MODE3_GAME_ENTER_P (64) - 模式3游戏进入
- SC_MODE3_RETURNERROR_P (65) - 模式3错误返回
- SC_MODE3_ENTER_PIPEI_P (67) - 模式3进入匹配
- SC_MODE3_PIPEI_OVER_P (68) - 模式3匹配完成
- SC_MODE3_QUIT_PIPEI_SUCCESS_P (69) - 模式3退出匹配成功
- SC_MODE3_CHAT_P (71) - 模式3聊天
- SC_MODE3_PALYERONLINE_P (72) - 模式3玩家上下线
- SC_MODE3_GETCOMPPLAYERRANK_P (74) - 模式3玩家排名
- SC_MODE3_OTHER_ONLINE_P (75) - 模式3其他在线玩家
- SC_GAME_CLOSE_P (76) - 游戏关闭
- SC_VIRTUAL_ENDGAME_P (85) - 虚拟结束游戏
- SC_ROOM_DELETE_P (86) - 房间删除
- SC_REQUEST_UNONLINE_CLEW_P (91) - 离线挽留信息
- SC_ROBOT_START_PREPARE_P (92) - 通知准备
- SC_MODE1_ENTER_PIPEI_P (94) - 模式1进入匹配
- SC_MODE1_PIPEI_OVER_P (95) - 模式1匹配完成
- SC_MODE1_QUIT_PIPEI_SUCCESS_P (96) - 模式1退出匹配成功
- SC_CHANGE_PLAYER_GAME_RESULT_P (98) - 改变玩家游戏结果
- SC_MODE3_PIPEI_STOP_P (99) - 停止比赛匹配
- SC_PLAYER_LEAVE_MODE_P (108) - 离开模式
- SC_REQUEST_ROOM_P (110) - 重新请求房间数据
- SC_REQUEST_BROADCAST_PLAYERNUM_P (112) - 主播房间人数
- SC_ADD_BROADCAST_GAME_P (113) - 增加主播游戏
- SC_DEL_BROADCAST_GAME_P (114) - 减少主播游戏
- SC_RAND_ROOM_CHAT_P (115) - 随机房间聊天
- SC_ROOM_VIP_DEL_P (116) - VIP退出删除房间
- SC_BROAD_PLAYERNUM_DETAIL_P (118) - 主播玩家数量统计
- SC_UPDATE_GAME_P (119) - 变更游戏服务器参数
- SC_SET_PLAYER_SEATID_P (120) - 设置玩家座位ID
- SC_CLEAR_PLAYER_SEATID_P (121) - 清除玩家座位ID
- SC_REQUEST_ENTER_GAME_P (124) - 请求进入游戏房间返回
- SC_PLAYER_REPORT_P (126) - 举报响应
- SC_SLIDE_VERIFY_REQUEST_P (127) - 发送验证请求
- SC_SLIDE_VERIFY_P (129) - 服务器验证结果
- GS_2_CLIENT_LOGIN_KEY_P (132) - 登录密钥
- SC_ROOM_TIPDEALER_P (159) - 打赏荷官(广播)
- SC_GAME_BJL_ALLINFO_P (1310) - 百家乐全部桌面信息
- SC_MODE4_ENTER_P (1340) - 模式4进入
- SC_MODE4_ROBOT_FAILD_P (1350) - 模式4机器人失败

### 9. XC模块 (MainProto: 5)
- XC_ROOM_INFO_P (0) - 房间数据
- XC_JIESUAN_P (1) - 结算数据
- SC_VIRTUAL_BROADCAST_TIPS_P (2) - 虚拟主播游戏TIPS
- XC_BROADCAST_PROTOCOL_P (3) - 子游戏广播消息
- XC_ROBOT_BROADCAST_P (4) - 子游戏机器人广播

### 10. DbServer模块 (MainProto: 34)
- SC_SET_HEADID_P (43) - 设置头像返回
- SC_WEB_CHANGE_ATTRIB_P (37) - web请求变更玩家属性
- SC_CUSTSRV_REPLY_P (111) - 响应客服消息数据
- SC_NEW_CHARGE_LIST_P (113) - 获取未读充值消息

### 11. Task模块 (MainProto: 42)
- SC_UPDATE_TASK_LIST (1) - 更新任务列表信息
- SC_GET_TASK_REWARD (3) - 领取任务奖励返回
- SC_UPDATE_MATCH_ATTR (10) - 更新比赛状态
- SC_GET_MATCH_LIST (12) - 获取比赛列表信息
- SC_GET_MATCH_RANK_REWARD (14) - 获取比赛排名和奖励返回
- SC_GET_TODAY_MATCH_LIST (16) - 获取今日比赛列表信息

### 12. HallActivity模块 (MainProto: 101)
- SC_LOGINCASH_INFO_P (1) - 登录活动信息返回
- SC_FETCH_LOGINCASH_AWARD_P (3) - 领取登录活动奖励返回
- SC_GET_USER_MONEY_P (5) - 返回用户金币信息
- SC_FETCH_USER_BONUS_P (7) - 返回领取用户积分信息
- SC_GET_SEVEN_DAYS_P (9) - 返回7日签到活动信息
- SC_FETCH_SEVEN_DAYS_AWARD_P (11) - 返回7日签到活动奖励
- SC_GET_THIRTY_CARD_P (13) - 返回30次刮刮卡活动信息
- SC_FETCH_THIRTY_CARD_P (15) - 返回30次刮刮卡领取结果
- SC_GAME_TASK_P (17) - 返回游戏任务信息
- SC_FETCH_GAME_TASK_AWARD_P (19) - 返回游戏任务奖励领取结果
- SC_GET_GIFT_CHARGE_P (21) - 返回礼包活动信息
- SC_GET_CARD_TASK_P (23) - 返回周卡月卡活动信息
- SC_FETCH_CARD_TASK_P (25) - 返回周卡月卡活动奖励领取结果
- SC_GET_VIP_GIFT_P (27) - 返回VIP礼包活动信息
- SC_FETCH_VIP_GIFT_P (29) - 返回VIP活动奖励领取结果
- SC_GET_FREE_BONUS_P (31) - 返回免费积分活动信息
- SC_FETCH_FREE_BONUS_P (33) - 返回免费积分活动奖励领取结果
- SC_GET_FREE_CASH_P (35) - 返回免费提现活动信息
- SC_FETCH_FREE_CASH_P (37) - 返回免费提现领取结果
- SC_GET_FREE_CASH_INVITATION_P (39) - 返回免费提现推广数据
- SC_FETCH_BROKE_AWARD_P (41) - 返回破产补助信息
- SC_FETCH_MAIL_AWARD_P (43) - 返回邮件奖励领取结果
- SC_BIND_PHONE_USER_P (45) - 返回绑定手机号码结果
- SC_BIND_MAIL_USER_P (47) - 返回绑定邮箱结果
- SC_FETCH_CDKEY_AWARD_P (49) - 返回CDKEY兑换结果

## 二、Cypridina服务端SC协议实现情况

### 已发现的协议实现文件：
1. teen_patti_message_builder.ex - Teen Patti游戏协议
2. longhu_message_builder.ex - 龙虎斗游戏协议
3. 各种游戏room模块 - 包含游戏特定协议

### 已实现的游戏协议（基于代码扫描）：
#### Teen Patti游戏协议 (子协议ID 1000+)
- sc_teenpatti_start: 1000
- sc_teenpatti_sendcard: 1001
- sc_teenpatti_bet: 1003
- sc_teenpatti_look: 1005
- sc_teenpatti_competition: 1007
- sc_teenpatti_compconfirm: 1009
- sc_teenpatti_fold: 1011
- sc_teenpatti_waitrecharge: 1013
- sc_teenpatti_waitopt: 1014

## 三、待审计的协议分类

### 高优先级（核心业务）：
1. 用户登录注册相关 (RegLogin)
2. 金币管理相关 (Money)
3. 游戏房间管理 (Game)
4. 大厅活动 (HallActivity)

### 中优先级（功能支持）：
1. 支付相关 (Pay)
2. 邮件公告 (MailManager, NoticeManager)
3. 基础信息 (BaseInfo)

### 低优先级（辅助功能）：
1. 排行榜 (Rank)
2. 任务系统 (Task)
3. 代理系统 (QMAgent)

## 四、审计发现的问题

### 1. RegLogin模块 (已审计)

#### 主要问题：
1. **主协议ID不匹配**：
   - Protocol.ts定义：MainProto.RegLogin = 0
   - 服务端实现：@protocol_id = 0 ✅（一致）

2. **SC_LOGIN_P (子协议3) 字段差异**：
   - **缺失字段**（客户端期望但服务端未提供）：
     - `playerid` - 应该使用 int64 类型
     - `nickname` - 玩家昵称
     - `money` - 游戏币余额  
     - `walletmoney` - 钱包余额
     - `headid` - 头像ID
     - `firstlogin` - 是否首次登录
     - `regsendmoney` - 注册赠送金额
     - `isbindaccount` - 是否绑定账号
     - `gamelist` - 游戏列表配置
     - `Function` - 功能开关配置
     - `loginparam` - 登录参数
     - `random` - 随机证书
   - **服务端提供的字段**：
     - `loginResult` - 使用了不同的字段名（应该是 `code`）
     - `userid` - 应该是 `playerid`
     - `username` - 客户端期望 `nickname`
     - `points` - 客户端未定义此字段
     - `vipLevel` - 客户端未定义此字段

3. **缺失的SC协议实现**：
   - SC_ONLINE_P (5)
   - SC_OHTER_LOGIN_P (10) 
   - SC_LOGIN_OTHER_P (11)
   - SC_SERVER_STOP_P (12)
   - SC_UPDATE_SAVE_RANDOM_P (13)
   - SC_FULLCONNECT_ATTACK_P (14)
   - SC_WEB_KILL_P (15)
   - SC_GAMESERVER_LOGIN_P (17)
   - SC_GAMESERVER_ONLIEN_P (18)
   - SC_HALL_SERVER_VERSION_P (20)
   - SC_GAME_SERVER_VERSION_P (21)

### 2. Money模块 (审计中)

#### 已发现问题：
1. **缺失的核心SC协议**：
   - SC_SET_MONEY_P (0) - 金钱改变通知 ❌ 未实现
   - SC_SET_WALLETMONEY_P (1) - 钱包改变通知 ❌ 未实现
   - SC_SET_GAME_MONEY_P (7) - 游戏中金钱改变 ❌ 未实现
   - SC_SET_GAME_WALLETMONEY_P (8) - 游戏中钱包改变 ❌ 未实现
   - SC_SET_HONOR_VALUE_P (9) - 荣誉点改变 ❌ 未实现
   - SC_SET_DIAMONDS_VALUE_P (10) - 钻石改变 ❌ 未实现

2. **子协议ID映射问题**：
   - 服务端定义的子协议ID与Protocol.ts不匹配
   - 例如：服务端使用 @sc_get_balance_p = 1，但客户端未定义此协议

### 3. 协议发送机制问题

1. **消息结构不一致**：
   - 客户端期望：`{ mainId, subId, data }`
   - 服务端通过Phoenix Channel推送：`push(socket, "message", payload)`
   - 需要确认payload的结构是否符合客户端期望

2. **协议响应机制**：
   - 服务端使用 `{:ok, sub_protocol_id, response_data}` 返回
   - 需要验证这如何转换为客户端期望的格式

## 五、修复建议

### 1. 紧急修复（影响核心功能）

#### 修复SC_LOGIN_P响应结构
```elixir
# 文件：lib/teen/protocol/reg_login_protocol.ex
# 修改 handle_login 函数

defp handle_login(data, context) do
  username = Map.get(data, "username")
  password = Map.get(data, "password")
  session_id = context.session_id
  
  case LoginService.authenticate_user(username, password) do
    {:ok, user} ->
      # 更新session信息
      LoginService.update_session(session_id, user.id)
      
      # 获取用户完整信息
      {:ok, user_info} = Accounts.get_user_full_info(user.id)
      {:ok, wallet_info} = Ledger.get_user_balance(user.id)
      
      response_data = %{
        "code" => 0,  # 成功
        "msg" => "",
        "playerid" => user.id,  # 使用playerid而非userid
        "nickname" => user.nickname || user.username,
        "money" => wallet_info.game_balance,
        "walletmoney" => wallet_info.wallet_balance,
        "headid" => user.avatar_id || 0,
        "firstlogin" => if(user.first_login_at == nil, do: 1, else: 0),
        "regsendmoney" => user.registration_bonus || 0,
        "isbindaccount" => if(user.phone_verified || user.email_verified, do: 1, else: 0),
        "gamelist" => get_game_list(),
        "Function" => %{
          "6" => %{  # BaseInfo
            "nickname" => user.nickname,
            "sex" => user.gender || 0,
            "headurl" => user.avatar_url || "",
            "vip" => user.vip_level || 0
          },
          "7" => %{  # Money
            "walletmoney" => wallet_info.wallet_balance,
            "money" => wallet_info.game_balance,
            "winningmoney" => wallet_info.winning_balance || 0,
            "bonusmoney" => wallet_info.bonus_balance || 0
          },
          "33" => %{  # AccountInfo
            "account" => user.username,
            "phone" => user.phone || "",
            "email" => user.email || ""
          }
        },
        "loginparam" => %{},
        "random" => generate_random_token(),
        "state" => 0,  # 用户状态
        "isgame" => 0  # 是否在游戏中
      }
      
      {:ok, @sc_login_p, response_data}
      
    # ... 错误处理保持不变，但使用code字段
  end
end
```

#### 实现金币变更通知协议
```elixir
# 新建文件：lib/teen/protocol/money_notifications.ex

defmodule Teen.Protocol.MoneyNotifications do
  @moduledoc """
  处理金币相关的主动通知协议
  """
  
  alias Teen.Protocol.ProtocolUtils
  alias CypridinaWeb.Endpoint
  
  # 协议常量
  @main_id 7  # Money主协议
  @sc_set_money_p 0
  @sc_set_walletmoney_p 1
  @sc_set_game_money_p 7
  @sc_set_game_walletmoney_p 8
  
  @doc """
  发送金币变更通知
  """
  def notify_money_change(user_id, new_balance) do
    message = %{
      "mainId" => @main_id,
      "subId" => @sc_set_money_p,
      "data" => %{
        "money" => new_balance
      }
    }
    
    # 通过用户的channel发送
    Endpoint.broadcast("user:#{user_id}", "message", message)
  end
  
  @doc """
  发送钱包余额变更通知
  """
  def notify_wallet_change(user_id, new_balance) do
    message = %{
      "mainId" => @main_id,
      "subId" => @sc_set_walletmoney_p,
      "data" => %{
        "walletmoney" => new_balance
      }
    }
    
    Endpoint.broadcast("user:#{user_id}", "message", message)
  end
end
```

### 2. 中期改进

1. **创建协议常量模块**：
```elixir
# 新建文件：lib/teen/protocol/protocol_constants.ex
defmodule Teen.Protocol.ProtocolConstants do
  @moduledoc """
  统一管理所有协议ID，确保与客户端Protocol.ts一致
  """
  
  # 主协议ID映射
  def main_protocols do
    %{
      reg_login: 0,
      find_psw: 1,
      pay: 2,
      game: 4,
      xc: 5,
      base_info: 6,
      money: 7,
      mail_manager: 14,
      notice_manager: 15,
      rank: 40,
      task: 42,
      hall_activity: 101
    }
  end
  
  # RegLogin子协议
  def reg_login_protocols do
    %{
      sc_normal_reg_p: 1,
      sc_login_p: 3,
      sc_online_p: 5,
      # ... 其他协议
    }
  end
end
```

2. **统一协议发送接口**：
```elixir
# 新建文件：lib/teen/protocol/protocol_sender.ex
defmodule Teen.Protocol.ProtocolSender do
  @moduledoc """
  统一的协议发送接口，确保消息格式一致
  """
  
  alias CypridinaWeb.Endpoint
  
  def send_to_user(user_id, main_id, sub_id, data) do
    message = %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data
    }
    
    Endpoint.broadcast("user:#{user_id}", "message", message)
  end
  
  def send_to_room(room_id, main_id, sub_id, data) do
    message = %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data
    }
    
    Endpoint.broadcast("room:#{room_id}", "message", message)
  end
end
```

### 3. 测试建议

1. **协议兼容性测试**：
   - 使用实际的客户端验证登录流程
   - 监控客户端是否正确解析所有字段
   - 验证金币变更通知是否实时更新

2. **回归测试**：
   - 确保修改不影响现有功能
   - 验证所有游戏的金币同步机制

### 4. Game模块 (已审计)

#### 发现问题：
1. **主协议ID一致**：
   - Protocol.ts: MainProto.Game = 4
   - 服务端: @protocol_id = 4 ✅

2. **SC协议定义不完整**：
   - 服务端只定义了部分SC协议常量
   - 大量客户端期望的SC协议未实现：
     - SC_ROOM_INFO_P (2) ❌
     - SC_ROOM_SET_PLAYER_STATE_P (4) ❌  
     - SC_ROOM_SET_STATE_P (5) ❌
     - SC_ROOM_CHAT_P (7) ❌
     - SC_ROOM_PLAYER_ENTER_P (12) ❌
     - SC_ROOM_PLAYER_QUIT_P (14) ❌
     - SC_ROOM_DEL_P (16) ❌

3. **游戏房间协议实现位置分散**：
   - 部分在GameChannel中处理
   - 部分在各个游戏的Room模块中
   - 缺乏统一的协议发送机制

### 5. HallActivity模块 (已审计)

#### 发现问题：
1. **主协议ID一致**：
   - Protocol.ts: MainProto.HallActivity = 101
   - 服务端: @protocol_id = 101 ✅

2. **子协议ID完全匹配** ✅：
   - 服务端正确定义了所有客户端期望的子协议ID
   - 从 @sc_logincash_info_p = 1 到 @sc_fetch_cdkey_award_p = 49

3. **新增协议未同步**：
   - 服务端新增了登录奖励协议 (52-55)
   - 客户端Protocol.ts中未定义这些协议

### 6. 其他模块审计概要

#### BaseInfo模块：
- 服务端未找到对应的协议处理器 ❌
- 需要实现昵称、头像、密码修改等功能

#### Pay模块：
- 部分在MoneyProtocol中实现 (CS_PAY = 49)
- 但缺少独立的Pay协议处理器

#### MailManager/NoticeManager：
- 服务端未找到对应实现 ❌

#### Rank/Task：
- 服务端未找到对应实现 ❌

## 六、审计总结

### 严重问题：
1. **核心协议缺失**：多个重要的SC协议未实现，包括SC_SET_MONEY_P、SC_ROOM_INFO_P等
2. **数据结构不匹配**：SC_LOGIN_P等协议的字段名称和结构与客户端期望不一致
3. **协议发送机制不统一**：缺乏统一的协议发送接口

### 中等问题：
1. **模块缺失**：BaseInfo、MailManager、NoticeManager、Rank、Task等模块未实现
2. **协议版本管理**：服务端新增协议未同步到客户端

### 建议优先级：
1. **P0 - 立即修复**：
   - 修正SC_LOGIN_P响应结构
   - 实现SC_SET_MONEY_P和SC_SET_WALLETMONEY_P
   - 实现SC_ROOM_INFO_P等核心房间协议

2. **P1 - 短期修复**：
   - 创建统一的协议发送机制
   - 实现BaseInfo模块协议
   - 完善Game模块的所有SC协议

3. **P2 - 中期改进**：
   - 实现其他缺失的模块
   - 建立协议版本管理机制
   - 创建自动化测试

## 七、下一步行动计划
1. 立即开始修复P0级别的问题
2. 创建协议一致性测试用例
3. 定期同步服务端和客户端的协议定义
4. 建立协议变更的文档和通知机制