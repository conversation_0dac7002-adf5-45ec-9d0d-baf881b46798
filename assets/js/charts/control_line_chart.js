// 四级控制线波动图表 - 展示控制线状态变化趋势
import ApexCharts from 'apexcharts'

// 创建控制线波动图表
export function createControlLineChart(element, chartData) {
    // 如果没有数据，直接返回
    if (!chartData || !chartData.timestamps || chartData.timestamps.length === 0) {
        console.error("没有足够的数据来绘制控制线图表");
        return null;
    }

    // 提取控制线数据
    const {
        timestamps = [],
        current_inventory = [],
        center_line = [],
        absolute_collect = [],
        pre_collect = [],
        pre_release = [],
        absolute_release = [],
        control_modes = []
    } = chartData;

    // 格式化时间标签
    const timeRange = window.currentTimeRange || 'hour';
    const formattedLabels = timestamps.map(timestamp => {
        if (typeof timestamp === 'string' && timestamp.includes('T')) {
            const date = new Date(timestamp);
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');

            if (timeRange === 'minute') {
                return `${hours}:${minutes}`;
            } else if (timeRange === 'hour') {
                return `${hours}时`;
            } else if (timeRange === 'day') {
                return `${day}日`;
            }
            return `${hours}:${minutes}`;
        }
        return timestamp;
    });

    // 计算Y轴范围
    const allValues = [
        ...current_inventory,
        ...center_line,
        ...absolute_collect,
        ...pre_collect,
        ...pre_release,
        ...absolute_release
    ].filter(v => v !== null && v !== undefined);

    const minValue = Math.min(...allValues);
    const maxValue = Math.max(...allValues);
    const dataRange = Math.max(maxValue - minValue, 1);
    const padding = dataRange * 0.1;

    // 图表配置
    const options = {
        series: [
            {
                name: '当前库存',
                data: current_inventory,
                type: 'line'
            },
            {
                name: '中心线',
                data: center_line,
                type: 'line'
            },
            {
                name: '绝对收分线',
                data: absolute_collect,
                type: 'line'
            },
            {
                name: '预收分线',
                data: pre_collect,
                type: 'line'
            },
            {
                name: '预放分线',
                data: pre_release,
                type: 'line'
            },
            {
                name: '绝对放分线',
                data: absolute_release,
                type: 'line'
            }
        ],
        chart: {
            height: 450,
            type: 'line',
            background: 'transparent',
            foreColor: '#374151',
            fontFamily: 'inherit',
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: [
            '#3B82F6', // 当前库存 - 蓝色
            '#10B981', // 中心线 - 绿色
            '#EF4444', // 绝对收分线 - 红色
            '#F59E0B', // 预收分线 - 橙色
            '#8B5CF6', // 预放分线 - 紫色
            '#06B6D4'  // 绝对放分线 - 青色
        ],
        dataLabels: {
            enabled: false
        },
        markers: {
            size: 0,
            hover: {
                size: 6,
                sizeOffset: 3
            }
        },
        stroke: {
            curve: 'smooth',
            width: [3, 2, 2, 2, 2, 2], // 当前库存线条更粗
            dashArray: [0, 5, 0, 3, 3, 0] // 中心线虚线，预收分/预放分点线
        },
        fill: {
            type: 'solid',
            opacity: [0.8, 0.6, 0.6, 0.6, 0.6, 0.6]
        },
        grid: {
            borderColor: 'rgba(59, 130, 246, 0.1)',
            strokeDashArray: 3,
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            },
            padding: {
                top: 10,
                right: 10,
                bottom: 10,
                left: 10
            }
        },
        xaxis: {
            categories: formattedLabels,
            labels: {
                rotate: -45,
                style: {
                    fontSize: '12px',
                    colors: '#6B7280',
                    fontWeight: 500
                },
                offsetY: 5
            },
            axisBorder: {
                show: true,
                color: '#E5E7EB',
                height: 1
            },
            axisTicks: {
                show: true,
                color: '#E5E7EB',
                height: 6
            }
        },
        yaxis: {
            title: {
                text: '金额 (¥)',
                style: {
                    fontSize: '14px',
                    fontWeight: 600,
                    color: '#374151'
                }
            },
            labels: {
                style: {
                    fontSize: '12px',
                    colors: '#6B7280',
                    fontWeight: 500
                },
                formatter: function(value) {
                    // Convert from cents to currency (divide by 100)
                    const currencyValue = value / 100;
                    if (Math.abs(currencyValue) >= 10000) {
                        return '¥' + (currencyValue / 10000).toFixed(1) + '万';
                    }
                    return '¥' + currencyValue.toFixed(2);
                }
            },
            axisBorder: {
                show: true,
                color: '#E5E7EB'
            },
            min: minValue - padding,
            max: maxValue + padding
        },
        tooltip: {
            enabled: true,
            shared: true,
            intersect: false,
            theme: 'light',
            style: {
                fontSize: '13px',
                fontFamily: 'inherit'
            },
            custom: function({series, dataPointIndex, w}) {
                const timestamp = formattedLabels[dataPointIndex];
                const controlMode = control_modes[dataPointIndex] || 'unknown';
                
                // 获取控制模式的中文名称和颜色
                const modeNames = {
                    'absolute_collect': '强制收分',
                    'pre_collect': '预备收分',
                    'random': '随机',
                    'pre_release': '预备放分',
                    'absolute_release': '强制放分'
                };
                
                const modeColors = {
                    'absolute_collect': '#EF4444',
                    'pre_collect': '#F59E0B',
                    'random': '#6B7280',
                    'pre_release': '#8B5CF6',
                    'absolute_release': '#06B6D4'
                };

                const currentMode = modeNames[controlMode] || '未知';
                const currentModeColor = modeColors[controlMode] || '#6B7280';

                let tooltipContent = `
                    <div class="bg-white p-4 rounded-lg shadow-xl border border-gray-200">
                        <div class="text-sm font-semibold text-gray-800 mb-3">
                            ${timestamp}
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">控制模式:</span>
                                <span class="font-bold text-sm px-2 py-1 rounded" style="color: ${currentModeColor}; background-color: ${currentModeColor}20">
                                    ${currentMode}
                                </span>
                            </div>
                `;

                // 添加各条线的数值
                const seriesNames = ['当前库存', '中心线', '绝对收分线', '预收分线', '预放分线', '绝对放分线'];
                const seriesColors = ['#3B82F6', '#10B981', '#EF4444', '#F59E0B', '#8B5CF6', '#06B6D4'];
                
                series.forEach((seriesData, index) => {
                    const value = seriesData[dataPointIndex];
                    if (value !== null && value !== undefined) {
                        tooltipContent += `
                            <div class="flex justify-between items-center">
                                <span class="text-sm flex items-center">
                                    <span class="w-3 h-3 rounded-full mr-2" style="background-color: ${seriesColors[index]}"></span>
                                    ${seriesNames[index]}:
                                </span>
                                <span class="font-semibold text-sm">¥${(value / 100).toFixed(2)}</span>
                            </div>
                        `;
                    }
                });

                tooltipContent += `
                        </div>
                    </div>
                `;

                return tooltipContent;
            }
        },
        legend: {
            show: true,
            position: 'top',
            horizontalAlign: 'center',
            fontSize: '12px',
            fontWeight: 500,
            markers: {
                width: 12,
                height: 12
            },
            itemMargin: {
                horizontal: 10,
                vertical: 5
            }
        },
        annotations: {
            yaxis: [
                {
                    y: 0,
                    borderColor: '#6B7280',
                    borderWidth: 1,
                    strokeDashArray: 5,
                    label: {
                        text: '平衡线',
                        style: {
                            background: '#6B7280',
                            color: '#FFFFFF',
                            fontSize: '11px'
                        }
                    }
                }
            ]
        }
    };

    // 创建图表实例
    console.log("📈 创建控制线图表实例");
    const chart = new ApexCharts(element, options);

    // 添加更新方法
    chart.updateControlLineData = function(newChartData) {
        const {
            timestamps = [],
            current_inventory = [],
            center_line = [],
            absolute_collect = [],
            pre_collect = [],
            pre_release = [],
            absolute_release = [],
            control_modes = []
        } = newChartData;

        // 格式化新的时间标签
        const timeRange = window.currentTimeRange || 'hour';
        const newFormattedLabels = timestamps.map(timestamp => {
            if (typeof timestamp === 'string' && timestamp.includes('T')) {
                const date = new Date(timestamp);
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');

                if (timeRange === 'minute') {
                    return `${hours}:${minutes}`;
                } else if (timeRange === 'hour') {
                    return `${hours}时`;
                } else if (timeRange === 'day') {
                    return `${day}日`;
                }
                return `${hours}:${minutes}`;
            }
            return timestamp;
        });

        // 更新图表数据
        chart.updateOptions({
            xaxis: {
                categories: newFormattedLabels
            }
        });

        chart.updateSeries([
            {
                name: '当前库存',
                data: current_inventory
            },
            {
                name: '中心线',
                data: center_line
            },
            {
                name: '绝对收分线',
                data: absolute_collect
            },
            {
                name: '预收分线',
                data: pre_collect
            },
            {
                name: '预放分线',
                data: pre_release
            },
            {
                name: '绝对放分线',
                data: absolute_release
            }
        ]);
    };

    return chart;
}