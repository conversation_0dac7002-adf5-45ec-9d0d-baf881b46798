#!/usr/bin/env node

/**
 * 生成优化的 Heroicons 图标 CSS 定义
 * 这个脚本会读取所有 SVG 文件并生成优化的 CSS 类
 * 优化特性：
 * - 减少重复代码
 * - 统一的基础样式
 * - 尺寸变体支持
 * - 实用工具类
 * - 动画效果
 */

const fs = require('fs');
const path = require('path');

// 图标目录配置
const iconsDir = path.join(__dirname, '../../deps/heroicons/optimized');
const outputFile = path.join(__dirname, '../css/heroicons-optimized.css');

// 图标变体配置
const variants = [
  { suffix: '', dir: '/24/outline', size: '1.5rem' },
  { suffix: '-solid', dir: '/24/solid', size: '1.5rem' },
  { suffix: '-mini', dir: '/20/solid', size: '1.25rem' },
  { suffix: '-micro', dir: '/16/solid', size: '1rem' }
];

/**
 * URL 编码 SVG 内容
 */
function encodeSvgContent(content) {
  return encodeURIComponent(content.replace(/\r?\n|\r/g, ''));
}

/**
 * 生成优化的单个图标 CSS 类（只包含图标定义）
 */
function generateIconCSS(name, svgContent) {
  const encodedContent = encodeSvgContent(svgContent);
  return `.hero-${name} {
  --hero-icon: url('data:image/svg+xml;utf8,${encodedContent}');
  -webkit-mask: var(--hero-icon);
  mask: var(--hero-icon);
}`;
}

/**
 * 生成基础样式和实用工具类
 */
function generateBaseStyles() {
  return `/* ===== 基础样式定义 ===== */

/* 所有 Heroicon 图标的基础样式 */
[class^="hero-"],
[class*=" hero-"] {
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

/* ===== 尺寸变体 ===== */

/* 小尺寸图标 (1rem) */
.hero-sm,
[class^="hero-"].hero-sm,
[class*=" hero-"].hero-sm {
  width: 1rem;
  height: 1rem;
}

/* 大尺寸图标 (2rem) */
.hero-lg,
[class^="hero-"].hero-lg,
[class*=" hero-"].hero-lg {
  width: 2rem;
  height: 2rem;
}

/* 超大尺寸图标 (2.5rem) */
.hero-xl,
[class^="hero-"].hero-xl,
[class*=" hero-"].hero-xl {
  width: 2.5rem;
  height: 2.5rem;
}

/* ===== 实用工具类 ===== */

/* 图标旋转 */
.hero-rotate-90 { transform: rotate(90deg); }
.hero-rotate-180 { transform: rotate(180deg); }
.hero-rotate-270 { transform: rotate(270deg); }

/* 图标翻转 */
.hero-flip-x { transform: scaleX(-1); }
.hero-flip-y { transform: scaleY(-1); }

/* 图标对齐 */
.hero-align-top { vertical-align: top; }
.hero-align-bottom { vertical-align: bottom; }
.hero-align-text-top { vertical-align: text-top; }
.hero-align-text-bottom { vertical-align: text-bottom; }

/* 图标颜色变体 */
.hero-text-primary { color: var(--color-primary, #3b82f6); }
.hero-text-secondary { color: var(--color-secondary, #6b7280); }
.hero-text-success { color: var(--color-success, #10b981); }
.hero-text-warning { color: var(--color-warning, #f59e0b); }
.hero-text-error { color: var(--color-error, #ef4444); }

/* 响应式尺寸 */
@media (min-width: 640px) {
  .sm\\:hero-sm { width: 1rem; height: 1rem; }
  .sm\\:hero-lg { width: 2rem; height: 2rem; }
  .sm\\:hero-xl { width: 2.5rem; height: 2.5rem; }
}

@media (min-width: 768px) {
  .md\\:hero-sm { width: 1rem; height: 1rem; }
  .md\\:hero-lg { width: 2rem; height: 2rem; }
  .md\\:hero-xl { width: 2.5rem; height: 2.5rem; }
}

@media (min-width: 1024px) {
  .lg\\:hero-sm { width: 1rem; height: 1rem; }
  .lg\\:hero-lg { width: 2rem; height: 2rem; }
  .lg\\:hero-xl { width: 2.5rem; height: 2.5rem; }
}`;
}

/**
 * 生成动画效果样式
 */
function generateAnimationStyles() {
  return `
/* ===== 动画效果 ===== */

/* 悬停效果 */
.hero-hover-scale:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease-in-out;
}

.hero-hover-rotate:hover {
  transform: rotate(15deg);
  transition: transform 0.2s ease-in-out;
}

/* 加载动画 */
.hero-spin {
  animation: hero-spin 1s linear infinite;
}

@keyframes hero-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 脉冲动画 */
.hero-pulse {
  animation: hero-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes hero-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 弹跳动画 */
.hero-bounce {
  animation: hero-bounce 1s infinite;
}

@keyframes hero-bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}`;
}

/**
 * 处理单个图标变体目录（优化版）
 */
function processVariant(variant) {
  const variantDir = path.join(iconsDir, variant.dir);
  const cssRules = [];

  if (!fs.existsSync(variantDir)) {
    console.warn(`⚠️  目录不存在: ${variantDir}`);
    return cssRules;
  }

  const files = fs.readdirSync(variantDir);

  files.forEach(file => {
    if (file.endsWith('.svg')) {
      const iconName = path.basename(file, '.svg') + variant.suffix;
      const filePath = path.join(variantDir, file);

      try {
        const svgContent = fs.readFileSync(filePath, 'utf8');
        const cssRule = generateIconCSS(iconName, svgContent);
        cssRules.push(cssRule);
      } catch (error) {
        console.error(`❌ 处理图标失败 ${iconName}:`, error.message);
      }
    }
  });

  return cssRules;
}

/**
 * 生成优化的完整 CSS 文件
 */
function generateCSS() {
  console.log('🎨 开始生成优化的 Heroicons CSS 定义...');

  // 文件头部注释
  let cssContent = `/*
 * 优化的 Heroicons CSS 定义
 * 包含所有图标的完整 CSS 类定义，经过优化以减少重复代码
 * 生成时间: ${new Date().toISOString()}
 *
 * 优化特性：
 * - 统一的基础样式，减少重复代码
 * - 尺寸变体支持 (sm, lg, xl)
 * - 实用工具类 (旋转、翻转、对齐)
 * - 颜色变体支持
 * - 响应式尺寸
 * - 动画效果
 */

`;

  // 添加基础样式
  console.log('📝 生成基础样式和实用工具类...');
  cssContent += generateBaseStyles() + '\n\n';

  let totalIcons = 0;

  // 添加图标定义部分
  cssContent += '/* ===== 图标定义 ===== */\n\n';

  // 处理每个变体
  variants.forEach(variant => {
    console.log(`📂 处理变体: ${variant.suffix || 'outline'}`);
    const cssRules = processVariant(variant);

    if (cssRules.length > 0) {
      cssContent += `/* ${variant.suffix || 'Outline'} 图标 (${variant.dir}) */\n`;
      cssContent += cssRules.join('\n\n') + '\n\n';
      totalIcons += cssRules.length;
      console.log(`   ✅ 生成了 ${cssRules.length} 个图标`);
    }
  });

  // 添加动画样式
  console.log('🎬 添加动画效果样式...');
  cssContent += generateAnimationStyles() + '\n';

  // 写入文件
  try {
    fs.writeFileSync(outputFile, cssContent, 'utf8');

    // 计算文件大小
    const stats = fs.statSync(outputFile);
    const fileSizeKB = (stats.size / 1024).toFixed(2);

    console.log(`🎉 成功生成优化的 CSS 文件: ${outputFile}`);
    console.log(`📊 总计生成 ${totalIcons} 个图标类`);
    console.log(`📏 文件大小: ${fileSizeKB} KB`);
    console.log(`✨ 优化特性: 基础样式统一、尺寸变体、实用工具类、动画效果`);
  } catch (error) {
    console.error('❌ 写入文件失败:', error.message);
    process.exit(1);
  }
}

// 执行生成
if (require.main === module) {
  generateCSS();
}

module.exports = { generateCSS };
