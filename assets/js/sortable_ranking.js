// SortableRanking Hook for Phoenix LiveView
// 用于处理动物排名的拖拽排序功能

import Sortable from 'sortablejs';

export const SortableRanking = {
  mounted() {
    const container = this.el;
    const target = container.getAttribute('data-target');

    // 初始化 Sortable
    this.sortable = Sortable.create(container, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',

      // 拖拽结束时的回调
      onEnd: (evt) => {
        // 获取新的排序
        const newOrder = Array.from(container.children).map(item =>
          item.getAttribute('data-animal-id')
        );

        // 发送排序结果到指定的 LiveView 组件
        if (target) {
          this.pushEventTo(target, 'update_ranking', { ranking: newOrder });
        } else {
          this.pushEvent('update_ranking', { ranking: newOrder });
        }
      }
    });
  },

  destroyed() {
    if (this.sortable) {
      this.sortable.destroy();
    }
  }
};
