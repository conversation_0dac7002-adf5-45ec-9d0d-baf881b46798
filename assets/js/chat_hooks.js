/**
 * 通用聊天系统的JavaScript Hooks
 * 
 * 处理聊天界面的交互功能，包括：
 * - 自动滚动到底部
 * - 消息实时更新
 * - WebSocket连接管理
 */

export const ChatScroll = {
  mounted() {
    // 延迟初始滚动，确保内容已渲染
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);

    // 监听新消息，自动滚动到底部
    this.handleEvent("scroll_to_bottom", () => {
      this.scrollToBottom();
    });

    // 当有新内容时自动滚动
    const observer = new MutationObserver(() => {
      // 延迟检查，确保DOM更新完成
      setTimeout(() => {
        if (this.isNearBottom()) {
          this.scrollToBottom();
        }
      }, 50);
    });

    observer.observe(this.el, {
      childList: true,
      subtree: true,
      characterData: true
    });

    this.observer = observer;
  },

  updated() {
    // 内容更新后延迟滚动到底部，确保DOM更新完成
    setTimeout(() => {
      this.scrollToBottom();
    }, 50);
  },

  destroyed() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },

  scrollToBottom() {
    try {
      // 强制滚动到底部
      this.el.scrollTop = this.el.scrollHeight;

      // 然后使用平滑滚动微调
      setTimeout(() => {
        this.el.scrollTo({
          top: this.el.scrollHeight,
          behavior: 'smooth'
        });
      }, 10);
    } catch (error) {
      console.warn('Chat scroll error:', error);
    }
  },

  isNearBottom() {
    // 检查是否接近底部（距离底部小于150px）
    const threshold = 150;
    return this.el.scrollTop + this.el.clientHeight >= this.el.scrollHeight - threshold;
  }
};

export const ChatConnection = {
  mounted() {
    this.initializeChat();
  },

  initializeChat() {
    // 获取用户token和会话ID
    const userToken = this.el.dataset.userToken;
    const sessionId = this.el.dataset.sessionId;

    if (!userToken) {
      console.error("Chat: 缺少用户token");
      return;
    }

    // 创建WebSocket连接
    this.socket = new Phoenix.Socket("/socket", {
      params: { token: userToken }
    });

    this.socket.connect();

    // 连接到聊天频道
    if (sessionId) {
      this.joinChatSession(sessionId);
    }

    // 连接到聊天大厅
    this.joinChatLobby();
  },

  joinChatSession(sessionId) {
    this.chatChannel = this.socket.channel(`chat:session:${sessionId}`, {});

    this.chatChannel.join()
      .receive("ok", resp => {
        console.log("Chat: 成功加入聊天会话", sessionId);
        this.setupChatEventHandlers();
      })
      .receive("error", resp => {
        console.error("Chat: 加入聊天会话失败", resp);
      });
  },

  joinChatLobby() {
    this.lobbyChannel = this.socket.channel("chat:lobby", {});

    this.lobbyChannel.join()
      .receive("ok", resp => {
        console.log("Chat: 成功加入聊天大厅");
      })
      .receive("error", resp => {
        console.error("Chat: 加入聊天大厅失败", resp);
      });
  },

  setupChatEventHandlers() {
    if (!this.chatChannel) return;

    // 监听新消息
    this.chatChannel.on("new_message", (message) => {
      console.log("Chat: 收到新消息", message);
      // 通知LiveView更新
      this.pushEvent("new_message_received", message);
    });

    // 监听用户加入
    this.chatChannel.on("user_joined", (data) => {
      console.log("Chat: 用户加入", data);
    });

    // 监听用户离开
    this.chatChannel.on("user_left", (data) => {
      console.log("Chat: 用户离开", data);
    });

    // 监听正在输入状态
    this.chatChannel.on("user_typing", (data) => {
      console.log("Chat: 用户正在输入", data);
      // 可以在这里显示"正在输入"指示器
    });

    // 监听消息已读状态
    this.chatChannel.on("messages_read", (data) => {
      console.log("Chat: 消息已读", data);
    });
  },

  sendMessage(content) {
    if (!this.chatChannel) {
      console.error("Chat: 聊天频道未连接");
      return;
    }

    this.chatChannel.push("send_message", { content: content })
      .receive("ok", resp => {
        console.log("Chat: 消息发送成功", resp);
      })
      .receive("error", resp => {
        console.error("Chat: 消息发送失败", resp);
      });
  },

  markAsRead(messageIds) {
    if (!this.chatChannel) return;

    this.chatChannel.push("mark_as_read", { message_ids: messageIds })
      .receive("ok", () => {
        console.log("Chat: 消息已标记为已读");
      })
      .receive("error", resp => {
        console.error("Chat: 标记已读失败", resp);
      });
  },

  setTyping(typing) {
    if (!this.chatChannel) return;

    this.chatChannel.push("typing", { typing: typing });
  },

  destroyed() {
    if (this.chatChannel) {
      this.chatChannel.leave();
    }
    if (this.lobbyChannel) {
      this.lobbyChannel.leave();
    }
    if (this.socket) {
      this.socket.disconnect();
    }
  }
};

export const MessageInput = {
  mounted() {
    this.setupTypingIndicator();
  },

  setupTypingIndicator() {
    let typingTimer;
    const typingDelay = 1000; // 1秒后停止"正在输入"状态

    this.el.addEventListener('input', () => {
      // 发送"正在输入"状态
      if (this.chatConnection) {
        this.chatConnection.setTyping(true);
      }

      // 清除之前的定时器
      clearTimeout(typingTimer);

      // 设置新的定时器，1秒后停止"正在输入"状态
      typingTimer = setTimeout(() => {
        if (this.chatConnection) {
          this.chatConnection.setTyping(false);
        }
      }, typingDelay);
    });

    // 当输入框失去焦点时停止"正在输入"状态
    this.el.addEventListener('blur', () => {
      clearTimeout(typingTimer);
      if (this.chatConnection) {
        this.chatConnection.setTyping(false);
      }
    });
  }
};

export const SearchFocus = {
  mounted() {
    // 监听焦点事件
    this.el.addEventListener('focus', () => {
      console.log("Search: 搜索框获得焦点");
      this.pushEvent("search_focus", {});
    });

    // 监听失焦事件
    this.el.addEventListener('blur', () => {
      console.log("Search: 搜索框失去焦点");
      this.pushEvent("search_blur", {});
    });

    // 移除自动清空逻辑 - 用户需要主动点击清空按钮或删除所有文本
  }
};

export const AutoResize = {
  mounted() {
    this.setupAutoResize();
    this.setupFormSubmit();
    this.setupKeyboardHandler();
  },

  updated() {
    this.adjustHeight();
  },

  setupAutoResize() {
    // 初始化高度
    this.adjustHeight();

    // 监听输入事件
    this.el.addEventListener('input', () => {
      this.adjustHeight();
    });

    // 监听键盘事件（仅用于高度调整，组合键处理在 setupKeyboardHandler 中）
    this.el.addEventListener('keydown', () => {
      // 延迟调整高度，确保内容已更新
      setTimeout(() => {
        this.adjustHeight();
      }, 0);
    });

    // 监听粘贴事件
    this.el.addEventListener('paste', () => {
      setTimeout(() => {
        this.adjustHeight();
      }, 0);
    });
  },

  setupFormSubmit() {
    // 监听来自 LiveView 的表单提交事件
    this.handleEvent("submit_form", () => {
      console.log('💬 [CHAT_HOOK] 收到表单提交事件');

      // 查找包含此 textarea 的表单
      const form = this.el.closest('form');
      if (form) {
        console.log('💬 [CHAT_HOOK] 提交表单');

        // 创建并触发表单提交事件
        const submitEvent = new Event('submit', {
          bubbles: true,
          cancelable: true
        });

        form.dispatchEvent(submitEvent);

        // 清空输入框
        this.el.value = '';

        // 重置高度
        this.adjustHeight();
      } else {
        console.error('💬 [CHAT_HOOK] 未找到表单元素');
      }
    });
  },

  setupKeyboardHandler() {
    // 监听键盘事件，正确捕获组合键
    this.el.addEventListener('keydown', (event) => {
      console.log('💬 [CHAT_HOOK] 键盘事件:', {
        key: event.key,
        shiftKey: event.shiftKey,
        ctrlKey: event.ctrlKey,
        metaKey: event.metaKey,
        altKey: event.altKey
      });

      // 只处理 Enter 键
      if (event.key === 'Enter') {
        // 发送完整的键盘事件信息到 LiveView
        this.pushEvent('handle_keydown_with_modifiers', {
          key: event.key,
          shiftKey: event.shiftKey,
          ctrlKey: event.ctrlKey,
          metaKey: event.metaKey,
          altKey: event.altKey,
          value: this.el.value
        });

        // 如果是单独的 Enter 键（无组合键），阻止默认行为
        if (!event.shiftKey && !event.ctrlKey && !event.metaKey && !event.altKey) {
          event.preventDefault();
          return false;
        }
      }
    });
  },

  adjustHeight() {
    const textarea = this.el;

    // 重置高度以获取正确的 scrollHeight
    textarea.style.height = 'auto';

    // 获取内容高度
    const scrollHeight = textarea.scrollHeight;

    // 获取最小和最大高度（从 CSS 类中获取）
    const minHeight = 48; // min-h-[48px]
    const maxHeight = 120; // max-h-[120px]

    // 计算新高度
    let newHeight = Math.max(minHeight, scrollHeight);
    newHeight = Math.min(newHeight, maxHeight);

    // 设置新高度
    textarea.style.height = newHeight + 'px';

    // 如果内容超过最大高度，显示滚动条
    if (scrollHeight > maxHeight) {
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.overflowY = 'hidden';
    }
  }
};

// 导出所有hooks
export default {
  ChatScroll,
  ChatConnection,
  MessageInput,
  SearchFocus,
  AutoResize
};
