/**
 * 主题切换器组件
 * 支持多种主题切换和本地存储
 */

class ThemeSwitcher {
  constructor() {
    this.themes = [
      { name: 'light', label: '浅色', icon: '☀️' },
      { name: 'dark', label: '深色', icon: '🌙' },
      { name: 'modern-blue', label: '现代蓝', icon: '💙' },
      { name: 'purple-dark', label: '紫色暗', icon: '💜' },
      { name: 'nature-green', label: '自然绿', icon: '🌿' },
      { name: 'warm-orange', label: '温暖橙', icon: '🧡' },
      { name: 'minimal-mono', label: '极简', icon: '⚫' },
      { name: 'cupcake', label: '纸杯蛋糕', icon: '🧁' },
      { name: 'cyberpunk', label: '赛博朋克', icon: '🤖' },
      { name: 'forest', label: '森林', icon: '🌲' },
      { name: 'luxury', label: '奢华', icon: '💎' },
      { name: 'business', label: '商务', icon: '💼' },
      { name: 'emerald', label: '翡翠', icon: '💚' },
      { name: 'corporate', label: '企业', icon: '🏢' },
      { name: 'synthwave', label: '合成波', icon: '🌆' },
      { name: 'retro', label: '复古', icon: '📻' },
      { name: 'valentine', label: '情人节', icon: '💕' },
      { name: 'garden', label: '花园', icon: '🌺' },
      { name: 'aqua', label: '水蓝', icon: '🌊' },
      { name: 'lofi', label: 'Lo-Fi', icon: '🎵' },
      { name: 'pastel', label: '粉彩', icon: '🎨' },
      { name: 'fantasy', label: '幻想', icon: '🦄' },
      { name: 'wireframe', label: '线框', icon: '📐' },
      { name: 'black', label: '纯黑', icon: '⚫' },
      { name: 'dracula', label: '德古拉', icon: '🧛' },
      { name: 'cmyk', label: 'CMYK', icon: '🖨️' },
      { name: 'autumn', label: '秋天', icon: '🍂' },
      { name: 'acid', label: '酸性', icon: '🟢' },
      { name: 'lemonade', label: '柠檬水', icon: '🍋' },
      { name: 'night', label: '夜晚', icon: '🌃' },
      { name: 'coffee', label: '咖啡', icon: '☕' },
      { name: 'winter', label: '冬天', icon: '❄️' },
      { name: 'dim', label: '昏暗', icon: '🔅' },
      { name: 'nord', label: '北欧', icon: '🏔️' },
      { name: 'sunset', label: '日落', icon: '🌅' }
    ];
    
    this.currentTheme = this.getStoredTheme() || 'light';
    this.init();
  }

  init() {
    this.applyTheme(this.currentTheme);
    this.createThemeSwitcher();
    this.bindEvents();
    this.detectSystemTheme();
  }

  getStoredTheme() {
    return localStorage.getItem('theme');
  }

  storeTheme(theme) {
    localStorage.setItem('theme', theme);
  }

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    this.currentTheme = theme;
    this.storeTheme(theme);
    this.updateThemeSwitcher();
    this.announceThemeChange(theme);
  }

  createThemeSwitcher() {
    // 创建主题切换器 HTML
    const switcherHTML = `
      <div class="theme-switcher-container">
        <div class="dropdown dropdown-end">
          <div tabindex="0" role="button" class="btn btn-ghost btn-circle theme-toggle-btn">
            <span class="theme-icon">${this.getThemeIcon(this.currentTheme)}</span>
          </div>
          <div tabindex="0" class="dropdown-content z-[1] p-2 shadow-2xl bg-base-300 rounded-box w-80 max-h-96 overflow-y-auto">
            <div class="grid grid-cols-1 gap-1 theme-grid">
              ${this.themes.map(theme => `
                <div class="theme-option-item ${theme.name === this.currentTheme ? 'active' : ''}" 
                     data-theme="${theme.name}">
                  <div class="flex items-center gap-3 p-3 rounded-lg hover:bg-base-200 cursor-pointer transition-all">
                    <span class="text-lg">${theme.icon}</span>
                    <div class="flex-1">
                      <div class="font-medium">${theme.label}</div>
                      <div class="text-xs opacity-60">${theme.name}</div>
                    </div>
                    <div class="theme-preview" data-theme="${theme.name}">
                      <div class="flex gap-1">
                        <div class="w-2 h-2 rounded-full bg-primary"></div>
                        <div class="w-2 h-2 rounded-full bg-secondary"></div>
                        <div class="w-2 h-2 rounded-full bg-accent"></div>
                      </div>
                    </div>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </div>
    `;

    // 查找合适的位置插入主题切换器
    const navbar = document.querySelector('.navbar');
    const topbar = document.querySelector('.admin-topbar');
    const header = document.querySelector('header');
    
    const targetElement = navbar || topbar || header;
    
    if (targetElement) {
      const switcherElement = document.createElement('div');
      switcherElement.innerHTML = switcherHTML;
      targetElement.appendChild(switcherElement.firstElementChild);
    }
  }

  bindEvents() {
    // 绑定主题选项点击事件
    document.addEventListener('click', (e) => {
      const themeOption = e.target.closest('.theme-option-item');
      if (themeOption) {
        const theme = themeOption.dataset.theme;
        this.applyTheme(theme);
        
        // 关闭下拉菜单
        const dropdown = themeOption.closest('.dropdown');
        if (dropdown) {
          dropdown.removeAttribute('open');
          dropdown.blur();
        }
      }
    });

    // 键盘快捷键支持
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        this.toggleTheme();
      }
    });
  }

  updateThemeSwitcher() {
    // 更新主题切换器的显示
    const themeIcon = document.querySelector('.theme-icon');
    if (themeIcon) {
      themeIcon.textContent = this.getThemeIcon(this.currentTheme);
    }

    // 更新活动状态
    document.querySelectorAll('.theme-option-item').forEach(item => {
      item.classList.toggle('active', item.dataset.theme === this.currentTheme);
    });
  }

  getThemeIcon(theme) {
    const themeData = this.themes.find(t => t.name === theme);
    return themeData ? themeData.icon : '🎨';
  }

  getThemeLabel(theme) {
    const themeData = this.themes.find(t => t.name === theme);
    return themeData ? themeData.label : theme;
  }

  toggleTheme() {
    // 在浅色和深色主题之间切换
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);
  }

  detectSystemTheme() {
    // 检测系统主题偏好
    if (window.matchMedia && !this.getStoredTheme()) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const systemTheme = mediaQuery.matches ? 'dark' : 'light';
      this.applyTheme(systemTheme);

      // 监听系统主题变化
      mediaQuery.addEventListener('change', (e) => {
        if (!this.getStoredTheme()) {
          const newSystemTheme = e.matches ? 'dark' : 'light';
          this.applyTheme(newSystemTheme);
        }
      });
    }
  }

  announceThemeChange(theme) {
    // 通知主题变化（用于其他组件响应）
    const event = new CustomEvent('themeChanged', {
      detail: { theme, label: this.getThemeLabel(theme) }
    });
    document.dispatchEvent(event);

    // 显示主题切换提示
    this.showThemeToast(theme);
  }

  showThemeToast(theme) {
    const themeLabel = this.getThemeLabel(theme);
    const themeIcon = this.getThemeIcon(theme);
    
    // 创建提示消息
    const toast = document.createElement('div');
    toast.className = 'toast toast-top toast-end z-50';
    toast.innerHTML = `
      <div class="alert alert-info animate-fade-in-down">
        <span class="text-lg">${themeIcon}</span>
        <span>已切换到 ${themeLabel} 主题</span>
      </div>
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后自动移除
    setTimeout(() => {
      toast.remove();
    }, 3000);
  }

  // 公共 API
  setTheme(theme) {
    if (this.themes.some(t => t.name === theme)) {
      this.applyTheme(theme);
    }
  }

  getCurrentTheme() {
    return this.currentTheme;
  }

  getAvailableThemes() {
    return this.themes;
  }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  window.themeSwitcher = new ThemeSwitcher();
});

// 导出供其他模块使用
export default ThemeSwitcher;
