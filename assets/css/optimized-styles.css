/* ===== 优化后的样式文件 ===== */

/* ===== CSS 变量定义 ===== */
:root {
  /* 动画时长 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* 阴影系统 */
  --shadow-sm: 0 2px 4px oklch(var(--color-base-content) / 0.1);
  --shadow-md: 0 4px 8px oklch(var(--color-base-content) / 0.1);
  --shadow-lg: 0 8px 16px oklch(var(--color-base-content) / 0.15);
  --shadow-xl: 0 12px 24px oklch(var(--color-base-content) / 0.2);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, oklch(var(--color-primary)) 0%, oklch(var(--color-secondary)) 100%);
  --gradient-surface: linear-gradient(135deg, oklch(var(--color-base-100)) 0%, oklch(var(--color-base-200)) 100%);
  --gradient-accent: linear-gradient(90deg, oklch(var(--color-primary)) 0%, oklch(var(--color-secondary)) 50%, oklch(var(--color-accent)) 100%);
  
  /* 边框半径 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* 毛玻璃效果 */
  --backdrop-blur: blur(20px);
  --backdrop-blur-sm: blur(10px);
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.5rem;
  --spacing-2xl: 2rem;
}

/* ===== 通用工具类 ===== */
.glass-effect {
  backdrop-filter: var(--backdrop-blur);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .glass-effect {
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.transition-smooth {
  transition: all var(--transition-normal) ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.gradient-text {
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ===== 通用动画 ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 0 0 oklch(var(--color-primary) / 0.7);
  }
  50% {
    box-shadow: 0 0 0 4px oklch(var(--color-primary) / 0);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* ===== 布局组件 ===== */
.admin-topbar {
  backdrop-filter: var(--backdrop-blur-sm);
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal) ease;
}

[data-theme="dark"] .admin-topbar {
  background: rgba(0, 0, 0, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-sidebar {
  background: var(--gradient-surface);
  border-right: 1px solid oklch(var(--color-base-300));
}

.admin-main-content {
  background: var(--gradient-surface);
  min-height: 100vh;
}

/* ===== 侧边栏组件 ===== */
.sidebar-item-enhanced,
.game-sidebar-item {
  transition: all var(--transition-fast) ease;
  border-radius: var(--radius-md);
  margin: var(--spacing-xs) var(--spacing-sm);
  position: relative;
  overflow: hidden;
}

.sidebar-item-enhanced:hover,
.game-sidebar-item:hover {
  background: oklch(var(--color-primary) / 0.1);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.sidebar-item-enhanced::before,
.game-sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    oklch(var(--color-primary) / 0.1) 50%,
    transparent 100%);
  transition: left var(--transition-slow) ease;
  z-index: 0;
}

.sidebar-item-enhanced:hover::before,
.game-sidebar-item:hover::before {
  left: 100%;
}

.sidebar-item-enhanced > *,
.game-sidebar-item > * {
  position: relative;
  z-index: 1;
}

.sidebar-section-title {
  position: relative;
  padding: var(--spacing-md) var(--spacing-lg);
  margin: var(--spacing-sm) 0;
}

.sidebar-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: var(--spacing-lg);
  right: var(--spacing-lg);
  height: 1px;
  background: linear-gradient(90deg,
    oklch(var(--color-primary) / 0.3) 0%,
    transparent 100%);
}

/* ===== 卡片组件 ===== */
.stat-card,
.game-card,
.room-config-card,
.revenue-stat {
  background: oklch(var(--color-base-100));
  border: 1px solid oklch(var(--color-base-300));
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-xl);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all var(--transition-normal) ease;
}

.stat-card::before,
.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
}

.stat-card:hover,
.game-card:hover,
.room-config-card:hover,
.revenue-stat:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card:hover::before,
.game-card:hover::before {
  opacity: 1;
}

/* ===== 文本和数值组件 ===== */
.stat-value,
.jackpot-balance,
.metric-value {
  font-weight: 700;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: pulse 3s ease-in-out infinite;
}

.jackpot-balance {
  font-size: 1.5rem;
}

.metric-value {
  font-size: 1.125rem;
}

/* ===== 按钮组件 ===== */
.quick-action-btn,
.btn-enhanced {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid oklch(var(--color-base-300));
  background: oklch(var(--color-base-100));
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal) ease;
}

.quick-action-btn::before,
.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    oklch(var(--color-primary) / 0.2) 50%,
    transparent 100%);
  transition: left var(--transition-slow) ease;
}

.quick-action-btn:hover::before,
.btn-enhanced:hover::before {
  left: 100%;
}

.quick-action-btn:hover,
.btn-enhanced:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

/* ===== 状态指示器 ===== */
.notification-badge,
.status-indicator::after {
  animation: glow 2s ease-in-out infinite;
}

.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: oklch(var(--color-success));
  border: 2px solid oklch(var(--color-base-100));
}

/* ===== 游戏状态徽章 ===== */
.game-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.game-status-active {
  background: oklch(var(--color-success) / 0.1);
  color: oklch(var(--color-success));
  border: 1px solid oklch(var(--color-success) / 0.2);
}

.game-status-inactive {
  background: oklch(var(--color-neutral) / 0.1);
  color: oklch(var(--color-neutral));
  border: 1px solid oklch(var(--color-neutral) / 0.2);
}

.game-status-maintenance {
  background: oklch(var(--color-warning) / 0.1);
  color: oklch(var(--color-warning));
  border: 1px solid oklch(var(--color-warning) / 0.2);
}

.game-status-error {
  background: oklch(var(--color-error) / 0.1);
  color: oklch(var(--color-error));
  border: 1px solid oklch(var(--color-error) / 0.2);
}

/* ===== 容器组件 ===== */
.chart-container,
.game-stats-chart,
.monitoring-panel {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  background: var(--gradient-surface);
  border: 1px solid oklch(var(--color-base-300));
  padding: var(--spacing-lg);
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 16rem;
  color: oklch(var(--color-base-content) / 0.4);
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    oklch(var(--color-base-300) / 0.1) 10px,
    oklch(var(--color-base-300) / 0.1) 20px
  );
  border-radius: var(--radius-md);
}

/* ===== 列表组件 ===== */
.activity-item,
.monitoring-metric,
.user-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  border-left: 3px solid transparent;
  transition: all var(--transition-fast) ease;
  margin-bottom: var(--spacing-xs);
}

.activity-item:hover,
.monitoring-metric:hover,
.user-list-item:hover {
  background: oklch(var(--color-base-200));
  border-left-color: oklch(var(--color-primary));
  transform: translateX(4px);
}

/* ===== 表格组件 ===== */
.dashboard-table,
.enhanced-table {
  width: 100%;
  border-collapse: collapse;
  background: oklch(var(--color-base-100));
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.dashboard-table th,
.enhanced-table th {
  background: oklch(var(--color-base-200));
  color: oklch(var(--color-base-content));
  font-weight: 600;
  padding: var(--spacing-md);
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid oklch(var(--color-base-300));
}

.dashboard-table td,
.enhanced-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid oklch(var(--color-base-300) / 0.5);
  transition: background-color var(--transition-fast) ease;
}

.dashboard-table tr:hover td,
.enhanced-table tr:hover td {
  background: oklch(var(--color-base-200) / 0.5);
}

/* ===== 表单组件 ===== */
.form-enhanced {
  background: oklch(var(--color-base-100));
  border: 1px solid oklch(var(--color-base-300));
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: 600;
  color: oklch(var(--color-base-content));
  margin-bottom: var(--spacing-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid oklch(var(--color-base-300));
  border-radius: var(--radius-md);
  background: oklch(var(--color-base-100));
  color: oklch(var(--color-base-content));
  transition: all var(--transition-fast) ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: oklch(var(--color-primary));
  box-shadow: 0 0 0 3px oklch(var(--color-primary) / 0.1);
}

/* ===== 警告和通知组件 ===== */
.system-alert {
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border-left: 4px solid currentColor;
  transition: all var(--transition-normal) ease;
  margin-bottom: var(--spacing-lg);
}

.alert-info {
  background: oklch(var(--color-info) / 0.1);
  color: oklch(var(--color-info));
  border-left-color: oklch(var(--color-info));
}

.alert-success {
  background: oklch(var(--color-success) / 0.1);
  color: oklch(var(--color-success));
  border-left-color: oklch(var(--color-success));
}

.alert-warning {
  background: oklch(var(--color-warning) / 0.1);
  color: oklch(var(--color-warning));
  border-left-color: oklch(var(--color-warning));
}

.alert-error {
  background: oklch(var(--color-error) / 0.1);
  color: oklch(var(--color-error));
  border-left-color: oklch(var(--color-error));
}

/* ===== 加载和骨架屏 ===== */
.loading-skeleton {
  background: linear-gradient(90deg,
    oklch(var(--color-base-300) / 0.3) 0%,
    oklch(var(--color-base-300) / 0.5) 50%,
    oklch(var(--color-base-300) / 0.3) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s ease-in-out infinite;
  border-radius: var(--radius-md);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid oklch(var(--color-base-300));
  border-top: 4px solid oklch(var(--color-primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  :root {
    --spacing-xs: 0.125rem;
    --spacing-sm: 0.25rem;
    --spacing-md: 0.5rem;
    --spacing-lg: 0.75rem;
    --spacing-xl: 1rem;
    --spacing-2xl: 1.5rem;
  }

  .stat-card,
  .game-card,
  .room-config-card {
    padding: var(--spacing-lg);
  }

  .sidebar-item-enhanced:hover,
  .game-sidebar-item:hover {
    transform: none;
  }

  .dashboard-table,
  .enhanced-table {
    font-size: 0.875rem;
  }

  .dashboard-table th,
  .enhanced-table th,
  .dashboard-table td,
  .enhanced-table td {
    padding: var(--spacing-sm);
  }
}

@media (min-width: 1024px) {
  .hover-lift:hover {
    transform: translateY(-4px);
  }

  .hover-scale:hover {
    transform: scale(1.05);
  }
}

/* ===== 深色模式优化 ===== */
@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .loading-skeleton {
    background: linear-gradient(90deg,
      oklch(var(--color-base-content) / 0.1) 0%,
      oklch(var(--color-base-content) / 0.2) 50%,
      oklch(var(--color-base-content) / 0.1) 100%);
  }
}

/* ===== 打印样式 ===== */
@media print {
  .sidebar-item-enhanced::before,
  .game-sidebar-item::before,
  .quick-action-btn::before,
  .btn-enhanced::before {
    display: none;
  }

  .stat-card,
  .game-card,
  .room-config-card {
    box-shadow: none;
    border: 1px solid #000;
  }

  .system-alert {
    border: 1px solid currentColor;
  }
}
