/* ===== Heroicons 图标保护样式 ===== */
/* 此文件用于防止 Tailwind CSS 的 purge 功能删除 Heroicons 图标类 */
/* 通过明确定义这些类，确保它们始终包含在最终的 CSS 输出中 */


/* 通用图标保护规则 */
/* 确保所有 hero- 开头的类都被保留 */
[class*="hero-"] {
  /* 基础保护样式 */
  content: "";
}

/* 悬停和交互状态保护 */
[class*="hero-"]:hover,
[class*="hero-"]:focus,
[class*="hero-"]:active {
  /* 交互状态保护 */
  content: "";
}

/* 响应式变体保护 */
@media (min-width: 640px) {
  .sm\:[class*="hero-"] { content: ""; }
}

@media (min-width: 768px) {
  .md\:[class*="hero-"] { content: ""; }
}

@media (min-width: 1024px) {
  .lg\:[class*="hero-"] { content: ""; }
}

@media (min-width: 1280px) {
  .xl\:[class*="hero-"] { content: ""; }
}

@media (min-width: 1536px) {
  .\32xl\:[class*="hero-"] { content: ""; }
}

/* 暗色模式变体保护 */
.dark [class*="hero-"],
[data-theme="dark"] [class*="hero-"] {
  content: "";
}

/* 组合选择器保护 */
.group:hover [class*="hero-"],
.group:focus [class*="hero-"],
.group-hover [class*="hero-"],
.group-focus [class*="hero-"],
.peer:hover ~ [class*="hero-"],
.peer:focus ~ [class*="hero-"],
.peer-hover [class*="hero-"],
.peer-focus [class*="hero-"] {
  content: "";
}

/* 伪类变体保护 */
.hover\:[class*="hero-"]:hover,
.focus\:[class*="hero-"]:focus,
.active\:[class*="hero-"]:active,
.disabled\:[class*="hero-"]:disabled {
  content: "";
}

/* 确保图标在各种容器中都被保留 */
.btn [class*="hero-"],
.card [class*="hero-"],
.modal [class*="hero-"],
.dropdown [class*="hero-"],
.navbar [class*="hero-"],
.sidebar [class*="hero-"],
.menu [class*="hero-"],
.tab [class*="hero-"],
.alert [class*="hero-"],
.badge [class*="hero-"],
.tooltip [class*="hero-"] {
  content: "";
}

/* 特殊状态保护 */
.loading [class*="hero-"],
.disabled [class*="hero-"],
.selected [class*="hero-"],
.highlighted [class*="hero-"] {
  content: "";
}
