/* ===== 统一页面样式管理系统 ===== */

/* ===== 页面布局基础变量 ===== */
:root {
  /* 页面布局 */
  --page-max-width: 1400px;
  --page-padding: 1.5rem;
  --page-gap: 1.5rem;
  
  /* 内容区域 */
  --content-padding: 2rem;
  --content-gap: 1.5rem;
  --content-radius: 1rem;
  
  /* 卡片系统 */
  --card-padding: 1.5rem;
  --card-radius: 0.75rem;
  --card-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --card-shadow-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* 表格系统 */
  --table-header-bg: #f8fafc;
  --table-border: #e2e8f0;
  --table-hover: #f1f5f9;
  --table-padding: 0.75rem;
  
  /* 表单系统 */
  --form-input-padding: 0.75rem;
  --form-input-radius: 0.5rem;
  --form-input-border: #d1d5db;
  --form-input-focus: #3b82f6;
  
  /* 按钮系统 */
  --btn-padding: 0.75rem 1.5rem;
  --btn-radius: 0.5rem;
  --btn-font-weight: 500;
  
  /* 状态颜色 */
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;
  --status-neutral: #6b7280;
}

/* ===== 页面容器基础样式 ===== */
.page-container {
  width: 100%;
  max-width: var(--page-max-width);
  margin: 0 auto;
  padding: var(--page-padding);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: var(--page-gap);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: var(--content-gap);
}

.page-title-section {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
}

.page-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

/* ===== 内容区域样式 ===== */
.content-section {
  background: white;
  border-radius: var(--content-radius);
  box-shadow: var(--card-shadow);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.content-header {
  padding: var(--content-padding);
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.content-body {
  padding: var(--content-padding);
}

.content-footer {
  padding: var(--content-padding);
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

/* ===== 卡片组件统一样式 ===== */
.unified-card {
  background: white;
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  border: 1px solid #e5e7eb;
  padding: var(--card-padding);
  transition: all 0.2s ease;
}

.unified-card:hover {
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-1px);
}

.unified-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.unified-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.unified-card-body {
  flex: 1;
}

.unified-card-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* ===== 表格统一样式 ===== */
.unified-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.unified-table thead {
  background: var(--table-header-bg);
}

.unified-table th {
  padding: var(--table-padding);
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--table-border);
}

.unified-table td {
  padding: var(--table-padding);
  border-bottom: 1px solid var(--table-border);
  color: #1f2937;
  vertical-align: middle;
}

.unified-table tbody tr:hover {
  background: var(--table-hover);
}

.unified-table tbody tr:last-child td {
  border-bottom: none;
}

/* ===== 表单统一样式 ===== */
.unified-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.unified-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.unified-form-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.unified-form-input,
.unified-form-select,
.unified-form-textarea {
  padding: var(--form-input-padding);
  border: 1px solid var(--form-input-border);
  border-radius: var(--form-input-radius);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: white;
}

.unified-form-input:focus,
.unified-form-select:focus,
.unified-form-textarea:focus {
  outline: none;
  border-color: var(--form-input-focus);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.unified-form-grid {
  display: grid;
  gap: 1.5rem;
}

.unified-form-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.unified-form-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* ===== 按钮统一样式 ===== */
.unified-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: var(--btn-padding);
  border-radius: var(--btn-radius);
  font-weight: var(--btn-font-weight);
  font-size: 0.875rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  white-space: nowrap;
}

.unified-btn-primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.unified-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgb(59 130 246 / 0.3);
}

.unified-btn-secondary {
  background: #6b7280;
  color: white;
  border-color: #6b7280;
}

.unified-btn-secondary:hover {
  background: #4b5563;
  border-color: #4b5563;
}

.unified-btn-success {
  background: var(--status-success);
  color: white;
  border-color: var(--status-success);
}

.unified-btn-warning {
  background: var(--status-warning);
  color: white;
  border-color: var(--status-warning);
}

.unified-btn-error {
  background: var(--status-error);
  color: white;
  border-color: var(--status-error);
}

.unified-btn-outline {
  background: white;
  color: #374151;
  border-color: #d1d5db;
}

.unified-btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.unified-btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.unified-btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* ===== 状态徽章统一样式 ===== */
.unified-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.unified-badge-success {
  background: rgb(16 185 129 / 0.1);
  color: var(--status-success);
  border: 1px solid rgb(16 185 129 / 0.2);
}

.unified-badge-warning {
  background: rgb(245 158 11 / 0.1);
  color: var(--status-warning);
  border: 1px solid rgb(245 158 11 / 0.2);
}

.unified-badge-error {
  background: rgb(239 68 68 / 0.1);
  color: var(--status-error);
  border: 1px solid rgb(239 68 68 / 0.2);
}

.unified-badge-info {
  background: rgb(59 130 246 / 0.1);
  color: var(--status-info);
  border: 1px solid rgb(59 130 246 / 0.2);
}

.unified-badge-neutral {
  background: rgb(107 114 128 / 0.1);
  color: var(--status-neutral);
  border: 1px solid rgb(107 114 128 / 0.2);
}

/* ===== 搜索和筛选区域 ===== */
.unified-search-section {
  background: white;
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  border: 1px solid #e5e7eb;
  padding: var(--card-padding);
  margin-bottom: var(--content-gap);
}

.unified-search-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.unified-search-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.unified-search-input-group {
  display: flex;
  gap: 0.5rem;
}

.unified-search-input {
  flex: 1;
  padding: var(--form-input-padding);
  border: 1px solid var(--form-input-border);
  border-radius: var(--form-input-radius);
  font-size: 0.875rem;
}

/* ===== 空状态样式 ===== */
.unified-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
  color: #6b7280;
}

.unified-empty-icon {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1rem;
  color: #d1d5db;
}

.unified-empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.unified-empty-description {
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

/* ===== 加载状态样式 ===== */
.unified-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 0.75rem;
}

.unified-loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  :root {
    --page-padding: 1rem;
    --content-padding: 1rem;
    --card-padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .page-actions {
    justify-content: stretch;
  }

  .unified-form-grid-2,
  .unified-form-grid-3 {
    grid-template-columns: 1fr;
  }

  .unified-search-grid {
    grid-template-columns: 1fr;
  }

  .unified-table {
    font-size: 0.75rem;
  }

  .unified-table th,
  .unified-table td {
    padding: 0.5rem;
  }
}

@media (max-width: 640px) {
  .page-title {
    font-size: 1.5rem;
  }

  .unified-btn {
    width: 100%;
    justify-content: center;
  }

  .unified-card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .unified-card-footer {
    flex-direction: column;
  }
}
