/* 客服聊天界面样式增强 */

/* 聊天消息动画 */
.chat-message {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 消息气泡悬停效果 */
.message-bubble {
  transition: all 0.2s ease-in-out;
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 输入框聚焦效果 */
.chat-input:focus {
  ring-color: #3b82f6;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 滚动条样式 */
.chat-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 打字指示器动画 */
.typing-indicator {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    height: 100dvh; /* 动态视口高度 */
  }
  
  .message-bubble {
    max-width: 85%;
  }
}

/* 状态指示器 */
.status-indicator {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 侧边栏滑动动画 */
.sidebar-slide {
  transition: transform 0.3s ease-in-out;
}

/* 加载状态 */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 消息发送状态 */
.message-status {
  opacity: 0.7;
  transition: opacity 0.2s ease-in-out;
}

.message-status:hover {
  opacity: 1;
}

/* 响应式字体大小 */
@media (max-width: 640px) {
  .chat-text {
    font-size: 14px;
    line-height: 1.4;
  }
  
  .chat-time {
    font-size: 11px;
  }
}

/* 聊天列表项悬停效果 */
.chat-list-item {
  transition: all 0.2s ease-in-out;
}

.chat-list-item:hover {
  background-color: #f8fafc;
  transform: translateX(2px);
}

.chat-list-item.active {
  background-color: #eff6ff;
  border-left: 3px solid #3b82f6;
}

/* 在线状态指示器脉冲动画 */
.online-indicator {
  animation: pulse-green 2s ease-in-out infinite;
}

@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 新消息提示 */
.new-message-indicator {
  animation: bounce 1s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 渐变背景 */
.chat-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 消息时间戳淡入效果 */
.message-time {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.message-bubble:hover .message-time {
  opacity: 1;
}
