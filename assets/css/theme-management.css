/* 主题管理专用样式 */

/* ===== Backpex 主题选择器增强样式 ===== */
.enhanced-theme-selector {
  position: relative;
}

.enhanced-theme-selector .btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enhanced-theme-selector .btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px oklch(var(--color-primary) / 0.3);
}

.enhanced-theme-selector .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, oklch(var(--color-primary) / 0.2), transparent);
  transition: left 0.5s ease;
}

.enhanced-theme-selector .btn:hover::before {
  left: 100%;
}

/* 下拉菜单样式增强 */
.enhanced-theme-selector .dropdown-content {
  backdrop-filter: blur(10px);
  border: 1px solid oklch(var(--color-base-300) / 0.5);
  box-shadow: 0 20px 40px oklch(var(--color-base-content) / 0.15);
  width: 280px;
  max-height: 400px;
}

.enhanced-theme-selector .menu li {
  margin: 2px 4px;
  border-radius: var(--radius-md);
}

.enhanced-theme-selector .menu li label {
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.enhanced-theme-selector .menu li label:hover {
  background: oklch(var(--color-primary) / 0.1);
  transform: translateX(4px);
  box-shadow: 0 2px 8px oklch(var(--color-primary) / 0.2);
}

.enhanced-theme-selector .menu li label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, oklch(var(--color-primary) / 0.1), transparent);
  transition: left 0.3s ease;
}

.enhanced-theme-selector .menu li label:hover::before {
  left: 100%;
}

/* 选中状态样式 */
.enhanced-theme-selector .menu li label.has-checked\:bg-neutral {
  background: oklch(var(--color-primary));
  color: oklch(var(--color-primary-content));
  box-shadow: 0 0 0 2px oklch(var(--color-primary) / 0.3);
}

.enhanced-theme-selector .menu li label.has-checked\:bg-neutral:hover {
  background: oklch(var(--color-primary));
  transform: translateX(0);
}

/* 主题预览点 */
.enhanced-theme-selector .menu li label::after {
  content: '';
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.3;
  margin-left: auto;
  transition: all 0.2s ease;
}

.enhanced-theme-selector .menu li label:hover::after {
  opacity: 0.6;
  transform: scale(1.2);
}

.enhanced-theme-selector .menu li label.has-checked\:bg-neutral::after {
  opacity: 1;
  background: oklch(var(--color-primary-content));
}

/* 移动端优化 */
@media (max-width: 768px) {
  .enhanced-theme-selector .dropdown-content {
    width: 260px;
    max-height: 350px;
  }

  .enhanced-theme-selector .menu li label {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

/* ===== 主题切换器增强样式 ===== */
.theme-switcher-container {
  position: relative;
}

.theme-toggle-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px oklch(var(--color-primary) / 0.3);
}

.theme-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, oklch(var(--color-primary) / 0.2), transparent);
  transition: left 0.5s ease;
}

.theme-toggle-btn:hover::before {
  left: 100%;
}

/* ===== 主题选项样式 ===== */
.theme-option {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
  border-radius: var(--radius-lg);
}

.theme-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px oklch(var(--color-base-content) / 0.1);
}

.theme-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, oklch(var(--color-primary) / 0.1), transparent);
  transition: left 0.3s ease;
}

.theme-option:hover::before {
  left: 100%;
}

/* ===== 主题预览效果 ===== */
.theme-preview {
  position: relative;
  transition: all 0.2s ease;
}

.theme-preview:hover {
  transform: scale(1.1);
}

.theme-preview .bg-primary,
.theme-preview .bg-secondary,
.theme-preview .bg-accent {
  transition: all 0.3s ease;
  animation: color-pulse 2s ease-in-out infinite;
}

@keyframes color-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* ===== 主题切换动画 ===== */
.theme-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主题切换时的页面动画 */
[data-theme] {
  transition: background-color 0.4s ease, color 0.4s ease;
}

[data-theme] * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* ===== 主题提示样式 ===== */
.theme-toast {
  z-index: 9999;
}

.theme-toast .alert {
  backdrop-filter: blur(10px);
  background: oklch(var(--color-info) / 0.9);
  border: 1px solid oklch(var(--color-info) / 0.3);
  box-shadow: 0 8px 32px oklch(var(--color-base-content) / 0.1);
}

/* ===== 主题管理页面样式 ===== */
.theme-management-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    oklch(var(--color-base-100)) 0%, 
    oklch(var(--color-base-200)) 100%);
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.theme-card {
  position: relative;
  background: oklch(var(--color-base-100));
  border: 2px solid oklch(var(--color-base-300));
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

.theme-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px oklch(var(--color-base-content) / 0.15);
  border-color: oklch(var(--color-primary) / 0.5);
}

.theme-card.active {
  border-color: oklch(var(--color-primary));
  background: oklch(var(--color-primary) / 0.05);
  box-shadow: 0 0 0 4px oklch(var(--color-primary) / 0.2);
}

.theme-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, 
    oklch(var(--color-primary)) 0%, 
    oklch(var(--color-secondary)) 50%, 
    oklch(var(--color-accent)) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.theme-card:hover::before,
.theme-card.active::before {
  opacity: 1;
}

/* ===== 主题统计样式 ===== */
.theme-stats {
  background: oklch(var(--color-base-200));
  border-radius: var(--radius-xl);
  padding: 2rem;
  margin: 2rem 0;
}

.theme-stat-item {
  text-align: center;
  padding: 1rem;
  background: oklch(var(--color-base-100));
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
}

.theme-stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px oklch(var(--color-base-content) / 0.1);
}

.theme-stat-value {
  font-size: 2rem;
  font-weight: 700;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .theme-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .theme-card {
    padding: 1rem;
  }

  .theme-stats {
    padding: 1rem;
  }

  .theme-stat-value {
    font-size: 1.5rem;
  }
}

/* ===== 主题特定样式 ===== */
[data-theme="dark"] .theme-management-container {
  background: linear-gradient(135deg, 
    oklch(var(--color-base-100)) 0%, 
    oklch(var(--color-base-300)) 100%);
}

[data-theme="cyberpunk"] .theme-card {
  box-shadow: 0 0 20px oklch(var(--color-primary) / 0.3);
}

[data-theme="cyberpunk"] .theme-card:hover {
  box-shadow: 0 0 40px oklch(var(--color-primary) / 0.5);
}

[data-theme="synthwave"] .theme-card::before {
  background: linear-gradient(90deg, 
    #ff006e 0%, 
    #8338ec 25%, 
    #3a86ff 50%, 
    #06ffa5 75%, 
    #ffbe0b 100%);
}

/* ===== 主题切换加载动画 ===== */
.theme-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: oklch(var(--color-base-100) / 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.theme-loading.active {
  opacity: 1;
  pointer-events: all;
}

.theme-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid oklch(var(--color-base-300));
  border-top: 4px solid oklch(var(--color-primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== 主题预览模式样式 ===== */
.theme-preview-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: oklch(var(--color-warning) / 0.1);
  border-bottom: 2px solid oklch(var(--color-warning) / 0.3);
  padding: 0.75rem;
  z-index: 1000;
  backdrop-filter: blur(8px);
}

.theme-preview-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

/* ===== 主题快捷键提示 ===== */
.theme-shortcuts {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: oklch(var(--color-base-100));
  border: 1px solid oklch(var(--color-base-300));
  border-radius: var(--radius-lg);
  padding: 1rem;
  box-shadow: 0 8px 32px oklch(var(--color-base-content) / 0.1);
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.theme-shortcuts.show {
  opacity: 1;
  transform: translateY(0);
}

.theme-shortcuts kbd {
  background: oklch(var(--color-base-200));
  border: 1px solid oklch(var(--color-base-300));
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-family: monospace;
}
