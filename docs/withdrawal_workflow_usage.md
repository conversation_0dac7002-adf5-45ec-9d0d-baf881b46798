# 提现工作流使用指南

## 概述

新的提现工作流系统使用Reactor实现，确保了积分流转的原子性和数据一致性。本指南说明如何使用新系统。

## 核心流程

### 1. 创建提现申请

```elixir
# 用户创建提现申请
{:ok, withdrawal} = Teen.Services.WithdrawalService.create_withdrawal(user_id, %{
  withdrawal_amount: Decimal.new("1000.00"),
  payment_method: "bank_card",  # 或 "alipay", "upi"
  bank_info: %{
    bank_name: "工商银行",
    account_number: "****************",
    account_name: "张三"
  }
})

# 此时积分已自动从用户账户扣除到待审核账户
```

### 2. 管理员审核

#### 审核通过
```elixir
{:ok, withdrawal} = Teen.Services.WithdrawalService.approve_withdrawal(
  withdrawal_id,
  auditor_id
)
# 积分保持在待审核账户，准备支付
```

#### 审核拒绝
```elixir
{:ok, withdrawal} = Teen.Services.WithdrawalService.reject_withdrawal(
  withdrawal_id,
  auditor_id,
  "资料不完整"  # 拒绝原因
)
# 积分自动退回用户账户
```

### 3. 执行支付（审核通过后）

```elixir
# 手动执行单个提现支付
{:ok, :submitted} = Teen.Services.WithdrawalService.process_withdrawal_payment(withdrawal_id)

# 或批量处理已审核的提现
results = Teen.Services.WithdrawalService.process_approved_withdrawals(10)
# 返回 [{:ok, withdrawal_id}, {:error, {withdrawal_id, reason}}, ...]
```

### 4. 处理支付回调

```elixir
# 支付网关回调
callback_data = %{
  "order_id" => "WD1234567890",
  "status" => "success",  # 或 "failed", "pending"
  "gateway_order_id" => "GW123456",
  "message" => "Payment completed"
}

{:ok, :processed} = Teen.Services.WithdrawalService.handle_withdrawal_callback(
  order_id,
  callback_data,
  signature  # 可选的签名验证
)

# 根据状态自动处理：
# - success: 积分转入已支付账户
# - failed: 积分退回用户账户
# - pending: 仅更新状态
```

## 查询功能

### 检查提现资格
```elixir
{:ok, eligibility} = Teen.Services.WithdrawalService.check_withdrawal_eligibility(
  user_id,
  Decimal.new("500")
)
# 返回：
# %{
#   eligible: true,
#   balance: 1000,
#   turnover_info: %{...}
# }
```

### 获取用户提现记录
```elixir
# 最近30天的提现记录
{:ok, withdrawals} = Teen.Services.WithdrawalService.get_user_recent_withdrawals(user_id)

# 用户提现统计
{:ok, stats} = Teen.Services.WithdrawalService.get_user_withdrawal_stats(user_id)
# 返回：
# %{
#   total_count: 10,
#   total_amount: #Decimal<10000>,
#   success_count: 8,
#   success_amount: #Decimal<8000>,
#   pending_count: 2,
#   pending_amount: #Decimal<2000>
# }
```

### 管理功能
```elixir
# 获取待审核列表
{:ok, pending} = Teen.Services.WithdrawalService.get_pending_audit_withdrawals(
  limit: 20,
  offset: 0
)

# 重试失败的提现
{:ok, :retrying} = Teen.Services.WithdrawalService.retry_failed_withdrawal(withdrawal_id)
```

## 状态说明

### audit_status（审核状态）
- 0: 待审核
- 1: 审核通过
- 2: 审核拒绝

### progress_status（处理进度）
- 0: 待处理
- 1: 处理中
- 2: 支付成功
- 3: 支付失败

### result_status（最终结果）
- 0: 进行中
- 1: 成功
- 2: 失败

## 积分流转图

```
创建提现:     用户账户 -----> 待审核账户
审核拒绝:     待审核账户 ---> 用户账户
支付成功:     待审核账户 ---> 已支付账户
支付失败:     待审核账户 ---> 用户账户
```

## 事件通知

系统会通过Phoenix.PubSub发布以下事件：

- `withdrawal:#{user_id}` - 用户相关的提现事件
- `withdrawal:admin` - 管理员通知频道
- `system:alerts` - 系统告警（如退款失败）

事件类型：
- `:withdrawal_created` - 提现创建
- `:withdrawal_approved` - 审核通过
- `:withdrawal_rejected` - 审核拒绝
- `:withdrawal_payment_submitted` - 支付提交
- `:withdrawal_payment_failed` - 支付失败
- `:withdrawal_callback_completed` - 回调完成
- `:withdrawal_callback_refunded` - 回调退款

## 错误处理

所有函数返回标准的Elixir结果元组：
- `{:ok, result}` - 成功
- `{:error, reason}` - 失败，reason为字符串说明

常见错误：
- "余额不足"
- "流水未达标"
- "用户状态异常"
- "提现未审核通过"
- "提现已在处理中"

## 定时任务示例

```elixir
defmodule MyApp.WithdrawalWorker do
  use Oban.Worker, queue: :withdrawal

  @impl Oban.Worker
  def perform(%Oban.Job{}) do
    # 每小时处理10个已审核的提现
    results = Teen.Services.WithdrawalService.process_approved_withdrawals(10)
    
    Enum.each(results, fn
      {:ok, withdrawal_id} ->
        Logger.info("提现处理成功: #{withdrawal_id}")
      
      {:error, {withdrawal_id, reason}} ->
        Logger.error("提现处理失败: #{withdrawal_id}, 原因: #{reason}")
    end)
    
    :ok
  end
end

# 配置定时任务
config :my_app, Oban,
  queues: [withdrawal: 10],
  plugins: [
    {Oban.Plugins.Cron,
     crontab: [
       {"0 * * * *", MyApp.WithdrawalWorker}  # 每小时执行
     ]}
  ]
```

## 注意事项

1. 所有金额使用Decimal类型，避免浮点数精度问题
2. 积分操作通过Cypridina.Ledger保证原子性
3. 失败时会自动回滚，无需手动处理
4. 建议通过定时任务批量处理已审核的提现
5. 回调处理是幂等的，可以安全重试