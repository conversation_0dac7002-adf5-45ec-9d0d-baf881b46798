# MasterPay 生产环境部署检查清单

## 🚀 部署前准备

### 1. 环境变量配置
- [ ] `MASTERPAY_MERCHANT_ID` - 从MasterPay获取的正式商户ID
- [ ] `MASTERPAY_SECRET_KEY` - 从MasterPay获取的正式密钥
- [ ] `MASTERPAY_API_URL` - MasterPay生产环境API地址
- [ ] `MASTERPAY_CALLBACK_URL` - 您的回调URL (必须HTTPS)
- [ ] `BASE_URL` - 您的应用域名 (必须HTTPS)
- [ ] `SECRET_KEY_BASE` - Phoenix应用密钥 (64字符)
- [ ] `DATABASE_URL` - 生产数据库连接字符串

### 2. 安全配置验证
- [ ] 所有密钥通过环境变量管理，无硬编码
- [ ] HTTPS协议启用
- [ ] 回调URL白名单配置
- [ ] IP白名单设置 (如果MasterPay要求)

### 3. 数据库配置
- [ ] 更新payment_gateway_configs表中的MasterPay配置
- [ ] 确认生产环境数据库连接正常
- [ ] 运行必要的数据库迁移

## 🔧 配置文件检查

### 1. 配置文件更新
- [ ] `config/config.exs` - MasterPay生产配置已更新
- [ ] `config/prod.exs` - 禁用模拟支付，启用生产日志
- [ ] `config/runtime.exs` - 运行时配置 (如需要)

### 2. 代码检查
- [ ] 移除所有测试/模拟相关代码
- [ ] MasterPayService模块正确集成
- [ ] 错误处理和日志记录完整
- [ ] 签名验证逻辑正确

## 🧪 测试验证

### 1. 配置验证
```bash
# 运行配置验证脚本
elixir scripts/validate_masterpay_config.exs
```

### 2. 连接测试
```elixir
# 在生产控制台中运行
Teen.PaymentSystem.MasterPayService.validate_config()
Teen.PaymentSystem.MasterPayService.test_connection()
```

### 3. 功能测试
- [ ] 创建测试订单
- [ ] 验证回调处理
- [ ] 测试订单查询
- [ ] 检查签名验证

## 📊 监控和日志

### 1. 日志配置
- [ ] 支付日志单独存储到 `logs/payment.log`
- [ ] 错误级别日志监控设置
- [ ] 敏感信息屏蔽 (密钥、个人信息)

### 2. 监控指标
- [ ] 支付成功率监控
- [ ] API响应时间监控
- [ ] 错误率和失败原因统计
- [ ] 回调接收率监控

### 3. 报警设置
- [ ] 支付失败率超阈值报警
- [ ] API连接失败报警
- [ ] 签名验证失败报警

## 🔄 部署步骤

### 1. 预发布验证
```bash
# 1. 验证配置
elixir scripts/validate_masterpay_config.exs

# 2. 运行测试
mix test

# 3. 代码格式检查
mix format --check-formatted

# 4. 静态分析
mix credo
```

### 2. 部署执行
```bash
# 1. 构建发布版本
MIX_ENV=prod mix assets.deploy
MIX_ENV=prod mix phx.digest
MIX_ENV=prod mix release

# 2. 数据库迁移 (如需要)
MIX_ENV=prod mix ecto.migrate

# 3. 启动服务
MIX_ENV=prod mix phx.server
```

### 3. 部署后验证
- [ ] 应用启动正常
- [ ] 数据库连接正常
- [ ] MasterPay API连接正常
- [ ] 支付功能正常工作
- [ ] 日志正常输出

## 🚨 故障排除

### 常见问题

1. **配置错误**
   - 检查环境变量是否正确设置
   - 验证MasterPay提供的参数

2. **网络连接问题**
   - 检查防火墙设置
   - 验证DNS解析
   - 确认HTTPS证书有效

3. **签名验证失败**
   - 检查密钥是否正确
   - 验证签名算法实现
   - 确认参数顺序和编码

4. **回调接收失败**
   - 检查回调URL可访问性
   - 验证IP白名单设置
   - 确认HTTPS配置正确

### 紧急联系

- **MasterPay技术支持**: [联系方式]
- **运维团队**: [联系方式]
- **开发团队**: [联系方式]

## 📋 部署记录

- **部署日期**: ___________
- **部署人员**: ___________
- **版本号**: ___________
- **环境**: 生产环境
- **验证状态**: [ ] 通过 [ ] 失败
- **备注**: ___________

---

**重要提醒**: 生产环境部署前必须完成所有检查项，确保支付安全！