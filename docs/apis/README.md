# API 文档索引

本目录包含项目中使用的各种 API 和框架的文档。

## Backpex 文档

Backpex 是我们项目中使用的 Phoenix LiveView 管理面板框架。

### 📚 文档列表

1. **[Backpex 概览](backpex_overview.md)**
   - Backpex 简介和核心特性
   - 版本信息和依赖要求
   - 快速开始指南
   - 核心概念介绍

2. **[安装指南](backpex_installation.md)**
   - 详细的安装步骤
   - 依赖配置
   - JavaScript 和 CSS 设置
   - 主题配置

3. **[LiveResource 详解](backpex_live_resource.md)**
   - LiveResource 的核心概念
   - 基本结构和配置选项
   - 必需和可选的回调函数
   - 字段配置和自定义渲染
   - 关联字段处理

4. **[字段配置](backpex_fields.md)**
   - 内置字段类型详解
   - 字段配置选项
   - 自定义渲染
   - 关联字段
   - 项目中的实际应用

5. **[过滤器和搜索](backpex_filters.md)**
   - 搜索功能配置
   - 内置过滤器类型
   - 自定义过滤器
   - 权限控制
   - 性能优化

6. **[操作和权限](backpex_actions.md)**
   - Item Actions 和 Resource Actions
   - 自定义操作
   - 权限控制系统
   - 项目中的实际应用

### 🚀 快速导航

#### 新手入门
- 从 [Backpex 概览](backpex_overview.md) 开始了解基本概念
- 按照 [安装指南](backpex_installation.md) 进行配置
- 阅读 [LiveResource 详解](backpex_live_resource.md) 了解核心功能

#### 开发参考
- [字段配置](backpex_fields.md) - 配置各种字段类型
- [过滤器和搜索](backpex_filters.md) - 实现搜索和过滤功能
- [操作和权限](backpex_actions.md) - 添加自定义操作和权限控制

### 📋 项目中的使用

我们的项目中 Backpex 被广泛用于管理后台，包括：

#### 用户管理
- **文件**: `lib/teen/live/user_live.ex`
- **功能**: 用户列表、编辑、权限管理
- **字段**: 用户名、权限级别、性别等
- **过滤器**: 用户状态、权限级别

#### 商品管理
- **文件**: `lib/teen/live/product_live.ex`
- **功能**: 商品列表、上下架、价格管理
- **字段**: 商品名称、类型、价格、状态
- **过滤器**: 商品类型、状态、货币类型

#### 订单管理
- **文件**: `lib/teen/live/payment_order_live.ex`
- **功能**: 订单查看、状态管理、退款处理
- **字段**: 订单号、金额、状态、支付方式
- **过滤器**: 订单状态、支付方式、时间范围

#### 渠道管理
- **文件**: `lib/teen/live/channel_live.ex`
- **功能**: 渠道配置、状态管理
- **字段**: 渠道名称、状态、配置信息
- **过滤器**: 渠道状态

#### 支付网关管理
- **文件**: `lib/teen/live/payment_gateway_live.ex`
- **功能**: 支付网关配置、测试、监控
- **字段**: 网关名称、类型、配置、状态
- **过滤器**: 网关类型、状态

### 🔧 常见配置模式

#### 基本 LiveResource 结构
```elixir
defmodule Teen.Live.ExampleLive do
  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.Example.Resource,
    ],
    layout: {Teen.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "示例"

  @impl Backpex.LiveResource
  def plural_name, do: "示例列表"

  @impl Backpex.LiveResource
  def fields do
    %{
      # 字段配置
    }
  end
end
```

#### 常用字段类型
```elixir
# 文本字段
name: %{
  module: Backpex.Fields.Text,
  label: "名称",
  searchable: true
}

# 选择字段
status: %{
  module: Backpex.Fields.Select,
  label: "状态",
  options: [
    {"启用", 1},
    {"禁用", 0}
  ]
}

# 数字字段
amount: %{
  module: Backpex.Fields.Number,
  label: "金额",
  orderable: true
}
```

#### 权限控制模式
```elixir
@impl Backpex.LiveResource
def can?(assigns, action, item) do
  case assigns.current_user.permission_level do
    level when level >= 2 -> true  # 超级管理员
    1 -> action in [:index, :show, :edit]  # 管理员
    0 -> action in [:index, :show]  # 普通用户
    _ -> false
  end
end
```

### 📝 注意事项

1. **选项格式**: Select 字段的选项必须使用元组格式 `{"显示文本", 值}`
2. **变量访问**: 在自定义渲染中必须使用 `assign/3` 分配变量后才能在模板中访问
3. **权限控制**: 始终实现 `can?/3` 回调进行权限控制
4. **性能优化**: 为搜索和过滤字段创建数据库索引

### 🔗 相关资源

- **官方文档**: https://hexdocs.pm/backpex
- **GitHub**: https://github.com/naymspace/backpex
- **演示站点**: https://backpex.live/
- **Hex.pm**: https://hex.pm/packages/backpex

### 📞 获取帮助

如果在使用 Backpex 过程中遇到问题：

1. 首先查阅相关文档
2. 检查项目中的现有实现
3. 查看 Backpex 官方文档和示例
4. 在项目中搜索类似的实现模式

---

*最后更新: 2025-01-10*
