# Backpex 操作和权限

## 概述

Backpex 提供了两种类型的操作：
- **Item Actions**: 针对单个或多个选中项目的操作
- **Resource Actions**: 针对整个资源的操作

## Item Actions（项目操作）

### 内置 Item Actions

Backpex 提供了几个内置的项目操作：

```elixir
@impl Backpex.LiveResource
def item_actions(_) do
  [
    show: %{
      module: Backpex.ItemActions.Show,
      only: [:row]  # 只在行中显示
    },
    edit: %{
      module: Backpex.ItemActions.Edit,
      only: [:row, :show]  # 在行和详情页显示
    },
    delete: %{
      module: Backpex.ItemActions.Delete,
      only: [:row, :index, :show]  # 在行、列表页和详情页显示
    }
  ]
end
```

### 可用的内置 ItemActions

- `Backpex.ItemActions.Show` - 查看详情
- `Backpex.ItemActions.Edit` - 编辑项目
- `Backpex.ItemActions.Delete` - 删除项目

### 显示位置控制

使用 `only` 选项控制操作在哪些位置显示：

- `:row` - 在表格行中显示
- `:index` - 在列表页的批量操作中显示
- `:show` - 在详情页显示

## 自定义 Item Actions

### 创建自定义 ItemAction

```elixir
defmodule MyApp.ItemActions.Approve do
  use Backpex.ItemAction

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "批准"

  @impl Backpex.ItemAction
  def icon(_assigns, _item), do: "hero-check-circle"

  @impl Backpex.ItemAction
  def can?(_assigns, _item), do: true

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认批准此项目？"

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    case MyApp.Service.approve_items(items) do
      {:ok, _result} ->
        {:ok, socket, "项目已批准"}
      {:error, reason} ->
        {:error, socket, "批准失败: #{inspect(reason)}"}
    end
  end
end
```

### 在 LiveResource 中使用自定义操作

```elixir
@impl Backpex.LiveResource
def item_actions(_) do
  [
    show: %{
      module: Backpex.ItemActions.Show,
      only: [:row]
    },
    edit: %{
      module: Backpex.ItemActions.Edit,
      only: [:row, :show]
    },
    approve: %{
      module: MyApp.ItemActions.Approve,
      only: [:row, :index]
    },
    delete: %{
      module: Backpex.ItemActions.Delete,
      only: [:row, :index, :show]
    }
  ]
end
```

### 条件显示操作

```elixir
defmodule MyApp.ItemActions.Activate do
  use Backpex.ItemAction

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "激活"

  @impl Backpex.ItemAction
  def can?(assigns, item) do
    # 只有管理员可以激活，且项目当前是非激活状态
    assigns.current_user.role == :admin and item.status != :active
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    case MyApp.Service.activate_items(items) do
      {:ok, _result} ->
        {:ok, socket, "项目已激活"}
      {:error, reason} ->
        {:error, socket, "激活失败: #{inspect(reason)}"}
    end
  end
end
```

## Resource Actions（资源操作）

### 基本 Resource Action

```elixir
defmodule MyApp.ResourceActions.ExportUsers do
  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def label(_assigns), do: "导出用户"

  @impl Backpex.ResourceAction
  def icon(_assigns), do: "hero-document-arrow-down"

  @impl Backpex.ResourceAction
  def can?(_assigns), do: true

  @impl Backpex.ResourceAction
  def handle(socket, _params) do
    case MyApp.Service.export_users() do
      {:ok, file_path} ->
        # 触发文件下载
        {:ok, socket, "导出成功", %{download: file_path}}
      {:error, reason} ->
        {:error, socket, "导出失败: #{inspect(reason)}"}
    end
  end
end
```

### 带表单的 Resource Action

```elixir
defmodule MyApp.ResourceActions.BulkInvite do
  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def label(_assigns), do: "批量邀请"

  @impl Backpex.ResourceAction
  def icon(_assigns), do: "hero-user-plus"

  @impl Backpex.ResourceAction
  def can?(assigns), do: assigns.current_user.role == :admin

  @impl Backpex.ResourceAction
  def fields do
    [
      emails: %{
        module: Backpex.Fields.Textarea,
        label: "邮箱列表",
        placeholder: "每行一个邮箱地址",
        required: true
      },
      role: %{
        module: Backpex.Fields.Select,
        label: "角色",
        options: [
          {"用户", :user},
          {"管理员", :admin}
        ],
        default: :user
      }
    ]
  end

  @impl Backpex.ResourceAction
  def handle(socket, %{emails: emails, role: role}) do
    email_list = 
      emails
      |> String.split("\n")
      |> Enum.map(&String.trim/1)
      |> Enum.reject(&(&1 == ""))

    case MyApp.Service.bulk_invite(email_list, role) do
      {:ok, count} ->
        {:ok, socket, "成功邀请 #{count} 个用户"}
      {:error, reason} ->
        {:error, socket, "邀请失败: #{inspect(reason)}"}
    end
  end
end
```

### 在 LiveResource 中配置 Resource Actions

```elixir
@impl Backpex.LiveResource
def resource_actions do
  [
    export: %{
      module: MyApp.ResourceActions.ExportUsers
    },
    bulk_invite: %{
      module: MyApp.ResourceActions.BulkInvite
    }
  ]
end
```

## 权限控制

### 基本权限控制

在 LiveResource 中实现 `can?/3` 回调：

```elixir
@impl Backpex.LiveResource
def can?(assigns, action, item) do
  case action do
    :index -> true  # 所有人都可以查看列表
    :show -> true   # 所有人都可以查看详情
    :new -> assigns.current_user.role in [:admin, :manager]
    :edit -> can_edit?(assigns.current_user, item)
    :delete -> assigns.current_user.role == :admin
    _ -> false
  end
end

defp can_edit?(user, item) do
  case user.role do
    :admin -> true
    :manager -> true
    :user -> item.user_id == user.id  # 用户只能编辑自己的项目
    _ -> false
  end
end
```

### 细粒度权限控制

```elixir
@impl Backpex.LiveResource
def can?(assigns, action, item) do
  user = assigns.current_user
  
  case {action, user.permission_level} do
    # 超级管理员可以做任何事
    {_, level} when level >= 2 -> true
    
    # 管理员权限
    {:index, level} when level >= 1 -> true
    {:show, level} when level >= 1 -> true
    {:new, level} when level >= 1 -> true
    {:edit, level} when level >= 1 -> true
    {:delete, level} when level >= 1 -> false  # 管理员不能删除
    
    # 普通用户权限
    {:index, 0} -> true
    {:show, 0} -> true
    {:new, 0} -> false
    {:edit, 0} -> item && item.user_id == user.id
    {:delete, 0} -> false
    
    _ -> false
  end
end
```

## 项目中的实际应用

### 用户管理操作

```elixir
# lib/teen/live/user_live.ex
@impl Backpex.LiveResource
def item_actions(_) do
  [
    show: %{
      module: Backpex.ItemActions.Show,
      only: [:row]
    },
    edit: %{
      module: Backpex.ItemActions.Edit,
      only: [:row, :show]
    },
    delete: %{
      module: Backpex.ItemActions.Delete,
      only: [:row, :index, :show]
    }
  ]
end

@impl Backpex.LiveResource
def can?(assigns, action, item) do
  case assigns.current_user.permission_level do
    level when level >= 2 -> true  # 超级管理员
    1 -> action in [:index, :show, :edit]  # 管理员
    0 -> action in [:index, :show]  # 普通用户
    _ -> false
  end
end
```

### 商品管理操作

```elixir
# lib/teen/live/product_live.ex
@impl Backpex.LiveResource
def item_actions(_) do
  [
    show: %{
      module: Backpex.ItemActions.Show,
      only: [:row]
    },
    edit: %{
      module: Backpex.ItemActions.Edit,
      only: [:row, :show]
    },
    delete: %{
      module: Backpex.ItemActions.Delete,
      only: [:row, :index, :show]
    }
  ]
end

@impl Backpex.LiveResource
def resource_actions do
  [
    import_products: %{
      module: Teen.ResourceActions.ImportProducts
    },
    export_products: %{
      module: Teen.ResourceActions.ExportProducts
    }
  ]
end
```

### 订单管理操作

```elixir
# lib/teen/live/payment_order_live.ex
@impl Backpex.LiveResource
def item_actions(_) do
  [
    show: %{
      module: Backpex.ItemActions.Show,
      only: [:row]
    },
    refund: %{
      module: Teen.ItemActions.RefundOrder,
      only: [:row, :show]
    }
  ]
end
```

## 操作响应处理

### 成功响应

```elixir
@impl Backpex.ItemAction
def handle(socket, items, _params) do
  case process_items(items) do
    {:ok, result} ->
      # 成功响应，显示成功消息
      {:ok, socket, "操作成功完成"}
    
    {:ok, result, redirect_path} ->
      # 成功响应并重定向
      {:ok, socket, "操作成功完成", %{redirect: redirect_path}}
    
    {:error, reason} ->
      # 错误响应
      {:error, socket, "操作失败: #{inspect(reason)}"}
  end
end
```

### 异步操作

```elixir
@impl Backpex.ResourceAction
def handle(socket, params) do
  # 启动异步任务
  Task.start(fn ->
    process_large_dataset(params)
  end)
  
  {:ok, socket, "任务已启动，将在后台处理"}
end
```

## 最佳实践

1. **权限优先**: 始终实现适当的权限控制
2. **用户反馈**: 提供清晰的成功和错误消息
3. **确认对话框**: 对危险操作使用确认对话框
4. **批量操作**: 支持批量操作以提高效率
5. **异步处理**: 对耗时操作使用异步处理
6. **日志记录**: 记录重要操作的日志
7. **事务处理**: 使用数据库事务确保数据一致性

## 常见问题

### 1. 操作不显示

检查 `can?/3` 函数是否返回 `true`：

```elixir
@impl Backpex.ItemAction
def can?(assigns, item) do
  # 确保返回布尔值
  assigns.current_user.role == :admin
end
```

### 2. 批量操作处理

```elixir
@impl Backpex.ItemAction
def handle(socket, items, _params) when is_list(items) do
  # 处理多个项目
  results = Enum.map(items, &process_single_item/1)
  
  case Enum.all?(results, &match?({:ok, _}, &1)) do
    true -> {:ok, socket, "所有项目处理成功"}
    false -> {:error, socket, "部分项目处理失败"}
  end
end
```

### 3. 表单验证

```elixir
@impl Backpex.ResourceAction
def handle(socket, params) do
  case validate_params(params) do
    {:ok, valid_params} ->
      process_action(valid_params)
      {:ok, socket, "操作成功"}
    
    {:error, errors} ->
      {:error, socket, "参数验证失败: #{inspect(errors)}"}
  end
end
```
