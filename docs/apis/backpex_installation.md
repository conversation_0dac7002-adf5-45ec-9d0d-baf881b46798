# Backpex 安装指南

## 前置条件

在开始安装 Backpex 之前，请确保满足以下条件：

### 必需组件

1. **Phoenix LiveView**: Backpex 基于 Phoenix LiveView 构建
2. **Tailwind CSS**: 用于样式设计（需要版本 4）
3. **daisyUI**: UI 组件库（需要版本 5）
4. **Ecto**: 数据库层

### 数据库要求

⚠️ **重要**: Backpex 要求数据库模式中有单一主键字段，不支持复合键。

支持的主键类型：
- UUID (binary_id)
- Integer (bigserial)  
- String

主键用于 Show 和 Edit 视图的 URL 中，请确保它始终是 URL 编码的或可安全用于 URL。

## 安装步骤

### 1. 添加依赖

在 `mix.exs` 文件中添加 Backpex 依赖：

```elixir
defp deps do
  [
    {:backpex, "~> 0.13.0"}
  ]
end
```

### 2. 全局配置

在 `config.exs` 中设置 PubSub 服务器：

```elixir
config :backpex, :pubsub_server, MyApp.PubSub
```

### 3. 配置 JavaScript Hooks

Backpex 提供了一些 JS hooks，需要在 `app.js` 中包含：

```javascript
import { Hooks as BackpexHooks } from 'backpex';

const Hooks = [] // 你的应用程序 hooks（可选）

const liveSocket = new LiveSocket('/live', Socket, {
  params: { _csrf_token: csrfToken },
  hooks: {...Hooks, ...BackpexHooks }
})
```

### 4. 配置 Tailwind CSS

确保在样式表中添加 Backpex 文件作为 Tailwind 源：

```css
@source "../../deps/backpex/**/*.*ex";
@source '../../deps/backpex/assets/js/**/*.*js'
```

⚠️ **注意**: 路径可能因项目设置而异。

### 5. 设置格式化器

在 `.formatter.exs` 中添加 Backpex：

```elixir
[
  import_deps: [:backpex]
]
```

### 6. 配置 daisyUI

#### 添加主题

在样式表中添加主题配置：

```css
@plugin "daisyui" {
  themes: dark, cyberpunk;
}

@plugin "daisyui/theme" {
  name: "light";

  --color-primary: #1d4ed8;
  --color-primary-content: white;
  --color-secondary: #f39325;
  --color-secondary-content: white;
}
```

#### 移除冲突插件

移除 `@tailwindcss/forms` 插件以避免与 daisyUI 冲突：

```css
// 删除这一行
@plugin "tailwindcss/forms";
```

### 7. 配置 Heroicons

#### 添加依赖

在 `mix.exs` 中添加：

```elixir
def deps do
  [
    {:heroicons,
      github: "tailwindlabs/heroicons",
      tag: "v2.1.1",
      sparse: "optimized",
      app: false,
      compile: false,
      depth: 1}
  ]
end
```

#### 创建 Tailwind 插件

创建 `tailwind_heroicons.js` 文件：

```javascript
const plugin = require('tailwindcss/plugin')
const fs = require('fs')
const path = require('path')

module.exports = plugin(function ({ matchComponents, theme }) {
  const iconsDir = path.join(__dirname, '../../deps/heroicons/optimized')
  const values = {}
  const icons = [
    ['', '/24/outline'],
    ['-solid', '/24/solid'],
    ['-mini', '/20/solid'],
    ['-micro', '/16/solid']
  ]
  icons.forEach(([suffix, dir]) => {
    fs.readdirSync(path.join(iconsDir, dir)).forEach((file) => {
      const name = path.basename(file, '.svg') + suffix
      values[name] = { name, fullPath: path.join(iconsDir, dir, file) }
    })
  })
  matchComponents(
    {
      hero: ({ name, fullPath }) => {
        let content = fs
          .readFileSync(fullPath)
          .toString()
          .replace(/\r?\n|\r/g, '')
        content = encodeURIComponent(content)
        let size = theme('spacing.6')
        if (name.endsWith('-mini')) {
          size = theme('spacing.5')
        } else if (name.endsWith('-micro')) {
          size = theme('spacing.4')
        }
        return {
          [`--hero-${name}`]: `url('data:image/svg+xml;utf8,${content}')`,
          '-webkit-mask': `var(--hero-${name})`,
          mask: `var(--hero-${name})`,
          'mask-repeat': 'no-repeat',
          'background-color': 'currentColor',
          'vertical-align': 'middle',
          display: 'inline-block',
          width: size,
          height: size
        }
      }
    },
    { values }
  )
})
```

然后在样式表中引入：

```css
@plugin "./tailwind_heroicons.js";
```

## 创建示例资源

为了演示，我们创建一个简单的 `Post` 资源：

```bash
$ mix phx.gen.schema Blog.Post blog_posts title:string views:integer
$ mix ecto.migrate
```

## 创建布局

创建管理后台布局文件 `lib/myapp_web/templates/layout/admin.html.heex`：

```heex
<Backpex.HTML.Layout.app_shell fluid={@fluid?}>
  <:topbar>
    <Backpex.HTML.Layout.topbar_branding />

    <Backpex.HTML.Layout.topbar_dropdown class="mr-2 md:mr-0">
      <:label>
        <label tabindex="0" class="btn btn-square btn-ghost">
          <.icon name="hero-user" class="size-6" />
        </label>
      </:label>
      <li>
        <.link navigate={~p"/"} class="text-error flex justify-between hover:bg-base-200">
          <p>Logout</p>
          <.icon name="hero-arrow-right-on-rectangle" class="size-5" />
        </.link>
      </li>
    </Backpex.HTML.Layout.topbar_dropdown>
  </:topbar>
  <:sidebar>
    <Backpex.HTML.Layout.sidebar_item current_url={@current_url} navigate={~p"/admin/posts"}>
      <.icon name="hero-book-open" class="size-5" /> Posts
    </Backpex.HTML.Layout.sidebar_item>
  </:sidebar>
  <Backpex.HTML.Layout.flash_messages flash={@flash} />
  <%= @inner_content %>
</Backpex.HTML.Layout.app_shell>
```

## 配置 LiveResource

创建 LiveResource 模块：

```elixir
defmodule MyAppWeb.Live.PostLive do
  use Backpex.LiveResource,
    adapter_config: [
      schema: MyApp.Blog.Post,
      repo: MyApp.Repo,
      update_changeset: &MyApp.Blog.Post.update_changeset/3,
      create_changeset: &MyApp.Blog.Post.create_changeset/3
    ],
    layout: {MyAppWeb.Layouts, :admin}

  @impl Backpex.LiveResource
  def singular_name, do: "Post"

  @impl Backpex.LiveResource
  def plural_name, do: "Posts"

  @impl Backpex.LiveResource
  def fields do
    [
      title: %{
        module: Backpex.Fields.Text,
        label: "Title"
      },
      views: %{
        module: Backpex.Fields.Number,
        label: "Views"
      }
    ]
  end
end
```

## 配置路由

在 `router.ex` 中配置路由：

```elixir
import Backpex.Router

scope "/admin", MyAppWeb do
  pipe_through :browser

  # 添加 Backpex 路由（只需添加一次）
  backpex_routes()

  # 添加初始化 assigns 和 live session
  live_session :default, on_mount: Backpex.InitAssigns do
    # 添加 LiveResource 路由
    live_resources "/posts", PostLive
  end
end
```

### 配置默认路由

创建重定向控制器 `my_app_web/controllers/redirect_controller.ex`：

```elixir
defmodule MyAppWeb.RedirectController do
  use MyAppWeb, :controller

  def redirect_to_posts(conn, _params) do
    conn
    |> Phoenix.Controller.redirect(to: ~p"/admin/posts")
    |> Plug.Conn.halt()
  end
end
```

在路由中添加：

```elixir
scope "/admin", MyAppWeb do
  pipe_through :browser

  backpex_routes()

  # 添加默认路由
  get "/", RedirectController, :redirect_to_posts

  live_session :default, on_mount: Backpex.InitAssigns do
    live_resources "/posts", PostLive
  end
end
```

## 主题配置

### 设置主题选择器

1. 在 `root.html.heex` 中设置主题属性：

```heex
<html data-theme={assigns[:theme] || "light"}>
  ...
</html>
```

2. 添加主题选择器插件到路由管道：

```elixir
pipeline :browser do
  # ...
  plug Backpex.ThemeSelectorPlug
end
```

3. 在布局中添加主题选择器：

```heex
<Backpex.HTML.Layout.theme_selector
  socket={@socket}
  themes={[
    {"Light", "light"},
    {"Dark", "dark"},
    {"Cyberpunk", "cyberpunk"}
  ]}
/>
```

4. 在 `app.js` 中设置存储的主题：

```javascript
import { Hooks as BackpexHooks } from 'backpex';
// ...
BackpexHooks.BackpexThemeSelector.setStoredTheme()
```

## 移除默认背景色

如果你的 `root.html.heex` 中有默认背景色：

```heex
<body class="bg-white">
</body>
```

应该移除 `bg-white` 类以避免与 Backpex `app_shell` 的背景色冲突。

## 完成

现在你可以访问 `/admin/posts` 来查看你的第一个 Backpex LiveResource！
