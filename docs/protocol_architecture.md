# 协议处理架构文档 (Protocol Architecture Documentation)

## 概述 (Overview)

本项目已完成对协议处理架构的全面重构，实现了基于 Ash Framework 设计理念的统一、可扩展协议处理系统。重构遵循"数据模型为核心，声明即定义"的原则，创建了一个标准化、易维护的协议架构。

## 核心组件 (Core Components)

### 1. ProtocolBehaviour (`lib/teen/protocol/protocol_behaviour.ex`)

定义了所有协议处理器必须实现的标准接口:

```elixir
@callback protocol_id() :: integer()
@callback protocol_info() :: %{name: String.t(), description: String.t()}
@callback handle_protocol(sub_id :: integer(), data :: map(), context :: user_context()) :: protocol_result()
@callback supported_sub_protocols() :: [{sub_id :: integer(), description :: String.t()}]
@callback validate_data(sub_id :: integer(), data :: map()) :: :ok | {:error, reason :: term()}
```

### 2. ProtocolRouter (`lib/teen/protocol/protocol_router.ex`)

统一的协议路由分发中心，负责:
- 根据主协议ID将消息路由到对应的处理器
- 统一的错误处理和日志记录
- 性能监控和统计

### 3. ProtocolUtils (`lib/teen/protocol/protocol_utils.ex`)

提供通用的协议处理工具函数:
- 参数验证 (`validate_params/2`)
- 标准化响应格式 (`success_response/1`, `error_response/2`)
- 分页响应构建 (`build_pagination_response/4`)
- 统一日志记录 (`log_protocol/5`)

## 协议映射表 (Protocol Mapping)

| 协议ID | 模块名 | 描述 | 状态 |
|--------|--------|------|------|
| 0 | RegLoginProtocol | 注册登录 | ✅ |
| 1 | FindPasswordProtocol | 找回密码 | ✅ |
| 4 | GameProtocol | 游戏逻辑 | ✅ |
| 5 | XCProtocol | 子游戏服务器协议 | ✅ |
| 6 | BaseInfoProtocol | 基本信息 | ✅ |
| 7 | MoneyProtocol | 金钱钱包 | ✅ |
| 14 | MailProtocol | 邮件系统 | ✅ |
| 15 | NoticeProtocol | 公告系统 | ✅ |
| 34 | DBServerProtocol | 数据库服务器 | ✅ |
| 40 | RankProtocol | 排行榜 | ✅ |
| 42 | TaskProtocol | 活动任务 | ✅ |
| 101 | HallActivityProtocol | 大厅活动 | ✅ |

## 协议处理流程 (Protocol Processing Flow)

```
客户端请求 → WebSocketHandler → ProtocolRouter → 具体协议处理器 → 业务逻辑 → 响应返回
```

1. **WebSocketHandler** 接收协议消息
2. **ProtocolRouter** 根据主协议ID分发到相应处理器
3. **协议处理器** 验证数据并处理业务逻辑
4. **ProtocolUtils** 提供通用功能支持
5. 返回标准化响应

## 主要协议模块详解

### BaseInfoProtocol (协议ID: 6)
处理用户基本信息相关功能:
- 获取用户信息
- 头像上传
- 系统配置查询
- 用户设置管理

### MoneyProtocol (协议ID: 7)
处理财务操作:
- 余额查询
- 交易记录
- 提现操作
- 转账功能

### XCProtocol (协议ID: 5)
子游戏服务器通信:
- 游戏房间管理
- 游戏状态同步
- 玩家操作传递
- 断线重连机制

### DBServerProtocol (协议ID: 34)
数据库服务器操作:
- 数据同步
- 批量操作
- 健康检查
- 性能监控

### TaskProtocol (协议ID: 42)
活动任务系统:
- 任务列表管理
- 任务进度跟踪
- 奖励发放
- 任务分类

### RankProtocol (协议ID: 40)
排行榜系统:
- 各类排行榜查询
- 用户排名查询
- 历史记录
- 奖励领取

## 数据验证规则 (Data Validation Rules)

所有协议处理器使用统一的验证规则:

```elixir
ProtocolUtils.validate_params(data, [
  {:required, [:field_name]},                    # 必填字段
  {:string_length, :field, min, max, "描述"},     # 字符串长度
  {:number_range, :field, min, max, "描述"},      # 数字范围
  {:email, :email_field, "描述"},                 # 邮箱格式
  {:phone, :phone_field, "描述"}                  # 手机号格式
])
```

## 错误处理标准 (Error Handling Standards)

### 标准错误类型:
- `:invalid_params` - 参数验证失败
- `:unauthorized` - 未授权访问
- `:forbidden` - 权限不足
- `:not_found` - 资源不存在
- `:insufficient_balance` - 余额不足
- `:internal_error` - 系统内部错误

### 响应格式:
```elixir
# 成功响应
%{
  "success" => true,
  "data" => %{...},
  "timestamp" => 1625097600000
}

# 错误响应
%{
  "success" => false,
  "error" => "error_code",
  "message" => "错误描述",
  "timestamp" => 1625097600000
}
```

## 性能优化 (Performance Optimization)

1. **协议路由缓存**: 编译时生成映射表，避免运行时查找
2. **参数验证优化**: 提前返回，减少不必要的处理
3. **日志异步处理**: 避免日志记录影响主流程性能
4. **错误恢复机制**: 单个协议处理器异常不影响整体系统

## 扩展指南 (Extension Guide)

### 添加新协议处理器:

1. **创建协议模块**:
```elixir
defmodule Teen.Protocol.NewProtocol do
  @behaviour Teen.Protocol.ProtocolBehaviour
  
  @protocol_id 99
  
  @impl true
  def protocol_id, do: @protocol_id
  
  @impl true
  def protocol_info do
    %{name: "New", description: "新协议处理器"}
  end
  
  # 实现其他必需的回调函数...
end
```

2. **注册到路由器**:
```elixir
# 在 protocol_router.ex 中添加
@protocol_handlers %{
  # 现有协议...
  99 => Teen.Protocol.NewProtocol
}
```

### 最佳实践:

1. **遵循命名规范**: 协议处理器使用 `*Protocol` 后缀
2. **完整的错误处理**: 每个处理函数都要有完整的错误处理
3. **参数验证**: 使用 `ProtocolUtils.validate_params/2` 进行统一验证
4. **日志记录**: 使用 `ProtocolUtils.log_protocol/5` 记录关键操作
5. **响应格式统一**: 使用 `ProtocolUtils.success_response/1` 和 `error_response/2`

## 测试策略 (Testing Strategy)

### 单元测试:
- 每个协议处理器独立测试
- 参数验证测试
- 错误处理测试

### 集成测试:
- 协议路由测试
- 端到端协议处理测试
- 性能基准测试

### 测试工具:
```bash
# 运行协议相关测试
mix test test/teen/protocol/

# 性能测试
mix test --only performance
```

## 监控与维护 (Monitoring & Maintenance)

### 关键指标:
- 协议处理延迟
- 错误率统计
- 并发处理能力
- 内存使用情况

### 日志分析:
```bash
# 查看协议处理日志
grep "PROTOCOL_ROUTER" logs/app.log

# 错误统计
grep "协议处理失败" logs/app.log | wc -l
```

## 总结 (Summary)

新的协议处理架构具有以下优势:

1. **统一标准**: 所有协议遵循相同的接口和处理模式
2. **高度可扩展**: 添加新协议只需实现标准接口
3. **易于维护**: 清晰的代码结构和标准化错误处理
4. **性能优化**: 编译时优化和运行时监控
5. **测试友好**: 模块化设计便于单元测试和集成测试

这个重构为系统的长期发展奠定了坚实的基础，提高了代码质量和开发效率。