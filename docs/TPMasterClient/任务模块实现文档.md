# TPMasterClient 任务模块实现文档

## 目录
1. [概述](#概述)
2. [任务系统架构](#任务系统架构)
3. [任务类型分类](#任务类型分类)
4. [游戏内任务系统](#游戏内任务系统)
5. [大厅任务系统](#大厅任务系统)
6. [通信协议](#通信协议)
7. [数据模型](#数据模型)
8. [实现细节](#实现细节)
9. [最佳实践](#最佳实践)

## 概述

TPMasterClient的任务系统是提升用户活跃度和留存的重要功能。系统分为两大类：游戏内任务和大厅任务。游戏内任务主要引导玩家进行游戏对局，大厅任务则涵盖充值、社交、提现等多种行为。

## 任务系统架构

### 整体架构设计

```
任务系统
├── 游戏内任务
│   ├── TeenPatti任务系统
│   ├── TPAK47任务系统
│   └── TPPotBlind任务系统
└── 大厅任务
    ├── 免费奖励任务（邀请、赢分、提现）
    ├── 七日签到任务
    ├── 刮刮卡任务
    └── 免费现金任务
```

### 文件组织结构

```
TPMasterClient/
├── assets/
│   ├── games/                      # 游戏内任务
│   │   ├── teenpatti/src/view/
│   │   │   ├── TPTaskNode.ts       # 任务节点UI
│   │   │   └── TPTaskRewardLayer.ts # 任务奖励界面
│   │   ├── tpak47/src/view/
│   │   │   ├── TPAKTaskNode.ts
│   │   │   └── TPAKTaskRewardLayer.ts
│   │   └── tppotblind/src/view/
│   │       ├── TPPBTaskNode.ts
│   │       └── TPPBTaskRewardLayer.ts
│   └── script/hall/popup/          # 大厅任务
│       ├── HallActivityFreePopup.ts # 免费奖励任务
│       ├── HallSignDayPopup.ts     # 七日签到任务
│       └── HallFreeCashPopup.ts    # 免费现金任务
```

## 任务类型分类

### 1. 按完成条件分类

| 任务类型 | 说明 | 示例 |
|---------|------|------|
| 游戏对局任务 | 完成指定局数游戏 | 完成5局TeenPatti |
| 游戏胜利任务 | 赢得指定局数 | 赢得3局游戏 |
| 充值任务 | 充值达到指定金额 | 充值100卢比 |
| 邀请任务 | 邀请指定数量好友 | 邀请3位好友 |
| 赢分任务 | 游戏中赢得指定分数 | 赢得10000分 |
| 提现任务 | 完成指定次数提现 | 完成1次提现 |
| 登录任务 | 连续登录天数 | 连续登录7天 |

### 2. 按奖励类型分类

| 奖励类型 | 说明 | 使用场景 |
|---------|------|---------|
| 金币奖励 | 游戏币奖励 | 游戏内任务 |
| 现金奖励 | 可提现金额 | 大厅活动任务 |
| VIP经验 | 提升VIP等级 | VIP相关任务 |
| 道具奖励 | 游戏道具 | 特殊活动任务 |

## 游戏内任务系统

### 1. TeenPatti任务系统详解

#### 任务节点组件（TPTaskNode.ts）

**主要功能**：
- 显示任务进度和状态
- 处理任务交互
- 管理任务动画效果

**核心实现**：

```typescript
export default class TPTaskNode extends cc.Component {
    // 任务状态管理
    private _btnStatus = 1;  // 1:显示按钮 2:完成状态 3:奖励状态 4:无状态
    private _completed = false;  // 任务是否完成
    
    // 初始化方法
    start() {
        this.callGameTask();  // 获取任务信息
        this.playAddAnima();  // 播放入场动画
    }
    
    // 更新任务显示
    updateTask(info: any) {
        // 更新进度条
        let progress = info.currentnumber / info.limitnumber;
        this.nodeProgress.getComponent(cc.ProgressBar).progress = progress;
        
        // 更新进度文本
        let taskText = info.tasktype == 0 ? "Play" : "Win";
        this.lblProgress.string = `${taskText} ${info.currentnumber}/${info.limitnumber}`;
        
        // 检查完成状态
        if (info.currentnumber >= info.limitnumber) {
            this.setCompleted();
        }
    }
}
```

#### 任务奖励界面（TPTaskRewardLayer.ts）

**翻牌机制实现**：

```typescript
export default class TPTaskRewardLayer extends cc.Component {
    private _fetchid: number = 0;
    private _fetching: boolean = false;
    
    // 显示奖励界面
    show(fetchid: number) {
        this._fetchid = fetchid;
        this.node.active = true;
        this.playEnterAnimation();
    }
    
    // 点击卡牌领取奖励
    onClickCard(event: cc.Event.EventTouch, index: string) {
        if (this._fetching) return;
        
        this._fetching = true;
        this._selectIndex = Number(index);
        
        // 发送领取请求
        HallManager.instance.callFetchGameTask(this._fetchid);
    }
    
    // 处理领取响应
    onFetchResponse(data: any) {
        // 翻开选中的卡牌
        this.flipCard(this._selectIndex, data.fetchaward);
        
        // 1秒后翻开其他卡牌
        this.scheduleOnce(() => {
            this.flipAllCards(data.awardlist);
        }, 1);
    }
}
```

### 2. 任务数据流程

```
游戏开始 → 请求任务信息 → 显示任务进度 → 游戏进行中 → 
游戏结算 → 更新任务进度 → 任务完成 → 显示完成动画 → 
点击领取 → 显示翻牌界面 → 选择卡牌 → 领取奖励 → 
刷新任务 → 显示新任务
```

## 大厅任务系统

### 1. 免费奖励任务（HallActivityFreePopup）

**任务类型定义**：

```typescript
enum FreeBonusType {
    TypeInvite = 1,      // 邀请任务
    TypeWinTp = 2,       // 游戏赢分任务
    TypewithDrawal = 3   // 提现任务
}
```

**任务管理实现**：

```typescript
// 创建任务列表
private createTaskList(data: BonusTaskInfo) {
    // 邀请任务
    if (data.sharecount > 0) {
        this.addTaskItem({
            type: FreeBonusType.TypeInvite,
            title: `邀请${data.sharecount}位好友`,
            progress: `${data.currentshare}/${data.sharecount}`,
            completed: data.currentshare >= data.sharecount
        });
    }
    
    // 赢分任务
    if (data.gamewinning > 0) {
        this.addTaskItem({
            type: FreeBonusType.TypeWinTp,
            title: `赢得${data.gamewinning}`,
            progress: `${data.currentwinning}/${data.gamewinning}`,
            completed: data.currentwinning >= data.gamewinning
        });
    }
    
    // 提现任务
    if (data.exchangecount > 0) {
        this.addTaskItem({
            type: FreeBonusType.TypewithDrawal,
            title: `完成${data.exchangecount}次提现`,
            progress: `${data.currentexchange}/${data.exchangecount}`,
            completed: data.currentexchange >= data.exchangecount
        });
    }
}
```

### 2. 七日签到任务（HallSignDayPopup）

**签到逻辑实现**：

```typescript
// 更新签到状态
private updateSignStatus(signlist: SignDayItem[]) {
    for (let i = 0; i < signlist.length; i++) {
        let dayInfo = signlist[i];
        let dayNode = this.LayerItems[`day${i + 1}`];
        
        // 更新充值进度
        let progress = dayInfo.currentcharge / dayInfo.chargeamount;
        dayNode.progressBar.progress = Math.min(progress, 1);
        
        // 更新领取状态
        if (dayInfo.fetchaward == 1) {
            // 已领取
            dayNode.btnClaim.active = false;
            dayNode.imgClaimed.active = true;
        } else if (dayInfo.currentcharge >= dayInfo.chargeamount) {
            // 可领取
            dayNode.btnClaim.active = true;
            this.playClaimableAnimation(dayNode);
        } else {
            // 未达成
            dayNode.btnClaim.active = false;
            dayNode.lblProgress.string = `${dayInfo.currentcharge}/${dayInfo.chargeamount}`;
        }
    }
}
```

## 通信协议

### 游戏内任务协议

```typescript
// TeenPatti任务协议
namespace HallActivity {
    // 请求游戏任务信息
    export const CS_GAME_TASK_P = 16;
    export const SC_GAME_TASK_P = 17;
    
    // 领取游戏任务奖励
    export const CS_FETCH_GAME_TASK_AWARD_P = 18;
    export const SC_FETCH_GAME_TASK_AWARD_P = 19;
}
```

### 大厅任务协议

```typescript
// 免费奖励任务
HallActivity.CS_GET_FREE_BONUS_P = 10;
HallActivity.SC_GET_FREE_BONUS_P = 11;

// 七日签到任务
HallActivity.CS_SEVEN_DAY_LOGIN_P = 2;
HallActivity.SC_SEVEN_DAY_LOGIN_P = 3;
HallActivity.CS_FETCH_SEVEN_DAYAWARD_P = 4;
HallActivity.SC_FETCH_SEVEN_DAYAWARD_P = 5;
```

## 数据模型

### 1. 游戏任务数据模型

```typescript
// 任务信息
interface GameTaskInfo {
    tasktype: number;        // 任务类型 0:游戏局数 1:胜利局数
    currentnumber: number;   // 当前进度
    limitnumber: number;     // 目标数量
    totalAward: number;      // 任务奖励金额
    fetchid: number;        // 领取ID
}

// 领取响应
interface FetchTaskResponse {
    fetchaward: number;     // 实际获得的奖励
    fetchid: number;        // 领取ID
    awardlist: {           // 所有可能的奖励（翻牌用）
        [key: string]: {
            award: number;
            used?: boolean;
        }
    }
}
```

### 2. 大厅任务数据模型

```typescript
// 免费奖励任务
interface BonusTaskInfo {
    awardbonus: number;      // 奖励总金额
    sharecount: number;      // 需要分享次数
    currentshare: number;    // 当前分享次数
    exchangecount: number;   // 需要提现次数
    currentexchange: number; // 当前提现次数
    gameid: number;         // 游戏ID
    gamewinning: number;    // 需要赢分金额
    currentwinning: number; // 当前赢分金额
}

// 七日签到任务
interface SignDayItem {
    daynumber: number;       // 天数（1-7）
    awardcash: number;       // 奖励金额
    chargeamount: number;    // 充值要求
    currentcharge: number;   // 当前充值
    fetchaward: number;      // 是否已领取
    fetchid: string;        // 领取ID
}
```

## 实现细节

### 1. 任务进度更新机制

```typescript
// 实时更新任务进度
class TaskProgressManager {
    // 游戏结算时更新
    onGameSettle(result: GameResult) {
        // 更新游戏局数
        this.updatePlayCount();
        
        // 如果胜利，更新胜利局数
        if (result.isWin) {
            this.updateWinCount();
        }
        
        // 更新赢分任务
        if (result.winScore > 0) {
            this.updateWinScore(result.winScore);
        }
        
        // 通知UI更新
        EventManager.emit(GameEvent.TASK_PROGRESS_UPDATE);
    }
}
```

### 2. 任务完成动画

```typescript
// 任务完成时的动画效果
private playCompletedAnimation() {
    // 播放完成特效
    let animNode = cc.instantiate(this.completedEffectPrefab);
    this.node.addChild(animNode);
    
    // 震动反馈
    if (cc.sys.platform === cc.sys.ANDROID) {
        jsb.reflection.callStaticMethod(
            "org/cocos2dx/javascript/AppActivity",
            "vibrate",
            "(I)V",
            200
        );
    }
    
    // 自动展开任务详情
    this.expandTaskDetail();
    
    // 3秒后自动收起
    this.scheduleOnce(() => {
        this.collapseTaskDetail();
    }, 3);
}
```

### 3. 任务跳转逻辑

```typescript
// 根据任务类型跳转到对应功能
private onTaskItemClick(taskType: number) {
    switch (taskType) {
        case FreeBonusType.TypeInvite:
            // 跳转到邀请界面
            PopUpManager.instance.addPopUp(PopUpNames.HallInviteFriends);
            break;
            
        case FreeBonusType.TypeWinTp:
            // 跳转到指定游戏
            let gameId = DataManager.instance.bonusTaskInfo.gameid;
            GameManager.instance.enterGame(gameId);
            break;
            
        case FreeBonusType.TypewithDrawal:
            // 跳转到提现界面
            PopUpManager.instance.addPopUp(PopUpNames.HallWithdraw);
            break;
    }
}
```

### 4. 防作弊机制

```typescript
// 任务完成验证
class TaskValidator {
    // 服务端验证任务进度
    validateProgress(taskId: number, progress: number): boolean {
        // 检查时间间隔
        if (!this.checkTimeInterval(taskId)) {
            return false;
        }
        
        // 检查进度合理性
        if (!this.checkProgressReasonable(taskId, progress)) {
            return false;
        }
        
        // 检查用户行为
        if (!this.checkUserBehavior(taskId)) {
            return false;
        }
        
        return true;
    }
}
```

## 最佳实践

### 1. 任务设计原则

1. **渐进式难度**：任务难度应该循序渐进，避免新手任务过难
2. **即时反馈**：任务进度变化时立即更新UI，给用户正反馈
3. **合理奖励**：奖励应该与任务难度匹配，保持用户动力
4. **防沉迷考虑**：设置每日任务上限，避免用户过度游戏

### 2. 技术实现建议

```typescript
// 1. 使用事件驱动更新任务
EventManager.on(GameEvent.TASK_PROGRESS_CHANGE, this.updateTaskUI, this);

// 2. 任务数据缓存
class TaskCache {
    private cache: Map<number, TaskInfo> = new Map();
    
    getTask(taskId: number): TaskInfo {
        if (!this.cache.has(taskId)) {
            this.cache.set(taskId, this.fetchTaskFromServer(taskId));
        }
        return this.cache.get(taskId);
    }
}

// 3. 批量更新优化
class TaskBatchUpdater {
    private pendingUpdates: TaskUpdate[] = [];
    
    addUpdate(update: TaskUpdate) {
        this.pendingUpdates.push(update);
        this.scheduleFlush();
    }
    
    private flush() {
        if (this.pendingUpdates.length > 0) {
            this.sendBatchUpdate(this.pendingUpdates);
            this.pendingUpdates = [];
        }
    }
}
```

### 3. 用户体验优化

1. **新手引导**：首次完成任务时显示引导
2. **红点提示**：有可领取奖励时显示红点
3. **推送提醒**：任务即将过期时推送提醒
4. **一键领取**：支持批量领取多个任务奖励

### 4. 性能优化

```typescript
// 1. 任务列表虚拟滚动
class VirtualTaskList extends cc.Component {
    private itemHeight = 100;
    private visibleCount = 5;
    
    updateView(scrollOffset: number) {
        let startIndex = Math.floor(scrollOffset / this.itemHeight);
        let endIndex = startIndex + this.visibleCount;
        
        // 只渲染可见区域的任务项
        this.renderTasks(startIndex, endIndex);
    }
}

// 2. 任务图标延迟加载
class TaskIconLoader {
    loadIcon(taskId: number, callback: (icon: cc.SpriteFrame) => void) {
        // 优先从缓存加载
        if (this.iconCache.has(taskId)) {
            callback(this.iconCache.get(taskId));
            return;
        }
        
        // 异步加载
        this.scheduleOnce(() => {
            cc.loader.loadRes(`icons/task_${taskId}`, cc.SpriteFrame, (err, icon) => {
                if (!err) {
                    this.iconCache.set(taskId, icon);
                    callback(icon);
                }
            });
        }, 0.1);
    }
}
```

## 扩展开发

### 添加新任务类型

1. **定义任务类型枚举**
```typescript
enum TaskType {
    // ... 现有类型
    NEW_TYPE = 10  // 新任务类型
}
```

2. **实现任务逻辑**
```typescript
class NewTaskHandler implements ITaskHandler {
    canComplete(progress: any): boolean {
        // 实现完成条件判断
    }
    
    updateProgress(event: any): void {
        // 实现进度更新逻辑
    }
    
    getReward(): TaskReward {
        // 返回任务奖励
    }
}
```

3. **注册任务处理器**
```typescript
TaskManager.registerHandler(TaskType.NEW_TYPE, new NewTaskHandler());
```

4. **添加UI组件**
```typescript
// 继承基础任务UI组件
class NewTaskUI extends BaseTaskUI {
    updateDisplay(taskInfo: TaskInfo): void {
        // 实现特定的显示逻辑
    }
}
```

通过模块化的设计，任务系统可以方便地扩展新的任务类型，同时保持代码的整洁和可维护性。