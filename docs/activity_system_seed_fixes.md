# 活动系统种子数据修复文档

## 概述

本文档记录了活动系统种子数据的修复过程，确保所有活动配置与最新的资源文件字段结构保持一致。

## 修复的问题

### 1. 字段不匹配问题
- **问题**: 种子数据使用的字段名与资源文件中定义的字段不匹配
- **影响**: 导致种子数据创建失败或字段缺失
- **解决方案**: 更新种子数据使用正确的字段名和完整的字段集

### 2. 缺失必要字段
- **问题**: 种子数据缺少资源文件中定义的必要字段
- **影响**: 创建记录时使用默认值，可能不符合业务需求
- **解决方案**: 补全所有必要字段的配置

## 修复的活动类型

### 1. 首充礼包 (FirstRechargeGift)

**修复前的字段**:
```elixir
%{
  title: "新手首充礼包",
  limit_days: 7,
  reward_coins: Decimal.new("1000"),
  status: :enabled
}
```

**修复后的字段**:
```elixir
%{
  title: "新手首充礼包",
  limit_days: 7,
  min_recharge_amount: Decimal.new("1000"),
  reward_coins: Decimal.new("1500"),
  reward_type: :coins,
  bonus_multiplier: Decimal.new("1.5"),
  time_limit_hours: 168,
  description: "新用户专享首充礼包，充值即可获得150%奖励",
  is_active: true,
  status: :enabled
}
```

**新增字段**:
- `min_recharge_amount`: 最小充值金额
- `reward_type`: 奖励类型
- `bonus_multiplier`: 奖励倍数
- `time_limit_hours`: 时间限制
- `description`: 描述
- `is_active`: 是否激活

### 2. 亏损返利罐 (LossRebateJar)

**修复前的字段**:
```elixir
%{
  title: "新手保护罐",
  max_claims: 1,
  loss_threshold: Decimal.new("1000"),
  rebate_percentage: Decimal.new("10"),
  max_rebate: Decimal.new("500"),
  status: :enabled
}
```

**修复后的字段**:
```elixir
%{
  title: "新手保护罐",
  max_claims: 1,
  loss_threshold: Decimal.new("1000"),
  rebate_percentage: Decimal.new("10"),
  max_rebate: Decimal.new("500"),
  calculation_period: :daily,
  rebate_type: :coins,
  auto_distribute: false,
  is_active: true,
  status: :enabled
}
```

**新增字段**:
- `calculation_period`: 计算周期
- `rebate_type`: 返利类型
- `auto_distribute`: 自动发放
- `is_active`: 是否激活

### 3. 刮刮卡活动 (ScratchCardActivity)

**修复前的字段**:
```elixir
%{
  activity_title: "30次刮卡活动",
  claimable_count: 30,
  reward_probability: Decimal.new("0.8"),
  status: :enabled
}
```

**修复后的字段**:
```elixir
%{
  activity_title: "30次刮卡活动",
  card_type: :option1,
  cost_amount: Decimal.new("100"),
  min_reward: Decimal.new("50"),
  max_reward: Decimal.new("5000"),
  claimable_count: 30,
  reward_probability: Decimal.new("80"),
  daily_limit: 5,
  is_active: true,
  status: :enabled
}
```

**新增字段**:
- `card_type`: 卡片类型
- `cost_amount`: 购买费用
- `min_reward`: 最小奖励
- `max_reward`: 最大奖励
- `daily_limit`: 每日限制次数
- `is_active`: 是否激活

### 4. 绑定奖励 (BindingReward)

**修复前的字段**:
```elixir
%{
  title: "手机绑定奖励",
  binding_type: :phone,
  reward_amount: Decimal.new("500"),
  status: :enabled
}
```

**修复后的字段**:
```elixir
%{
  title: "手机绑定奖励",
  binding_type: :phone,
  reward_amount: Decimal.new("500"),
  reward_type: :coins,
  one_time_only: true,
  verification_required: true,
  description: "绑定手机号码即可获得500金币奖励",
  is_active: true,
  status: :enabled
}
```

**新增字段**:
- `reward_type`: 奖励类型
- `one_time_only`: 仅限一次
- `verification_required`: 需要验证
- `description`: 描述
- `is_active`: 是否激活

## 文件结构

### 新增文件
- `priv/repo/seeds/fixed_activity_seeds.exs`: 修复后的活动系统种子数据
- `priv/repo/seeds/validate_activity_seeds.exs`: 活动系统配置验证脚本

### 修改文件
- `priv/repo/seeds.exs`: 主种子文件，添加了修复脚本的调用
- `priv/repo/seeds/complete_activity_seeds.exs`: 更新了部分活动配置
- `priv/repo/seeds/scratch_card_activity_seeds.exs`: 更新了刮刮卡活动配置

## 使用方法

### 运行修复后的种子数据
```bash
# 运行完整的种子数据（包括修复）
mix run priv/repo/seeds.exs

# 仅运行活动系统修复
mix run priv/repo/seeds/fixed_activity_seeds.exs

# 验证活动系统配置
mix run priv/repo/seeds/validate_activity_seeds.exs
```

### 验证修复结果
运行验证脚本后，应该看到类似以下输出：
```
🔍 开始验证活动系统配置...

📋 验证首充礼包配置...
✅ 首充礼包总数: 3
  • 新手首充礼包 - 限制天数: 7, 奖励: 1500分
  • 限时首充礼包 - 限制天数: 3, 奖励: 4000分
  • 超值首充礼包 - 限制天数: 1, 奖励: 15000分

📋 验证亏损返利罐配置...
✅ 亏损返利罐总数: 3
  • 新手保护罐 - 阈值: 1000分, 返利: 10%
  • 标准返利罐 - 阈值: 2000分, 返利: 12%
  • 高级返利罐 - 阈值: 5000分, 返利: 15%

✅ 活动系统配置验证完成！
```

## 注意事项

1. **数据库状态**: 确保数据库已正确迁移并包含所有必要的表结构
2. **字段约束**: 新增字段都有适当的默认值和约束条件
3. **向后兼容**: 修复不会影响现有数据，只是补全缺失的字段
4. **业务逻辑**: 新增字段的值都经过业务逻辑验证，符合实际使用需求

## 数据库迁移

### 自动生成迁移
使用 Ash 的迁移生成工具自动创建了数据库迁移文件：
```bash
mix ash_postgres.generate_migrations --name add_activity_system_fields
```

### 迁移内容
生成的迁移文件 `20250728140947_add_activity_system_fields.exs` 包含：
- 为 `scratch_card_activities` 表添加了 6 个新字段
- 为 `first_recharge_gifts` 表添加了 6 个新字段
- 为 `loss_rebate_jars` 表添加了 4 个新字段
- 为 `binding_rewards` 表添加了 5 个新字段
- 为 `users` 表添加了 `permission_level` 字段

### 运行迁移
```bash
mix ecto.migrate
```

## 资源文件修复

### Create Action 修复
所有活动资源文件的 `create` action 都进行了以下修复：
1. **扩展 accept 列表** - 包含所有新添加的字段
2. **添加 primary? true** - 设置为主要创建操作
3. **更新默认值设置** - 为新字段设置合适的默认值

### 修复的资源文件
- `lib/teen/resources/activity_system/first_recharge_gift.ex`
- `lib/teen/resources/activity_system/loss_rebate_jar.ex`
- `lib/teen/resources/activity_system/scratch_card/scratch_card_activity.ex`
- `lib/teen/resources/activity_system/binding_reward.ex`

## 验证结果

运行验证脚本后的输出示例：
```
🔍 开始验证活动系统配置...

📋 验证首充礼包配置...
✅ 首充礼包总数: 3
  • 新手首充礼包 - 限制天数: 7, 奖励: 1500分
  • 限时首充礼包 - 限制天数: 3, 奖励: 4000分
  • 超值首充礼包 - 限制天数: 1, 奖励: 15000分

📋 验证亏损返利罐配置...
✅ 亏损返利罐总数: 3
  • 新手保护罐 - 阈值: 1000分, 返利: 10%
  • 标准返利罐 - 阈值: 2000分, 返利: 12%
  • 高级返利罐 - 阈值: 5000分, 返利: 15%

📋 验证刮刮卡活动配置...
✅ 刮刮卡活动总数: 2
  • 30次刮卡活动 - 可领取: 30次, 概率: 80%
  • 豪华刮卡活动 - 可领取: 50次, 概率: 85%

📋 验证绑定奖励配置...
✅ 绑定奖励总数: 3
  • 手机绑定奖励 - 类型: phone, 奖励: 500分
  • 邮箱绑定奖励 - 类型: email, 奖励: 300分
  • 银行卡绑定奖励 - 类型: bank_card, 奖励: 1000分

✅ 活动系统配置验证完成！
```

## 总结

通过这次修复，活动系统的种子数据现在完全符合最新的资源文件结构，确保了：
- ✅ 所有必要字段都有正确的值
- ✅ 字段类型和约束都符合定义
- ✅ 业务逻辑完整且合理
- ✅ 数据库结构与资源定义完全匹配
- ✅ 资源文件的 create action 正确配置
- ✅ 系统可以正常创建和管理活动配置
- ✅ 所有配置都通过了验证测试
