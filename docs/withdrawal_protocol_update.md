# MoneyProtocol 提现接口更新说明

## 概述

`handle_withdrawal_exchange` 函数已更新为使用新的基于 Reactor 的提现流程。这确保了积分流转的原子性和数据一致性。

## 主要变更

### 1. 简化的处理流程

**旧流程**：
- 在协议处理函数中直接操作数据库
- 手动管理事务
- 手动扣除积分
- 复杂的错误处理

**新流程**：
- 调用 `Teen.Services.WithdrawalService.create_withdrawal/2`
- 所有复杂逻辑由 Reactor 处理
- 自动化的积分扣除和回滚机制
- 统一的错误处理

### 2. 代码对比

#### 旧代码（180行）：
```elixir
defp handle_withdrawal_exchange(user_id, data) do
  # 开始数据库事务
  Cypridina.Repo.transaction(fn ->
    # 1. 验证用户余额
    # 2. 验证银行配置
    # 3. 验证提现限额
    # 4. 计算手续费
    # 5. 创建提现记录
    # 6. 冻结用户余额
    # 7. 记录交易日志
  end)
  |> case do
    # 处理各种错误情况
  end
end
```

#### 新代码（100行）：
```elixir
defp handle_withdrawal_exchange(user_id, data) do
  # 构建参数
  withdrawal_params = %{
    withdrawal_amount: Decimal.new(amount),
    payment_method: "bank_card",
    bank_info: bank_info,
    ip_address: Map.get(data, "ip_address", "127.0.0.1")
  }

  # 调用服务
  case Teen.Services.WithdrawalService.create_withdrawal(user_id, withdrawal_params) do
    {:ok, withdrawal_record} -> # 成功响应
    {:error, reason} -> # 错误响应
  end
end
```

### 3. 功能改进

1. **自动积分扣除**：创建提现时自动扣除积分到待审核账户
2. **统一错误处理**：服务层返回标准化的错误信息
3. **更好的可维护性**：业务逻辑集中在服务层和 Reactor 中
4. **原子性保证**：使用 Reactor 确保操作的原子性

### 4. 错误响应映射

新的错误响应更加精确：

| 错误类型 | 响应代码 | 说明 |
|---------|---------|------|
| "余额不足" | :insufficient_balance | 用户余额不足 |
| "流水未达标" | :forbidden | 用户流水未达到提现要求 |
| "用户状态异常" | :forbidden | 用户账号状态异常 |
| "用户支付功能被限制" | :forbidden | 用户被限制提现功能 |
| 其他错误 | :internal_error | 系统内部错误 |

## 客户端影响

**无影响**。接口响应格式保持不变：

```json
{
  "code": 0,
  "data": {
    "orderId": "WD**********",
    "amount": "1000.00",
    "finalAmount": "950.00",
    "feeAmount": "50.00",
    "bankName": "ICICI",
    "bankAccountName": "John Doe",
    "bankAccountNumber": "**********",
    "bankIfsc": "ICIC0001234",
    "auditStatus": 0,
    "progressStatus": 0,
    "createdAt": **********,
    "msg": "提现申请已提交，请等待审核"
  }
}
```

## 新流程优势

1. **数据一致性**：Reactor 保证积分和提现记录的一致性
2. **自动回滚**：失败时自动处理，无需手动回滚
3. **更少的代码**：减少了 80 行代码，提高可维护性
4. **统一的业务逻辑**：所有提现相关逻辑集中管理
5. **更好的测试性**：服务层更容易测试

## 注意事项

1. 银行配置验证现在由 `CreateWithdrawalReactor` 内部处理
2. 手续费计算逻辑已集成到 Reactor 中
3. 流水验证通过 `TurnoverService` 统一处理
4. 所有积分操作通过 `Cypridina.Ledger` 保证原子性

## 相关文件

- `/app/cypridina/lib/teen/services/withdrawal_service.ex` - 提现服务层
- `/app/cypridina/lib/teen/resources/payment_system/reactors/create_withdrawal_reactor.ex` - 创建提现 Reactor
- `/app/cypridina/docs/withdrawal_workflow_usage.md` - 完整的使用指南