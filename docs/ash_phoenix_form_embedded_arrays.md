# AshPhoenix.Form 处理嵌入式数组的示例代码

基于项目中的代码分析，以下是处理嵌入式数组（embedded arrays）的主要方法和示例。

## 1. 资源定义

首先，需要定义嵌入式资源和使用它的属性：

```elixir
# 定义嵌入式资源
defmodule Teen.Reward do
  use Ash.Resource,
    data_layer: :embedded

  attributes do
    attribute :type, :atom do
      allow_nil? false
      constraints [one_of: [:coins, :items, :experience]]
    end

    attribute :amount, :integer do
      allow_nil? false
      default 0
    end

    attribute :description, :string
    attribute :metadata, :map, default: %{}
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      primary? true
      accept [:type, :amount, :description, :metadata]
    end

    update :update do
      primary? true
      accept [:type, :amount, :description, :metadata]
    end
  end
end

# 在主资源中使用嵌入式数组
defmodule Teen.ActivitySystem.Cdkey do
  use Ash.Resource,
    data_layer: AshPostgres.DataLayer

  attributes do
    # 定义嵌入式数组属性
    attribute :rewards, {:array, Teen.Reward} do
      allow_nil? false
      public? true
      description "奖励配置列表"
      default []
    end
  end
end
```

## 2. 在 LiveView 中使用 inputs_for

### 基本用法（来自 Teen.Live.Fields.AshRewardsField）

```elixir
<.inputs_for :let={f_reward} field={@form[@name]}>
  <div class="mb-3 flex items-start gap-x-4">
    <div class="grow">
      <p :if={f_reward.index == 0} class="mb-1 text-xs">
        奖励类型
      </p>
      <BackpexForm.input
        type="select"
        field={f_reward[:type]}
        options={[
          {"金币", :coins},
          {"积分", :points},
          {"道具", :items}
        ]}
      />
    </div>
    
    <div class="grow">
      <p :if={f_reward.index == 0} class="mb-1 text-xs">
        奖励数量
      </p>
      <BackpexForm.input
        type="number"
        field={f_reward[:amount]}
      />
    </div>
    
    <div class="grow">
      <p :if={f_reward.index == 0} class="mb-1 text-xs">
        描述
      </p>
      <BackpexForm.input
        type="text"
        field={f_reward[:description]}
        placeholder="奖励描述（可选）"
      />
    </div>
    
    <div class={if f_reward.index == 0, do: "mt-5", else: nil}>
      <label>
        <input
          type="checkbox"
          name="change[rewards_sort][]"
          value={f_reward.index}
          class="sr-only"
          checked
        />
        <div class="btn btn-outline btn-error" aria-label="删除">
          <Backpex.HTML.CoreComponents.icon name="hero-trash" class="h-5 w-5" />
        </div>
      </label>
    </div>
  </div>
</.inputs_for>

<!-- 添加隐藏字段来处理排序和删除 -->
<input type="hidden" name="change[rewards_sort][]" />
<input type="hidden" name="change[rewards_drop][]" />
```

### LiveView 事件处理（来自 Teen.Live.ActivitySystem.CdkeyCinderLive）

```elixir
# 添加嵌入项
def handle_event("add_reward", _params, socket) do
  current_rewards = Map.get(socket.assigns.form.params, "rewards", [])
  new_rewards = current_rewards ++ [%{"type" => "coins", "amount" => "0", "description" => ""}]
  
  params = Map.put(socket.assigns.form.params, "rewards", new_rewards)
  form = AshPhoenix.Form.validate(socket.assigns.form, params)
  
  {:noreply, assign(socket, :form, form)}
end

# 删除嵌入项
def handle_event("remove_reward", %{"index" => index}, socket) do
  index = String.to_integer(index)
  current_rewards = Map.get(socket.assigns.form.params, "rewards", [])
  new_rewards = List.delete_at(current_rewards, index)
  
  params = Map.put(socket.assigns.form.params, "rewards", new_rewards)
  form = AshPhoenix.Form.validate(socket.assigns.form, params)
  
  {:noreply, assign(socket, :form, form)}
end
```

## 3. FormData 协议实现（来自 ash_backpex）

处理嵌入式数组时，FormData 协议需要正确识别并处理嵌入属性：

```elixir
defimpl Phoenix.HTML.FormData, for: Ash.Changeset do
  def to_form(source, form, field, opts) do
    case find_inputs_for_type!(source, field) do
      {:one, _cast, module} ->
        [
          AshPhoenix.Form.for_create(
            module,
            :create,
            Keyword.merge(opts,
              as: form.name <> "[#{field}]",
              id: form.id <> "_#{field}"
            )
          )
        ]

      {:many, _cast, module} ->
        existing_data = get_relationship_data(source, field)

        Enum.with_index(existing_data || [], fn item, index ->
          AshPhoenix.Form.for_update(
            item,
            :update,
            Keyword.merge(opts,
              as: form.name <> "[#{field}][#{index}]",
              id: form.id <> "_#{field}_#{index}"
            )
          )
        end)
    end
  end

  defp find_inputs_for_type!(changeset, field) do
    resource = changeset.resource
    attribute = Ash.Resource.Info.attribute(resource, field)

    case attribute do
      %{type: {:array, module}} when is_atom(module) ->
        # 处理 embeds_many 类型
        if function_exported?(module, :__ash_resource__, 0) do
          {:many, nil, module}
        else
          raise ArgumentError,
                "field #{inspect(field)} array type #{inspect(module)} is not an Ash resource"
        end

      %{type: module} when is_atom(module) ->
        # 处理 embeds_one 类型
        if function_exported?(module, :__ash_resource__, 0) do
          {:one, nil, module}
        else
          raise ArgumentError,
                "field #{inspect(field)} type #{inspect(module)} is not an Ash resource"
        end
    end
  end
end
```

## 4. 创建和更新表单

### 创建新记录的表单

```elixir
form = 
  Teen.ActivitySystem.Cdkey
  |> AshPhoenix.Form.for_create(:create,
    actor: socket.assigns.current_user
  )
  |> AshPhoenix.Form.to_form()
```

### 更新现有记录的表单

```elixir
form = 
  cdkey
  |> AshPhoenix.Form.for_update(:update,
    actor: socket.assigns.current_user
  )
  |> AshPhoenix.Form.to_form()
```

## 5. 表单验证和提交

```elixir
def handle_event("validate_cdkey", %{"cdkey" => cdkey_params}, socket) do
  form = AshPhoenix.Form.validate(socket.assigns.form, cdkey_params)
  {:noreply, assign(socket, :form, form)}
end

def handle_event("save_cdkey", %{"cdkey" => cdkey_params}, socket) do
  form = AshPhoenix.Form.validate(socket.assigns.form, cdkey_params)
  
  case AshPhoenix.Form.submit(form) do
    {:ok, _cdkey} ->
      {:noreply,
       socket
       |> put_flash(:info, "CDKEY保存成功")
       |> assign(:show_form, false)
       |> assign(:form, nil)}

    {:error, form} ->
      {:noreply, assign(socket, :form, form)}
  end
end
```

## 6. 使用 sort 和 drop 处理动态添加/删除

如 `Teen.Live.Fields.AshRewardsField` 所示，可以使用 `rewards_sort` 和 `rewards_drop` 隐藏字段来处理嵌入项的动态管理：

- `rewards_sort[]`: 用于跟踪哪些项应该保留
- `rewards_drop[]`: 用于标记哪些项应该删除

这种方法允许使用标准的 HTML 表单提交来管理嵌入式数组，而不需要 JavaScript。

## 注意事项

1. **确保嵌入式资源定义了必要的 actions**（至少 `:create` 和 `:update`）
2. **在表单中使用 `.inputs_for` 来处理嵌入式资源**
3. **处理添加/删除操作时，需要更新整个参数并重新验证表单**
4. **FormData 协议实现是关键**，确保正确识别嵌入式属性类型

## 相关文件

- `/app/cypridina/lib/teen/live/fields/ash_rewards_field.ex` - 简单的嵌入式数组字段实现
- `/app/cypridina/lib/teen/live/fields/ash_embed_field.ex` - 更通用的嵌入式字段实现
- `/app/cypridina/lib/ash_backpex/ash_backpex/ash_changeset_to_phoenix_form.ex` - FormData 协议实现
- `/app/cypridina/lib/teen/live/activity_system/cdkey_cinder_live.ex` - 完整的 LiveView 示例