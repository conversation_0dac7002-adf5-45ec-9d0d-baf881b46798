# 金币比例1:1改造评估报告

## 一、当前系统现状

### 1.1 当前比例机制
- **存储比例**: 1元 = 100分（服务端存储）
- **显示比例**: 服务端值 ÷ 100 = 客户端显示值
- **核心函数**: `ProtocolUtils.format_money/1` 负责转换

### 1.2 涉及范围
- **货币单位**: XAA（内部货币代码）
- **市场定位**: 印度市场（INR卢比，1卢比=100派萨）
- **存储方式**: 所有金额以最小单位（分/派萨）存储为整数

## 二、改造影响评估

### 2.1 核心影响模块

#### 1. **协议层 (Protocol Layer)**
- `lib/teen/protocol/protocol_utils.ex` - format_money函数
- `lib/teen/protocol/money_protocol.ex` - 所有金额相关协议
- 所有使用 `ProtocolUtils.format_money` 的协议文件

#### 2. **账本系统 (Ledger System)**
- `lib/cypridina/ledger/` - 整个账本系统
- 余额存储和计算逻辑
- 转账和交易记录

#### 3. **支付系统 (Payment System)**
- `lib/teen/resources/payment_system/` - 充值、提现记录
- `lib/teen/payment_system/gateways/` - 支付网关集成
- 所有支付相关的Reactor

#### 4. **游戏系统 (Game System)**
- 所有游戏房间的下注、结算逻辑
- 游戏配置中的金额设置
- 机器人初始金额配置

#### 5. **活动系统 (Activity System)**
- 奖励金额配置
- 任务奖励计算
- VIP等级金额门槛

### 2.2 具体改造点

#### 1. **协议转换函数**
```elixir
# 当前实现
def format_money(amount_in_cents) do
  Float.round(amount / 100, 2)
end

# 1:1改造后
def format_money(amount) do
  amount  # 直接返回，无需转换
end
```

#### 2. **支付网关**
- 需要修改所有支付网关的金额转换逻辑
- 特别是 `format_amount_in_cents` 函数

#### 3. **数据库存储**
- 所有现有金额数据需要 ÷ 100
- 涉及表：recharge_records, withdrawal_records, 所有游戏记录等

#### 4. **配置文件**
- 游戏房间配置的最小/最大下注
- VIP等级门槛
- 活动奖励金额

## 三、改造难度评估

### 3.1 技术难度：⭐⭐⭐⭐ (高)

**原因**：
1. **影响范围广**: 涉及几乎所有核心模块
2. **数据迁移复杂**: 需要处理历史数据
3. **兼容性问题**: 可能需要支持新旧客户端并存

### 3.2 风险评估：⭐⭐⭐⭐⭐ (极高)

**主要风险**：
1. **数据一致性风险**: 迁移过程中可能造成金额错误
2. **业务中断风险**: 需要停服维护
3. **客户端兼容**: 需要强制更新所有客户端
4. **第三方集成**: 支付网关可能需要重新对接

### 3.3 工作量评估

| 任务 | 预估工时 | 说明 |
|------|---------|------|
| 代码改造 | 80-120小时 | 修改所有涉及金额转换的代码 |
| 数据迁移脚本 | 40-60小时 | 编写和测试数据迁移脚本 |
| 测试验证 | 120-160小时 | 全面的功能和数据测试 |
| 客户端适配 | 40-80小时 | 客户端代码修改和测试 |
| 上线部署 | 16-24小时 | 分步骤上线和监控 |
| **总计** | **296-444小时** | 约2-3个月 |

## 四、改造方案建议

### 4.1 渐进式改造（推荐）

1. **第一阶段**：新增兼容层
   - 在协议层增加版本标识
   - 根据客户端版本返回不同比例的数据
   - 服务端内部保持100:1存储

2. **第二阶段**：客户端迁移
   - 发布新版本客户端支持1:1
   - 通过配置控制新老版本行为
   - 逐步引导用户升级

3. **第三阶段**：数据迁移
   - 在业务低峰期执行数据迁移
   - 分批次迁移，确保数据准确性
   - 保留回滚方案

### 4.2 一次性改造（不推荐）

- 停服维护8-12小时
- 一次性完成所有改造
- 风险极高，不建议采用

## 五、替代方案

### 5.1 仅客户端显示调整
- **方案**：客户端接收数据后不再÷100
- **优点**：改动最小，风险低
- **缺点**：需要更新所有客户端

### 5.2 保持现状
- **理由**：
  1. 当前方案是金融系统标准做法
  2. 避免浮点数精度问题
  3. 改造成本高，收益有限

## 六、结论与建议

### 结论
将系统改为1:1金币比例的改造难度为**高**，主要挑战在于：
1. 影响范围涵盖所有核心模块
2. 数据迁移风险高
3. 需要客户端配合更新
4. 改造周期长（2-3个月）

### 建议
1. **短期**：保持现有100:1的比例，这是金融系统的标准做法
2. **如必须改造**：采用渐进式方案，降低风险
3. **优先考虑**：仅在客户端调整显示，避免服务端大改
4. **成本效益**：改造成本高但业务收益有限，建议维持现状

### 关键决策点
- 是否所有客户端都能强制更新？
- 是否能接受2-3个月的改造周期？
- 是否有充足的测试资源？
- 历史数据的准确性要求如何？

---
*评估日期：2025-01-16*
*评估人：AI Assistant*