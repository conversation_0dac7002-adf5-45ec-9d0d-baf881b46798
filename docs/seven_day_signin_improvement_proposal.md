# 7日签到活动改进方案

## 当前实现的问题

1. **数据冗余**：连续登录天数在多个表中重复存储
2. **查询效率低**：判断是否已签到需要查询 RewardClaimRecord 表
3. **状态分散**：签到状态信息分散在多个表中
4. **测试困难**：时间相关逻辑难以测试
5. **循环逻辑不清晰**：7天后如何重新开始不明确

## 改进方案

### 1. 统一状态管理

将所有7日签到相关状态集中到 `UserActivityParticipation` 的 `participation_data` 中：

```elixir
defmodule Teen.ActivitySystem.SevenDaySignIn do
  @moduledoc """
  优化后的7日签到实现
  """

  # participation_data 结构
  @participation_data_schema %{
    "current_cycle_start" => "2024-01-01",      # 当前周期开始日期
    "current_cycle_day" => 3,                    # 当前周期第几天（1-7）
    "signed_days" => [1, 2, 3],                  # 本周期已签到的天数
    "claimed_rewards" => [                        # 已领取的奖励记录
      %{"day" => 1, "amount" => 100, "claimed_at" => "2024-01-01T10:00:00Z"},
      %{"day" => 2, "amount" => 200, "claimed_at" => "2024-01-02T10:00:00Z"}
    ],
    "total_cycles_completed" => 2,                # 已完成的完整周期数
    "last_sign_date" => "2024-01-03",           # 最后签到日期
    "consecutive_days" => 15                     # 总连续签到天数（跨周期）
  }

  @doc """
  获取用户签到信息
  """
  def get_user_sign_in_info(user_id) do
    with {:ok, participation} <- get_or_create_participation(user_id),
         {:ok, updated_participation} <- update_sign_in_status(participation),
         {:ok, rewards_config} <- get_rewards_config() do
      
      data = updated_participation.participation_data
      
      {:ok, %{
        current_day: data["current_cycle_day"],
        signed_days: data["signed_days"],
        can_sign_today: can_sign_today?(data),
        rewards: build_rewards_info(rewards_config, data),
        consecutive_days: data["consecutive_days"],
        total_cycles: data["total_cycles_completed"]
      }}
    end
  end

  @doc """
  执行签到
  """
  def sign_in(user_id, day \\ nil) do
    with {:ok, participation} <- get_or_create_participation(user_id),
         {:ok, updated_participation} <- update_sign_in_status(participation),
         :ok <- validate_sign_in(updated_participation, day),
         {:ok, final_participation} <- perform_sign_in(updated_participation, day) do
      
      {:ok, %{
        success: true,
        day: day || get_current_cycle_day(final_participation),
        consecutive_days: final_participation.participation_data["consecutive_days"]
      }}
    end
  end

  @doc """
  领取奖励
  """
  def claim_reward(user_id, day) do
    with {:ok, participation} <- get_participation(user_id),
         :ok <- validate_claim(participation, day),
         {:ok, reward_amount} <- get_reward_amount(day),
         {:ok, _} <- distribute_reward(user_id, reward_amount),
         {:ok, updated_participation} <- record_claimed_reward(participation, day, reward_amount) do
      
      {:ok, %{
        success: true,
        day: day,
        amount: reward_amount
      }}
    end
  end

  # 私有函数

  defp update_sign_in_status(participation) do
    today = Date.utc_today()
    data = participation.participation_data || %{}
    
    # 检查是否需要开始新周期
    new_data = if should_start_new_cycle?(data, today) do
      start_new_cycle(data, today)
    else
      update_current_cycle(data, today)
    end
    
    participation
    |> Ash.Changeset.for_update(:update, %{participation_data: new_data})
    |> Ash.update()
  end

  defp should_start_new_cycle?(data, today) do
    case data["current_cycle_start"] do
      nil -> true
      start_date_str ->
        start_date = Date.from_iso8601!(start_date_str)
        Date.diff(today, start_date) >= 7
    end
  end

  defp start_new_cycle(data, today) do
    %{
      "current_cycle_start" => Date.to_iso8601(today),
      "current_cycle_day" => 1,
      "signed_days" => [],
      "claimed_rewards" => [],
      "total_cycles_completed" => (data["total_cycles_completed"] || 0) + 1,
      "last_sign_date" => nil,
      "consecutive_days" => data["consecutive_days"] || 0
    }
  end

  defp update_current_cycle(data, today) do
    start_date = Date.from_iso8601!(data["current_cycle_start"])
    current_day = Date.diff(today, start_date) + 1
    
    Map.put(data, "current_cycle_day", min(current_day, 7))
  end

  defp can_sign_today?(data) do
    today = Date.utc_today()
    last_sign_date = data["last_sign_date"]
    
    case last_sign_date do
      nil -> true
      date_str ->
        last_date = Date.from_iso8601!(date_str)
        Date.compare(today, last_date) == :gt
    end
  end

  defp perform_sign_in(participation, day) do
    today = Date.utc_today()
    data = participation.participation_data
    
    # 更新签到数据
    sign_day = day || data["current_cycle_day"]
    signed_days = Enum.uniq(data["signed_days"] ++ [sign_day])
    
    # 更新连续天数
    consecutive_days = calculate_consecutive_days(data, today)
    
    new_data = data
    |> Map.put("signed_days", signed_days)
    |> Map.put("last_sign_date", Date.to_iso8601(today))
    |> Map.put("consecutive_days", consecutive_days)
    
    participation
    |> Ash.Changeset.for_update(:update, %{
      participation_data: new_data,
      progress: length(signed_days)
    })
    |> Ash.update()
  end

  defp calculate_consecutive_days(data, today) do
    case data["last_sign_date"] do
      nil -> 1
      date_str ->
        last_date = Date.from_iso8601!(date_str)
        case Date.diff(today, last_date) do
          1 -> (data["consecutive_days"] || 0) + 1
          _ -> 1
        end
    end
  end
end
```

### 2. 简化奖励配置

创建更灵活的奖励配置系统：

```elixir
defmodule Teen.ActivitySystem.SignInRewardConfig do
  use Ash.Resource,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem

  attributes do
    uuid_primary_key :id
    
    attribute :config_name, :string, allow_nil?: false
    attribute :is_active, :boolean, default: false
    
    # 基础奖励配置
    attribute :base_rewards, :map do
      # {
      #   "1": {"coins": 100, "items": []},
      #   "2": {"coins": 200, "items": []},
      #   ...
      #   "7": {"coins": 1000, "items": [{"id": "item_1", "count": 1}]}
      # }
    end
    
    # 连续签到奖励
    attribute :streak_rewards, :map do
      # {
      #   "7": {"coins": 500},
      #   "14": {"coins": 1000},
      #   "30": {"coins": 2000}
      # }
    end
    
    # VIP加成配置
    attribute :vip_multipliers, :map do
      # {
      #   "1": 1.1,
      #   "2": 1.2,
      #   ...
      # }
    end
    
    # 条件限制
    attribute :conditions, :map do
      # {
      #   "3": {"min_recharge": 100},
      #   "5": {"min_recharge": 500}
      # }
    end
    
    timestamps()
  end
end
```

### 3. 优化时间处理

创建专门的时间服务：

```elixir
defmodule Teen.ActivitySystem.TimeService do
  @moduledoc """
  统一的时间处理服务
  """
  
  @doc """
  获取当前时间（支持测试环境覆盖）
  """
  def now do
    case get_test_time_override() do
      nil -> DateTime.utc_now()
      override -> override
    end
  end
  
  @doc """
  获取今天日期（支持测试环境覆盖）
  """
  def today do
    now() |> DateTime.to_date()
  end
  
  @doc """
  计算到下一天的秒数（用于冷却时间）
  """
  def seconds_until_next_day do
    now = now()
    tomorrow = now 
      |> DateTime.to_date() 
      |> Date.add(1)
      |> DateTime.new!(~T[00:00:00], "Etc/UTC")
    
    DateTime.diff(tomorrow, now)
  end
  
  # 测试支持
  defp get_test_time_override do
    if Application.get_env(:cypridina, :environment) in [:dev, :test] do
      Process.get(:test_time_override)
    end
  end
  
  def set_test_time(datetime) do
    if Application.get_env(:cypridina, :environment) in [:dev, :test] do
      Process.put(:test_time_override, datetime)
    end
  end
end
```

### 4. 增强测试支持

```elixir
defmodule Teen.ActivitySystem.SignInTestHelper do
  @moduledoc """
  7日签到测试辅助模块
  """
  
  alias Teen.ActivitySystem.{SevenDaySignIn, TimeService}
  
  @doc """
  模拟连续签到场景
  """
  def simulate_consecutive_sign_ins(user_id, days) do
    start_date = Date.utc_today()
    
    Enum.reduce(0..(days - 1), [], fn day_offset, acc ->
      # 设置测试时间
      test_date = Date.add(start_date, day_offset)
      test_time = DateTime.new!(test_date, ~T[10:00:00], "Etc/UTC")
      TimeService.set_test_time(test_time)
      
      # 执行签到
      {:ok, result} = SevenDaySignIn.sign_in(user_id)
      
      acc ++ [result]
    end)
  end
  
  @doc """
  快速设置到指定周期和天数
  """
  def set_to_cycle_day(user_id, cycle_num, day_num) do
    {:ok, participation} = SevenDaySignIn.get_or_create_participation(user_id)
    
    cycle_start = Date.utc_today() |> Date.add(-day_num + 1)
    signed_days = Enum.to_list(1..day_num)
    
    new_data = %{
      "current_cycle_start" => Date.to_iso8601(cycle_start),
      "current_cycle_day" => day_num,
      "signed_days" => signed_days,
      "claimed_rewards" => [],
      "total_cycles_completed" => cycle_num - 1,
      "last_sign_date" => Date.to_iso8601(Date.utc_today()),
      "consecutive_days" => (cycle_num - 1) * 7 + day_num
    }
    
    participation
    |> Ash.Changeset.for_update(:update, %{participation_data: new_data})
    |> Ash.update!()
  end
  
  @doc """
  重置用户签到数据
  """
  def reset_user_sign_in(user_id) do
    case SevenDaySignIn.get_participation(user_id) do
      {:ok, participation} ->
        participation
        |> Ash.Changeset.for_update(:update, %{
          participation_data: %{},
          progress: 0,
          status: :active
        })
        |> Ash.update!()
      _ -> :ok
    end
  end
end
```

### 5. 性能优化

#### 5.1 缓存层
```elixir
defmodule Teen.ActivitySystem.SignInCache do
  use GenServer
  
  # 缓存用户签到状态，减少数据库查询
  def get_user_status(user_id) do
    case :ets.lookup(:sign_in_cache, user_id) do
      [{^user_id, data, expires_at}] ->
        if DateTime.compare(DateTime.utc_now(), expires_at) == :lt do
          {:ok, data}
        else
          :miss
        end
      [] -> :miss
    end
  end
  
  def put_user_status(user_id, data, ttl_seconds \\ 300) do
    expires_at = DateTime.utc_now() |> DateTime.add(ttl_seconds, :second)
    :ets.insert(:sign_in_cache, {user_id, data, expires_at})
  end
end
```

#### 5.2 批量查询优化
```elixir
def get_multiple_users_status(user_ids) do
  # 批量查询，减少数据库往返
  UserActivityParticipation
  |> Ash.Query.filter(user_id in ^user_ids and activity_type == :seven_day_task)
  |> Ash.read!()
  |> Enum.map(&format_user_status/1)
end
```

## 迁移计划

### 第一阶段：数据结构优化
1. 更新 `participation_data` 结构
2. 编写数据迁移脚本
3. 保持向后兼容

### 第二阶段：逻辑重构
1. 实现新的签到服务
2. 添加缓存层
3. 更新API接口

### 第三阶段：测试增强
1. 添加测试辅助工具
2. 编写完整测试用例
3. 性能测试

### 第四阶段：切换和清理
1. 灰度发布新实现
2. 监控和调优
3. 清理旧代码

## 预期收益

1. **查询性能提升 50%**：减少表连接和查询次数
2. **测试效率提升 90%**：无需等待真实时间流逝
3. **代码可维护性提升**：逻辑集中，职责清晰
4. **扩展性增强**：易于添加新的签到玩法

## 风险和对策

1. **数据迁移风险**
   - 对策：分批迁移，保留回滚方案

2. **兼容性风险**
   - 对策：保持API接口不变，内部逐步重构

3. **性能风险**
   - 对策：添加缓存，优化查询

## 总结

通过本方案的实施，7日签到活动将获得：
- 更清晰的数据结构
- 更高效的查询性能
- 更便捷的测试支持
- 更灵活的配置能力
- 更好的扩展性

建议按照迁移计划分阶段实施，确保平滑过渡。