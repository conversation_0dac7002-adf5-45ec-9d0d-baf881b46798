# IndiaGameServer 活动模块技术文档

## 目录
- [1. 系统概述](#1-系统概述)
- [2. 活动类型详解](#2-活动类型详解)
- [3. 技术架构](#3-技术架构)
- [4. 数据库设计](#4-数据库设计)
- [5. 通信协议](#5-通信协议)
- [6. 核心业务流程](#6-核心业务流程)
- [7. 活动配置管理](#7-活动配置管理)
- [8. 日志与监控](#8-日志与监控)

## 1. 系统概述

IndiaGameServer的活动系统是一个综合性的用户激励和留存体系，通过多样化的活动类型来提升用户参与度和平台收益。该系统采用Node.js + SQL Server的技术架构，实现了14种不同类型的活动模块。

### 1.1 技术栈
- **服务端语言**: Node.js
- **数据库**: SQL Server
- **通信协议**: 自定义二进制协议
- **日志系统**: 自定义日志管理器

### 1.2 系统特点
- **模块化设计**: 每个活动独立封装，便于维护和扩展
- **存储过程驱动**: 核心业务逻辑封装在数据库存储过程中
- **统一消息处理**: 所有活动使用相同的消息处理框架
- **安全机制**: IP记录、操作日志、防刷限制

## 2. 活动类型详解

### 2.1 破产补助活动 (Broke Award)
**功能描述**: 当玩家资金低于一定阈值时，可领取破产补助
```javascript
// 文件: fetchBrokeAward.js
// 存储过程: PrPs_FetchBrokeAward
// 参数:
// - isfetch: 0获取状态，1领取奖励
// 返回:
// - fetchcount: 已领取次数
// - awardcash: 可领取金额
```

### 2.2 周卡月卡活动 (Card Task)
**功能描述**: 购买周卡或月卡后，每日可领取对应奖励
```javascript
// 获取活动: getCardTask.js -> PrPs_GetCardTask
// 领取奖励: fetchCardTaskAward.js -> PrPs_FetchCardTaskAward
// 数据结构:
// - cardtype: 卡片类型(周卡/月卡)
// - awardcash: 每日奖励金额
// - remaindays: 剩余天数
```

### 2.3 CDKEY兑换活动 (CDKEY Award)
**功能描述**: 使用兑换码领取奖励
```javascript
// 文件: fetchCdkeyAward.js
// 存储过程: PrPs_FetchCdkeyAward
// 参数: cdkey (兑换码)
```

### 2.4 免费积分活动 (Free Bonus)
**功能描述**: 通过分享、游戏等行为获取免费积分
```javascript
// 数据结构:
bonustask = {
    awardbonus: number,      // 奖励总金额
    sharecount: number,      // 分享次数
    currentshare: number,    // 当前分享次数
    exchangecount: number,   // 全民推广提现次数
    currentexchange: number, // 当前提现次数
    gameid: number,         // 游戏ID
    gamewinning: number,    // 游戏赢分
    currentwinning: number  // 当前游戏赢分金额
}
```

### 2.5 免费提现活动 (Free Cash)
**功能描述**: 免费提现奖励，包含邀请推广机制
```javascript
// 主活动: getFreeCash.js / fetchFreeCashAward.js
// 邀请活动: getFreeCashInvitation.js
// 参数: page, pagesize (用于邀请列表分页)
```

### 2.6 游戏任务活动 (Game Task)
**功能描述**: 完成指定游戏任务获得奖励
```javascript
// 任务结构:
gametask = {
    taskid: number,         // 任务ID
    gameid: number,         // 游戏ID
    tasktarget: number,     // 目标值
    currentprogress: number,// 当前进度
    awardcash: number,      // 奖励金币
    awardbonus: number      // 奖励积分
}
```

### 2.7 登录现金奖励活动 (Login Cash Award)
**功能描述**: 多种触发方式的现金奖励系统
```javascript
// 奖励类型 (fetchtype):
// 0: 登录奖励
// 1: 充值奖励
// 2: 游戏局数奖励
// 3: 转盘奖励
// 4: 游戏赢分奖励
// 10: 完成所有任务奖励
```

### 2.8 七日签到活动 (Seven Days Award)
**功能描述**: 连续七天签到领取递增奖励
```javascript
// 数据结构:
sevendata = {
    fetchid: number,        // 领取ID
    daynumber: number,      // 天数
    chargeamount: number,   // 充值金额要求(0为无限制)
    currentcharge: number,  // 已充值金额
    awardcash: number,      // 奖励金额
    fetchaward: number,     // 已领取金额(0未领取)
    fetchday: date         // 领取时间
}
```

### 2.9 30次卡活动 (Thirty Card)
**功能描述**: 完成30次卡片游戏后领取奖励
```javascript
// 文件: getThirtyCard.js / fetchThirtyCardAward.js
// 统计玩家的卡片游戏次数，达标后可领取奖励
```

### 2.10 VIP礼包活动 (VIP Gift)
**功能描述**: VIP等级对应的日/周/月礼包
```javascript
// 领取类型 (fetchtype):
// 0: 日奖励
// 1: 周奖励
// 2: 月奖励

// VIP数据结构:
viptask = {
    viplevel: number,       // VIP等级
    dayaward: number,       // 每日奖励金额
    weekaward: number,      // 每周奖励金额
    monthaward: number,     // 每月奖励金额
    minamount: number,      // 等级最小充值额度
    maxamount: number       // 等级最大充值额度
}
```

### 2.11 礼包充值活动 (Gift Charge)
**功能描述**: 七日礼包充值活动，充值后每日可领取奖励

### 2.12 邮件奖励 (Mail Award)
**功能描述**: 通过邮件系统发放的奖励
```javascript
// 参数: fetchid
// -1: 领取并已读所有邮件奖励
// 其他: 指定邮件ID
```

### 2.13 任务系统 (Task System)
**功能描述**: 综合任务管理系统，包含比赛、进度追踪等
```javascript
// 相关文件:
// - taskProcess.js: 任务进度
// - taskReceive.js: 任务领取
// - taskMatchList.js: 任务比赛列表
// - taskMatchRankRewardList.js: 排名奖励
// - taskMatchToDayList.js: 今日任务
```

## 3. 技术架构

### 3.1 文件组织结构
```
IndiaGameServer/
├── DBServer/
│   ├── service/
│   │   └── com/              # 活动处理器目录
│   │       ├── fetch*.js     # 领取奖励类处理器
│   │       └── get*.js       # 获取数据类处理器
│   ├── config/
│   │   └── sysconfig.js      # 系统配置和协议定义
│   └── util/
│       ├── logManager.js     # 日志管理
│       └── util.js           # 工具函数
```

### 3.2 消息处理流程
```javascript
// 标准处理器模板
function handler() {
    this.dbname = "CenterDB";  // 数据库名称
}

// 协议匹配
handler.prototype.ishook = function (cid, sid) {
    return cid == config.msgCode.emFunction_HallActivity && 
           sid == config.msgCode.CS_XXX_P;
}

// 业务处理
handler.prototype.exec = function (msg, conn, callback) {
    // 1. 参数提取
    var userid = data.__playerid;
    var ip = data.ip;
    
    // 2. 调用存储过程
    request.execute("PrPs_XXX", function (err, recordsets, returnValue) {
        // 3. 结果处理
        // 4. 回调返回
    });
}
```

### 3.3 安全机制
1. **IP地址记录**: 所有领取操作记录用户IP
2. **防刷限制**: 存储过程内置防刷逻辑
3. **操作日志**: 详细的操作日志记录
4. **参数验证**: 服务端严格验证输入参数

## 4. 数据库设计

### 4.1 核心表结构（推测）
```sql
-- 活动配置表
ActivityConfig (
    ActivityID int,          -- 活动ID
    ActivityType varchar,    -- 活动类型
    StartTime datetime,      -- 开始时间
    EndTime datetime,        -- 结束时间
    ConfigData text         -- JSON配置数据
)

-- 用户活动参与记录
UserActivityRecord (
    UserID int,             -- 用户ID
    ActivityID int,         -- 活动ID
    FetchCount int,         -- 领取次数
    TotalAward bigint,      -- 总奖励
    LastFetchTime datetime, -- 最后领取时间
    ExtData text           -- 扩展数据
)

-- 活动奖励日志
ActivityRewardLog (
    LogID bigint,          -- 日志ID
    UserID int,            -- 用户ID
    ActivityID int,        -- 活动ID
    RewardType int,        -- 奖励类型
    RewardAmount bigint,   -- 奖励数量
    IPAddress varchar,     -- IP地址
    CreateTime datetime    -- 创建时间
)
```

### 4.2 存储过程规范
- 命名规范: `PrPs_` 前缀
- 返回值: 1成功，其他失败
- 输出参数: 使用OUTPUT参数返回结果
- 错误信息: chvErrMsg参数返回错误描述

## 5. 通信协议

### 5.1 协议定义
```javascript
// 一级协议
emFunction_HallActivity: 101  // 大厅活动

// 二级协议示例
CS_GET_VIP_GIFT_P: 26       // 获取VIP活动
SC_GET_VIP_GIFT_P: 27       // 返回VIP活动
CS_FETCH_VIP_GIFT_P: 28     // 领取VIP奖励
SC_FETCH_VIP_GIFT_P: 29     // 返回VIP奖励
```

### 5.2 消息格式
```javascript
// 请求消息
{
    cid: number,      // 一级协议
    sid: number,      // 二级协议
    data: {
        __playerid: number,  // 玩家ID
        __cer: string,       // 认证信息
        // 其他业务参数
    }
}

// 响应消息
{
    cid: number,
    sid: number,
    pack: {
        code: number,    // 0成功，1失败
        msg: string,     // 错误信息
        // 业务数据
    }
}
```

## 6. 核心业务流程

### 6.1 活动领取通用流程
```
1. 客户端发起领取请求
   ↓
2. 服务端验证用户身份
   ↓
3. 检查活动有效性
   ↓
4. 验证领取条件
   ↓
5. 调用存储过程处理
   ↓
6. 更新用户数据
   ↓
7. 记录操作日志
   ↓
8. 返回领取结果
```

### 6.2 活动状态查询流程
```
1. 客户端请求活动数据
   ↓
2. 查询活动配置
   ↓
3. 获取用户参与记录
   ↓
4. 计算可领取状态
   ↓
5. 组装返回数据
```

## 7. 活动配置管理

### 7.1 配置方式
- 数据库配置表
- 存储过程内置规则
- 系统配置文件(sysconfig.js)

### 7.2 配置更新机制
- 热更新: 部分配置支持不重启更新
- 定时任务: 自动检查活动有效期
- 版本控制: 配置变更记录

## 8. 日志与监控

### 8.1 日志级别
```javascript
log.logdebug()  // 调试信息
log.loginfo()   // 普通信息
log.logwarn()   // 警告信息
log.logerror()  // 错误信息
log.logfatal()  // 致命错误
```

### 8.2 监控指标
- 活动参与率
- 奖励发放总额
- 异常请求统计
- 性能指标(响应时间、并发数)

## 9. 最佳实践

### 9.1 开发规范
1. 所有活动处理器继承标准handler模板
2. 严格遵循命名规范
3. 完善的错误处理和日志记录
4. 参数验证前置

### 9.2 性能优化
1. 存储过程优化索引
2. 缓存热点数据
3. 异步处理非关键业务
4. 批量操作减少数据库访问

### 9.3 安全建议
1. 所有操作记录IP地址
2. 设置合理的领取限制
3. 敏感操作二次验证
4. 定期审计活动数据

## 10. 扩展说明

活动系统采用高度模块化的设计，新增活动类型只需：
1. 创建对应的处理器文件
2. 定义协议号
3. 实现存储过程
4. 注册到消息处理系统

这种设计保证了系统的可扩展性和维护性，同时也便于团队协作开发。