# ash_backpex 使用文档

## 概述

ash_backpex 是一个将 Ash Framework 资源与 Backpex 管理界面集成的库。它提供了一个优雅的 DSL，允许你通过简单的配置就能为 Ash 资源创建功能完整的管理界面。

## 版本说明

⚠️ **注意**: ash_backpex 目前处于 0.0.1 版本，是一个早期实现。虽然基本功能已经可用，但仍有一些限制：
- 授权功能尚未完全集成
- 某些高级功能可能不稳定

## 快速开始

### 1. 添加依赖

在 `mix.exs` 中添加：

```elixir
def deps do
  [
    {:ash_backpex, "~> 0.0.1"}
  ]
end
```

### 2. 创建 LiveResource

```elixir
defmodule MyAppWeb.Live.Admin.PostLive do
  use AshBackpex.LiveResource

  backpex do
    # 必需：指定 Ash 资源
    resource MyApp.Blog.Post
    
    # 必需：指定布局
    layout({MyAppWeb.Layouts, :admin})
    
    # 可选：预加载关联
    load [:author, :comments]
    
    # 可选：字段配置
    fields do
      field :title
      field :content
      field :published_at
      field :author do
        display_field(:name)
        live_resource(MyAppWeb.Live.Admin.AuthorLive)
      end
    end
    
    # 可选：过滤器配置
    filters do
      filter :status do
        module MyAppWeb.Live.Admin.Filters.PostStatusFilter
      end
    end
  end

  # 必需：定义单数名称
  @impl Backpex.LiveResource
  def singular_name, do: "文章"

  # 必需：定义复数名称
  @impl Backpex.LiveResource
  def plural_name, do: "文章管理"
end
```

## DSL 详解

### backpex 配置块

#### 基础配置

```elixir
backpex do
  # 必需：Ash 资源
  resource MyApp.Resource
  
  # 必需：布局配置
  layout({MyAppWeb.Layouts, :admin})
  
  # 可选：预加载关联
  load [:relation1, :relation2]
  
  # 可选：是否流式布局
  fluid?(true)
  
  # 可选：指定 CRUD 操作的 action
  create_action :custom_create  # 默认使用主 create action
  read_action :custom_read      # 默认使用主 read action
  update_action :custom_update  # 默认使用主 update action
  destroy_action :custom_destroy # 默认使用主 destroy action
end
```

### 字段配置

ash_backpex 会自动根据 Ash 资源的属性类型推断合适的 Backpex 字段模块：

#### 自动类型映射

| Ash 类型 | Backpex 字段模块 |
|---------|-----------------|
| `Ash.Type.Boolean` | `Backpex.Fields.Boolean` |
| `Ash.Type.String` | `Backpex.Fields.Text` |
| `Ash.Type.Atom` | `Backpex.Fields.Text` |
| `Ash.Type.CiString` | `Backpex.Fields.Text` |
| `Ash.Type.Integer` | `Backpex.Fields.Number` |
| `Ash.Type.Float` | `Backpex.Fields.Number` |
| `Ash.Type.Date` | `Backpex.Fields.Date` |
| `Ash.Type.Time` | `Backpex.Fields.Time` |
| `Ash.Type.DateTime` | `Backpex.Fields.DateTime` |
| `Ash.Type.UtcDatetime` | `Backpex.Fields.DateTime` |
| `:belongs_to` | `Backpex.Fields.BelongsTo` |
| `:has_many` | `Backpex.Fields.HasMany` |

#### 字段基础配置

```elixir
fields do
  # 最简单的配置 - 自动推断类型
  field :title
  
  # 自定义标签
  field :created_at do
    label "创建时间"
  end
  
  # 指定字段模块
  field :description do
    module Backpex.Fields.Textarea
    rows 5
  end
  
  # 控制显示位置
  field :internal_notes do
    only [:show, :edit]  # 只在详情和编辑页显示
  end
  
  field :id do
    except [:new, :edit]  # 除了新建和编辑页都显示
  end
end
```

#### 关联字段配置

```elixir
fields do
  # belongs_to 关联
  field :author do
    display_field(:name)  # 显示关联资源的哪个字段
    live_resource(MyAppWeb.Live.Admin.AuthorLive)  # 关联的 LiveResource
  end
  
  # has_many 关联
  field :comments do
    display_field(:content)
    live_resource(MyAppWeb.Live.Admin.CommentLive)
    link_assocs(true)  # 是否生成链接，默认 true
  end
end
```

#### 高级字段选项

```elixir
fields do
  field :email do
    # 搜索和排序
    searchable(true)
    orderable(true)
    
    # 帮助文本
    help_text "请输入有效的邮箱地址"
    # 或使用 Ash 属性的描述
    help_text :description
    
    # 占位符
    placeholder "<EMAIL>"
    
    # 防抖和节流
    debounce(500)  # 毫秒
    throttle(1000)
    
    # 只读
    readonly(true)
    # 或条件只读
    readonly(fn assigns -> 
      assigns.current_user.role != :admin
    end)
  end
  
  # 选择字段
  field :status do
    options [
      {"草稿", :draft},
      {"已发布", :published},
      {"已归档", :archived}
    ]
  end
  
  # 日期时间格式化
  field :published_at do
    format "%Y年%m月%d日 %H:%M"
    # 或自定义函数
    format fn datetime -> 
      Calendar.strftime(datetime, "%Y年%m月%d日")
    end
  end
end
```

### 过滤器配置

过滤器需要创建独立的模块，然后在 DSL 中引用：

#### 创建过滤器模块

```elixir
defmodule MyAppWeb.Live.Admin.Filters.PostStatusFilter do
  use Backpex.Filter
  import Ash.Query
  import Phoenix.Component
  import MyAppWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <.input
      field={@form[:value]}
      type="select"
      options={[
        {"全部", ""},
        {"草稿", "draft"},
        {"已发布", "published"},
        {"已归档", "archived"}
      ]}
      prompt="选择状态"
    />
    """
  end

  @impl Backpex.Filter
  def filter(query, %{"value" => value}) when value != "" do
    status = String.to_atom(value)
    Ash.Query.filter(query, status == ^status)
  end

  def filter(query, _params), do: query
end
```

#### 使用过滤器

```elixir
backpex do
  filters do
    filter :status do
      module MyAppWeb.Live.Admin.Filters.PostStatusFilter
      label "文章状态"  # 可选，覆盖模块中的 label
    end
    
    filter :created_at do
      module MyAppWeb.Live.Admin.Filters.DateRangeFilter
    end
  end
end
```

### 动作配置

#### 项目动作（Item Actions）

默认包含 `:show`、`:edit`、`:delete` 动作，可以移除或添加自定义动作：

```elixir
backpex do
  item_actions do
    # 移除默认动作
    strip_default [:delete]
    
    # 添加自定义动作
    action :publish, MyAppWeb.Live.Admin.Actions.PublishAction
    action :archive, MyAppWeb.Live.Admin.Actions.ArchiveAction
  end
end
```

#### 资源动作（Resource Actions）

资源动作用于批量操作或创建操作：

```elixir
# 创建资源动作模块
defmodule MyAppWeb.Live.Admin.Actions.BatchPublishAction do
  use Backpex.ResourceAction
  import Ecto.Changeset

  @impl Backpex.ResourceAction
  def label, do: "批量发布"

  @impl Backpex.ResourceAction
  def fields do
    [
      publish_at: %{
        module: Backpex.Fields.DateTime,
        label: "发布时间",
        type: :utc_datetime,
        required: true
      }
    ]
  end

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata) do
    change
    |> cast(attrs, [:publish_at])
    |> validate_required([:publish_at])
  end

  @impl Backpex.ResourceAction
  def handle(socket, data) do
    # 实现批量发布逻辑
    {:ok, socket |> Phoenix.LiveView.put_flash(:info, "批量发布成功")}
  end
end
```

## 授权集成

ash_backpex 基础集成了 Ash 的授权系统：

```elixir
# 自动实现的 can? 回调
@impl Backpex.LiveResource
def can?(assigns, action, item) when action in [:index, :show, :edit, :delete, :new] do
  case Map.get(assigns, :current_user) do
    nil -> 
      # 未登录用户：只允许查看
      action in [:index, :show]
    
    user ->
      # 已登录用户：使用 Ash 的授权系统
      Ash.can?({resource, ash_action}, user)
  end
end
```

其中：
- `:index`, `:show` → 使用 read_action
- `:edit` → 使用 update_action
- `:delete` → 使用 destroy_action
- `:new` → 使用 create_action

## 自定义 Changeset

如果需要自定义创建或更新的 changeset：

```elixir
backpex do
  create_changeset &MyApp.custom_create_changeset/3
  update_changeset &MyApp.custom_update_changeset/3
end

# 在模块中定义
def custom_create_changeset(item, params, metadata) do
  # metadata 包含：
  # - assigns: LiveView assigns
  # - target: 触发 changeset 的表单字段名
  
  Ash.Changeset.for_create(item.__struct__, :create, params,
    actor: metadata[:assigns].current_user
  )
  |> Ash.Changeset.change_attribute(:created_by, metadata[:assigns].current_user.id)
end
```

## 路由配置

在 router.ex 中添加路由：

```elixir
scope "/admin", MyAppWeb do
  pipe_through [:browser, :require_authenticated_user]
  
  live_session :admin,
    on_mount: [{MyAppWeb.UserAuth, :ensure_authenticated}] do
    
    live "/posts", Live.Admin.PostLive, :index
    live "/posts/new", Live.Admin.PostLive, :new
    live "/posts/:id", Live.Admin.PostLive, :show
    live "/posts/:id/edit", Live.Admin.PostLive, :edit
  end
end
```

## 完整示例

### Ash 资源

```elixir
defmodule MyApp.Blog.Post do
  use Ash.Resource,
    data_layer: AshPostgres.DataLayer,
    domain: MyApp.Blog

  postgres do
    table "posts"
    repo MyApp.Repo
  end

  attributes do
    uuid_primary_key :id
    
    attribute :title, :string do
      allow_nil? false
      public? true
    end
    
    attribute :content, :string do
      allow_nil? false
      public? true
    end
    
    attribute :status, :atom do
      constraints one_of: [:draft, :published, :archived]
      default :draft
      public? true
    end
    
    attribute :published_at, :utc_datetime do
      public? true
    end
    
    timestamps()
  end

  relationships do
    belongs_to :author, MyApp.Accounts.User do
      allow_nil? false
      public? true
    end
    
    has_many :comments, MyApp.Blog.Comment do
      public? true
    end
  end

  actions do
    defaults [:create, :read, :update, :destroy]
  end
end
```

### LiveResource

```elixir
defmodule MyAppWeb.Live.Admin.PostLive do
  use AshBackpex.LiveResource

  backpex do
    resource MyApp.Blog.Post
    layout({MyAppWeb.Layouts, :admin})
    load [:author, comments: :author]
    
    fields do
      field :title do
        searchable(true)
        orderable(true)
      end
      
      field :content do
        module Backpex.Fields.Textarea
        rows 10
      end
      
      field :status do
        options [
          {"草稿", :draft},
          {"已发布", :published},
          {"已归档", :archived}
        ]
      end
      
      field :published_at do
        format "%Y-%m-%d %H:%M"
      end
      
      field :author do
        display_field(:name)
        live_resource(MyAppWeb.Live.Admin.UserLive)
      end
      
      field :comments do
        display_field(:excerpt)
        only [:show]
      end
    end
    
    filters do
      filter :status do
        module MyAppWeb.Live.Admin.Filters.PostStatusFilter
      end
      
      filter :author do
        module MyAppWeb.Live.Admin.Filters.AuthorSelectFilter
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "文章"

  @impl Backpex.LiveResource
  def plural_name, do: "文章管理"
end
```

## 注意事项

1. **必须实现的回调**：
   - `singular_name/0` - 资源的单数名称
   - `plural_name/0` - 资源的复数名称

2. **字段类型推断**：
   - 如果无法自动推断字段类型，必须手动指定 `module`
   - 计算字段和聚合字段也支持自动推断

3. **授权限制**：
   - 当前版本的授权集成较为基础
   - 复杂的授权逻辑可能需要自定义实现

4. **性能考虑**：
   - 使用 `load` 预加载关联以避免 N+1 查询
   - 大数据集应考虑添加适当的过滤器和分页

5. **调试**：
   - Adapter 中包含了 `dbg` 调用，可用于调试
   - 查看生成的配置：`YourLiveResource.__info__(:functions)`

## 迁移指南

从直接使用 Backpex.Adapters.Ash 迁移到 AshBackpex.LiveResource：

```elixir
# 旧方式
use Backpex.LiveResource,
  adapter: Backpex.Adapters.Ash,
  adapter_config: [
    resource: MyApp.Resource,
    # ...
  ]

# 新方式
use AshBackpex.LiveResource

backpex do
  resource MyApp.Resource
  # ...
end
```

主要优势：
- 更简洁的 DSL
- 自动类型推断
- 更好的默认配置
- 集成的授权支持

## 故障排除

### 常见问题

1. **"Unable to derive the field type" 错误**
   - 确保字段名正确
   - 手动指定 `module` 选项

2. **过滤器不工作**
   - 检查过滤器模块是否正确实现了 `Backpex.Filter` 行为
   - 确保 `filter/2` 函数返回了正确的查询

3. **关联不显示**
   - 确保在 `load` 中包含了关联
   - 检查关联的 `display_field` 是否存在

4. **授权问题**
   - 确保 Ash 资源的 action 配置正确
   - 检查 `current_user` 是否在 assigns 中

## 未来计划

- 完整的 Ash 授权策略支持
- 更多的字段类型自动映射
- 批量操作的更好支持
- 自定义视图组件
- 更丰富的过滤器类型

## 贡献

ash_backpex 是开源项目，欢迎贡献：
- 报告问题
- 提交 PR
- 改进文档
- 分享使用经验