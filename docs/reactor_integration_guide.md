# Ash.Reactor 充值流程集成指南

## 概述

本文档介绍如何在充值系统中集成 Ash.Reactor 来处理复杂的业务流程，提供更好的事务一致性、错误处理和补偿机制。

## 依赖配置

### 1. 添加依赖到 mix.exs

```elixir
defp deps do
  [
    # 现有依赖...
    {:reactor, "~> 0.9"},
    {:ash_reactor, "~> 0.1"}
  ]
end
```

### 2. 更新应用配置

在 `config/config.exs` 中添加 Reactor 配置：

```elixir
# Reactor 配置
config :reactor,
  # 配置默认超时时间
  default_timeout: 30_000,
  # 配置日志级别
  log_level: :info

# Ash.Reactor 配置
config :ash_reactor,
  # 启用调试模式（开发环境）
  debug: Mix.env() == :dev
```

## 架构优势

### 与之前 after_action 方案的对比

| 特性 | after_action | Ash.Reactor |
|------|-------------|-------------|
| **事务一致性** | ❌ 有限 | ✅ 完整支持 |
| **错误处理** | ❌ 无法回滚 | ✅ 自动补偿 |
| **步骤可见性** | ❌ 隐藏在回调中 | ✅ 声明式定义 |
| **测试便利性** | ❌ 难以单独测试 | ✅ 可分步测试 |
| **监控和调试** | ❌ 有限 | ✅ 完整追踪 |
| **并发处理** | ⚠️ 需要小心处理 | ✅ 内置支持 |

## 核心组件

### 1. CompleteRechargeReactor

主要的工作流定义，包含以下步骤：

1. **update_record**: 更新充值记录状态
2. **get_balance_before**: 获取充值前余额
3. **add_coins**: 向用户账户添加金币
4. **validate_balance**: 验证余额变化
5. **publish_event**: 发布充值完成事件

### 2. 补偿机制

- **add_coins 补偿**: 如果后续步骤失败，回滚充值记录状态
- **validate_balance 补偿**: 如果验证失败，发送关键报警

### 3. 集成接口

- `complete_recharge_with_reactor/3`: 直接调用 Reactor
- `complete_recharge_order/2`: 现有接口，内部使用 Reactor

## 使用方法

### 基本用法

```elixir
# 直接调用 Reactor
{:ok, result} = Teen.PaymentSystem.RechargeRecord.complete_recharge_with_reactor(
  record_id,
  external_order_id,
  callback_data
)

# 通过现有接口（推荐）
{:ok, record} = Teen.PaymentSystem.RechargeRecord.complete_recharge_order(
  order_id,
  %{
    status: :success,
    gateway_order_id: "gateway_order_123"
  }
)
```

### 错误处理

```elixir
case Teen.PaymentSystem.RechargeRecord.complete_recharge_order(order_id, callback) do
  {:ok, record} ->
    Logger.info("充值完成成功: #{record.id}")
    
  {:error, reason} ->
    Logger.error("充值完成失败: #{inspect(reason)}")
    # 错误已经通过补偿机制处理
end
```

## 监控和日志

### 日志格式

Reactor 使用统一的日志前缀 `💰 [REACTOR]`，便于过滤和监控：

```
💰 [REACTOR] 启动充值完成工作流: record_id=abc123
💰 [REACTOR] 获取用户 user123 充值前余额
💰 [REACTOR] 开始为用户 user123 添加金币，金额: 100
💰 [REACTOR] 金币添加成功
💰 [REACTOR] 验证余额变化
💰 [REACTOR] 余额验证成功: 1000 -> 1100 (增加 100)
💰 [REACTOR] 发布充值完成事件
💰 [REACTOR] 充值完成流程成功
```

### 关键指标监控

建议监控以下指标：

- **成功率**: Reactor 执行成功的比例
- **执行时间**: 每个步骤和整体流程的执行时间
- **补偿频率**: 补偿机制触发的频率
- **余额验证失败**: 需要人工处理的关键案例

## 测试策略

### 单元测试

```elixir
test "完整的充值完成流程" do
  inputs = %{
    record_id: record.id,
    external_order_id: "TEST_EXT_123",
    callback_data: %{status: :success}
  }

  assert {:ok, result} = Reactor.run(CompleteRechargeReactor, inputs)
  assert result.status == :completed
end
```

### 集成测试

```elixir
test "通过 complete_recharge_order 调用" do
  callback_result = %{
    status: :success,
    gateway_order_id: "GW_123"
  }

  assert {:ok, result_record} = RechargeRecord.complete_recharge_order(
    record.order_id,
    callback_result
  )
end
```

### 失败场景测试

```elixir
test "Ledger 转账失败的补偿机制" do
  # 使用无效金额触发失败
  {:ok, record} = RechargeRecord.create_recharge(%{
    amount: Decimal.new("-1")  # 无效金额
  })

  assert {:error, _reason} = Reactor.run(CompleteRechargeReactor, inputs)
  
  # 验证补偿：记录被标记为失败
  {:ok, compensated_record} = RechargeRecord.get_by_id(record.id)
  assert compensated_record.status == :failed
end
```

## 部署注意事项

### 1. 渐进式迁移

建议采用渐进式迁移策略：

1. **阶段1**: 部署新代码但保持使用原有流程
2. **阶段2**: 启用 Reactor 流程，监控关键指标
3. **阶段3**: 完全切换到 Reactor 流程


### 3. 性能考虑

- Reactor 有轻微的性能开销，但通过更好的错误处理和一致性保证来补偿
- 在高并发场景下进行压力测试
- 监控内存使用情况，特别是长时间运行的工作流

## 故障排除

### 常见问题

1. **依赖缺失**
   ```
   Error: module Reactor is not available
   ```
   解决：确保已添加 `{:reactor, "~> 0.9"}` 和 `{:ash_reactor, "~> 0.1"}` 依赖

2. **步骤执行失败**
   ```
   Error: Step :add_coins failed
   ```
   解决：检查 Ledger 系统状态和用户账户有效性

3. **补偿失败**
   ```
   Warning: Compensation for :validate_balance failed
   ```
   处理：这种情况需要人工介入，检查具体的补偿逻辑

### 调试技巧

1. **启用详细日志**：
   ```elixir
   config :logger, level: :debug
   config :reactor, log_level: :debug
   ```

2. **检查 Reactor 状态**：
   ```elixir
   # 在 iex 中
   {:ok, result} = Reactor.run(CompleteRechargeReactor, inputs, [debug: true])
   ```

3. **单步调试**：
   ```elixir
   # 可以单独测试每个步骤
   Reactor.run_step(CompleteRechargeReactor, :add_coins, context)
   ```

## 下一步改进

1. **指标收集**: 集成 Telemetry 收集详细的执行指标
2. **可视化监控**: 创建 Reactor 执行状态的可视化界面
3. **自动化回滚**: 在某些失败场景下实现更智能的自动回滚
4. **批处理支持**: 支持批量处理多个充值请求

## 总结

通过集成 Ash.Reactor，充值系统获得了：

- ✅ **更强的事务一致性保证**
- ✅ **自动化的错误处理和补偿机制**
- ✅ **更好的可观测性和调试能力**
- ✅ **声明式的业务流程定义**
- ✅ **更容易的测试和维护**

这为系统的稳定性和可维护性提供了显著改进。