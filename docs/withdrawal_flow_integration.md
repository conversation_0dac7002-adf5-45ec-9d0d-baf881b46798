# 提现流程集成说明

## 概述

本文档说明了提现审核和支付两个 Reactor 的集成方式。在新的实现中，审核通过后会自动触发支付流程，实现了无缝的工作流程。

## 流程图

```
用户申请提现
    ↓
创建提现记录（CreateWithdrawalReactor）
    ↓
管理员审核（AuditWithdrawalReactor）
    ├─ 审核通过 → 自动触发支付（CompleteWithdrawalReactor）
    │            ├─ 支付成功 → 等待回调
    │            └─ 支付失败 → 积分退回用户
    └─ 审核拒绝 → 积分退回用户
```

## 主要改动

### 1. AuditWithdrawalReactor 的步骤6更新

```elixir
# 步骤6: 触发后续流程（仅限审核通过）
step :trigger_payment_process do
  argument :approval_result, result(:handle_approval)

  run fn %{approval_result: result}, _context ->
    case result do
      %{withdrawal: withdrawal, action: :approved} ->
        # 直接调用 CompleteWithdrawalReactor 执行支付
        case Ash.Reactor.run(
               Teen.PaymentSystem.CompleteWithdrawalReactor,
               %{
                 withdrawal_id: withdrawal.id,
                 gateway_config: nil # 让 reactor 自动选择网关
               },
               %{},
               async?: false
             ) do
          {:ok, payment_result} ->
            # 支付流程成功启动
          {:error, reason} ->
            # 支付启动失败，但审核已通过
        end
    end
  end
end
```

### 2. WithdrawalService 的更新

`approve_withdrawal/2` 函数现在返回更详细的信息：

```elixir
{:ok, %{
  withdrawal: withdrawal,          # 更新后的提现记录
  audit_status: :approved,        # 审核状态
  payment_initiated: true/false,  # 是否成功启动支付
  payment_result: result,         # 支付启动结果（如果成功）
  payment_error: error,           # 支付启动错误（如果失败）
  message: "描述信息"
}}
```

### 3. 新增手动支付功能

为了处理自动支付失败的情况，新增了手动执行支付的函数：

```elixir
def execute_withdrawal_payment(withdrawal_id, gateway_config \\ nil)
```

## 使用示例

### 1. 审核通过（自动支付）

```elixir
# 审核通过提现
{:ok, result} = WithdrawalService.approve_withdrawal(withdrawal_id, auditor_id)

# 检查支付是否自动启动
if result.payment_initiated do
  IO.puts("支付已自动启动：#{result.payment_status}")
else
  IO.puts("支付启动失败：#{result.payment_error}")
  # 可以选择手动重试
  WithdrawalService.execute_withdrawal_payment(withdrawal_id)
end
```

### 2. 审核拒绝

```elixir
# 审核拒绝提现
{:ok, withdrawal} = WithdrawalService.reject_withdrawal(
  withdrawal_id, 
  auditor_id, 
  "余额异常"
)
# 积分会自动退回用户账户
```

### 3. 手动执行支付

```elixir
# 当自动支付失败或需要手动控制时
{:ok, result} = WithdrawalService.execute_withdrawal_payment(withdrawal_id)
```

## 状态流转

### 审核状态 (audit_status)
- 0: 待审核
- 1: 审核通过
- 2: 审核拒绝

### 进度状态 (progress_status)
- 0: 排队中（待处理）
- 1: 处理中（已提交到网关）
- 2: 支付成功
- 3: 支付失败

### 结果状态 (result_status)
- 0: 进行中
- 1: 成功
- 2: 失败

## 积分流转说明

1. **创建提现时**：用户账户 → 待审核账户
2. **审核通过后**：积分保持在待审核账户
3. **支付成功后**：待审核账户 → 已支付账户
4. **审核拒绝时**：待审核账户 → 用户账户
5. **支付失败时**：待审核账户 → 用户账户

## 错误处理

1. **审核失败**：返回错误信息，不影响积分
2. **支付启动失败**：审核状态保持为通过，可手动重试
3. **积分退回失败**：发送系统告警，需要人工介入

## 事件通知

系统会通过 Phoenix.PubSub 发布以下事件：

- `withdrawal_approved`: 提现审核通过
- `withdrawal_rejected`: 提现审核拒绝
- `withdrawal_payment_submitted`: 支付已提交
- `withdrawal_payment_failed`: 支付失败

监听示例：

```elixir
Phoenix.PubSub.subscribe(Cypridina.PubSub, "withdrawal:#{user_id}")
```

## 注意事项

1. 审核通过后的支付是同步执行的，确保流程的连续性
2. 即使支付启动失败，审核状态也会保持为"通过"
3. 支付失败的积分退回是自动的，无需人工干预
4. 所有关键步骤都有日志记录，便于问题追踪

## 相关文件

- `/lib/teen/resources/payment_system/reactors/audit_withdrawal_reactor.ex`
- `/lib/teen/resources/payment_system/reactors/complete_withdrawal_reactor.ex`
- `/lib/teen/services/withdrawal_service.ex`
- `/test/teen/reactors/integrated_withdrawal_flow_test.exs`