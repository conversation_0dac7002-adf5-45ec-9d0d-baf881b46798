# Cypridina 项目文档

本目录包含了Cypridina游戏平台的所有技术文档和需求说明。

## 📋 文档索引

### 核心需求文档
- **[需求.md](./需求.md)** - 项目整体需求和开发计划
- **[INTEGRATION_SUMMARY.md](./INTEGRATION_SUMMARY.md)** - 系统集成总结

### 支付系统
- **[payment-api.md](./payment-api.md)** - MasterPay88支付网关API完整文档 ⭐
  - 📋 **快速参考** - 核心配置和接口速查表
  - 📖 **通用规则** - 协议规范和签名算法
  - 💻 **多语言签名示例** - Java、PHP、C#、Python、Elixir
  - ❓ **常见问题解答** - 故障排除和解决方案
  - 💰 **代收接口** - 充值订单创建、查询、回调
  - 💸 **代付接口** - 提现订单创建、查询、回调
  - 🔧 **Elixir集成示例** - 完整的代码实现
  - 📚 **官方文档链接** - 9个详细API文档（需密码：4sXxcWpJ）

### 商城系统
- **[shop_system_guide.md](./shop_system_guide.md)** - 商城系统指南
- **[shop_system_setup.md](./shop_system_setup.md)** - 商城系统设置

### 游戏系统
- **[thirty_card_protocol.md](./thirty_card_protocol.md)** - 30张卡游戏协议
- **[new_game_factories_implementation.md](./new_game_factories_implementation.md)** - 新游戏工厂实现
- **[cypridina_robot_system_word_document.md](./cypridina_robot_system_word_document.md)** - 机器人系统文档

## 🚀 快速开始

### 支付系统集成
如果你需要集成支付功能，请参考：
1. **[payment-api.md](./payment-api.md)** - 完整的API文档（已整理官方9个文档）
2. **快速开始**：查看文档中的[快速参考](#快速参考)部分
3. **代码实现**：参考文档中的[Elixir集成示例](#elixir集成示例)
4. **现有代码**：查看 `lib/teen/payment_system/` 目录
5. **配置部署**：参考文档中的部署配置说明
6. **官方文档**：访问密码 `4sXxcWpJ`

### 开发环境设置
1. 克隆项目：`git clone https://github.com/cypridina-team/cypridina.git`
2. 安装依赖：`mix deps.get`
3. 配置数据库：`mix ecto.setup`
4. 启动服务：`mix phx.server`

## 📁 项目结构

```
cypridina/
├── lib/
│   ├── cypridina/           # 核心业务逻辑
│   ├── cypridina_web/       # Web层（控制器、视图等）
│   └── teen/                # 游戏相关模块
├── docs/                    # 文档目录（当前目录）
├── config/                  # 配置文件
└── test/                    # 测试文件
```

## 🔧 主要技术栈

- **后端框架**: Phoenix (Elixir)
- **数据库**: PostgreSQL
- **缓存**: Redis
- **消息队列**: Oban
- **前端**: LiveView
- **支付网关**: MasterPay88

## 📞 联系信息

如有问题或需要支持，请联系开发团队。

## 📝 更新日志

- **2025-07-09**: 🎉 完成MasterPay88支付API文档整理
  - 整理了官方9个API文档的完整内容
  - 添加了快速参考和Elixir集成示例
  - 包含多语言签名算法实现
  - 提供详细的常见问题解答
- **2025-07-09**: 创建支付系统API文档框架
- **2025-07-09**: 整理项目文档结构

---

*最后更新: 2025-07-09*
