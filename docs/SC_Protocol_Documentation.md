## 5. 大厅活动系统协议 (共30个)

### 协议架构
- **主协议ID**: `emFunction_HallActivity` (101)
- **子协议范围**: 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59

### 登录活动协议

#### SC_LOGINCASH_INFO_P - 登录活动信息
- **协议ID**: `emFunction_HallActivity` + `SC_LOGINCASH_INFO_P` (1)
- **数据格式**:
```json
{
  "taskList": [
    {
      "taskType": "int - 任务类型(0=登录,1=充值,2=游戏局数,3=转盘,4=游戏赢分)",
      "taskTarget": "int - 任务目标值",
      "taskProgress": "int - 当前进度",
      "taskReward": "int64 - 奖励金额",
      "taskStatus": "int - 状态(0=未完成,1=可领取,2=已领取)",
      "taskDay": "int - 第几天任务"
    }
  ],
  "totalDays": "int - 总天数",
  "currentDay": "int - 当前天数",
  "isCompleted": "bool - 是否全部完成"
}
```
- **触发条件**: 玩家请求登录活动信息时
- **示例数据包**:
```json
{
  "cid": 101,
  "sid": 1,
  "pack": {
    "taskList": [
      {"taskType": 0, "taskTarget": 1, "taskProgress": 1, "taskReward": 1000, "taskStatus": 1, "taskDay": 1}
    ],
    "totalDays": 7,
    "currentDay": 1,
    "isCompleted": false
  }
}
```

#### SC_FETCH_LOGINCASH_AWARD_P - 登录活动奖励领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_LOGINCASH_AWARD_P` (3)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果(0=成功)",
  "fetchType": "int - 领取类型",
  "reward": "int64 - 获得奖励",
  "newMoney": "int64 - 当前金币",
  "taskStatus": "int - 任务新状态"
}
```
- **触发条件**: 玩家领取登录活动奖励后

### 用户资金协议

#### SC_GET_USER_MONEY_P - 用户资金信息
- **协议ID**: `emFunction_HallActivity` + `SC_GET_USER_MONEY_P` (5)
- **数据格式**:
```json
{
  "money": "int64 - 当前金币",
  "walletMoney": "int64 - 钱包金币",
  "diamonds": "int - 钻石数量",
  "vipLevel": "int - VIP等级",
  "vipExp": "int64 - VIP经验值"
}
```
- **触发条件**: 玩家请求资金信息或资金发生变化时

#### SC_FETCH_USER_BONUS_P - 用户积分领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_USER_BONUS_P` (7)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "fetchAmount": "int64 - 领取积分数量",
  "newBonus": "int64 - 当前积分余额"
}
```
- **触发条件**: 玩家领取积分后

### 七日签到协议

#### SC_GET_SEVEN_DAYS_P - 七日签到信息
- **协议ID**: `emFunction_HallActivity` + `SC_GET_SEVEN_DAYS_P` (9)
- **数据格式**:
```json
{
  "signDays": "int - 已签到天数",
  "todaySigned": "bool - 今日是否已签到",
  "rewards": [
    {
      "day": "int - 第几天",
      "reward": "int64 - 奖励金额",
      "status": "int - 状态(0=未签到,1=可签到,2=已签到)"
    }
  ],
  "canClaim": "bool - 是否可以领取"
}
```
- **触发条件**: 玩家请求七日签到信息时

#### SC_FETCH_SEVEN_DAYS_AWARD_P - 七日签到奖励领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_SEVEN_DAYS_AWARD_P` (11)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "day": "int - 签到天数",
  "reward": "int64 - 获得奖励",
  "newMoney": "int64 - 当前金币",
  "signDays": "int - 总签到天数"
}
```
- **触发条件**: 玩家领取七日签到奖励后

### 30次刮刮卡协议

#### SC_GET_THIRTY_CARD_P - 30次刮刮卡信息
- **协议ID**: `emFunction_HallActivity` + `SC_GET_THIRTY_CARD_P` (13)
- **数据格式**:
```json
{
  "userLevel": "int - 用户等级(基于VIP充值)",
  "totalCharge": "int64 - 累计充值金额",
  "levelConfig": {
    "level": "int - 等级",
    "minCharge": "int64 - 最低充值要求",
    "rewards": "array - 奖励配置"
  },
  "claimedCount": "int - 已领取次数",
  "maxCount": "int - 最大领取次数(30)",
  "pendingRewards": "array - 待领取奖励(断线重连用)"
}
```
- **触发条件**: 玩家请求刮刮卡活动信息时

#### SC_FETCH_THIRTY_CARD_P - 30次刮刮卡领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_THIRTY_CARD_P` (15)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "fetchType": "int - 领取类型(0=生成奖励,1=确认领取)",
  "rewards": [
    {
      "type": "int - 奖励类型(1=金币,2=钻石)",
      "amount": "int64 - 奖励数量"
    }
  ],
  "newMoney": "int64 - 当前金币",
  "newDiamonds": "int - 当前钻石",
  "claimedCount": "int - 已领取次数"
}
```
- **触发条件**: 玩家领取刮刮卡奖励后

### 游戏任务协议

#### SC_GAME_TASK_P - 游戏任务信息
- **协议ID**: `emFunction_HallActivity` + `SC_GAME_TASK_P` (17)
- **数据格式**:
```json
{
  "tasks": [
    {
      "taskId": "int - 任务ID",
      "gameId": "int - 游戏ID",
      "taskType": "int - 任务类型",
      "target": "int - 目标值",
      "progress": "int - 当前进度",
      "reward": "int64 - 奖励金额",
      "status": "int - 任务状态"
    }
  ],
  "dailyResetTime": "int64 - 每日重置时间"
}
```
- **触发条件**: 玩家请求游戏任务信息时

#### SC_FETCH_GAME_TASK_AWARD_P - 游戏任务奖励领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_GAME_TASK_AWARD_P` (19)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "taskId": "int - 任务ID",
  "reward": "int64 - 获得奖励",
  "newMoney": "int64 - 当前金币"
}
```
- **触发条件**: 玩家领取游戏任务奖励后

### 礼包活动协议

#### SC_GET_GIFT_CHARGE_P - 礼包活动信息
- **协议ID**: `emFunction_HallActivity` + `SC_GET_GIFT_CHARGE_P` (21)
- **数据格式**:
```json
{
  "giftPackages": [
    {
      "packageId": "int - 礼包ID",
      "packageName": "string - 礼包名称",
      "price": "int64 - 价格",
      "originalPrice": "int64 - 原价",
      "discount": "int - 折扣百分比",
      "rewards": "array - 奖励内容",
      "status": "int - 状态(0=不可购买,1=可购买,2=已购买)",
      "limitCount": "int - 限购次数",
      "boughtCount": "int - 已购买次数"
    }
  ]
}
```
- **触发条件**: 玩家请求礼包活动信息时

### 周卡月卡协议

#### SC_GET_CARD_TASK_P - 周卡月卡信息
- **协议ID**: `emFunction_HallActivity` + `SC_GET_CARD_TASK_P` (23)
- **数据格式**:
```json
{
  "weekCard": {
    "isActive": "bool - 是否激活",
    "expireTime": "int64 - 过期时间",
    "dailyReward": "int64 - 每日奖励",
    "canClaimToday": "bool - 今日是否可领取"
  },
  "monthCard": {
    "isActive": "bool - 是否激活",
    "expireTime": "int64 - 过期时间",
    "dailyReward": "int64 - 每日奖励",
    "canClaimToday": "bool - 今日是否可领取"
  }
}
```
- **触发条件**: 玩家请求周卡月卡信息时

#### SC_FETCH_CARD_TASK_P - 周卡月卡奖励领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_CARD_TASK_P` (25)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "cardType": "int - 卡类型(1=周卡,2=月卡)",
  "reward": "int64 - 获得奖励",
  "newMoney": "int64 - 当前金币",
  "nextClaimTime": "int64 - 下次可领取时间"
}
```
- **触发条件**: 玩家领取周卡月卡奖励后

### VIP礼包协议

#### SC_GET_VIP_GIFT_P - VIP礼包信息
- **协议ID**: `emFunction_HallActivity` + `SC_GET_VIP_GIFT_P` (27)
- **数据格式**:
```json
{
  "vipLevel": "int - 当前VIP等级",
  "vipExp": "int64 - VIP经验值",
  "nextLevelExp": "int64 - 下级所需经验",
  "gifts": [
    {
      "giftType": "int - 礼包类型(0=日,1=周,2=月)",
      "vipLevel": "int - 所需VIP等级",
      "reward": "int64 - 奖励金额",
      "status": "int - 状态(0=不可领,1=可领,2=已领)",
      "nextClaimTime": "int64 - 下次可领取时间"
    }
  ]
}
```
- **触发条件**: 玩家请求VIP礼包信息时

#### SC_FETCH_VIP_GIFT_P - VIP礼包奖励领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_VIP_GIFT_P` (29)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "giftType": "int - 礼包类型",
  "reward": "int64 - 获得奖励",
  "newMoney": "int64 - 当前金币",
  "nextClaimTime": "int64 - 下次可领取时间"
}
```
- **触发条件**: 玩家领取VIP礼包奖励后

### 免费奖励协议

#### SC_GET_FREE_BONUS_P - 免费奖励信息
- **协议ID**: `emFunction_HallActivity` + `SC_GET_FREE_BONUS_P` (31)
- **数据格式**:
```json
{
  "bonusTask": {
    "taskType": "int - 任务类型",
    "gameId": "int - 游戏ID",
    "target": "int - 目标值",
    "progress": "int - 当前进度",
    "reward": "int64 - 奖励金额",
    "status": "int - 任务状态",
    "resetTime": "int64 - 重置时间戳"
  },
  "canClaim": "bool - 是否可领取"
}
```
- **触发条件**: 玩家请求免费奖励信息时

#### SC_FETCH_FREE_BONUS_P - 免费奖励领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_FREE_BONUS_P` (33)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "reward": "int64 - 获得奖励",
  "newMoney": "int64 - 当前金币",
  "nextResetTime": "int64 - 下次重置时间"
}
```
- **触发条件**: 玩家领取免费奖励后

### 免费提现协议

#### SC_GET_FREE_CASH_P - 免费提现信息
- **协议ID**: `emFunction_HallActivity` + `SC_GET_FREE_CASH_P` (35)
- **数据格式**:
```json
{
  "cashTask": {
    "taskType": "int - 任务类型",
    "target": "int - 目标值",
    "progress": "int - 当前进度",
    "reward": "int64 - 奖励金额",
    "status": "int - 任务状态"
  },
  "inviteCount": "int - 邀请人数",
  "canClaim": "bool - 是否可领取"
}
```
- **触发条件**: 玩家请求免费提现信息时

#### SC_FETCH_FREE_CASH_P - 免费提现领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_FREE_CASH_P` (37)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "reward": "int64 - 获得奖励",
  "newMoney": "int64 - 当前金币"
}
```
- **触发条件**: 玩家领取免费提现奖励后

#### SC_GET_FREE_CASH_INVITATION_P - 免费提现邀请数据
- **协议ID**: `emFunction_HallActivity` + `SC_GET_FREE_CASH_INVITATION_P` (39)
- **数据格式**:
```json
{
  "inviteList": [
    {
      "inviteId": "int64 - 邀请ID",
      "inviteeNickname": "string - 被邀请人昵称",
      "inviteTime": "int64 - 邀请时间",
      "status": "int - 状态(0=未完成,1=已完成)",
      "reward": "int64 - 奖励金额"
    }
  ],
  "totalCount": "int - 总邀请数",
  "pageInfo": {
    "page": "int - 当前页",
    "pageSize": "int - 每页数量",
    "totalPages": "int - 总页数"
  }
}
```
- **触发条件**: 玩家请求邀请数据时

### 破产补助协议

#### SC_FETCH_BROKE_AWARD_P - 破产补助信息/领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_BROKE_AWARD_P` (41)
- **数据格式**:
```json
{
  "result": "emResult - 操作结果",
  "fetchCount": "int - 已领取次数",
  "maxCount": "int - 最大领取次数",
  "awardCash": "int64 - 可领取金额",
  "nextClaimTime": "int64 - 下次可领取时间",
  "newMoney": "int64 - 当前金币(领取后)"
}
```
- **触发条件**: 玩家查询或领取破产补助时

### 邮件奖励协议

#### SC_FETCH_MAIL_AWARD_P - 邮件奖励领取结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_MAIL_AWARD_P` (43)
- **数据格式**:
```json
{
  "result": "emResult - 领取结果",
  "fetchId": "int64 - 邮件ID(-1表示全部)",
  "fetchBonus": "int64 - 领取到的积分",
  "fetchCash": "int64 - 领取到的金币",
  "newMoney": "int64 - 当前金币",
  "newBonus": "int64 - 当前积分"
}
```
- **触发条件**: 玩家领取邮件奖励后

### 绑定奖励协议

#### SC_BIND_PHONE_USER_P - 绑定手机结果
- **协议ID**: `emFunction_HallActivity` + `SC_BIND_PHONE_USER_P` (45)
- **数据格式**:
```json
{
  "result": "emResult - 绑定结果",
  "phone": "string - 手机号码",
  "reward": "int64 - 绑定奖励",
  "newMoney": "int64 - 当前金币"
}
```
- **触发条件**: 玩家绑定手机号码后

#### SC_BIND_MAIL_USER_P - 绑定邮箱结果
- **协议ID**: `emFunction_HallActivity` + `SC_BIND_MAIL_USER_P` (47)
- **数据格式**:
```json
{
  "result": "emResult - 绑定结果",
  "mail": "string - 邮箱地址",
  "reward": "int64 - 绑定奖励",
  "newMoney": "int64 - 当前金币"
}
```
- **触发条件**: 玩家绑定邮箱后

### CDKEY兑换协议

#### SC_FETCH_CDKEY_AWARD_P - CDKEY兑换结果
- **协议ID**: `emFunction_HallActivity` + `SC_FETCH_CDKEY_AWARD_P` (49)
- **数据格式**:
```json
{
  "result": "emResult - 兑换结果",
  "cdkey": "string - 兑换码",
  "rewards": [
    {
      "type": "int - 奖励类型(1=金币,2=钻石)",
      "amount": "int64 - 奖励数量"
    }
  ],
  "newMoney": "int64 - 当前金币",
  "newDiamonds": "int - 当前钻石"
}
```
- **触发条件**: 玩家兑换CDKEY后

## 大厅活动协议特点

### 统一的消息处理框架
所有大厅活动协议都使用相同的处理模式：
- 信息查询协议 (奇数sid): 返回活动状态和配置
- 奖励领取协议 (偶数sid): 处理奖励发放和状态更新

### 安全机制
- **IP地址记录**: 所有奖励领取都记录用户IP
- **防重复领取**: 数据库层面的唯一性约束
- **操作日志**: 完整的用户操作记录
- **时间验证**: 严格的时间窗口检查

### 数据持久化
- 使用SQL Server存储过程处理核心业务逻辑
- 统一的错误处理和事务管理
- 支持断线重连和状态恢复
- 完整的用户行为追踪

### 活动类型总结
1. **每日活动**: 登录奖励、签到、免费奖励
2. **充值活动**: 礼包、周卡月卡、VIP礼包
3. **游戏活动**: 游戏任务、30次刮刮卡
4. **社交活动**: 邀请奖励、绑定奖励
5. **救济活动**: 破产补助、CDKEY兑换
6. **通信活动**: 邮件奖励系统

这套大厅活动协议为IndiaGameServer提供了完整的用户激励和留存体系，通过多样化的活动类型有效提升用户参与度和平台收益。

**更新后总计: 298个SC_*协议**