{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "robot_id", "type": "bigint"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "nickname", "type": "text"}, {"allow_nil?": true, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "avatar_id", "type": "bigint"}, {"allow_nil?": true, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "level", "type": "bigint"}, {"allow_nil?": true, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "current_game_type", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "current_room_id", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "seat_number", "type": "bigint"}, {"allow_nil?": true, "default": "100000", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "current_points", "type": "bigint"}, {"allow_nil?": true, "default": "5000", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "min_points_threshold", "type": "bigint"}, {"allow_nil?": true, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "last_bet_amount", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status_changed_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "game_joined_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "last_activity_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "recycle_reason", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "recycled_by", "type": "text"}, {"allow_nil?": true, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "robot_config", "type": "map"}, {"allow_nil?": true, "default": "true", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "is_auto_created", "type": "boolean"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "creator_admin_id", "type": "text"}, {"allow_nil?": true, "default": "[]", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "tags", "type": ["array", "text"]}, {"allow_nil?": true, "default": "true", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "is_enabled", "type": "boolean"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [{"all_tenants?": false, "concurrently": false, "error_fields": ["status"], "fields": [{"type": "atom", "value": "status"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}, {"all_tenants?": false, "concurrently": false, "error_fields": ["is_enabled"], "fields": [{"type": "atom", "value": "is_enabled"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}, {"all_tenants?": false, "concurrently": false, "error_fields": ["current_game_type"], "fields": [{"type": "atom", "value": "current_game_type"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}, {"all_tenants?": false, "concurrently": false, "error_fields": ["current_room_id"], "fields": [{"type": "atom", "value": "current_room_id"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}, {"all_tenants?": false, "concurrently": false, "error_fields": ["status", "is_enabled"], "fields": [{"type": "atom", "value": "status"}, {"type": "atom", "value": "is_enabled"}], "include": null, "message": null, "name": null, "nulls_distinct": true, "prefix": null, "table": null, "unique": false, "using": null, "where": null}], "custom_statements": [], "has_create_action": true, "hash": "B136858A9736EFD8F1F7BDF3D572AA727FF68999EEC0A5F5F2C7E9DFE0114AB7", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "robot_entities_unique_robot_id_index", "keys": [{"type": "atom", "value": "robot_id"}], "name": "unique_robot_id", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "robot_entities"}