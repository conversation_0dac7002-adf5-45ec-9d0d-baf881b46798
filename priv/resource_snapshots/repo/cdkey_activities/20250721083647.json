{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "code", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "batch_name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward_type", "type": "text"}, {"allow_nil?": false, "default": "\"0\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward_amount", "type": "decimal"}, {"allow_nil?": true, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reward_items", "type": "map"}, {"allow_nil?": false, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "max_uses", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "valid_from", "type": "utc_datetime"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "valid_to", "type": "utc_datetime"}, {"allow_nil?": true, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "metadata", "type": "map"}, {"allow_nil?": false, "default": "\"active\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "text"}, {"allow_nil?": false, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "used_count", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": "channel_id", "global": true, "strategy": "attribute"}, "name": "cdkey_activities_used_by_user_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "users"}, "scale": null, "size": null, "source": "used_by_user_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "7F1EE2CCE3DFB0DE2733860226639E0E6EE43C5257A6A062A8A5C46E8F5E2418", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "cdkey_activities_unique_code_index", "keys": [{"type": "atom", "value": "code"}], "name": "unique_code", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "cdkey_activities"}