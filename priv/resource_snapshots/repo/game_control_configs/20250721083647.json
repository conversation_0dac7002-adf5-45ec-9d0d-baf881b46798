{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "game_id", "type": "bigint"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "game_type", "type": "bigint"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "game_name", "type": "text"}, {"allow_nil?": false, "default": "\"1000000\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "base_inventory", "type": "decimal"}, {"allow_nil?": false, "default": "500", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "control_weight", "type": "bigint"}, {"allow_nil?": false, "default": "-50", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "dark_tax_rate", "type": "bigint"}, {"allow_nil?": false, "default": "\"0.05\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "winner_tax_rate", "type": "decimal"}, {"allow_nil?": false, "default": "30000", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "collect_line_max", "type": "bigint"}, {"allow_nil?": false, "default": "5000", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "collect_line_min", "type": "bigint"}, {"allow_nil?": false, "default": "\"0.2\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "collect_line_ratio", "type": "decimal"}, {"allow_nil?": false, "default": "\"0.7\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "pre_collect_line_ratio", "type": "decimal"}, {"allow_nil?": false, "default": "30000", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "release_line_max", "type": "bigint"}, {"allow_nil?": false, "default": "5000", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "release_line_min", "type": "bigint"}, {"allow_nil?": false, "default": "\"0.2\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "release_line_ratio", "type": "decimal"}, {"allow_nil?": false, "default": "\"0.7\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "pre_release_line_ratio", "type": "decimal"}, {"allow_nil?": false, "default": "33", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "long_weight", "type": "bigint"}, {"allow_nil?": false, "default": "33", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "hu_weight", "type": "bigint"}, {"allow_nil?": false, "default": "34", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "he_weight", "type": "bigint"}, {"allow_nil?": false, "default": "true", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "is_active", "type": "boolean"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "FA039CC68BAED1E1FEBDFC90EC98D835852DEFDAD8804EB2EF92FF3E08B6EA6A", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "game_control_configs_unique_game_and_type_index", "keys": [{"type": "atom", "value": "game_id"}, {"type": "atom", "value": "game_type"}], "name": "unique_game_and_type", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "game_control_configs"}