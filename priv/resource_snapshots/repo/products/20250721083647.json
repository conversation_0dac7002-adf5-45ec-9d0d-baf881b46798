{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "product_type", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "category", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "sku", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "price", "type": "decimal"}, {"allow_nil?": false, "default": "\"inr\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "currency", "type": "text"}, {"allow_nil?": false, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "product_config", "type": "map"}, {"allow_nil?": true, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "display_config", "type": "map"}, {"allow_nil?": false, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "sort_order", "type": "bigint"}, {"allow_nil?": false, "default": "\"active\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "created_at", "type": "utc_datetime"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "4EC644AB736D38FE77819854CD7B0BCEA2A547E739A4AC469E779F9AD7A0BF2C", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "products_unique_sku_index", "keys": [{"type": "atom", "value": "sku"}], "name": "unique_sku", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "products"}