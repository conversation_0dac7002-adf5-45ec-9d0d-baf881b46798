{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "channel_id", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "channel_name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "package_name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": true, "default": "%{}", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "config", "type": "map"}, {"allow_nil?": false, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "D6A79915BBFE81C28626C90194361A5C8BF73BA20F48BC21AF76DA7C314E12C6", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "channels_unique_channel_id_index", "keys": [{"type": "atom", "value": "channel_id"}], "name": "unique_channel_id", "nils_distinct?": true, "where": null}, {"all_tenants?": false, "base_filter": null, "index_name": "channels_unique_package_name_index", "keys": [{"type": "atom", "value": "package_name"}], "name": "unique_package_name", "nils_distinct?": true, "where": "package_name IS NOT NULL"}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "channels"}