defmodule Cypridina.Repo.Migrations.AddActivitySystemFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:scratch_card_activities) do
      add :card_type, :text, null: false, default: "option1"
      add :cost_amount, :decimal, null: false, default: "0"
      add :min_reward, :decimal, null: false, default: "0"
      add :max_reward, :decimal, null: false, default: "0"
      add :daily_limit, :bigint, null: false, default: 1
      add :is_active, :boolean, null: false, default: true
    end

    alter table(:first_recharge_gifts) do
      add :min_recharge_amount, :decimal, null: false, default: "0"
      add :reward_type, :text, null: false, default: "coins"
      add :bonus_multiplier, :decimal, null: false, default: "1"
      add :time_limit_hours, :bigint
      add :description, :text
      add :is_active, :boolean, null: false, default: true
    end

    alter table(:loss_rebate_jars) do
      add :calculation_period, :text, null: false, default: "daily"
      add :rebate_type, :text, null: false, default: "coins"
      add :auto_distribute, :boolean, null: false, default: false
      add :is_active, :boolean, null: false, default: true
    end

    alter table(:binding_rewards) do
      add :reward_type, :text, null: false, default: "coins"
      add :one_time_only, :boolean, null: false, default: true
      add :verification_required, :boolean, null: false, default: true
      add :description, :text
      add :is_active, :boolean, null: false, default: true
    end

    alter table(:users) do
      add :permission_level, :bigint, null: false, default: 0
    end
  end

  def down do
    alter table(:users) do
      remove :permission_level
    end

    alter table(:binding_rewards) do
      remove :is_active
      remove :description
      remove :verification_required
      remove :one_time_only
      remove :reward_type
    end

    alter table(:loss_rebate_jars) do
      remove :is_active
      remove :auto_distribute
      remove :rebate_type
      remove :calculation_period
    end

    alter table(:first_recharge_gifts) do
      remove :is_active
      remove :description
      remove :time_limit_hours
      remove :bonus_multiplier
      remove :reward_type
      remove :min_recharge_amount
    end

    alter table(:scratch_card_activities) do
      remove :is_active
      remove :daily_limit
      remove :max_reward
      remove :min_reward
      remove :cost_amount
      remove :card_type
    end
  end
end
