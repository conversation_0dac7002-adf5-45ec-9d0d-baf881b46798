defmodule Cypridina.Repo.Migrations.AddMissingGameTaskColumns do
  use Ecto.Migration

  def change do
    # 检查并添加缺失的列
    alter table(:game_tasks) do
      # 只添加不存在的列
      add_if_not_exists :reward_type, :text, default: "coins"
      add_if_not_exists :target_value, :bigint, default: 1
      add_if_not_exists :game_config_id, :uuid
      add_if_not_exists :is_active, :boolean, default: true
      add_if_not_exists :start_date, :date, default: fragment("CURRENT_DATE")
      add_if_not_exists :end_date, :date
    end

    # 修改现有列的数据类型（如果需要）
    execute """
    DO $$
    BEGIN
      -- 检查并修改 game_id 列类型
      IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'game_tasks'
        AND column_name = 'game_id'
        AND data_type = 'text'
      ) THEN
        ALTER TABLE game_tasks ALTER COLUMN game_id TYPE bigint USING game_id::bigint;
      END IF;
    END $$;
    """
  end
end
