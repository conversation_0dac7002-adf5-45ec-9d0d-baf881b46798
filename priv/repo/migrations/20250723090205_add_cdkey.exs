defmodule Cypridina.Repo.Migrations.AddCdkey do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop constraint(:cdkey_claim_records, "cdkey_claim_records_cdkey_id_fkey")

    alter table(:cdkey_claim_records) do
      add :cdkey_code, :text, null: false
    end

    create table(:user_roles, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :user_id,
          references(:users,
            column: :id,
            name: "user_roles_user_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          primary_key: true,
          null: false

      add :role_id,
          references(:roles,
            column: :id,
            name: "user_roles_role_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          primary_key: true,
          null: false
    end

    create unique_index(:user_roles, [:user_id, :role_id],
             name: "user_roles_unique_user_role_index"
           )

    create table(:cdkeys, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:cdkey_claim_records) do
      modify :cdkey_id,
             references(:cdkeys,
               column: :id,
               name: "cdkey_claim_records_cdkey_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    create unique_index(:cdkey_claim_records, [:user_id, :cdkey_code],
             name: "cdkey_claim_records_unique_user_cdkey_code_index"
           )

    alter table(:cdkeys) do
      add :code, :text, null: false
      add :max_uses, :bigint, null: false, default: 1
      add :rewards, {:array, :map}, null: false, default: []
      add :valid_from, :utc_datetime, null: false
      add :valid_to, :utc_datetime, null: false
      add :creator, :text, null: false
      add :status, :text, null: false, default: "active"
      add :used_count, :bigint, null: false, default: 0

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:cdkeys, [:code], name: "cdkeys_unique_code_index")

    alter table(:users) do
      remove :permission_level
    end
  end

  def down do
    alter table(:users) do
      add :permission_level, :bigint, null: false, default: 0
    end

    drop_if_exists unique_index(:cdkeys, [:code], name: "cdkeys_unique_code_index")

    alter table(:cdkeys) do
      remove :updated_at
      remove :inserted_at
      remove :used_count
      remove :status
      remove :creator
      remove :valid_to
      remove :valid_from
      remove :rewards
      remove :max_uses
      remove :code
    end

    drop_if_exists unique_index(:cdkey_claim_records, [:user_id, :cdkey_code],
                     name: "cdkey_claim_records_unique_user_cdkey_code_index"
                   )

    drop constraint(:cdkey_claim_records, "cdkey_claim_records_cdkey_id_fkey")

    alter table(:cdkey_claim_records) do
      modify :cdkey_id,
             references(:cdkey_activities,
               column: :id,
               name: "cdkey_claim_records_cdkey_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    drop table(:cdkeys)

    drop_if_exists unique_index(:user_roles, [:user_id, :role_id],
                     name: "user_roles_unique_user_role_index"
                   )

    drop constraint(:user_roles, "user_roles_user_id_fkey")

    drop constraint(:user_roles, "user_roles_role_id_fkey")

    drop table(:user_roles)

    alter table(:cdkey_claim_records) do
      remove :cdkey_code
    end
  end
end
