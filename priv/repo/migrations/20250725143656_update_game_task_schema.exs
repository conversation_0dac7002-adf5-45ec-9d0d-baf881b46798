defmodule Cypridina.Repo.Migrations.UpdateGameTaskSchema do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    # 先修改数据类型
    execute "ALTER TABLE game_tasks ALTER COLUMN game_id TYPE bigint USING game_id::bigint"
    
    alter table(:game_tasks) do
      modify :task_type, :text, default: "game_rounds"
      modify :game_name, :text, default: "未知游戏名称"
      modify :game_id, :bigint, default: 1
      modify :task_name, :text, default: "新游戏任务"
      add :target_value, :bigint, default: 1
      add :reward_type, :text, default: "coins"
      add :game_config_id, :uuid
      add :is_active, :boolean, default: true
      add :start_date, :date, default: fragment("CURRENT_DATE")
      add :end_date, :date
    end

    create table(:user_game_task_progress, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :user_id, references(:users, column: :id, type: :uuid), null: false
      add :game_task_id, references(:game_tasks, column: :id, type: :uuid), null: false
      add :current_progress, :bigint, null: false, default: 0
      add :is_completed, :boolean, null: false, default: false
      add :reward_claimed, :boolean, null: false, default: false
      add :claimed_at, :utc_datetime
      add :task_date, :date, null: false, default: fragment("CURRENT_DATE")
      add :inserted_at, :utc_datetime_usec, null: false, default: fragment("(now() AT TIME ZONE 'utc')")
      add :updated_at, :utc_datetime_usec, null: false, default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:user_game_task_progress, [:user_id, :game_task_id, :task_date])
  end

  def down do
    drop_if_exists unique_index(:user_game_task_progress, [:user_id, :game_task_id, :task_date])
    drop table(:user_game_task_progress)
    
    alter table(:game_tasks) do
      remove :end_date
      remove :start_date
      remove :is_active
      remove :game_config_id
      remove :reward_type
      remove :target_value
      modify :task_name, :text, default: nil
      modify :game_name, :text, default: nil
      modify :task_type, :text, default: nil
    end
    
    # 恢复原始数据类型
    execute "ALTER TABLE game_tasks ALTER COLUMN game_id TYPE text USING game_id::text"
  end
end
