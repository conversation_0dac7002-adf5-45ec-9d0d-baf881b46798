# 商店系统初始化数据
# 运行方式: mix run priv/repo/seeds/shop_system_seeds.exs

alias Teen.ShopSystem.{Product, ProductTemplate}

IO.puts("🛍️ 开始初始化商店系统数据...")

# 辅助函数：安全创建记录（检查是否已存在）
safe_create = fn module, attrs, unique_field ->
  unique_value = Map.get(attrs, unique_field)

  # 首先尝试查找已存在的记录
  case apply(module, :read, []) do
    {:ok, records} ->
      existing = Enum.find(records, fn r ->
        Map.get(r, unique_field) == unique_value
      end)

      if existing do
        {:exists, existing}
      else
        apply(module, :create, [attrs])
      end
    _ ->
      apply(module, :create, [attrs])
  end
end

# ==================== 创建商品模板 ====================

IO.puts("📋 创建商品模板...")

# 月卡模板
monthly_card_template = %{
  template_name: "标准月卡模板",
  product_type: :monthly_card,
  is_default: true,
  default_config: %{
    "daily_reward" => 1000,                 # 保持整数
    "total_days" => 30,
    "card_benefits" => [
      "每日登录获得游戏币",
      "VIP特权加成",
      "专属客服服务"
    ]
  },
  config_schema: %{
    "daily_reward" => %{
      "type" => "number",
      "description" => "每日奖励金额（游戏币）",
      "min" => 100,
      "max" => 10000,
      "default" => 1000                     # 保持整数
    },
    "total_days" => %{
      "type" => "number",
      "description" => "卡片有效天数",
      "min" => 1,
      "max" => 365,
      "default" => 30
    }
  }
}

case safe_create.(ProductTemplate, monthly_card_template, :template_name) do
  {:ok, template} -> IO.puts("✅ 月卡模板创建成功")
  {:exists, template} -> IO.puts("ℹ️ 月卡模板已存在")
  {:error, reason} -> IO.puts("❌ 月卡模板创建失败: #{inspect(reason)}")
end

# 周卡模板
weekly_card_template = %{
  template_name: "标准周卡模板",
  product_type: :weekly_card,
  is_default: true,
  default_config: %{
    "daily_reward" => 500,
    "total_days" => 7,
    "card_benefits" => [
      "每日登录获得游戏币",
      "周卡专属活动"
    ]
  },
  config_schema: %{
    "daily_reward" => %{
      "type" => "number",
      "description" => "每日奖励金额（游戏币）",
      "min" => 100,
      "max" => 5000,
      "default" => 500
    }
  }
}

case safe_create.(ProductTemplate, weekly_card_template, :template_name) do
  {:ok, template} -> IO.puts("✅ 周卡模板创建成功")
  {:exists, template} -> IO.puts("ℹ️ 周卡模板已存在")
  {:error, reason} -> IO.puts("❌ 周卡模板创建失败: #{inspect(reason)}")
end

# 次卡模板
play_card_template = %{
  template_name: "标准次卡模板",
  product_type: :play_card,
  is_default: true,
  default_config: %{
    "play_count" => 30,
    "bonus_multiplier" => 1.2,
    "valid_days" => 30
  },
  config_schema: %{
    "play_count" => %{
      "type" => "number",
      "description" => "游戏次数",
      "min" => 1,
      "max" => 1000,
      "default" => 30
    },
    "bonus_multiplier" => %{
      "type" => "number",
      "description" => "奖励倍率",
      "min" => 1.0,
      "max" => 3.0,
      "default" => 1.2
    }
  }
}

case safe_create.(ProductTemplate, play_card_template, :template_name) do
  {:ok, template} -> IO.puts("✅ 次卡模板创建成功")
  {:exists, template} -> IO.puts("ℹ️ 次卡模板已存在")
  {:error, reason} -> IO.puts("❌ 次卡模板创建失败: #{inspect(reason)}")
end

# 金币礼包模板
coin_package_template = %{
  template_name: "标准金币礼包模板",
  product_type: :coin_package,
  is_default: true,
  default_config: %{
    "coin_amount" => 10000,
    "bonus_percentage" => 20,
    "instant_delivery" => true
  },
  config_schema: %{
    "coin_amount" => %{
      "type" => "number",
      "description" => "游戏币数量",
      "min" => 1000,
      "max" => 1000000,
      "default" => 10000
    },
    "bonus_percentage" => %{
      "type" => "number",
      "description" => "额外奖励百分比",
      "min" => 0,
      "max" => 100,
      "default" => 20
    }
  }
}

case safe_create.(ProductTemplate, coin_package_template, :template_name) do
  {:ok, template} -> IO.puts("✅ 金币礼包模板创建成功")
  {:exists, template} -> IO.puts("ℹ️ 金币礼包模板已存在")
  {:error, reason} -> IO.puts("❌ 金币礼包模板创建失败: #{inspect(reason)}")
end

# VIP礼包模板
vip_package_template = %{
  template_name: "标准VIP礼包模板",
  product_type: :vip_package,
  is_default: true,
  default_config: %{
    "vip_level" => 1,
    "vip_duration" => 30,
    "included_items" => [
      %{"type" => "coins", "amount" => 5000},
      %{"type" => "play_times", "amount" => 10}
    ]
  },
  config_schema: %{
    "vip_level" => %{
      "type" => "number",
      "description" => "VIP等级",
      "min" => 1,
      "max" => 10,
      "default" => 1
    },
    "vip_duration" => %{
      "type" => "number",
      "description" => "VIP时长（天）",
      "min" => 1,
      "max" => 365,
      "default" => 30
    }
  }
}

case safe_create.(ProductTemplate, vip_package_template, :template_name) do
  {:ok, template} -> IO.puts("✅ VIP礼包模板创建成功")
  {:exists, template} -> IO.puts("ℹ️ VIP礼包模板已存在")
  {:error, reason} -> IO.puts("❌ VIP礼包模板创建失败: #{inspect(reason)}")
end

# 充值奖励包模板
recharge_bonus_template = %{
  template_name: "标准充值奖励包模板",
  product_type: :recharge_bonus,
  is_default: true,
  default_config: %{
    "bonus_amount" => 2000,
    "bonus_type" => "coins",
    "requires_claim" => true,
    "expires_in_days" => 7
  },
  config_schema: %{
    "bonus_amount" => %{
      "type" => "number",
      "description" => "奖励金额",
      "min" => 100,
      "max" => 100000,
      "default" => 2000
    },
    "expires_in_days" => %{
      "type" => "number",
      "description" => "过期天数",
      "min" => 1,
      "max" => 30,
      "default" => 7
    }
  }
}

case safe_create.(ProductTemplate, recharge_bonus_template, :template_name) do
  {:ok, template} -> IO.puts("✅ 充值奖励包模板创建成功")
  {:exists, template} -> IO.puts("ℹ️ 充值奖励包模板已存在")
  {:error, reason} -> IO.puts("❌ 充值奖励包模板创建失败: #{inspect(reason)}")
end

IO.puts("✅ 商品模板创建完成")

# ==================== 创建示例商品 ====================

IO.puts("🛍️ 创建示例商品...")

# 月卡系列
monthly_cards = [
  %{
    name: "新手月卡",
    description: "适合新手玩家的月卡，30天内每日可领取800游戏币",
    product_type: :monthly_card,
    category: "卡类商品",
    sku: "MONTHLY_CARD_NEWBIE",
    price: Decimal.new("1999"),  # 19.99 INR
    currency: :inr,
    product_config: %{
      "daily_reward" => 800,
      "total_days" => 30,
      "card_benefits" => ["每日登录获得800游戏币", "新手专属福利"]
    },
    display_config: %{
      "icon" => "monthly_card_newbie.png",
      "color" => "#90EE90",
      "badge" => "新手"
    },
    sort_order: 1
  },
  %{
    name: "豪华月卡",
    description: "高级玩家首选，30天内每日可领取1500游戏币",
    product_type: :monthly_card,
    category: "卡类商品",
    sku: "MONTHLY_CARD_LUXURY",
    price: Decimal.new("4999"),  # 49.99 INR
    currency: :inr,
    product_config: %{
      "daily_reward" => 1500,
      "total_days" => 30,
      "card_benefits" => ["每日登录获得1500游戏币", "VIP特权加成", "专属客服服务"]
    },
    display_config: %{
      "icon" => "monthly_card_luxury.png",
      "color" => "#FFD700",
      "badge" => "热门"
    },
    sort_order: 2
  },
  %{
    name: "至尊月卡",
    description: "顶级月卡体验，30天内每日可领取2500游戏币",
    product_type: :monthly_card,
    category: "卡类商品",
    sku: "MONTHLY_CARD_SUPREME",
    price: Decimal.new("9999"),  # 99.99 INR
    currency: :inr,
    product_config: %{
      "daily_reward" => 2500,
      "total_days" => 30,
      "card_benefits" => ["每日登录获得2500游戏币", "至尊VIP特权", "专属活动", "优先客服"]
    },
    display_config: %{
      "icon" => "monthly_card_supreme.png",
      "color" => "#FF6347",
      "badge" => "至尊"
    },
    sort_order: 3
  }
]

Enum.each(monthly_cards, fn card_data ->
  case safe_create.(Product, card_data, :sku) do
    {:ok, product} -> IO.puts("✅ 创建月卡商品成功: #{product.name}")
    {:exists, product} -> IO.puts("ℹ️ 月卡商品已存在: #{product.name}")
    {:error, reason} -> IO.puts("❌ 创建月卡商品失败: #{card_data.name}, 原因: #{inspect(reason)}")
  end
end)

# 周卡系列
weekly_cards = [
  %{
    name: "精品周卡",
    description: "7天内每日可领取500游戏币",
    product_type: :weekly_card,
    category: "卡类商品",
    sku: "WEEKLY_CARD_PREMIUM",
    price: Decimal.new("999"),   # 9.99 INR
    currency: :inr,
    product_config: %{
      "daily_reward" => 500,
      "total_days" => 7,
      "card_benefits" => ["每日登录获得500游戏币", "周卡专属活动"]
    },
    display_config: %{
      "icon" => "weekly_card_premium.png",
      "color" => "#87CEEB",
      "badge" => "推荐"
    },
    sort_order: 4
  },
  %{
    name: "豪华周卡",
    description: "7天内每日可领取800游戏币",
    product_type: :weekly_card,
    category: "卡类商品",
    sku: "WEEKLY_CARD_LUXURY",
    price: Decimal.new("1599"),  # 15.99 INR
    currency: :inr,
    product_config: %{
      "daily_reward" => 800,
      "total_days" => 7,
      "card_benefits" => ["每日登录获得800游戏币", "豪华周卡特权", "专属活动"]
    },
    display_config: %{
      "icon" => "weekly_card_luxury.png",
      "color" => "#DDA0DD",
      "badge" => "超值"
    },
    sort_order: 5
  }
]

Enum.each(weekly_cards, fn card_data ->
  case safe_create.(Product, card_data, :sku) do
    {:ok, product} -> IO.puts("✅ 创建周卡商品成功: #{product.name}")
    {:exists, product} -> IO.puts("ℹ️ 周卡商品已存在: #{product.name}")
    {:error, reason} -> IO.puts("❌ 创建周卡商品失败: #{card_data.name}, 原因: #{inspect(reason)}")
  end
end)

# 次卡系列
play_cards = [
  %{
    name: "10次游戏卡",
    description: "获得10次游戏机会，奖励倍率1.1倍",
    product_type: :play_card,
    category: "卡类商品",
    sku: "PLAY_CARD_10",
    price: Decimal.new("799"),   # 7.99 INR
    currency: :inr,
    product_config: %{
      "play_count" => 10,
      "bonus_multiplier" => 1.1,
      "valid_days" => 15
    },
    display_config: %{
      "icon" => "play_card_10.png",
      "color" => "#98FB98"
    },
    sort_order: 6
  },
  %{
    name: "30次游戏卡",
    description: "获得30次游戏机会，奖励倍率1.2倍",
    product_type: :play_card,
    category: "卡类商品",
    sku: "PLAY_CARD_30",
    price: Decimal.new("1999"),  # 19.99 INR
    currency: :inr,
    product_config: %{
      "play_count" => 30,
      "bonus_multiplier" => 1.2,
      "valid_days" => 30
    },
    display_config: %{
      "icon" => "play_card_30.png",
      "color" => "#98FB98",
      "badge" => "热门"
    },
    sort_order: 7
  },
  %{
    name: "100次游戏卡",
    description: "获得100次游戏机会，奖励倍率1.5倍",
    product_type: :play_card,
    category: "卡类商品",
    sku: "PLAY_CARD_100",
    price: Decimal.new("5999"),  # 59.99 INR
    currency: :inr,
    product_config: %{
      "play_count" => 100,
      "bonus_multiplier" => 1.5,
      "valid_days" => 60
    },
    display_config: %{
      "icon" => "play_card_100.png",
      "color" => "#FFD700",
      "badge" => "超值"
    },
    sort_order: 8
  }
]

Enum.each(play_cards, fn card_data ->
  case safe_create.(Product, card_data, :sku) do
    {:ok, product} -> IO.puts("✅ 创建次卡商品成功: #{product.name}")
    {:exists, product} -> IO.puts("ℹ️ 次卡商品已存在: #{product.name}")
    {:error, reason} -> IO.puts("❌ 创建次卡商品失败: #{card_data.name}, 原因: #{inspect(reason)}")
  end
end)

# 金币礼包系列
coin_packages = [
  %{
    name: "5000金币礼包",
    description: "立即获得5000游戏币+10%额外奖励",
    product_type: :coin_package,
    category: "金币商品",
    sku: "COIN_PACKAGE_5K",
    price: Decimal.new("2499"),  # 24.99 INR
    currency: :inr,
    product_config: %{
      "coin_amount" => 5000,
      "bonus_percentage" => 10,
      "instant_delivery" => true
    },
    display_config: %{
      "icon" => "coin_package_5k.png",
      "color" => "#FFD700"
    },
    sort_order: 9
  },
  %{
    name: "10000金币礼包",
    description: "立即获得10000游戏币+20%额外奖励",
    product_type: :coin_package,
    category: "金币商品",
    sku: "COIN_PACKAGE_10K",
    price: Decimal.new("4999"),  # 49.99 INR
    currency: :inr,
    product_config: %{
      "coin_amount" => 10000,
      "bonus_percentage" => 20,
      "instant_delivery" => true
    },
    display_config: %{
      "icon" => "coin_package_10k.png",
      "color" => "#FFD700",
      "badge" => "热门"
    },
    sort_order: 10
  },
  %{
    name: "50000金币礼包",
    description: "立即获得50000游戏币+30%额外奖励",
    product_type: :coin_package,
    category: "金币商品",
    sku: "COIN_PACKAGE_50K",
    price: Decimal.new("19999"), # 199.99 INR
    currency: :inr,
    product_config: %{
      "coin_amount" => 50000,
      "bonus_percentage" => 30,
      "instant_delivery" => true
    },
    display_config: %{
      "icon" => "coin_package_50k.png",
      "color" => "#FF6347",
      "badge" => "超值"
    },
    sort_order: 11
  },
  %{
    name: "100000金币礼包",
    description: "立即获得100000游戏币+50%额外奖励",
    product_type: :coin_package,
    category: "金币商品",
    sku: "COIN_PACKAGE_100K",
    price: Decimal.new("39999"), # 399.99 INR
    currency: :inr,
    product_config: %{
      "coin_amount" => 100000,
      "bonus_percentage" => 50,
      "instant_delivery" => true
    },
    display_config: %{
      "icon" => "coin_package_100k.png",
      "color" => "#8A2BE2",
      "badge" => "至尊"
    },
    sort_order: 12
  }
]

Enum.each(coin_packages, fn package_data ->
  case safe_create.(Product, package_data, :sku) do
    {:ok, product} -> IO.puts("✅ 创建金币礼包成功: #{product.name}")
    {:exists, product} -> IO.puts("ℹ️ 金币礼包已存在: #{product.name}")
    {:error, reason} -> IO.puts("❌ 创建金币礼包失败: #{package_data.name}, 原因: #{inspect(reason)}")
  end
end)

# VIP礼包系列
vip_packages = [
  %{
    name: "VIP1礼包",
    description: "升级到VIP1，享受30天特权+5000游戏币+10次游戏",
    product_type: :vip_package,
    category: "VIP商品",
    sku: "VIP_PACKAGE_1",
    price: Decimal.new("9999"),  # 99.99 INR
    currency: :inr,
    product_config: %{
      "vip_level" => 1,
      "vip_duration" => 30,
      "included_items" => [
        %{"type" => "coins", "amount" => 5000},
        %{"type" => "play_times", "amount" => 10}
      ]
    },
    display_config: %{
      "icon" => "vip_package_1.png",
      "color" => "#FF6347",
      "badge" => "VIP"
    },
    sort_order: 13
  },
  %{
    name: "VIP2礼包",
    description: "升级到VIP2，享受60天特权+15000游戏币+30次游戏",
    product_type: :vip_package,
    category: "VIP商品",
    sku: "VIP_PACKAGE_2",
    price: Decimal.new("24999"), # 249.99 INR
    currency: :inr,
    product_config: %{
      "vip_level" => 2,
      "vip_duration" => 60,
      "included_items" => [
        %{"type" => "coins", "amount" => 15000},
        %{"type" => "play_times", "amount" => 30}
      ]
    },
    display_config: %{
      "icon" => "vip_package_2.png",
      "color" => "#8A2BE2",
      "badge" => "VIP2"
    },
    sort_order: 14
  },
  %{
    name: "VIP3礼包",
    description: "升级到VIP3，享受90天特权+50000游戏币+100次游戏",
    product_type: :vip_package,
    category: "VIP商品",
    sku: "VIP_PACKAGE_3",
    price: Decimal.new("49999"), # 499.99 INR
    currency: :inr,
    product_config: %{
      "vip_level" => 3,
      "vip_duration" => 90,
      "included_items" => [
        %{"type" => "coins", "amount" => 50000},
        %{"type" => "play_times", "amount" => 100}
      ]
    },
    display_config: %{
      "icon" => "vip_package_3.png",
      "color" => "#FFD700",
      "badge" => "至尊VIP"
    },
    sort_order: 15
  }
]

Enum.each(vip_packages, fn package_data ->
  case safe_create.(Product, package_data, :sku) do
    {:ok, product} -> IO.puts("✅ 创建VIP礼包成功: #{product.name}")
    {:exists, product} -> IO.puts("ℹ️ VIP礼包已存在: #{product.name}")
    {:error, reason} -> IO.puts("❌ 创建VIP礼包失败: #{package_data.name}, 原因: #{inspect(reason)}")
  end
end)

# 特殊道具系列
special_items = [
  %{
    name: "幸运符",
    description: "7天内游戏幸运值提升20%",
    product_type: :special_item,
    category: "道具商品",
    sku: "LUCKY_CHARM_7D",
    price: Decimal.new("1499"),  # 14.99 INR
    currency: :inr,
    product_config: %{
      "item_type" => "lucky_charm",
      "item_count" => 1,
      "item_duration" => 7,
      "item_effects" => [
        %{"type" => "luck_boost", "value" => 20}
      ]
    },
    display_config: %{
      "icon" => "lucky_charm.png",
      "color" => "#32CD32"
    },
    sort_order: 16
  },
  %{
    name: "经验加速器",
    description: "3天内经验获得提升50%",
    product_type: :special_item,
    category: "道具商品",
    sku: "EXP_BOOSTER_3D",
    price: Decimal.new("999"),   # 9.99 INR
    currency: :inr,
    product_config: %{
      "item_type" => "exp_booster",
      "item_count" => 1,
      "item_duration" => 3,
      "item_effects" => [
        %{"type" => "exp_boost", "value" => 50}
      ]
    },
    display_config: %{
      "icon" => "exp_booster.png",
      "color" => "#1E90FF"
    },
    sort_order: 17
  },
  %{
    name: "金币倍增器",
    description: "5天内金币获得提升100%",
    product_type: :special_item,
    category: "道具商品",
    sku: "COIN_MULTIPLIER_5D",
    price: Decimal.new("1999"),  # 19.99 INR
    currency: :inr,
    product_config: %{
      "item_type" => "coin_multiplier",
      "item_count" => 1,
      "item_duration" => 5,
      "item_effects" => [
        %{"type" => "coin_boost", "value" => 100}
      ]
    },
    display_config: %{
      "icon" => "coin_multiplier.png",
      "color" => "#FFD700",
      "badge" => "热门"
    },
    sort_order: 18
  }
]

Enum.each(special_items, fn item_data ->
  case safe_create.(Product, item_data, :sku) do
    {:ok, product} -> IO.puts("✅ 创建特殊道具成功: #{product.name}")
    {:exists, product} -> IO.puts("ℹ️ 特殊道具已存在: #{product.name}")
    {:error, reason} -> IO.puts("❌ 创建特殊道具失败: #{item_data.name}, 原因: #{inspect(reason)}")
  end
end)

# 充值奖励包系列
recharge_bonuses = [
  %{
    name: "首充奖励包",
    description: "首次充值专享，额外获得2000游戏币奖励",
    product_type: :recharge_bonus,
    category: "限时商品",
    sku: "FIRST_RECHARGE_BONUS",
    price: Decimal.new("999"),   # 9.99 INR
    currency: :inr,
    product_config: %{
      "bonus_amount" => 2000,
      "bonus_type" => "coins",
      "requires_claim" => true,
      "expires_in_days" => 7
    },
    display_config: %{
      "icon" => "first_recharge_bonus.png",
      "color" => "#FF69B4",
      "badge" => "首充"
    },
    sort_order: 19
  },
  %{
    name: "每日充值奖励包",
    description: "每日充值专享，额外获得1000游戏币奖励",
    product_type: :recharge_bonus,
    category: "限时商品",
    sku: "DAILY_RECHARGE_BONUS",
    price: Decimal.new("499"),   # 4.99 INR
    currency: :inr,
    product_config: %{
      "bonus_amount" => 1000,
      "bonus_type" => "coins",
      "requires_claim" => true,
      "expires_in_days" => 1
    },
    display_config: %{
      "icon" => "daily_recharge_bonus.png",
      "color" => "#FFA500",
      "badge" => "每日"
    },
    sort_order: 20
  }
]

Enum.each(recharge_bonuses, fn bonus_data ->
  case safe_create.(Product, bonus_data, :sku) do
    {:ok, product} -> IO.puts("✅ 创建充值奖励包成功: #{product.name}")
    {:exists, product} -> IO.puts("ℹ️ 充值奖励包已存在: #{product.name}")
    {:error, reason} -> IO.puts("❌ 创建充值奖励包失败: #{bonus_data.name}, 原因: #{inspect(reason)}")
  end
end)

IO.puts("✅ 示例商品创建完成")

# ==================== 统计信息 ====================

IO.puts("")
IO.puts("📊 商店系统初始化统计:")

case Product.read() do
  {:ok, products} ->
    total_products = length(products)
    active_products = Enum.count(products, &(&1.status == :active))

    type_counts = products
    |> Enum.group_by(& &1.product_type)
    |> Enum.map(fn {type, products} -> {type, length(products)} end)
    |> Enum.into(%{})

    IO.puts("  • 商品总数: #{total_products}")
    IO.puts("  • 上架商品: #{active_products}")
    IO.puts("  • 月卡商品: #{Map.get(type_counts, :monthly_card, 0)}")
    IO.puts("  • 周卡商品: #{Map.get(type_counts, :weekly_card, 0)}")
    IO.puts("  • 次卡商品: #{Map.get(type_counts, :play_card, 0)}")
    IO.puts("  • 金币礼包: #{Map.get(type_counts, :coin_package, 0)}")
    IO.puts("  • VIP礼包: #{Map.get(type_counts, :vip_package, 0)}")
    IO.puts("  • 特殊道具: #{Map.get(type_counts, :special_item, 0)}")
    IO.puts("  • 充值奖励包: #{Map.get(type_counts, :recharge_bonus, 0)}")
  {:error, _} ->
    IO.puts("  • 无法获取商品统计信息")
end

case ProductTemplate.read() do
  {:ok, templates} ->
    IO.puts("  • 商品模板: #{length(templates)}")
  {:error, _} ->
    IO.puts("  • 无法获取模板统计信息")
end

IO.puts("")
IO.puts("🎉 商店系统初始化完成！")
IO.puts("")
IO.puts("📋 后台管理地址:")
IO.puts("  • 商品管理: /admin/products")
IO.puts("  • 商品模板: /admin/product-templates")
IO.puts("  • 购买记录: /admin/user-purchases")
IO.puts("")
IO.puts("🔗 API接口:")
IO.puts("  • 商品列表: GET /api/shop/products")
IO.puts("  • 商品详情: GET /api/shop/products/:id")
IO.puts("  • 创建订单: POST /api/shop/purchase")
IO.puts("  • 支付回调: POST /api/shop/payment_callback")
IO.puts("  • 购买历史: GET /api/shop/purchases")
