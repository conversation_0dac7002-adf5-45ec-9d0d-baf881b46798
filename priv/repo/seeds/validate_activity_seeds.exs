# 验证活动系统种子数据
# 运行方式: mix run priv/repo/seeds/validate_activity_seeds.exs

alias Teen.ActivitySystem.{
  FirstRechargeGift,
  LossRebateJar,
  ScratchCardActivity,
  WeeklyCard,
  BindingReward
}

IO.puts("🔍 开始验证活动系统配置...")

# 验证首充礼包
IO.puts("\n📋 验证首充礼包配置...")
case FirstRechargeGift.read() do
  {:ok, gifts} ->
    IO.puts("✅ 首充礼包总数: #{length(gifts)}")
    Enum.each(gifts, fn gift ->
      IO.puts("  • #{gift.title} - 限制天数: #{gift.limit_days}, 奖励: #{gift.reward_coins}分")
    end)
  {:error, reason} ->
    IO.puts("❌ 首充礼包查询失败: #{inspect(reason)}")
end

# 验证亏损返利罐
IO.puts("\n📋 验证亏损返利罐配置...")
case LossRebateJar.read() do
  {:ok, jars} ->
    IO.puts("✅ 亏损返利罐总数: #{length(jars)}")
    Enum.each(jars, fn jar ->
      IO.puts("  • #{jar.title} - 阈值: #{jar.loss_threshold}分, 返利: #{jar.rebate_percentage}%")
    end)
  {:error, reason} ->
    IO.puts("❌ 亏损返利罐查询失败: #{inspect(reason)}")
end

# 验证刮刮卡活动
IO.puts("\n📋 验证刮刮卡活动配置...")
case ScratchCardActivity.read() do
  {:ok, activities} ->
    IO.puts("✅ 刮刮卡活动总数: #{length(activities)}")
    Enum.each(activities, fn activity ->
      IO.puts("  • #{activity.activity_title} - 可领取: #{activity.claimable_count}次, 概率: #{activity.reward_probability}%")
    end)
  {:error, reason} ->
    IO.puts("❌ 刮刮卡活动查询失败: #{inspect(reason)}")
end

# 验证周卡
IO.puts("\n📋 验证周卡配置...")
case WeeklyCard.read() do
  {:ok, cards} ->
    IO.puts("✅ 周卡总数: #{length(cards)}")
    Enum.each(cards, fn card ->
      IO.puts("  • #{card.title} - 充值: #{card.recharge_amount}分, 每日奖励: #{card.daily_reward}分")
    end)
  {:error, reason} ->
    IO.puts("❌ 周卡查询失败: #{inspect(reason)}")
end

# 验证绑定奖励
IO.puts("\n📋 验证绑定奖励配置...")
case BindingReward.read() do
  {:ok, rewards} ->
    IO.puts("✅ 绑定奖励总数: #{length(rewards)}")
    Enum.each(rewards, fn reward ->
      IO.puts("  • #{reward.title} - 类型: #{reward.binding_type}, 奖励: #{reward.reward_amount}分")
    end)
  {:error, reason} ->
    IO.puts("❌ 绑定奖励查询失败: #{inspect(reason)}")
end

IO.puts("\n✅ 活动系统配置验证完成！")
