# Real Payment Data Seeds for Indian Payment System (with cleanup)
# This file sets up actual payment gateways, banks, and configurations

alias Teen.PaymentSystem.{PaymentGateway, PaymentGateway, BankConfig, PaymentConfig}
alias Cypridina.Repo
import Ecto.Query

IO.puts("🧹 Cleaning existing payment data...")

# Clean existing data to avoid conflicts
Repo.delete_all(PaymentConfig)
Repo.delete_all(PaymentGateway)
Repo.delete_all(BankConfig)
Repo.delete_all(PaymentGateway)

IO.puts("✅ Existing data cleaned")

IO.puts("🚀 Seeding real payment data...")

# ==================== Payment Gateways ====================
IO.puts("Creating payment gateways...")

# Primary Gateway - Razorpay (Popular in India)
{:ok, razorpay_gateway} = PaymentGateway.create(%{
  name: "<PERSON>zorpay",
  gateway_type: "recharge",
  gateway_url: "https://api.razorpay.com/v1",
  channel_id: "razorpay_main",
  priority: 1,
  min_amount: Decimal.new("100"),  # ₹100 minimum
  max_amount: Decimal.new("500000"), # ₹5,00,000 maximum
  supported_currencies: ["INR"],
  enabled: true,
  config_data: %{
    "webhook_secret" => "your_webhook_secret",
    "timeout" => 300,
    "retry_count" => 3
  }
})

# Secondary Gateway - Paytm
{:ok, paytm_gateway} = PaymentGateway.create(%{
  name: "Paytm",
  gateway_type: "recharge",
  gateway_url: "https://securegw.paytm.in",
  channel_id: "paytm_main",
  priority: 2,
  min_amount: Decimal.new("100"),
  max_amount: Decimal.new("200000"),
  supported_currencies: ["INR"],
  enabled: true,
  config_data: %{
    "environment" => "production",
    "callback_url" => "/api/payment/callback/paytm"
  }
})

# PhonePe Gateway
{:ok, phonepe_gateway} = PaymentGateway.create(%{
  name: "PhonePe",
  gateway_type: "recharge",
  gateway_url: "https://api.phonepe.com/apis/hermes",
  channel_id: "phonepe_main",
  priority: 3,
  min_amount: Decimal.new("100"),
  max_amount: Decimal.new("100000"),
  supported_currencies: ["INR"],
  enabled: true,
  config_data: %{
    "environment" => "production",
    "callback_url" => "/api/payment/callback/phonepe"
  }
})

# Withdrawal Gateway - Bank Transfer
{:ok, _withdrawal_gateway} = PaymentGateway.create(%{
  name: "Bank Transfer",
  gateway_type: "withdraw",
  gateway_url: "https://api.banktransfer.com",
  channel_id: "bank_withdrawal",
  priority: 1,
  min_amount: Decimal.new("1000"),
  max_amount: Decimal.new("100000"),
  supported_currencies: ["INR"],
  enabled: true,
  config_data: %{
    "processing_time" => "24-48 hours",
    "verification_required" => true
  }
})

IO.puts("✅ Payment gateways created")

# ==================== Gateway Configurations ====================
IO.puts("Creating gateway configurations...")

# Razorpay Configuration
{:ok, _razorpay_config} = PaymentGateway.create(%{
  gateway_name: "Razorpay",
  gateway_code: "RAZORPAY",
  merchant_id: "rzp_live_merchantid",
  merchant_key: "your_razorpay_key_here",
  gateway_url: "https://api.razorpay.com/v1",
  create_order_path: "/orders",
  query_order_path: "/orders/{order_id}",
  callback_ip: "************", # Razorpay's IP for webhooks
  recharge_channel: "razorpay_recharge",
  withdrawal_channel: "razorpay_payout",
  supported_currencies: ["INR"],
  supported_payment_methods: ["UPI", "NetBanking", "Card", "Wallet"],
  fee_rate: Decimal.new("0.0236"), # 2.36% as decimal
  min_amount: Decimal.new("100"),
  max_amount: Decimal.new("500000"),
  status: "active",
  config_data: %{
    "webhook_url" => "/api/webhooks/razorpay",
    "auto_capture" => true,
    "payment_capture" => 1
  }
})

# Paytm Configuration
{:ok, _paytm_config} = PaymentGateway.create(%{
  gateway_name: "Paytm",
  gateway_code: "PAYTM",
  merchant_id: "PAYTM_MERCHANT_ID",
  merchant_key: "your_paytm_key_here",
  gateway_url: "https://securegw.paytm.in",
  create_order_path: "/order/process",
  query_order_path: "/order/status",
  callback_ip: "*************", # Paytm's IP for callbacks
  recharge_channel: "paytm_recharge",
  withdrawal_channel: "paytm_withdraw",
  supported_currencies: ["INR"],
  supported_payment_methods: ["UPI", "Wallet", "NetBanking", "Card"],
  fee_rate: Decimal.new("0.0199"), # 1.99% as decimal
  min_amount: Decimal.new("100"),
  max_amount: Decimal.new("200000"),
  status: "active",
  config_data: %{
    "website" => "DEFAULT",
    "industry_type" => "Retail",
    "channel_id" => "WEB"
  }
})

IO.puts("✅ Gateway configurations created")

# ==================== Bank Configurations ====================
IO.puts("Creating bank configurations...")

banks = [
  # Major Indian Banks
  %{
    name: "State Bank of India",
    bank_code: "SBI",
    icon_url: "/images/banks/sbi.png",
    status: 1,
    sort_order: 1,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("0"),
    config_data: %{
      "ifsc_prefix" => "SBIN",
      "bank_type" => "public",
      "upi_handle" => "@sbi"
    }
  },
  %{
    name: "HDFC Bank",
    bank_code: "HDFC",
    icon_url: "/images/banks/hdfc.png",
    status: 1,
    sort_order: 2,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("0"),
    config_data: %{
      "ifsc_prefix" => "HDFC",
      "bank_type" => "private",
      "upi_handle" => "@hdfcbank"
    }
  },
  %{
    name: "ICICI Bank",
    bank_code: "ICICI",
    icon_url: "/images/banks/icici.png",
    status: 1,
    sort_order: 3,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("0"),
    config_data: %{
      "ifsc_prefix" => "ICIC",
      "bank_type" => "private",
      "upi_handle" => "@icici"
    }
  },
  %{
    name: "Axis Bank",
    bank_code: "AXIS",
    icon_url: "/images/banks/axis.png",
    status: 1,
    sort_order: 4,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("0"),
    config_data: %{
      "ifsc_prefix" => "UTIB",
      "bank_type" => "private",
      "upi_handle" => "@axisbank"
    }
  },
  %{
    name: "Punjab National Bank",
    bank_code: "PNB",
    icon_url: "/images/banks/pnb.png",
    status: 1,
    sort_order: 5,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("200000"),
    fee_rate: Decimal.new("0"),
    config_data: %{
      "ifsc_prefix" => "PUNB",
      "bank_type" => "public",
      "upi_handle" => "@pnb"
    }
  },
  %{
    name: "Bank of Baroda",
    bank_code: "BOB",
    icon_url: "/images/banks/bob.png",
    status: 1,
    sort_order: 6,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("200000"),
    fee_rate: Decimal.new("0"),
    config_data: %{
      "ifsc_prefix" => "BARB",
      "bank_type" => "public",
      "upi_handle" => "@barodampay"
    }
  },
  %{
    name: "Kotak Mahindra Bank",
    bank_code: "KOTAK",
    icon_url: "/images/banks/kotak.png",
    status: 1,
    sort_order: 7,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("300000"),
    fee_rate: Decimal.new("0"),
    config_data: %{
      "ifsc_prefix" => "KKBK",
      "bank_type" => "private",
      "upi_handle" => "@kotak"
    }
  },
  %{
    name: "Yes Bank",
    bank_code: "YES",
    icon_url: "/images/banks/yesbank.png",
    status: 1,
    sort_order: 8,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("200000"),
    fee_rate: Decimal.new("0"),
    config_data: %{
      "ifsc_prefix" => "YESB",
      "bank_type" => "private",
      "upi_handle" => "@yesbank"
    }
  }
]

Enum.each(banks, fn bank_data ->
  {:ok, _bank} = BankConfig.create(bank_data)
end)

IO.puts("✅ Bank configurations created")

# ==================== Payment Configurations ====================
IO.puts("Creating payment configurations...")

# UPI Payment Methods
upi_methods = [
  %{
    gateway_id: razorpay_gateway.id,
    gateway_name: "Razorpay",
    payment_type: "upi",
    payment_type_name: "UPI",
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("100000"),
    fee_rate: Decimal.new("0"), # No fee for UPI
    deduction_rate: Decimal.new("0"),
    status: 1,
    sort_order: 1,
    recharge_range: "100|500|1000|2000|5000|10000|20000|50000",
    bonus_range: "0|0|20|50|150|300|800|2500", # Bonus amounts for each tier
    config_data: %{
      "icon" => "/images/payment/upi.png",
      "color" => "#4CAF50",
      "popular" => true,
      "instant" => true,
      "description" => "Instant payment via UPI"
    }
  },
  %{
    gateway_id: paytm_gateway.id,
    gateway_name: "Paytm",
    payment_type: "paytm_upi",
    payment_type_name: "Paytm UPI",
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("50000"),
    fee_rate: Decimal.new("0"),
    deduction_rate: Decimal.new("0"),
    status: 1,
    sort_order: 2,
    recharge_range: "100|500|1000|2000|5000|10000|20000",
    bonus_range: "0|0|15|40|120|250|600",
    config_data: %{
      "icon" => "/images/payment/paytm.png",
      "color" => "#00BAF2",
      "instant" => true
    }
  },
  %{
    gateway_id: phonepe_gateway.id,
    gateway_name: "PhonePe",
    payment_type: "phonepe",
    payment_type_name: "PhonePe",
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("50000"),
    fee_rate: Decimal.new("0"),
    deduction_rate: Decimal.new("0"),
    status: 1,
    sort_order: 3,
    recharge_range: "100|500|1000|2000|5000|10000|20000",
    bonus_range: "0|0|15|40|120|250|600",
    config_data: %{
      "icon" => "/images/payment/phonepe.png",
      "color" => "#5E35B1",
      "instant" => true
    }
  }
]

# Net Banking Methods
netbanking_methods = [
  %{
    gateway_id: razorpay_gateway.id,
    gateway_name: "Razorpay",
    payment_type: "netbanking",
    payment_type_name: "Net Banking",
    min_amount: Decimal.new("500"),
    max_amount: Decimal.new("200000"),
    fee_rate: Decimal.new("0.018"), # 1.8% fee as decimal
    deduction_rate: Decimal.new("0"),
    status: 1,
    sort_order: 4,
    recharge_range: "500|1000|2000|5000|10000|20000|50000|100000",
    bonus_range: "0|10|25|75|200|500|1500|3500",
    config_data: %{
      "icon" => "/images/payment/netbanking.png",
      "color" => "#1976D2",
      "processing_time" => "5-10 minutes"
    }
  }
]

# Card Payment Methods
card_methods = [
  %{
    gateway_id: razorpay_gateway.id,
    gateway_name: "Razorpay",
    payment_type: "card",
    payment_type_name: "Credit/Debit Card",
    min_amount: Decimal.new("500"),
    max_amount: Decimal.new("100000"),
    fee_rate: Decimal.new("0.025"), # 2.5% fee as decimal
    deduction_rate: Decimal.new("0"),
    status: 1,
    sort_order: 5,
    recharge_range: "500|1000|2000|5000|10000|20000|50000",
    bonus_range: "0|5|20|60|150|400|1200",
    config_data: %{
      "icon" => "/images/payment/card.png",
      "color" => "#F44336",
      "supported_cards" => ["Visa", "Mastercard", "Rupay", "Amex"],
      "international" => true
    }
  }
]

# Wallet Methods
wallet_methods = [
  %{
    gateway_id: paytm_gateway.id,
    gateway_name: "Paytm",
    payment_type: "wallet",
    payment_type_name: "Paytm Wallet",
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("20000"),
    fee_rate: Decimal.new("0.015"), # 1.5% fee as decimal
    deduction_rate: Decimal.new("0"),
    status: 1,
    sort_order: 6,
    recharge_range: "100|500|1000|2000|5000|10000",
    bonus_range: "0|0|10|25|80|200",
    config_data: %{
      "icon" => "/images/payment/paytm_wallet.png",
      "color" => "#00BAF2",
      "instant" => true,
      "wallet_cashback" => true
    }
  }
]

# Create all payment configurations
all_methods = upi_methods ++ netbanking_methods ++ card_methods ++ wallet_methods

Enum.each(all_methods, fn method_data ->
  {:ok, _config} = PaymentConfig.create(method_data)
end)

IO.puts("✅ Payment configurations created")

# ==================== Summary ====================
IO.puts("\n🎉 Real payment data seeding completed!")
IO.puts("Created:")
IO.puts("  - #{4} Payment Gateways")
IO.puts("  - #{2} Gateway Configurations")
IO.puts("  - #{8} Bank Configurations")
IO.puts("  - #{length(all_methods)} Payment Configurations")
IO.puts("\nRecharge tiers configured with bonuses:")
IO.puts("  ₹100 - ₹1,00,000+ with varying bonus rates")
IO.puts("\nPayment methods include:")
IO.puts("  - UPI (Razorpay, Paytm, PhonePe)")
IO.puts("  - Net Banking")
IO.puts("  - Credit/Debit Cards")
IO.puts("  - Digital Wallets")
