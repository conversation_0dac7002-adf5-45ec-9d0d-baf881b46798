# Fix VIP levels with proper structure for the existing table
alias Teen.VipSystem.VipLevel

# First, let's check if we have any existing data and clear it
IO.puts("Checking existing VIP levels...")
case VipLevel.read() do
  {:ok, existing_levels} ->
    IO.puts("Found #{length(existing_levels)} existing VIP levels")
    
    # Clear existing data
    Enum.each(existing_levels, fn level ->
      VipLevel.destroy(level)
    end)
    
    IO.puts("Cleared existing VIP levels")
    
  {:error, reason} ->
    IO.puts("Error reading existing VIP levels: #{inspect(reason)}")
end

# Now create the proper VIP levels for the existing table structure
vip_levels = [
  %{
    level: 0,
    level_name: "普通用户",
    recharge_requirement: 0,
    daily_bonus: 0,
    exchange_rate_bonus: 0,
    recharge_bonus: 0,
    status: 1,
    icon_url: nil,
    description: "普通用户等级",
    privileges: []
  },
  %{
    level: 1,
    level_name: "青铜VIP",
    recharge_requirement: 100,
    daily_bonus: 10,
    exchange_rate_bonus: 0.5,
    recharge_bonus: 0.5,
    status: 1,
    icon_url: nil,
    description: "青铜VIP等级",
    privileges: ["每日奖励", "充值奖励"]
  },
  %{
    level: 2,
    level_name: "白银VIP",
    recharge_requirement: 500,
    daily_bonus: 20,
    exchange_rate_bonus: 1.0,
    recharge_bonus: 1.0,
    status: 1,
    icon_url: nil,
    description: "白银VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成"]
  },
  %{
    level: 3,
    level_name: "黄金VIP",
    recharge_requirement: 1000,
    daily_bonus: 50,
    exchange_rate_bonus: 1.5,
    recharge_bonus: 1.5,
    status: 1,
    icon_url: nil,
    description: "黄金VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成", "专属客服"]
  },
  %{
    level: 4,
    level_name: "白金VIP",
    recharge_requirement: 2000,
    daily_bonus: 100,
    exchange_rate_bonus: 2.0,
    recharge_bonus: 2.0,
    status: 1,
    icon_url: nil,
    description: "白金VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限"]
  },
  %{
    level: 5,
    level_name: "钻石VIP",
    recharge_requirement: 5000,
    daily_bonus: 200,
    exchange_rate_bonus: 2.5,
    recharge_bonus: 2.5,
    status: 1,
    icon_url: nil,
    description: "钻石VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动"]
  },
  %{
    level: 6,
    level_name: "皇冠VIP",
    recharge_requirement: 10000,
    daily_bonus: 500,
    exchange_rate_bonus: 3.0,
    recharge_bonus: 3.0,
    status: 1,
    icon_url: nil,
    description: "皇冠VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品"]
  },
  %{
    level: 7,
    level_name: "至尊VIP",
    recharge_requirement: 20000,
    daily_bonus: 1000,
    exchange_rate_bonus: 3.5,
    recharge_bonus: 3.5,
    status: 1,
    icon_url: nil,
    description: "至尊VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品", "生日礼物"]
  },
  %{
    level: 8,
    level_name: "传说VIP",
    recharge_requirement: 50000,
    daily_bonus: 2000,
    exchange_rate_bonus: 4.0,
    recharge_bonus: 4.0,
    status: 1,
    icon_url: nil,
    description: "传说VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品", "生日礼物", "专属聊天室"]
  },
  %{
    level: 9,
    level_name: "神话VIP",
    recharge_requirement: 100000,
    daily_bonus: 5000,
    exchange_rate_bonus: 4.5,
    recharge_bonus: 4.5,
    status: 1,
    icon_url: nil,
    description: "神话VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品", "生日礼物", "专属聊天室", "专属游戏"]
  },
  %{
    level: 10,
    level_name: "超凡VIP",
    recharge_requirement: 200000,
    daily_bonus: 10000,
    exchange_rate_bonus: 5.0,
    recharge_bonus: 5.0,
    status: 1,
    icon_url: nil,
    description: "超凡VIP等级",
    privileges: ["每日奖励", "充值奖励", "兑换加成", "专属客服", "高级权限", "专属活动", "VIP礼品", "生日礼物", "专属聊天室", "专属游戏", "终身特权"]
  }
]

IO.puts("Creating VIP levels...")

Enum.each(vip_levels, fn level_data ->
  case VipLevel.create(level_data) do
    {:ok, level} ->
      IO.puts("✓ Created VIP level #{level.level}: #{level.level_name}")
    {:error, reason} ->
      IO.puts("✗ Failed to create VIP level #{level_data.level}: #{inspect(reason)}")
  end
end)

IO.puts("VIP levels seed completed!")