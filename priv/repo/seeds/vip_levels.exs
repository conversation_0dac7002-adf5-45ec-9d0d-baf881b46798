# Seed VIP levels with default configuration
alias Teen.VipSystem.VipLevel

vip_levels = [
  %{
    level: 0,
    experience_required: 0,
    daily_bonus: 0,
    withdrawal_limit: 10000,
    withdrawal_times: 3,
    recharge_bonus_rate: 0,
    game_bonus_rate: 0,
    loss_rebate_rate: 0,
    benefits: %{},
    status: 1  # 1 = active
  },
  %{
    level: 1,
    experience_required: 100,
    daily_bonus: 10,
    withdrawal_limit: 20000,
    withdrawal_times: 5,
    recharge_bonus_rate: 0.5,
    game_bonus_rate: 0.1,
    loss_rebate_rate: 0.1,
    benefits: %{"badge" => "bronze"},
    status: 1  # 1 = active
  },
  %{
    level: 2,
    experience_required: 500,
    daily_bonus: 20,
    withdrawal_limit: 30000,
    withdrawal_times: 7,
    recharge_bonus_rate: 1.0,
    game_bonus_rate: 0.2,
    loss_rebate_rate: 0.2,
    benefits: %{"badge" => "silver"},
    status: 1  # 1 = active
  },
  %{
    level: 3,
    experience_required: 1000,
    daily_bonus: 50,
    withdrawal_limit: 50000,
    withdrawal_times: 10,
    recharge_bonus_rate: 1.5,
    game_bonus_rate: 0.3,
    loss_rebate_rate: 0.3,
    benefits: %{"badge" => "gold"},
    status: 1  # 1 = active
  },
  %{
    level: 4,
    experience_required: 2000,
    daily_bonus: 100,
    withdrawal_limit: 100000,
    withdrawal_times: 15,
    recharge_bonus_rate: 2.0,
    game_bonus_rate: 0.4,
    loss_rebate_rate: 0.4,
    benefits: %{"badge" => "platinum"},
    status: 1  # 1 = active
  },
  %{
    level: 5,
    experience_required: 5000,
    daily_bonus: 200,
    withdrawal_limit: 200000,
    withdrawal_times: 20,
    recharge_bonus_rate: 2.5,
    game_bonus_rate: 0.5,
    loss_rebate_rate: 0.5,
    benefits: %{"badge" => "diamond"},
    status: 1  # 1 = active
  }
]

Enum.each(vip_levels, fn level_data ->
  case VipLevel.by_level(level_data.level) do
    {:ok, []} ->
      case VipLevel.create(level_data) do
        {:ok, _} ->
          IO.puts("Created VIP level #{level_data.level}")
        {:error, reason} ->
          IO.puts("Failed to create VIP level #{level_data.level}: #{inspect(reason)}")
      end
    {:ok, _existing} ->
      IO.puts("VIP level #{level_data.level} already exists")
    {:error, reason} ->
      IO.puts("Error checking VIP level #{level_data.level}: #{inspect(reason)}")
  end
end)