# 额外银行配置种子文件
# 运行方式: mix run priv/repo/seeds/additional_banks_seeds_fixed.exs

alias Teen.PaymentSystem.BankConfig

IO.puts("🏛️ 开始添加额外银行配置...")

# 银行配置数据
bank_configs = [
  # 已有银行配置 (更新版本)
  %{
    name: "State Bank of India",
    bank_code: "SBI",
    icon_url: "/images/banks/sbi.png",
    status: 1,
    sort_order: 1,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.5"),
    config_data: %{
      "ifsc_prefix" => "SBIN",
      "bank_type" => "public",
      "upi_handle" => "@sbi",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.onlinesbi.com",
      "description" => "印度国家银行，最大的公共银行"
    }
  },
  %{
    name: "HDFC Bank",
    bank_code: "HDFC",
    icon_url: "/images/banks/hdfc.png",
    status: 1,
    sort_order: 2,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.5"),
    config_data: %{
      "ifsc_prefix" => "HDFC",
      "bank_type" => "private",
      "upi_handle" => "@hdfcbank",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://netbanking.hdfcbank.com",
      "description" => "HDFC银行，领先的私人银行"
    }
  },
  %{
    name: "ICICI Bank",
    bank_code: "ICICI",
    icon_url: "/images/banks/icici.png",
    status: 1,
    sort_order: 3,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.5"),
    config_data: %{
      "ifsc_prefix" => "ICIC",
      "bank_type" => "private",
      "upi_handle" => "@icici",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.icicibank.com",
      "description" => "ICICI银行，数字化银行先锋"
    }
  },
  %{
    name: "Axis Bank",
    bank_code: "AXIS",
    icon_url: "/images/banks/axis.png",
    status: 1,
    sort_order: 4,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.5"),
    config_data: %{
      "ifsc_prefix" => "UTIB",
      "bank_type" => "private",
      "upi_handle" => "@axisbank",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.axisbank.com",
      "description" => "Axis银行，创新金融服务"
    }
  },
  %{
    name: "Punjab National Bank",
    bank_code: "PNB",
    icon_url: "/images/banks/pnb.png",
    status: 1,
    sort_order: 5,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.8"),
    config_data: %{
      "ifsc_prefix" => "PUNB",
      "bank_type" => "public",
      "upi_handle" => "@pnb",
      "is_popular" => false,
      "supported_services" => ["net_banking", "upi"],
      "bank_url" => "https://www.pnbindia.in",
      "description" => "旁遮普国家银行"
    }
  },
  
  # 新增银行配置
  %{
    name: "Bank of Baroda",
    bank_code: "BOB",
    icon_url: "/images/banks/bob.png",
    status: 1,
    sort_order: 6,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.8"),
    config_data: %{
      "ifsc_prefix" => "BARB",
      "bank_type" => "public",
      "upi_handle" => "@barodampay",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.bankofbaroda.in",
      "description" => "印度银行，全球业务网络"
    }
  },
  %{
    name: "Kotak Mahindra Bank",
    bank_code: "KOTAK",
    icon_url: "/images/banks/kotak.png",
    status: 1,
    sort_order: 7,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.5"),
    config_data: %{
      "ifsc_prefix" => "KKBK",
      "bank_type" => "private",
      "upi_handle" => "@kotak",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.kotak.com",
      "description" => "Kotak Mahindra银行，数字化创新银行"
    }
  },
  %{
    name: "Yes Bank",
    bank_code: "YES",
    icon_url: "/images/banks/yes.png",
    status: 1,
    sort_order: 8,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.6"),
    config_data: %{
      "ifsc_prefix" => "YESB",
      "bank_type" => "private",
      "upi_handle" => "@yesbank",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.yesbank.in",
      "description" => "Yes Bank，专业商业银行"
    }
  },
  %{
    name: "Canara Bank",
    bank_code: "CANARA",
    icon_url: "/images/banks/canara.png",
    status: 1,
    sort_order: 9,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.8"),
    config_data: %{
      "ifsc_prefix" => "CNRB",
      "bank_type" => "public",
      "upi_handle" => "@canarabank",
      "is_popular" => false,
      "supported_services" => ["net_banking", "upi"],
      "bank_url" => "https://canarabank.com",
      "description" => "Canara银行，历史悠久的公共银行"
    }
  },
  %{
    name: "Union Bank of India",
    bank_code: "UNION",
    icon_url: "/images/banks/union.png",
    status: 1,
    sort_order: 10,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.8"),
    config_data: %{
      "ifsc_prefix" => "UBIN",
      "bank_type" => "public",
      "upi_handle" => "@unionbank",
      "is_popular" => false,
      "supported_services" => ["net_banking", "upi"],
      "bank_url" => "https://www.unionbankofindia.co.in",
      "description" => "联合银行，可信赖的公共银行"
    }
  },
  %{
    name: "Bank of India",
    bank_code: "BOI",
    icon_url: "/images/banks/boi.png",
    status: 1,
    sort_order: 11,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.8"),
    config_data: %{
      "ifsc_prefix" => "BKID",
      "bank_type" => "public",
      "upi_handle" => "@boi",
      "is_popular" => false,
      "supported_services" => ["net_banking", "upi"],
      "bank_url" => "https://www.bankofindia.co.in",
      "description" => "印度银行，国际业务专家"
    }
  },
  %{
    name: "IndusInd Bank",
    bank_code: "INDUSIND",
    icon_url: "/images/banks/indusind.png",
    status: 1,
    sort_order: 12,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.6"),
    config_data: %{
      "ifsc_prefix" => "INDB",
      "bank_type" => "private",
      "upi_handle" => "@indus",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.indusind.com",
      "description" => "IndusInd银行，创新金融解决方案"
    }
  },
  %{
    name: "Federal Bank",
    bank_code: "FEDERAL",
    icon_url: "/images/banks/federal.png",
    status: 1,
    sort_order: 13,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.7"),
    config_data: %{
      "ifsc_prefix" => "FDRL",
      "bank_type" => "private",
      "upi_handle" => "@federalbank",
      "is_popular" => false,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.federalbank.co.in",
      "description" => "Federal银行，南印度领先私人银行"
    }
  },
  %{
    name: "IDBI Bank",
    bank_code: "IDBI",
    icon_url: "/images/banks/idbi.png",
    status: 1,
    sort_order: 14,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.8"),
    config_data: %{
      "ifsc_prefix" => "IBKL",
      "bank_type" => "public",
      "upi_handle" => "@idbi",
      "is_popular" => false,
      "supported_services" => ["net_banking", "upi"],
      "bank_url" => "https://www.idbibank.in",
      "description" => "IDBI银行，工业发展银行"
    }
  },
  %{
    name: "Karnataka Bank",
    bank_code: "KARNATAKA",
    icon_url: "/images/banks/karnataka.png",
    status: 1,
    sort_order: 15,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.9"),
    config_data: %{
      "ifsc_prefix" => "KARB",
      "bank_type" => "private",
      "upi_handle" => "@karnatakabank",
      "is_popular" => false,
      "supported_services" => ["net_banking", "upi"],
      "bank_url" => "https://www.karnatakabank.com",
      "description" => "Karnataka银行，区域性私人银行"
    }
  }
]

# 批量创建银行配置
IO.puts("创建银行配置...")
success_count = 0
error_count = 0

for config_attrs <- bank_configs do
  # 先检查银行是否已存在
  existing = 
    case BankConfig.get_by_code(config_attrs.bank_code) do
      {:ok, bank} -> bank
      _ -> nil
    end

  result = 
    if existing do
      # 更新现有记录
      BankConfig.update(existing, Map.drop(config_attrs, [:bank_code]))
    else
      # 创建新记录
      BankConfig.create(config_attrs)
    end

  case result do
    {:ok, config} -> 
      action = if existing, do: "更新", else: "创建"
      IO.puts("✅ #{action}银行配置: #{config.name}")
      success_count = success_count + 1
    {:error, reason} -> 
      IO.puts("❌ 创建银行配置失败: #{config_attrs.name}, 原因: #{inspect(reason)}")
      error_count = error_count + 1
  end
end

IO.puts("")
IO.puts("📊 银行配置统计:")
IO.puts("  • 成功/更新: #{success_count} 个")
IO.puts("  • 失败: #{error_count} 个")
IO.puts("  • 总计: #{length(bank_configs)} 个")

# 获取最新统计
banks = BankConfig.read!()
active_banks = Enum.count(banks, &(&1.status == 1))
popular_banks = Enum.count(banks, fn bank -> 
  case bank.config_data do
    %{"is_popular" => true} -> true
    _ -> false
  end
end)

IO.puts("")
IO.puts("🏛️ 当前银行配置:")
IO.puts("  • 总银行数: #{length(banks)} 个")
IO.puts("  • 激活银行: #{active_banks} 个")
IO.puts("  • 热门银行: #{popular_banks} 个")

# 按类型分组
public_banks = Enum.count(banks, fn bank ->
  case bank.config_data do
    %{"bank_type" => "public"} -> true
    _ -> false
  end
end)

private_banks = Enum.count(banks, fn bank ->
  case bank.config_data do
    %{"bank_type" => "private"} -> true
    _ -> false
  end
end)

IO.puts("  • 公共银行: #{public_banks} 个")
IO.puts("  • 私人银行: #{private_banks} 个")

IO.puts("")
IO.puts("🔧 银行管理:")
IO.puts("  • 后台管理: /admin/bank-configs")
IO.puts("  • 用户银行卡: /admin/user-bank-cards")

IO.puts("")
IO.puts("🎉 银行配置初始化完成！")