# 30次刮卡活动种子数据
alias Teen.ActivitySystem.{
  ScratchCardActivity,
  ScratchCardTaskLevel,
  ScratchCardLevelReward
}

# 创建30次刮卡活动
{:ok, activity} = ScratchCardActivity.create(%{
  activity_title: "30次刮卡活动",
  card_type: :option1,                    # 卡片类型
  cost_amount: Decimal.new("100"),        # 购买费用（分）
  min_reward: Decimal.new("50"),          # 最小奖励（分）
  max_reward: Decimal.new("5000"),        # 最大奖励（分）
  claimable_count: 30,                    # 可领取数量
  reward_probability: Decimal.new("80"),  # 奖励概率（%）
  daily_limit: 5,                         # 每日限制次数
  is_active: true,                        # 是否激活
  status: :enabled
})

IO.puts("创建30次刮卡活动: #{activity.activity_title}")

# 创建任务等级
levels_data = [
  %{
    task_level: 1,
    recharge_amount: Decimal.new("10000")  # 100元
  },
  %{
    task_level: 2,
    recharge_amount: Decimal.new("20000")  # 200元
  },
  %{
    task_level: 3,
    recharge_amount: Decimal.new("50000")  # 500元
  }
]

levels = Enum.map(levels_data, fn level_data ->
  {:ok, level} = ScratchCardTaskLevel.create(Map.put(level_data, :activity_id, activity.id))
  IO.puts("创建任务等级#{level.task_level}: 充值#{Decimal.to_integer(level.recharge_amount)}分")
  level
end)

# 为每个等级创建奖励配置
rewards_data = [
  # 等级1奖励 - 积分
  %{
    level: 1,
    rewards: [
      %{
        reward_type: :coins,
        min_reward: Decimal.new("1000"),
        max_reward: Decimal.new("5000"),
        actual_max_reward: Decimal.new("5000"),
        probability: Decimal.new("1.0"),
        sort_order: 1
      }
    ]
  },
  # 等级2奖励 - 积分
  %{
    level: 2,
    rewards: [
      %{
        reward_type: :coins,
        min_reward: Decimal.new("2000"),
        max_reward: Decimal.new("10000"),
        actual_max_reward: Decimal.new("10000"),
        probability: Decimal.new("1.0"),
        sort_order: 1
      }
    ]
  },
  # 等级3奖励 - 金币
  %{
    level: 3,
    rewards: [
      %{
        reward_type: :cash,
        min_reward: Decimal.new("5000"),
        max_reward: Decimal.new("20000"),
        actual_max_reward: Decimal.new("20000"),
        probability: Decimal.new("1.0"),
        sort_order: 1
      }
    ]
  }
]

Enum.each(rewards_data, fn %{level: level_num, rewards: rewards} ->
  level = Enum.find(levels, fn l -> l.task_level == level_num end)

  Enum.each(rewards, fn reward_data ->
    {:ok, reward} = ScratchCardLevelReward.create(Map.put(reward_data, :task_level_id, level.id))
    IO.puts("创建等级#{level_num}奖励: #{reward.reward_type} #{reward.min_reward}-#{reward.max_reward}")
  end)
end)

IO.puts("30次刮卡活动种子数据创建完成！")
