# MasterPay支付网关配置种子文件
# 运行方式: mix run priv/repo/seeds/master_pay_seeds.exs

alias Teen.PaymentSystem.{
  PaymentGateway,
  PaymentConfig,
  WithdrawalConfig,
  BankConfig
}

IO.puts("🚀 开始初始化 MasterPay 支付系统...")

# 环境变量配置
# masterpay_env = Application.get_env(:cypridina, :masterpay, %{})
merchant_id = "10236"
merchant_key = "ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM"
gateway_url = "https://api.masterpay88.in/app-api"
callback_domain = "https://api.cypridina.com"
is_test_mode =  "false"

IO.puts("📋 配置信息:")
IO.puts("  • 商户ID: #{merchant_id}")
IO.puts("  • 商户密钥: #{merchant_key}")
IO.puts("  • 网关URL: #{gateway_url}")
IO.puts("  • 回调域名: #{callback_domain}")
IO.puts("  • 测试模式: #{is_test_mode}")

# 辅助函数：安全创建记录
safe_create = fn module, attrs, unique_field ->
  unique_value = Map.get(attrs, unique_field)

  case apply(module, :read, []) do
    {:ok, records} ->
      existing = Enum.find(records, fn r ->
        Map.get(r, unique_field) == unique_value
      end)

      if existing do
        # 更新现有记录
        case Ash.update(existing, Map.drop(attrs, [:id]), authorize?: false) do
          {:ok, updated} -> {:updated, updated}
          error -> error
        end
      else
        Ash.create(module, attrs, authorize?: false)
      end
    _ ->
      Ash.create(module, attrs, authorize?: false)
  end
end

# ==================== 创建网关配置 ====================

IO.puts("⚙️ 创建MasterPay网关配置...")

masterpay_config_attrs = %{
  name: "MasterPay支付网关",
  gateway_name: "MasterPay",
  gateway_code: "MASTERPAY",
  merchant_id: merchant_id,
  merchant_key: merchant_key,
  gateway_url: gateway_url,
  create_order_path: "/v1.0/api/order/create",
  query_order_path: "/v1.0/api/order/query",
  callback_ip: "*************",
  recharge_channel:  "3021",
  withdrawal_channel:  "3020",
  supported_currencies: ["INR"],
  supported_payment_methods: ["UPI", "Net Banking", "Wallet", "Card"],
  min_amount: Decimal.new("10000"),    # 100.00 INR
  max_amount: Decimal.new("5000000"),  # 50,000.00 INR
  fee_rate: Decimal.new("0.02"),       # 2%手续费
  status: "active",
  config_data: %{
    "notify_url" => "#{callback_domain}/api/payment/callback/masterpay",
    "return_url" => "#{callback_domain}/payment/success",
    "cancel_url" => "#{callback_domain}/payment/cancel",
    "sign_type" => "MD5",
    "charset" => "UTF-8",
    "version" => "1.0",
    "test_mode" => is_test_mode,
    "auto_settle" => true,
    "settlement_delay_hours" => 24,
    "risk_control" => %{
      "max_daily_amount" => 1000000,  # 10,000 INR
      "max_single_amount" => 500000,  # 5,000 INR
      "suspicious_amount_threshold" => 100000  # 1,000 INR
    },
    "webhook_config" => %{
      "max_retries" => 5,
      "retry_intervals" => [60, 300, 900, 3600, 7200],  # 秒
      "timeout" => 30
    }
  }
}

masterpay_recharge_id = case safe_create.(PaymentGateway, masterpay_config_attrs, :gateway_code) do
  {:ok, config} ->
    IO.puts("✅ MasterPay网关配置创建成功")
    config.id
  {:updated, config} ->
    IO.puts("🔄 MasterPay网关配置更新成功")
    config.id
  {:error, reason} ->
    IO.puts("❌ MasterPay网关配置创建失败: #{inspect(reason)}")
end

# ==================== 创建支付方式配置 ====================

IO.puts("💳 创建支付方式配置...")

if masterpay_recharge_id do
  # UPI支付配置
  upi_configs = [
    %{
      gateway_id: masterpay_recharge_id,
      gateway_name: "MasterPay - 充值网关",
      payment_type: "upi",
      payment_type_name: "UPI支付",
      min_amount: Decimal.new("10000"),    # 100.00 INR
      max_amount: Decimal.new("5000000"),  # 50,000.00 INR
      fee_rate: Decimal.new("1.8"),        # 1.8%
      deduction_rate: Decimal.new("0"),
      status: 1,
      sort_order: 1,
      config_data: %{
        "icon" => "/images/payment/upi.png",
        "color" => "#00BAF2",
        "description" => "使用UPI快速支付，支持所有主流UPI应用",
        "recommended" => true,
        "processing_time" => "即时",
        "success_rate" => 98,
        "supported_apps" => ["Google Pay", "PhonePe", "Paytm", "BHIM"]
      }
    }
  ]

  # 网银支付配置
  netbanking_configs = [
    %{
      gateway_id: masterpay_recharge_id,
      gateway_name: "MasterPay - 充值网关",
      payment_type: "net_banking",
      payment_type_name: "网银支付",
      min_amount: Decimal.new("50000"),    # 500.00 INR
      max_amount: Decimal.new("5000000"),
      fee_rate: Decimal.new("2.2"),        # 2.2%
      deduction_rate: Decimal.new("0"),
      status: 1,
      sort_order: 3,
      config_data: %{
        "icon" => "/images/payment/netbanking.png",
        "color" => "#FF6B00",
        "description" => "支持所有主流银行网银支付",
        "supported_banks" => [
          %{"code" => "SBI", "name" => "State Bank of India", "logo" => "/images/banks/sbi.png"},
          %{"code" => "HDFC", "name" => "HDFC Bank", "logo" => "/images/banks/hdfc.png"},
          %{"code" => "ICICI", "name" => "ICICI Bank", "logo" => "/images/banks/icici.png"},
          %{"code" => "AXIS", "name" => "Axis Bank", "logo" => "/images/banks/axis.png"},
          %{"code" => "PNB", "name" => "Punjab National Bank", "logo" => "/images/banks/pnb.png"}
        ]
      }
    }
  ]

  # 钱包支付配置
  wallet_configs = [
    %{
      gateway_id: masterpay_recharge_id,
      gateway_name: "MasterPay - 充值网关",
      payment_type: "wallet",
      payment_type_name: "电子钱包",
      min_amount: Decimal.new("10000"),
      max_amount: Decimal.new("2000000"),  # 20,000.00 INR
      fee_rate: Decimal.new("2.0"),
      deduction_rate: Decimal.new("0"),
      status: 1,
      sort_order: 4,
      config_data: %{
        "icon" => "/images/payment/wallet.png",
        "color" => "#00B9F1",
        "description" => "支持主流电子钱包支付",
        "supported_wallets" => [
          %{"code" => "paytm", "name" => "Paytm Wallet", "logo" => "/images/wallets/paytm.png"},
          %{"code" => "phonepe", "name" => "PhonePe Wallet", "logo" => "/images/wallets/phonepe.png"},
          %{"code" => "mobikwik", "name" => "MobiKwik", "logo" => "/images/wallets/mobikwik.png"}
        ]
      }
    }
  ]

  # 银行卡支付配置
  card_configs = [
    %{
      gateway_id: masterpay_recharge_id,
      gateway_name: "MasterPay - 充值网关",
      payment_type: "debit_card",
      payment_type_name: "借记卡",
      min_amount: Decimal.new("50000"),    # 500.00 INR
      max_amount: Decimal.new("5000000"),
      fee_rate: Decimal.new("2.8"),        # 2.8%
      deduction_rate: Decimal.new("0"),
      status: 1,
      sort_order: 5,
      config_data: %{
        "icon" => "/images/payment/debit_card.png",
        "color" => "#1A1F71",
        "description" => "支持所有银行借记卡",
        "supported_cards" => ["Visa", "Mastercard", "Rupay"],
        "requires_cvv" => true,
        "requires_otp" => true
      }
    },
    %{
      gateway_id: masterpay_recharge_id,
      gateway_name: "MasterPay - 充值网关",
      payment_type: "credit_card",
      payment_type_name: "信用卡",
      min_amount: Decimal.new("100000"),   # 1,000.00 INR
      max_amount: Decimal.new("5000000"),
      fee_rate: Decimal.new("3.2"),        # 3.2%
      deduction_rate: Decimal.new("0"),
      status: 1,
      sort_order: 6,
      config_data: %{
        "icon" => "/images/payment/credit_card.png",
        "color" => "#1A1F71",
        "description" => "支持所有银行信用卡",
        "supported_cards" => ["Visa", "Mastercard", "Amex"],
        "requires_cvv" => true,
        "requires_otp" => true,
        "installment_available" => false
      }
    }
  ]

  # 批量创建支付配置
  all_configs = upi_configs

  Enum.each(all_configs, fn config_attrs ->
    case safe_create.(PaymentConfig, config_attrs, :payment_type) do
      {:ok, config} ->
        IO.puts("✅ 创建支付配置: #{config.payment_type_name}")
      {:updated, config} ->
        IO.puts("🔄 更新支付配置: #{config.payment_type_name}")
      {:error, reason} ->
        IO.puts("❌ 创建支付配置失败: #{config_attrs.payment_type_name}, 原因: #{inspect(reason)}")
    end
  end)
else
  IO.puts("⚠️ 未找到MasterPay充值网关，跳过支付配置创建")
end

# ==================== 创建提现配置 ====================

IO.puts("🏦 创建提现配置...")

withdrawal_configs = [
  %{
    config_name: "UPI快速提现",
    payment_method: "upi",
    min_amount: Decimal.new("10000"),       # 100.00 INR
    max_amount: Decimal.new("1000000"),     # 10,000.00 INR
    fee_rate: Decimal.new("2.0"),           # 2%手续费
    tax_rate: Decimal.new("18.0"),          # 18% GST
    status: 1,
    turnover_multiplier: Decimal.new("3.0"), # 需要3倍流水
    processing_time_hours: 2,               # 2小时内处理
    business_hours_only: false,
    weekend_processing: true,
    vip_level_required: 0,
    description: "UPI快速提现，2小时内到账"
  },
  %{
    config_name: "UPI大额提现",
    payment_method: "upi",
    min_amount: Decimal.new("1000000"),     # 10,000.00 INR
    max_amount: Decimal.new("5000000"),     # 50,000.00 INR
    fee_rate: Decimal.new("1.5"),           # 1.5%手续费
    tax_rate: Decimal.new("18.0"),
    status: 1,
    turnover_multiplier: Decimal.new("5.0"), # 需要5倍流水
    processing_time_hours: 6,               # 6小时内处理
    business_hours_only: true,              # 仅工作时间
    weekend_processing: false,              # 周末不处理
    vip_level_required: 3,
    description: "UPI大额提现，工作日6小时内到账"
  },
  %{
    config_name: "银行转账标准",
    payment_method: "bank_transfer",
    min_amount: Decimal.new("50000"),       # 500.00 INR
    max_amount: Decimal.new("2000000"),     # 20,000.00 INR
    fee_rate: Decimal.new("2.5"),           # 2.5%手续费
    tax_rate: Decimal.new("18.0"),
    status: 1,
    turnover_multiplier: Decimal.new("5.0"),
    processing_time_hours: 24,              # 24小时内处理
    business_hours_only: true,
    weekend_processing: false,
    vip_level_required: 0,
    description: "银行转账提现，1-2个工作日到账"
  },
  %{
    config_name: "银行转账大额",
    payment_method: "bank_transfer",
    min_amount: Decimal.new("2000000"),     # 20,000.00 INR
    max_amount: Decimal.new("********"),    # 100,000.00 INR
    fee_rate: Decimal.new("1.8"),           # 1.8%手续费
    tax_rate: Decimal.new("18.0"),
    status: 1,
    turnover_multiplier: Decimal.new("8.0"), # 需要8倍流水
    processing_time_hours: 48,              # 48小时内处理
    business_hours_only: true,
    weekend_processing: false,
    vip_level_required: 4,
    description: "银行转账大额提现，需人工审核"
  }
]

Enum.each(withdrawal_configs, fn config_attrs ->
  case safe_create.(WithdrawalConfig, config_attrs, :config_name) do
    {:ok, config} ->
      IO.puts("✅ 创建提现配置: #{config.config_name}")
    {:updated, config} ->
      IO.puts("🔄 更新提现配置: #{config.config_name}")
    {:error, reason} ->
      IO.puts("❌ 创建提现配置失败: #{config_attrs.config_name}, 原因: #{inspect(reason)}")
  end
end)

# ==================== 创建银行配置 ====================

IO.puts("🏛️ 创建银行配置...")

bank_configs = [
  %{
    name: "State Bank of India",
    bank_code: "SBI",
    icon_url: "/images/banks/sbi.png",
    status: 1,
    sort_order: 1,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.5"),
    config_data: %{
      "ifsc_prefix" => "SBIN",
      "bank_type" => "public",
      "upi_handle" => "@sbi",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.onlinesbi.com",
      "description" => "印度国家银行，最大的公共银行"
    }
  },
  %{
    name: "HDFC Bank",
    bank_code: "HDFC",
    icon_url: "/images/banks/hdfc.png",
    status: 1,
    sort_order: 2,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.2"),
    config_data: %{
      "ifsc_prefix" => "HDFC",
      "bank_type" => "private",
      "upi_handle" => "@hdfcbank",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://netbanking.hdfcbank.com",
      "description" => "HDFC银行，领先的私人银行"
    }
  },
  %{
    name: "ICICI Bank",
    bank_code: "ICICI",
    icon_url: "/images/banks/icici.png",
    status: 1,
    sort_order: 3,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.3"),
    config_data: %{
      "ifsc_prefix" => "ICIC",
      "bank_type" => "private",
      "upi_handle" => "@icici",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.icicibank.com",
      "description" => "ICICI银行，数字化银行先锋"
    }
  },
  %{
    name: "Axis Bank",
    bank_code: "AXIS",
    icon_url: "/images/banks/axis.png",
    status: 1,
    sort_order: 4,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("500000"),
    fee_rate: Decimal.new("1.4"),
    config_data: %{
      "ifsc_prefix" => "UTIB",
      "bank_type" => "private",
      "upi_handle" => "@axisbank",
      "is_popular" => true,
      "supported_services" => ["net_banking", "upi", "cards"],
      "bank_url" => "https://www.axisbank.com",
      "description" => "Axis银行，创新金融服务"
    }
  },
  %{
    name: "Punjab National Bank",
    bank_code: "PNB",
    icon_url: "/images/banks/pnb.png",
    status: 1,
    sort_order: 5,
    min_amount: Decimal.new("100"),
    max_amount: Decimal.new("300000"),
    fee_rate: Decimal.new("1.8"),
    config_data: %{
      "ifsc_prefix" => "PUNB",
      "bank_type" => "public",
      "upi_handle" => "@pnb",
      "is_popular" => false,
      "supported_services" => ["net_banking", "upi"],
      "bank_url" => "https://www.pnbindia.in",
      "description" => "旁遮普国家银行"
    }
  }
]

Enum.each(bank_configs, fn config_attrs ->
  case safe_create.(BankConfig, config_attrs, :bank_code) do
    {:ok, config} ->
      IO.puts("✅ 创建银行配置: #{config.name}")
    {:updated, config} ->
      IO.puts("🔄 更新银行配置: #{config.name}")
    {:error, reason} ->
      IO.puts("❌ 创建银行配置失败: #{config_attrs.name}, 原因: #{inspect(reason)}")
  end
end)

# ==================== 生成配置总结 ====================

IO.puts("")
IO.puts("📊 MasterPay支付系统配置完成统计:")

# 获取最新统计
case PaymentGateway.read() do
  {:ok, gateways} ->
    masterpay_gateways = Enum.filter(gateways, &String.contains?(&1.name, "MasterPay"))
    IO.puts("  • MasterPay网关: #{length(masterpay_gateways)} 个")
  _ ->
    IO.puts("  • 无法获取网关统计")
end

case PaymentConfig.read() do
  {:ok, configs} ->
    active_configs = Enum.count(configs, &(&1.status == 1))
    IO.puts("  • 支付配置: #{length(configs)} 个 (激活: #{active_configs})")
  _ ->
    IO.puts("  • 无法获取支付配置统计")
end

case WithdrawalConfig.read() do
  {:ok, configs} ->
    active_withdrawals = Enum.count(configs, &(&1.status == 1))
    IO.puts("  • 提现配置: #{length(configs)} 个 (激活: #{active_withdrawals})")
  _ ->
    IO.puts("  • 无法获取提现配置统计")
end

IO.puts("")
IO.puts("🎉 MasterPay支付系统初始化完成！")
IO.puts("")
IO.puts("🔧 配置管理:")
IO.puts("  • 后台管理: /admin")
IO.puts("  • 支付网关: /admin/payment-gateways")
IO.puts("  • 支付配置: /admin/payment-configs")
IO.puts("")
IO.puts("🔗 API端点:")
IO.puts("  • 支付方式列表: GET /api/payment/methods")
IO.puts("  • 创建支付订单: POST /api/payment/create")
IO.puts("  • 查询订单状态: GET /api/payment/status/:order_id")
IO.puts("  • MasterPay回调: POST /api/payment/callback/masterpay")
IO.puts("")
IO.puts("⚠️ 安全提醒:")
IO.puts("  1. 请及时更新生产环境的商户密钥")
IO.puts("  2. 配置正确的回调IP白名单")
IO.puts("  3. 定期监控支付成功率")
IO.puts("  4. 设置适当的风控规则")
