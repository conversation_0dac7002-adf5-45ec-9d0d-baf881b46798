defmodule Cypridina.Chat.ErrorHandler do
  @moduledoc """
  聊天系统错误处理模块

  提供统一的错误处理机制，不使用try-catch块，
  遵循Elixir的"let it crash"哲学和with语句模式。
  """

  require Logger
  alias Cypridina.Chat.ChatLogger

  @doc """
  处理聊天会话相关错误
  """
  def handle_session_error(error, context \\ %{}) do
    case error do
      {:error, :not_found} ->
        log_and_format_error(:session_not_found, "聊天会话不存在", context)

      {:error, :permission_denied} ->
        log_and_format_error(:session_permission_denied, "无权限访问此会话", context)

      {:error, :session_full} ->
        log_and_format_error(:session_full, "会话参与者已满", context)

      {:error, :session_closed} ->
        log_and_format_error(:session_closed, "会话已关闭", context)

      {:error, changeset} when is_struct(changeset, Ash.Changeset) ->
        handle_changeset_error(changeset, :session_validation_error, context)

      {:error, reason} ->
        log_and_format_error(:session_unknown_error, "会话操作失败: #{inspect(reason)}", context)

      _ ->
        log_and_format_error(:session_unexpected_error, "会话操作出现意外错误", context)
    end
  end

  @doc """
  处理消息相关错误
  """
  def handle_message_error(error, context \\ %{}) do
    case error do
      {:error, :message_not_found} ->
        log_and_format_error(:message_not_found, "消息不存在", context)

      {:error, :message_deleted} ->
        log_and_format_error(:message_deleted, "消息已被删除", context)

      {:error, :content_too_long} ->
        log_and_format_error(:content_too_long, "消息内容过长", context)

      {:error, :invalid_message_type} ->
        log_and_format_error(:invalid_message_type, "无效的消息类型", context)

      {:error, :attachment_too_large} ->
        log_and_format_error(:attachment_too_large, "附件文件过大", context)

      {:error, :unsupported_file_type} ->
        log_and_format_error(:unsupported_file_type, "不支持的文件类型", context)

      {:error, changeset} when is_struct(changeset, Ash.Changeset) ->
        handle_changeset_error(changeset, :message_validation_error, context)

      {:error, reason} ->
        log_and_format_error(:message_unknown_error, "消息操作失败: #{inspect(reason)}", context)

      _ ->
        log_and_format_error(:message_unexpected_error, "消息操作出现意外错误", context)
    end
  end

  @doc """
  处理用户权限相关错误
  """
  def handle_permission_error(error, context \\ %{}) do
    case error do
      {:error, :not_participant} ->
        log_and_format_error(:not_participant, "用户不是会话参与者", context)

      {:error, :insufficient_permissions} ->
        log_and_format_error(:insufficient_permissions, "权限不足", context)

      {:error, :user_banned} ->
        log_and_format_error(:user_banned, "用户已被禁言", context)

      {:error, :session_read_only} ->
        log_and_format_error(:session_read_only, "会话为只读模式", context)

      {:error, reason} ->
        log_and_format_error(:permission_unknown_error, "权限验证失败: #{inspect(reason)}", context)

      _ ->
        log_and_format_error(:permission_unexpected_error, "权限验证出现意外错误", context)
    end
  end

  @doc """
  处理文件上传相关错误
  """
  def handle_file_error(error, context \\ %{}) do
    case error do
      {:error, :file_too_large} ->
        log_and_format_error(:file_too_large, "文件大小超出限制", context)

      {:error, :invalid_file_format} ->
        log_and_format_error(:invalid_file_format, "无效的文件格式", context)

      {:error, :upload_failed} ->
        log_and_format_error(:upload_failed, "文件上传失败", context)

      {:error, :storage_quota_exceeded} ->
        log_and_format_error(:storage_quota_exceeded, "存储空间不足", context)

      {:error, :virus_detected} ->
        log_and_format_error(:virus_detected, "文件包含病毒", context)

      {:error, :invalid_base64} ->
        log_and_format_error(:invalid_base64, "无效的Base64数据", context)

      {:error, reason} ->
        log_and_format_error(:file_unknown_error, "文件操作失败: #{inspect(reason)}", context)

      _ ->
        log_and_format_error(:file_unexpected_error, "文件操作出现意外错误", context)
    end
  end

  @doc """
  处理数据库相关错误
  """
  def handle_database_error(error, context \\ %{}) do
    case error do
      {:error, :timeout} ->
        log_and_format_error(:database_timeout, "数据库操作超时", context)

      {:error, :connection_failed} ->
        log_and_format_error(:database_connection_failed, "数据库连接失败", context)

      {:error, :constraint_violation} ->
        log_and_format_error(:database_constraint_violation, "数据约束违反", context)

      {:error, :unique_constraint} ->
        log_and_format_error(:database_unique_constraint, "数据重复", context)

      {:error, reason} ->
        log_and_format_error(:database_unknown_error, "数据库操作失败: #{inspect(reason)}", context)

      _ ->
        log_and_format_error(:database_unexpected_error, "数据库操作出现意外错误", context)
    end
  end

  @doc """
  处理网络相关错误
  """
  def handle_network_error(error, context \\ %{}) do
    case error do
      {:error, :timeout} ->
        log_and_format_error(:network_timeout, "网络请求超时", context)

      {:error, :connection_refused} ->
        log_and_format_error(:network_connection_refused, "网络连接被拒绝", context)

      {:error, :dns_resolution_failed} ->
        log_and_format_error(:network_dns_failed, "DNS解析失败", context)

      {:error, reason} ->
        log_and_format_error(:network_unknown_error, "网络操作失败: #{inspect(reason)}", context)

      _ ->
        log_and_format_error(:network_unexpected_error, "网络操作出现意外错误", context)
    end
  end

  @doc """
  安全地执行可能失败的操作
  """
  def safe_execute(operation, error_handler \\ &handle_generic_error/2) do
    case operation.() do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> error_handler.(reason, %{})
      result -> {:ok, result}
    end
  end

  @doc """
  批量处理错误结果
  """
  def handle_batch_results(results, context \\ %{}) do
    {successes, errors} = Enum.split_with(results, &match?({:ok, _}, &1))

    success_count = length(successes)
    error_count = length(errors)

    if error_count > 0 do
      ChatLogger.log_error(:batch_operation_partial_failure, %{
        type: :batch_error,
        reason: "批量操作部分失败",
        context:
          Map.merge(context, %{
            success_count: success_count,
            error_count: error_count,
            errors: Enum.map(errors, fn {:error, reason} -> reason end)
          })
      })
    end

    %{
      successes: Enum.map(successes, fn {:ok, result} -> result end),
      errors: errors,
      success_count: success_count,
      error_count: error_count
    }
  end

  @doc """
  验证和清理用户输入
  """
  def validate_and_sanitize_input(input, validation_rules) do
    with {:ok, validated} <- validate_input(input, validation_rules),
         {:ok, sanitized} <- sanitize_input(validated) do
      {:ok, sanitized}
    else
      {:error, reason} ->
        log_and_format_error(:input_validation_failed, "输入验证失败: #{inspect(reason)}", %{
          input: input
        })
    end
  end

  # 私有函数

  defp handle_changeset_error(changeset, error_type, context) do
    errors = Ash.Changeset.errors(changeset)
    error_messages = Enum.map(errors, &format_changeset_error/1)

    log_and_format_error(
      error_type,
      "验证失败: #{Enum.join(error_messages, ", ")}",
      Map.merge(context, %{validation_errors: errors})
    )
  end

  defp format_changeset_error(error) do
    case error do
      %{field: field, message: message} -> "#{field}: #{message}"
      %{message: message} -> message
      _ -> inspect(error)
    end
  end

  defp log_and_format_error(error_type, message, context) do
    game_type = Map.get(context, :game_type, :system)

    ChatLogger.log_error(
      error_type,
      %{
        type: error_type,
        reason: message,
        context: context
      },
      game_type: game_type
    )

    {:error,
     %{
       type: error_type,
       message: message,
       context: context
     }}
  end

  defp handle_generic_error(reason, context) do
    log_and_format_error(:generic_error, "操作失败: #{inspect(reason)}", context)
  end

  defp validate_input(input, rules) do
    # 实现输入验证逻辑
    # 这里可以根据具体需求实现各种验证规则
    {:ok, input}
  end

  defp sanitize_input(input) do
    # 实现输入清理逻辑
    # 例如HTML转义、SQL注入防护等
    {:ok, input}
  end
end
