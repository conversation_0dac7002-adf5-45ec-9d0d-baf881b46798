defmodule Cypridina.Chat.ChatMessage do
  @moduledoc """
  聊天消息资源

  管理聊天消息的发送、接收、状态更新等功能。
  支持文本消息、图片消息、文件消息等多种消息类型。
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.Chat,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :session_id, :sender_id, :message_type, :content, :status, :inserted_at]
  end

  postgres do
    table "chat_messages"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_session
    define :list_unread_messages
    define :mark_as_read
    define :send_message
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    # 根据会话ID查询消息
    read :list_by_session do
      argument :session_id, :uuid, allow_nil?: false
      argument :limit, :integer, default: 50
      argument :offset, :integer, default: 0

      filter expr(session_id == ^arg(:session_id))
      prepare build(load: [:sender, :read_receipts])
      # 改为升序，最新消息在下方
      prepare build(sort: [inserted_at: :asc])
      prepare build(limit: arg(:limit), offset: arg(:offset))
    end

    # 查询未读消息
    read :list_unread_messages do
      argument :user_id, :uuid, allow_nil?: false
      argument :session_id, :uuid, allow_nil?: true

      prepare fn query, _context ->
        require Ash.Query
        import Ash.Expr

        user_id = Ash.Query.get_argument(query, :user_id)
        session_id = Ash.Query.get_argument(query, :session_id)

        query =
          Ash.Query.filter(
            query,
            expr(
              sender_id != ^user_id and
                not exists(read_receipts, user_id == ^user_id)
            )
          )

        if session_id do
          Ash.Query.filter(query, expr(session_id == ^session_id))
        else
          query
        end
      end

      prepare build(load: [:sender, :session])
      prepare build(sort: [inserted_at: :desc])
    end

    # 发送消息
    create :send_message do
      argument :session_id, :uuid, allow_nil?: false
      argument :sender_id, :uuid, allow_nil?: false
      argument :content, :string, allow_nil?: false
      argument :message_type, :atom, default: :text
      argument :reply_to_id, :uuid, allow_nil?: true
      argument :attachments, {:array, :map}, default: []

      change set_attribute(:session_id, arg(:session_id))
      change set_attribute(:sender_id, arg(:sender_id))
      change set_attribute(:content, arg(:content))
      change set_attribute(:message_type, arg(:message_type))
      change set_attribute(:reply_to_id, arg(:reply_to_id))
      change set_attribute(:attachments, arg(:attachments))
      change set_attribute(:status, :sent)

      # 更新会话的最后消息时间
      change fn changeset, _context ->
        session_id = Ash.Changeset.get_argument(changeset, :session_id)

        # 这里应该更新对应会话的 last_message_at
        # 可以通过 after_action 钩子来实现
        changeset
      end
    end

    # 标记消息为已读
    update :mark_as_read do
      argument :user_id, :uuid, allow_nil?: false

      change fn changeset, _context ->
        # 这里需要创建或更新 read_receipt 记录
        # 实际实现中需要通过 after_action 钩子来处理
        changeset
      end
    end

    # 撤回消息
    update :recall_message do
      accept []
      change set_attribute(:status, :recalled)
      change set_attribute(:recalled_at, &DateTime.utc_now/0)
    end

    # 删除消息
    update :delete_message do
      accept []
      change set_attribute(:status, :deleted)
      change set_attribute(:deleted_at, &DateTime.utc_now/0)
    end

    # 编辑消息
    update :edit_message do
      accept [:content]
      change set_attribute(:edited_at, &DateTime.utc_now/0)

      validate fn changeset, _context ->
        # 只允许发送者编辑消息
        # 只允许在发送后一定时间内编辑
        :ok
      end
    end
  end

  validations do
    validate present([:session_id, :sender_id, :content, :message_type])

    validate fn changeset, _context ->
      message_type = Ash.Changeset.get_attribute(changeset, :message_type)
      content = Ash.Changeset.get_attribute(changeset, :content)
      attachments = Ash.Changeset.get_attribute(changeset, :attachments) || []

      case message_type do
        :text when is_nil(content) or content == "" ->
          {:error, field: :content, message: "文本消息内容不能为空"}

        :image when attachments == [] ->
          {:error, field: :attachments, message: "图片消息必须包含附件"}

        :file when attachments == [] ->
          {:error, field: :attachments, message: "文件消息必须包含附件"}

        _ ->
          :ok
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :session_id, :uuid do
      allow_nil? false
      public? true
      description "所属会话ID"
    end

    attribute :sender_id, :uuid do
      allow_nil? false
      public? true
      description "发送者ID"
    end

    attribute :message_type, :atom do
      allow_nil? false
      public? true
      description "消息类型：text-文本，image-图片，file-文件，system-系统消息"
      default :text
      constraints one_of: [:text, :image, :file, :audio, :video, :location, :system]
    end

    attribute :content, :string do
      allow_nil? false
      public? true
      description "消息内容"
      constraints max_length: 4000
    end

    attribute :attachments, {:array, :map} do
      allow_nil? true
      public? true
      description "附件信息（JSON数组）"
      default []
    end

    attribute :reply_to_id, :uuid do
      allow_nil? true
      public? true
      description "回复的消息ID"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "消息状态：sent-已发送，delivered-已送达，read-已读，recalled-已撤回，deleted-已删除"
      default :sent
      constraints one_of: [:sent, :delivered, :read, :recalled, :deleted]
    end

    attribute :metadata, :map do
      allow_nil? true
      public? true
      description "消息元数据（JSON格式）"
      default %{}
    end

    attribute :edited_at, :utc_datetime do
      allow_nil? true
      public? true
      description "编辑时间"
    end

    attribute :recalled_at, :utc_datetime do
      allow_nil? true
      public? true
      description "撤回时间"
    end

    attribute :deleted_at, :utc_datetime do
      allow_nil? true
      public? true
      description "删除时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :session, Cypridina.Chat.ChatSession do
      public? true
      source_attribute :session_id
      destination_attribute :id
    end

    belongs_to :sender, Cypridina.Accounts.User do
      public? true
      source_attribute :sender_id
      destination_attribute :id
    end

    belongs_to :reply_to, Cypridina.Chat.ChatMessage do
      public? true
      source_attribute :reply_to_id
      destination_attribute :id
    end

    has_many :replies, Cypridina.Chat.ChatMessage do
      public? true
      source_attribute :id
      destination_attribute :reply_to_id
    end

    has_many :read_receipts, Cypridina.Chat.MessageReadReceipt do
      public? true
      source_attribute :id
      destination_attribute :message_id
    end

    has_many :delivery_receipts, Cypridina.Chat.MessageDeliveryReceipt do
      public? true
      source_attribute :id
      destination_attribute :message_id
    end
  end

  calculations do
    # 计算消息是否已被特定用户读取
    calculate :is_read_by, :boolean do
      argument :user_id, :uuid, allow_nil?: false

      calculation fn records, %{user_id: user_id} ->
        Enum.map(records, fn message ->
          # 检查是否存在该用户的已读回执
          Enum.any?(message.read_receipts || [], fn receipt ->
            receipt.user_id == user_id
          end)
        end)
      end
    end

    # 计算消息是否已送达特定用户
    calculate :is_delivered_to, :boolean do
      argument :user_id, :uuid, allow_nil?: false

      calculation fn records, %{user_id: user_id} ->
        Enum.map(records, fn message ->
          # 检查是否存在该用户的送达回执
          Enum.any?(message.delivery_receipts || [], fn receipt ->
            receipt.user_id == user_id
          end)
        end)
      end
    end

    # 计算已读用户数量
    calculate :read_count, :integer, expr(count(read_receipts))

    # 计算已送达用户数量
    calculate :delivered_count, :integer, expr(count(delivery_receipts))

    # 计算消息的综合状态（用于前端显示）
    calculate :display_status, :atom do
      argument :session_participant_count, :integer, allow_nil?: false

      calculation fn records, %{session_participant_count: participant_count} ->
        Enum.map(records, fn message ->
          cond do
            # 消息已被撤回或删除
            message.status in [:recalled, :deleted] ->
              message.status

            # 所有人都已读（除了发送者）
            length(message.read_receipts || []) >= participant_count - 1 ->
              :read

            # 所有人都已送达（除了发送者）
            length(message.delivery_receipts || []) >= participant_count - 1 ->
              :delivered

            # 默认为已发送
            true ->
              :sent
          end
        end)
      end
    end
  end
end
