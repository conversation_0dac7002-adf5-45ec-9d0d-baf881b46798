defmodule Cypridina.Uploaders.Avatar do
  @moduledoc """
  Waffle头像上传器
  支持上传到MinIO/S3存储
  """

  use Waffle.Definition
  use Waffle.Ecto.Definition

  @versions [:original, :thumb]

  # 白名单文件扩展名
  def validate({file, _}) do
    file_extension = file.file_name |> Path.extname() |> String.downcase()

    case Enum.member?(~w(.jpg .jpeg .png .gif .webp), file_extension) do
      true -> :ok
      false -> {:error, "不支持的文件格式"}
    end
  end

  # 定义转换版本
  def transform(:thumb, _) do
    {:convert, "-strip -thumbnail 150x150^ -gravity center -extent 150x150 -format png", :png}
  end

  # 文件名生成
  def filename(version, {file, scope}) do
    user_id =
      case scope do
        %{id: id} -> id
        %{user_id: user_id} -> user_id
        %{"user_id" => user_id} -> user_id
        _ -> "unknown"
      end

    timestamp = System.system_time(:millisecond)
    file_name = Path.basename(file.file_name, Path.extname(file.file_name))
    "#{user_id}_#{timestamp}_#{version}"
  end

  # 存储目录
  def storage_dir(version, {file, scope}) do
    user_id =
      case scope do
        %{id: id} -> id
        %{user_id: user_id} -> user_id
        %{"user_id" => user_id} -> user_id
        _ -> "unknown"
      end

    "avatars/#{user_id}"
  end

  # 默认URL（当没有头像时）
  def default_url(version, scope) do
    "/images/avatars/default_#{version}.png"
  end

  # S3配置
  def s3_object_headers(version, {file, scope}) do
    [content_type: MIME.from_path(file.file_name)]
  end
end
