defmodule Cypridina.Uploaders.ChatFile do
  @moduledoc """
  聊天文件上传器
  支持上传聊天中的图片、文档、音频、视频等文件到MinIO/S3存储
  """

  use Waffle.Definition
  use Waffle.Ecto.Definition

  @versions [:original, :thumb]

  # 支持的文件类型
  @image_extensions ~w(.jpg .jpeg .png .gif .webp .bmp)
  @document_extensions ~w(.pdf .doc .docx .xls .xlsx .ppt .pptx .txt .rtf)
  @audio_extensions ~w(.mp3 .wav .aac .ogg .m4a)
  @video_extensions ~w(.mp4 .avi .mov .wmv .flv .webm)
  @archive_extensions ~w(.zip .rar .7z .tar .gz)

  @all_extensions @image_extensions ++
                    @document_extensions ++
                    @audio_extensions ++ @video_extensions ++ @archive_extensions

  # 文件验证
  def validate({file, _}) do
    file_extension = file.file_name |> Path.extname() |> String.downcase()

    cond do
      # 检查文件扩展名
      not Enum.member?(@all_extensions, file_extension) ->
        {:error, "不支持的文件格式"}

      # 检查文件大小（最大50MB）
      file.file_size && file.file_size > 50 * 1024 * 1024 ->
        {:error, "文件大小不能超过50MB"}

      # 图片文件大小限制（最大10MB）
      Enum.member?(@image_extensions, file_extension) && file.file_size &&
          file.file_size > 10 * 1024 * 1024 ->
        {:error, "图片文件大小不能超过10MB"}

      # 视频文件大小限制（最大100MB）
      Enum.member?(@video_extensions, file_extension) && file.file_size &&
          file.file_size > 100 * 1024 * 1024 ->
        {:error, "视频文件大小不能超过100MB"}

      true ->
        :ok
    end
  end

  # 定义转换版本 - 只对图片生成缩略图
  def transform(:thumb, {file, _scope}) do
    file_extension = file.file_name |> Path.extname() |> String.downcase()

    if Enum.member?(@image_extensions, file_extension) do
      # 为图片生成缩略图
      {:convert, "-strip -thumbnail 300x300^ -gravity center -extent 300x300 -format jpg", :jpg}
    else
      # 非图片文件不生成缩略图
      :noaction
    end
  end

  # 文件名生成
  def filename(version, {file, scope}) do
    user_id = get_user_id(scope)
    session_id = get_session_id(scope)

    timestamp = System.system_time(:millisecond)
    file_extension = Path.extname(file.file_name)
    base_name = Path.basename(file.file_name, file_extension)

    # 清理文件名，移除特殊字符
    clean_base_name =
      base_name
      |> String.replace(~r/[^\w\-_\.]/, "_")
      # 限制长度
      |> String.slice(0, 50)

    case version do
      :thumb -> "#{user_id}_#{session_id}_#{timestamp}_#{clean_base_name}_thumb.jpg"
      _ -> "#{user_id}_#{session_id}_#{timestamp}_#{clean_base_name}#{file_extension}"
    end
  end

  # 存储目录
  def storage_dir(version, {file, scope}) do
    user_id = get_user_id(scope)
    session_id = get_session_id(scope)

    file_extension = file.file_name |> Path.extname() |> String.downcase()

    # 根据文件类型分类存储
    category =
      cond do
        Enum.member?(@image_extensions, file_extension) -> "images"
        Enum.member?(@document_extensions, file_extension) -> "documents"
        Enum.member?(@audio_extensions, file_extension) -> "audio"
        Enum.member?(@video_extensions, file_extension) -> "videos"
        Enum.member?(@archive_extensions, file_extension) -> "archives"
        true -> "others"
      end

    "chat_files/#{category}/#{session_id}/#{user_id}"
  end

  # S3配置
  def s3_object_headers(version, {file, scope}) do
    content_type = MIME.from_path(file.file_name)

    headers = [
      content_type: content_type,
      # 1年缓存
      cache_control: "public, max-age=31536000"
    ]

    # 对于某些文件类型，设置为下载而不是在浏览器中打开
    file_extension = file.file_name |> Path.extname() |> String.downcase()

    if Enum.member?(@document_extensions ++ @archive_extensions, file_extension) do
      filename = Path.basename(file.file_name)
      headers ++ [content_disposition: "attachment; filename=\"#{filename}\""]
    else
      headers
    end
  end

  # 获取文件类型
  def get_file_type({file, _scope}) do
    file_extension = file.file_name |> Path.extname() |> String.downcase()

    cond do
      Enum.member?(@image_extensions, file_extension) -> :image
      Enum.member?(@document_extensions, file_extension) -> :document
      Enum.member?(@audio_extensions, file_extension) -> :audio
      Enum.member?(@video_extensions, file_extension) -> :video
      Enum.member?(@archive_extensions, file_extension) -> :archive
      true -> :other
    end
  end

  # 检查是否为图片
  def is_image?({file, _scope}) do
    file_extension = file.file_name |> Path.extname() |> String.downcase()
    Enum.member?(@image_extensions, file_extension)
  end

  # 获取文件大小的可读格式
  def format_file_size(size) when is_integer(size) do
    cond do
      size >= 1024 * 1024 * 1024 -> "#{Float.round(size / (1024 * 1024 * 1024), 2)} GB"
      size >= 1024 * 1024 -> "#{Float.round(size / (1024 * 1024), 2)} MB"
      size >= 1024 -> "#{Float.round(size / 1024, 2)} KB"
      true -> "#{size} B"
    end
  end

  def format_file_size(_), do: "未知大小"

  # 私有辅助函数
  defp get_user_id(scope) do
    case scope do
      %{user_id: user_id} -> user_id
      %{sender_id: sender_id} -> sender_id
      %{"user_id" => user_id} -> user_id
      %{"sender_id" => sender_id} -> sender_id
      _ -> "unknown"
    end
  end

  defp get_session_id(scope) do
    case scope do
      %{session_id: session_id} -> session_id
      %{"session_id" => session_id} -> session_id
      _ -> "unknown"
    end
  end
end
