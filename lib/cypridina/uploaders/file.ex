defmodule Cypridina.Uploaders.File do
  @moduledoc """
  Waffle通用文件上传器
  支持上传各种类型的文件到MinIO/S3存储
  """

  use Waffle.Definition
  use Waffle.Ecto.Definition

  @versions [:original]

  # 文件验证
  def validate({file, _}) do
    # 检查文件大小（最大10MB）
    max_size = 10 * 1024 * 1024

    if file.file_size && file.file_size > max_size do
      {:error, "文件大小不能超过10MB"}
    else
      :ok
    end
  end

  # 文件名生成
  def filename(version, {file, scope}) do
    user_id =
      case scope do
        %{id: id} -> id
        %{user_id: user_id} -> user_id
        %{"user_id" => user_id} -> user_id
        _ -> "unknown"
      end

    timestamp = System.system_time(:millisecond)
    file_extension = Path.extname(file.file_name)
    "#{user_id}_#{timestamp}#{file_extension}"
  end

  # 存储目录
  def storage_dir(version, {file, scope}) do
    user_id =
      case scope do
        %{id: id} -> id
        %{user_id: user_id} -> user_id
        %{"user_id" => user_id} -> user_id
        _ -> "unknown"
      end

    "files/#{user_id}"
  end

  # S3配置
  def s3_object_headers(version, {file, scope}) do
    [content_type: MIME.from_path(file.file_name)]
  end
end
