defmodule Cypridina.CustomCurrencies do
  @moduledoc """
  定义应用程序使用的自定义货币
  """

  @doc """
  定义POINTS货币
  这个函数会在Cldr.Currency监督器启动时被调用
  """
  def define_currencies do
    # 定义POINTS货币 - 使用XAA作为私有使用货币代码
    {:ok, _currency} = Cldr.Currency.new(:XAA, name: "Points", digits: 0)

    :ok
  end
end

defmodule Cypridina.Cldr do
  @moduledoc """
  Cypridina CLDR 后端

  定义了国际化和本地化支持，包括：
  - 数字格式化
  - 货币格式化
  - 日期和时间格式化
  - 多语言支持

  用于 ex_money 和其他需要本地化的功能。
  """

  use Cldr,
    otp_app: :cypridina,
    providers: [
      Cldr.Number,
      Cldr.Currency,
      Money.Cldr
    ],
    locales: ["zh", "zh-Hans", "zh-Hant", "en"],
    default_locale: "zh",
    gettext: CypridinaWeb.Gettext
end
