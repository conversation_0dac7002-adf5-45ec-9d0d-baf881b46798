defmodule Cypridina.Ledger.BalanceCache do
  @moduledoc """
  账户余额的 ETS 缓存服务

  支持多种账户类型的余额缓存，提供高性能的余额查询和原子更新：
  - 统一使用账户标识符进行操作
  - 支持用户账户、系统账户、游戏账户
  - 原子性余额增减操作，确保并发安全
  - 自动缓存过期和清理
  - 通过 Ash.Notifier 订阅余额变更，自动更新缓存
  - 缓存统计和监控

  ## 统一账户标识符API（所有通用方法只接受账户标识符）

      # 使用账户标识符获取余额
      {:ok, balance} = BalanceCache.get_balance("user:XAA:user_123")
      {:ok, balance} = BalanceCache.get_balance("system:XAA:main")
      {:ok, balance} = BalanceCache.get_balance("game:XAA:game_456")

      # 设置余额
      BalanceCache.set_balance("user:XAA:user_123", 1000)
      BalanceCache.set_balance("system:XAA:main", 50000)
      BalanceCache.set_balance("game:XAA:game_456", 2000)

      # 原子性增减余额
      {:ok, new_balance} = BalanceCache.add_balance("user:XAA:user_123", 100)
      {:ok, new_balance} = BalanceCache.subtract_balance("user:XAA:user_123", 50)

      # 清除缓存
      BalanceCache.invalidate("user:XAA:user_123")

      # 检查缓存状态
      BalanceCache.cached?("user:XAA:user_123")

  ## 缓存管理示例

      # 清除余额缓存
      BalanceCache.invalidate("user:XAA:user_123")

      # 检查缓存状态
      BalanceCache.stats()

      # 清除所有缓存
      BalanceCache.clear_all()

  ## 错误处理

      # 处理余额不足的情况
      case BalanceCache.subtract_balance("user:XAA:user_123", 1000) do
        {:ok, balance} ->
          # 成功减少余额
          IO.puts("新余额: \#{balance}")
        {:error, :insufficient_balance} ->
          # 余额不足
          IO.puts("余额不足")
        {:error, reason} ->
          # 其他错误
          IO.puts("操作失败: \#{reason}")
      end

  """
  use GenServer
  require Logger
  require Ash.Query
  alias Cypridina.Ledger.AccountIdentifier

  @table_name :balance_cache
  # 默认缓存1小时
  @ttl :timer.hours(1)
  # 每5分钟清理过期数据
  @cleanup_interval :timer.minutes(5)
  # 奖池更新回调函数存储表
  @jackpot_callbacks_table :jackpot_callbacks
  # account_id 到 identifier 的映射表
  @account_id_to_identifier_table :account_id_to_identifier
  # identifier 到 account_id 的映射表
  @identifier_to_account_id_table :identifier_to_account_id

  # Client API

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  # ========== 通用API ==========

  @doc """
  通用获取余额方法，只接受账户标识符

  ## 参数
  - `identifier`: 账户标识符（如 "user:XAA:user_123"、"system:XAA:main"、"game:XAA:game_456"）

  ## 返回值
  - `{:ok, balance}`: 成功，返回余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, balance} = BalanceCache.get_balance("user:XAA:user_123")
      {:ok, balance} = BalanceCache.get_balance("system:XAA:main")
      {:ok, balance} = BalanceCache.get_balance("game:XAA:game_456")
  """
  def get_balance(identifier) when is_binary(identifier) do
    case lookup(identifier) do
      {:ok, balance} ->
        {:ok, balance}

      :not_found ->
        load_balance_from_db(identifier)
    end
  end

  def get_balance(nil), do: {:error, :invalid_identifier}

  @doc """
  通用设置余额方法，只接受账户标识符

  ## 参数
  - `identifier`: 账户标识符（如 "user:XAA:user_123"、"system:XAA:main"、"game:XAA:game_456"）
  - `balance`: 余额值

  ## 返回值
  - `:ok`: 成功
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      BalanceCache.set_balance("user:XAA:user_123", 1000)
      BalanceCache.set_balance("system:XAA:main", 50000)
      BalanceCache.set_balance("game:XAA:game_456", 2000)
  """
  def set_balance(identifier, balance) when is_binary(identifier) and is_integer(balance) do
    case AccountIdentifier.parse(identifier) do
      {:ok, _parsed} ->
        GenServer.cast(__MODULE__, {:set_balance, identifier, balance})
        :ok

      {:error, reason} ->
        {:error, {:invalid_identifier, reason}}
    end
  end

  def set_balance(nil, _balance), do: {:error, :invalid_identifier}

  def set_balance(_identifier, balance) when not is_integer(balance),
    do: {:error, :invalid_balance}

  @doc """
  通用原子性增加余额方法，只接受账户标识符

  ## 参数
  - `identifier`: 账户标识符（如 "user:XAA:user_123"、"system:XAA:main"、"game:XAA:game_456"）
  - `amount`: 增加的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.add_balance("user:XAA:user_123", 100)
      {:ok, new_balance} = BalanceCache.add_balance("system:XAA:main", 1000)
      {:ok, new_balance} = BalanceCache.add_balance("game:XAA:game_456", 200)
  """
  def add_balance(identifier, amount)
      when is_binary(identifier) and is_integer(amount) and amount > 0 do
    case AccountIdentifier.parse(identifier) do
      {:ok, _parsed} ->
        GenServer.call(__MODULE__, {:add_balance, identifier, amount})

      {:error, reason} ->
        {:error, {:invalid_identifier, reason}}
    end
  end

  def add_balance(nil, _amount), do: {:error, :invalid_identifier}
  def add_balance(_identifier, amount) when amount <= 0, do: {:error, :invalid_amount}

  @doc """
  通用原子性减少余额方法，只接受账户标识符

  ## 参数
  - `identifier`: 账户标识符（如 "user:XAA:user_123"、"system:XAA:main"、"game:XAA:game_456"）
  - `amount`: 减少的金额（必须为正数）

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      {:ok, new_balance} = BalanceCache.subtract_balance("user:XAA:user_123", 50)
      {:ok, new_balance} = BalanceCache.subtract_balance("system:XAA:main", 500)
      {:ok, new_balance} = BalanceCache.subtract_balance("game:XAA:game_456", 100)
  """
  def subtract_balance(identifier, amount)
      when is_binary(identifier) and is_integer(amount) and amount > 0 do
    case AccountIdentifier.parse(identifier) do
      {:ok, _parsed} ->
        GenServer.call(__MODULE__, {:subtract_balance, identifier, amount})

      {:error, reason} ->
        {:error, {:invalid_identifier, reason}}
    end
  end

  def subtract_balance(nil, _amount), do: {:error, :invalid_identifier}
  def subtract_balance(_identifier, amount) when amount <= 0, do: {:error, :invalid_amount}

  @doc """
  批量获取多个identifier对应的account_id

  通过缓存机制快速获取多个账户标识符对应的account_id，
  支持用户账户、系统账户和游戏账户的混合查询。

  ## 参数
  - `identifiers`: 账户标识符列表，格式如：
    - `["user:XAA:user_123", "system:XAA:main", "game:XAA:game_456"]`

  ## 返回值
  - `{:ok, [account_id | nil]}`: 成功时返回account_id列表，按输入顺序排列，不存在的用nil填充
  - `{:error, reason}`: 失败时返回错误原因

  ## 示例
      identifiers = [
        "user:XAA:user_123",
        "system:XAA:main",
        "user:XAA:non_existent"
      ]

      {:ok, account_ids} = BalanceCache.get_account_ids_by_identifiers(identifiers)
      # => {:ok, [
      #      "********-89ab-cdef-0123-456789abcdef",  # user_123的account_id
      #      "fedcba98-7654-3210-fedc-ba9876543210",  # main系统账户的account_id
      #      nil                                       # 不存在的账户用nil填充
      #    ]}

  ## 性能特性
  - 使用ETS缓存提高查询性能
  - 支持批量查询，减少数据库访问
  - 自动缓存account_id映射关系
  - 缓存过期时间与余额缓存一致
  - 保持输入顺序，不存在的账户用nil填充
  """
  def get_account_ids_by_identifiers(identifiers) when is_list(identifiers) do
    # try do
    # 过滤有效的标识符，同时保持原始位置信息
    indexed_identifiers =
      identifiers
      |> Enum.with_index()
      |> Enum.filter(fn {identifier, _index} -> is_binary(identifier) end)

    if indexed_identifiers == [] do
      # 如果没有有效的标识符，返回与输入长度相同的nil列表
      result_list = List.duplicate(nil, length(identifiers))
      {:ok, result_list}
    else
      # 提取有效的标识符
      valid_identifiers = Enum.map(indexed_identifiers, fn {identifier, _index} -> identifier end)

      # 分离缓存命中和未命中的标识符
      {cached_results, missing_identifiers} =
        Enum.split_with(valid_identifiers, &account_id_cached?/1)

      # 获取缓存的结果
      cached_map =
        cached_results
        |> Enum.map(fn identifier ->
          case lookup_account_id(identifier) do
            {:ok, account_id} -> {identifier, account_id}
            _ -> nil
          end
        end)
        |> Enum.reject(&is_nil/1)
        |> Map.new()

      # 批量查询未缓存的account_id
      missing_map =
        if missing_identifiers != [] do
          batch_load_and_cache_account_ids(missing_identifiers)
        else
          %{}
        end

      # 合并结果
      result_map = Map.merge(cached_map, missing_map)

      # 按原始顺序构建结果列表，不存在的用nil填充
      result_list =
        Enum.map(identifiers, fn identifier ->
          if is_binary(identifier) do
            Map.get(result_map, identifier)
          else
            nil
          end
        end)

      {:ok, result_list}
    end

    # rescue
    #   error ->
    #     Logger.warning("Failed to get account IDs by identifiers: #{inspect(error)}")
    #     {:error, :batch_query_failed}
    # end
  end

  @doc """
  通用清除余额缓存方法，只接受账户标识符

  ## 参数
  - `identifier`: 账户标识符（如 "user:XAA:user_123"、"system:XAA:main"、"game:XAA:game_456"）

  ## 返回值
  - `:ok`: 成功
  - `{:error, reason}`: 失败，返回错误原因

  ## 示例
      BalanceCache.invalidate("user:XAA:user_123")
      BalanceCache.invalidate("system:XAA:main")
      BalanceCache.invalidate("game:XAA:game_456")
  """
  def invalidate(identifier) when is_binary(identifier) do
    case AccountIdentifier.parse(identifier) do
      {:ok, _parsed} ->
        GenServer.cast(__MODULE__, {:invalidate, identifier})
        :ok

      {:error, reason} ->
        {:error, {:invalid_identifier, reason}}
    end
  end

  def invalidate(nil), do: {:error, :invalid_identifier}

  # 保持向后兼容的方法名，但也只接受账户标识符
  def invalidate_balance(identifier) when is_binary(identifier) do
    invalidate(identifier)
  end

  @doc """
  清除所有缓存
  """
  def clear_all do
    GenServer.call(__MODULE__, :clear_all)
  end

  @doc """
  获取缓存统计信息
  """
  def stats do
    GenServer.call(__MODULE__, :stats)
  end

  @doc """
  订阅奖池余额变化通知

  ## 参数
  - `callback_fun`: 回调函数，当奖池余额变化时会被调用
    回调函数签名: `(game_id, jackpot_id, old_balance, new_balance) -> any()`
  - `game_id`: 可选，指定要监听的游戏ID，如果为nil则监听所有游戏的奖池
  - `jackpot_id`: 可选，指定要监听的奖池ID，如果为nil则监听指定游戏的所有奖池

  ## 返回值
  - `:ok`: 订阅成功
  - `{:error, reason}`: 订阅失败

  ## 示例
      # 监听所有奖池变化
      BalanceCache.subscribe_jackpot_updates(&on_jackpot_updated/4)

      # 监听特定游戏的所有奖池
      BalanceCache.subscribe_jackpot_updates(&on_jackpot_updated/4, "game_123")

      # 监听特定游戏的特定奖池
      BalanceCache.subscribe_jackpot_updates(&on_jackpot_updated/4, "game_123", "jackpot_456")

  ## 回调函数示例
  """

  def subscribe_jackpot_updates(callback_fun, game_id \\ nil, jackpot_id \\ nil)
      when is_function(callback_fun, 4) do
    GenServer.call(__MODULE__, {:subscribe_jackpot_updates, callback_fun, game_id, jackpot_id})
  end

  @doc """
  取消奖池余额变化订阅

  ## 参数
  - `callback_fun`: 要取消的回调函数
  - `game_id`: 可选，指定游戏ID
  - `jackpot_id`: 可选，指定奖池ID

  ## 返回值
  - `:ok`: 取消订阅成功
  """
  def unsubscribe_jackpot_updates(callback_fun, game_id \\ nil, jackpot_id \\ nil)
      when is_function(callback_fun, 4) do
    GenServer.call(__MODULE__, {:unsubscribe_jackpot_updates, callback_fun, game_id, jackpot_id})
  end

  @doc """
  通用检查缓存方法，只接受账户标识符

  ## 参数
  - `identifier`: 账户标识符（如 "user:XAA:user_123"、"system:XAA:main"、"game:XAA:game_456"）

  ## 返回值
  - `true`: 在缓存中且未过期
  - `false`: 不在缓存中或已过期

  ## 示例
      BalanceCache.cached?("user:XAA:user_123")
      BalanceCache.cached?("system:XAA:main")
      BalanceCache.cached?("game:XAA:game_456")
  """
  def cached?(cache_key) when is_binary(cache_key) do
    case :ets.lookup(@table_name, cache_key) do
      [{^cache_key, _balance, expires_at}] ->
        current_time = System.system_time(:second)
        expires_at > current_time

      [] ->
        false
    end
  end

  def cached?(nil), do: false
  # Server callbacks

  def init(opts) do
    # 创建 ETS 表
    table =
      :ets.new(@table_name, [
        :set,
        :public,
        :named_table,
        read_concurrency: true,
        write_concurrency: true
      ])

    # 创建奖池回调函数存储表
    callbacks_table =
      :ets.new(@jackpot_callbacks_table, [
        :bag,
        :public,
        :named_table,
        read_concurrency: true,
        write_concurrency: true
      ])

    # 创建 account_id 到 identifier 的映射表
    account_id_to_identifier_table =
      :ets.new(@account_id_to_identifier_table, [
        :set,
        :public,
        :named_table,
        read_concurrency: true,
        write_concurrency: true
      ])

    # 创建 identifier 到 account_id 的映射表
    identifier_to_account_id_table =
      :ets.new(@identifier_to_account_id_table, [
        :set,
        :public,
        :named_table,
        read_concurrency: true,
        write_concurrency: true
      ])

    # 订阅 Ash 通知
    subscribe_to_notifications()

    # 定期清理过期缓存
    schedule_cleanup()

    state = %{
      table: table,
      callbacks_table: callbacks_table,
      account_id_to_identifier_table: account_id_to_identifier_table,
      identifier_to_account_id_table: identifier_to_account_id_table,
      ttl: opts[:ttl] || @ttl,
      stats: %{
        hits: 0,
        misses: 0,
        evictions: 0
      }
    }

    {:ok, state}
  end

  def handle_cast({:set_balance, cache_key, balance}, state) do
    cache_balance(cache_key, balance)
    {:noreply, state}
  end

  def handle_cast({:invalidate, cache_key}, state) do
    :ets.delete(@table_name, cache_key)
    Logger.debug("Invalidated balance cache for #{cache_key}")
    {:noreply, state}
  end

  def handle_call(:clear_all, _from, state) do
    :ets.delete_all_objects(@table_name)
    :ets.delete_all_objects(@account_id_to_identifier_table)
    :ets.delete_all_objects(@identifier_to_account_id_table)
    Logger.info("Cleared all balance cache and account mappings")

    {:reply, :ok, %{state | stats: %{hits: 0, misses: 0, evictions: 0}}}
  end

  def handle_call(:stats, _from, state) do
    cache_size = :ets.info(@table_name, :size)

    stats =
      Map.merge(state.stats, %{
        cache_size: cache_size,
        hit_rate: calculate_hit_rate(state.stats)
      })

    {:reply, stats, state}
  end

  def handle_call({:add_balance, cache_key, amount}, _from, state) do
    result = atomic_update_balance(cache_key, amount, :add)
    {:reply, result, state}
  end

  def handle_call({:subtract_balance, cache_key, amount}, _from, state) do
    result = atomic_update_balance(cache_key, amount, :subtract)
    {:reply, result, state}
  end

  def handle_call({:subscribe_jackpot_updates, callback_fun, game_id, jackpot_id}, _from, state) do
    # 生成订阅键
    subscription_key = build_subscription_key(game_id, jackpot_id)

    # 存储回调函数
    :ets.insert(@jackpot_callbacks_table, {subscription_key, callback_fun})

    Logger.info("订阅奖池更新: #{subscription_key}")
    {:reply, :ok, state}
  end

  def handle_call({:unsubscribe_jackpot_updates, callback_fun, game_id, jackpot_id}, _from, state) do
    # 生成订阅键
    subscription_key = build_subscription_key(game_id, jackpot_id)

    # 删除匹配的回调函数
    :ets.match_delete(@jackpot_callbacks_table, {subscription_key, callback_fun})

    Logger.info("取消订阅奖池更新: #{subscription_key}")
    {:reply, :ok, state}
  end

  # 处理 Phoenix.Socket.Broadcast 广播的 Ash 通知
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: topic, event: event, payload: notification} = broadcast,
        state
      )
      when is_map(broadcast) do
    case topic do
      "balance" ->
        handle_balance_notification(notification)
        {:noreply, state}

      # 处理特定账户的余额通知 "balance:account_id"
      "balance:" <> _account_id ->
        handle_balance_notification(notification)
        {:noreply, state}

      _ ->
        # 忽略其他主题的消息
        {:noreply, state}
    end
  end

  # 定期清理过期缓存
  def handle_info(:cleanup, state) do
    evicted = cleanup_expired()

    new_stats = update_in(state.stats, [:evictions], &(&1 + evicted))

    schedule_cleanup()
    {:noreply, %{state | stats: new_stats}}
  end

  # 处理统计更新
  def handle_info({:update_stats, :hit}, state) do
    new_state = update_in(state, [:stats, :hits], &(&1 + 1))
    {:noreply, new_state}
  end

  def handle_info({:update_stats, :miss}, state) do
    new_state = update_in(state, [:stats, :misses], &(&1 + 1))
    {:noreply, new_state}
  end

  # ========== 缓存查找 ==========
  defp lookup(cache_key) do
    case :ets.lookup(@table_name, cache_key) do
      [{^cache_key, balance, expires_at}] ->
        current_time = System.system_time(:second)

        if expires_at > current_time do
          update_stats(:hit)
          {:ok, balance}
        else
          # 过期了，删除
          :ets.delete(@table_name, cache_key)
          update_stats(:miss)
          :not_found
        end

      [] ->
        update_stats(:miss)
        :not_found
    end
  end

  # ========== 余额加载和缓存 ==========
  defp cache_balance(cache_key, balance) do
    # 使用 System.system_time 来避免时区问题
    expires_at = System.system_time(:second) + div(@ttl, 1000)
    :ets.insert(@table_name, {cache_key, balance, expires_at})
    Logger.debug("Cached balance #{balance} for #{cache_key}")
  end

  # ========== 映射管理函数 ==========

  @doc false
  defp store_account_mapping(account_id, identifier) do
    :ets.insert(@account_id_to_identifier_table, {account_id, identifier})
    :ets.insert(@identifier_to_account_id_table, {identifier, account_id})
    Logger.debug("Stored account mapping: #{account_id} <-> #{identifier}")
  end

  @doc false
  defp get_identifier_by_account_id(account_id) do
    case :ets.lookup(@account_id_to_identifier_table, account_id) do
      [{^account_id, identifier}] -> {:ok, identifier}
      [] -> {:error, :not_found}
    end
  end

  @doc false
  defp get_account_id_by_identifier(identifier) do
    case :ets.lookup(@identifier_to_account_id_table, identifier) do
      [{^identifier, account_id}] -> {:ok, account_id}
      [] -> {:error, :not_found}
    end
  end

  @doc false
  defp remove_account_mapping(account_id, identifier) do
    :ets.delete(@account_id_to_identifier_table, account_id)
    :ets.delete(@identifier_to_account_id_table, identifier)
    Logger.debug("Removed account mapping: #{account_id} <-> #{identifier}")
  end

  defp subscribe_to_notifications do
    CypridinaWeb.Endpoint.subscribe("balance")
    Logger.info("BalanceCache subscribed to Balance notifications")
  end

  defp handle_balance_notification(notification) do
    IO.puts("Received balance notification: #{inspect(notification)}")

    case notification do
      %{resource: Cypridina.Ledger.Balance, action: :upsert_balance, data: balance} ->
        # 同步处理余额更新通知，确保按顺序处理
        update_balance_from_notification(balance)

      _ ->
        # 忽略其他通知
        :ok
    end
  end

  defp update_balance_from_notification(balance) do
    # 首先尝试从映射中获取 identifier
    case get_identifier_by_account_id(balance.account_id) do
      {:ok, identifier} ->
        # 从映射中找到了 identifier，直接使用
        balance_integer = Money.to_decimal(balance.balance) |> Decimal.to_integer()
        # 更新缓存
        cache_balance(identifier, balance_integer)
        Logger.debug("Updated balance cache for #{identifier}: #{balance_integer}")

      {:error, _} ->
        # 如果找不到映射，尝试从账户资源获取identifier
        case Cypridina.Ledger.Account |> Ash.get(balance.account_id) do
          {:ok, account} ->
            balance_integer = Money.to_decimal(balance.balance) |> Decimal.to_integer()
            cache_balance(account.identifier, balance_integer)
            # 同时存储映射关系
            store_account_mapping(account.id, account.identifier)
            Logger.debug("Updated balance cache for #{account.identifier}: #{balance_integer}")

          {:error, reason} ->
            Logger.warning("Failed to get account for balance notification: #{inspect(reason)}")
        end
    end
  end

  defp cleanup_expired do
    current_time = System.system_time(:second)

    # 使用 foldl 遍历表，找出过期的条目
    expired =
      :ets.foldl(
        fn {cache_key, _balance, expires_at}, acc ->
          if expires_at <= current_time do
            [cache_key | acc]
          else
            acc
          end
        end,
        [],
        @table_name
      )

    # 删除过期条目
    Enum.each(expired, fn cache_key ->
      :ets.delete(@table_name, cache_key)
    end)

    length(expired)
  end

  defp schedule_cleanup do
    Process.send_after(self(), :cleanup, @cleanup_interval)
  end

  defp update_stats(type) do
    case Process.whereis(__MODULE__) do
      nil -> :ok
      pid -> send(pid, {:update_stats, type})
    end
  end

  defp calculate_hit_rate(%{hits: hits, misses: misses}) do
    total = hits + misses

    if total > 0 do
      Float.round(hits / total * 100, 2)
    else
      0.0
    end
  end

  # ========== 原子更新操作 ==========

  @doc """
  原子性更新余额的核心实现

  使用 ETS 的原子操作来确保并发安全性。如果缓存中不存在该键，
  会先尝试从 Ledger 系统加载当前余额，然后执行更新操作。

  ## 参数
  - `cache_key`: 缓存键
  - `amount`: 变更金额
  - `operation`: 操作类型 (:add 或 :subtract)

  ## 返回值
  - `{:ok, new_balance}`: 成功，返回新的余额
  - `{:error, reason}`: 失败，返回错误原因
  """
  defp atomic_update_balance(cache_key, amount, operation) do
    current_time = System.system_time(:second)
    expires_at = current_time + div(@ttl, 1000)

    # 检查缓存是否存在
    case :ets.lookup(@table_name, cache_key) do
      [{^cache_key, balance, _expires_at}] ->
        # 缓存存在，直接执行原子操作
        new_balance =
          case operation do
            :add -> :ets.update_counter(@table_name, cache_key, {2, amount})
            :subtract -> :ets.update_counter(@table_name, cache_key, {2, -amount})
          end

        # 检查是否是奖池账户，如果是则触发回调
        case AccountIdentifier.parse(cache_key) do
          {:ok, %{type: :jackpot, game_id: game_id, jackpot_id: jackpot_id}} ->
            trigger_jackpot_callbacks(game_id, jackpot_id, balance, new_balance)

          _ ->
            :ok
        end

        {:ok, new_balance}

      [] ->
        # 缓存不存在，从数据库加载
        case load_balance_from_db(cache_key) do
          {:ok, db_balance} ->
            # 从数据库加载成功，执行操作并缓存结果

            new_balance =
              case operation do
                :add -> db_balance + amount
                :subtract -> db_balance - amount
              end

            # 检查是否是奖池账户，如果是则触发回调
            case AccountIdentifier.parse(cache_key) do
              {:ok, %{type: :jackpot, game_id: game_id, jackpot_id: jackpot_id}} ->
                trigger_jackpot_callbacks(game_id, jackpot_id, db_balance, new_balance)

              _ ->
                :ok
            end

            # 缓存新余额
            cache_balance(cache_key, new_balance)
            {:ok, new_balance}

          {:error, reason} ->
            {:error, reason}
        end
    end
  end

  # 根据缓存键从数据库加载余额
  defp load_balance_from_db(cache_key) do
    # 首先尝试作为identifier解析
    balance_integer = Cypridina.Ledger.Account.get_balance_by_identifier(cache_key)
    cache_balance(cache_key, balance_integer)
    {:ok, balance_integer}
  end

  # ========== Account ID 缓存相关函数 ==========

  # 检查account_id是否在缓存中
  defp account_id_cached?(identifier) when is_binary(identifier) do
    account_id_cache_key = "account_id:#{identifier}"
    cached?(account_id_cache_key)
  end

  # 从缓存中查找account_id
  defp lookup_account_id(identifier) when is_binary(identifier) do
    account_id_cache_key = "account_id:#{identifier}"

    case :ets.lookup(@table_name, account_id_cache_key) do
      [{^account_id_cache_key, account_id, expires_at}] ->
        current_time = System.system_time(:second)

        if expires_at > current_time do
          {:ok, account_id}
        else
          # 过期了，删除
          :ets.delete(@table_name, account_id_cache_key)
          :not_found
        end

      [] ->
        :not_found
    end
  end

  # 逐个调用Ledger方法获取并缓存account_id
  defp batch_load_and_cache_account_ids(identifiers) when is_list(identifiers) do
    identifiers
    |> Enum.map(&get_account_by_identifier/1)
    |> Enum.reject(&is_nil/1)
    |> Map.new()
  end

  # 根据标识符调用对应的Ledger方法获取账户
  defp get_account_by_identifier(identifier) when is_binary(identifier) do
    {:ok, account} =
      Cypridina.Ledger.Account.get_or_create_by_identifier(%{
        identifier: identifier,
        currency: :XAA
      })

    cache_account_id(identifier, account.id)
    # 同时存储双向映射
    store_account_mapping(account.id, identifier)
    {identifier, account.id}
  end

  # 缓存account_id
  defp cache_account_id(identifier, account_id)
       when is_binary(identifier) and is_binary(account_id) do
    account_id_cache_key = "account_id:#{identifier}"
    expires_at = System.system_time(:second) + div(@ttl, 1000)
    :ets.insert(@table_name, {account_id_cache_key, account_id, expires_at})
  end

  # ========== 奖池回调相关函数 ==========

  # 构建订阅键
  defp build_subscription_key(nil, nil), do: "jackpot:*:*"
  defp build_subscription_key(game_id, nil) when not is_nil(game_id), do: "jackpot:#{game_id}:*"

  defp build_subscription_key(game_id, jackpot_id)
       when not is_nil(game_id) and not is_nil(jackpot_id),
       do: "jackpot:#{game_id}:#{jackpot_id}"

  # 获取缓存中的余额，如果不存在返回0
  defp get_cached_balance(cache_key) do
    case :ets.lookup(@table_name, cache_key) do
      [{^cache_key, balance, expires_at}] ->
        current_time = System.system_time(:second)

        if expires_at > current_time do
          balance
        else
          0
        end

      [] ->
        0
    end
  end

  # 触发奖池回调函数
  defp trigger_jackpot_callbacks(game_id, jackpot_id, old_balance, new_balance) do
    # 构建可能匹配的订阅键列表
    subscription_keys = [
      # 监听所有奖池
      "jackpot:*:*",
      # 监听特定游戏的所有奖池
      "jackpot:#{game_id}:*",
      # 监听特定奖池
      "jackpot:#{game_id}:#{jackpot_id}"
    ]

    # 遍历所有可能的订阅键，查找并执行回调函数
    Enum.each(subscription_keys, fn subscription_key ->
      case :ets.lookup(@jackpot_callbacks_table, subscription_key) do
        [] ->
          :ok

        callbacks ->
          Enum.each(callbacks, fn {_key, callback_fun} ->
            try do
              # 异步执行回调函数，避免阻塞缓存更新
              Task.start(fn ->
                callback_fun.(game_id, jackpot_id, old_balance, new_balance)
              end)
            rescue
              error ->
                Logger.error("奖池回调函数执行失败: #{inspect(error)}")
            end
          end)
      end
    end)
  end
end
