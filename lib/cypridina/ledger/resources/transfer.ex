defmodule Cypridina.Ledger.Transfer do
  @moduledoc """
  转账资源

  记录所有的积分转账，包含丰富的业务元数据：
  - 交易类型（充值、消费、转账等）
  - 游戏相关信息
  - 业务描述和额外数据
  """

  use Ash.Resource,
    domain: Cypridina.Ledger,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshDoubleEntry.Transfer, AshAdmin.Resource]

  transfer do
    # 配置相关资源
    account_resource Cypridina.Ledger.Account
    balance_resource Cypridina.Ledger.Balance
  end

  admin do
    table_columns [:id, :amount, :transaction_type, :from_account, :to_account, :timestamp]
  end

  postgres do
    table "ledger_transfers"
    repo Cypridina.Repo
  end

  code_interface do
    define :transfer, action: :transfer
    define :read

    define :reverse_transfer
    define :cancel
  end

  # 引入交易类型枚举
  alias Cypridina.Types.TransactionType

  actions do
    # ash_double_entry会自动添加transfer action

    # 自定义读取操作
    read :read do
      primary? true
      pagination offset?: true, keyset?: true, countable: true
    end

    # 按账户查询转账记录
    read :by_account do
      argument :account_id, :uuid, allow_nil?: false
      # 简化过滤器，避免复杂的expr语法
      prepare build(load: [:from_account, :to_account])
    end

    # 按用户查询转账记录（通过identifier）
    read :by_user do
      argument :user_id, :string, allow_nil?: false
      argument :currency, :atom, allow_nil?: false, default: :XAA

      filter expr(from_account_id == ^arg(:account_id) or to_account_id == ^arg(:account_id))

      argument :account_id, :uuid, allow_nil?: false

      prepare fn query, _context ->
        user_id = Ash.Query.get_argument(query, :user_id)
        currency = Ash.Query.get_argument(query, :currency)
        user_identifier = "user:#{currency}:#{user_id}"

        # 首先获取用户账户
        case Cypridina.Ledger.Account.get_by_identifier(user_identifier) do
          {:ok, account} ->
            account_id = account.id
            Ash.Query.set_argument(query, :account_id, account_id)

          {:error, _} ->
            # 如果账户不存在，设置一个不存在的ID
            Ash.Query.set_argument(query, :account_id, Ash.UUID.generate())
        end
      end

      prepare build(load: [:from_account, :to_account])
      prepare build(sort: [timestamp: :desc])
    end

    # 按时间范围查询
    read :by_date_range do
      argument :start_date, :utc_datetime_usec, allow_nil?: false
      argument :end_date, :utc_datetime_usec, allow_nil?: false
      # 简化过滤器
      prepare build(load: [:from_account, :to_account])
    end

    # 查询代理收到的退费请求
    read :agent_refund_requests do
      argument :agent_account_id, :string, allow_nil?: false
      argument :status, :atom, allow_nil?: true

      filter expr(
               transaction_type == :refund and
                 to_account_id == ^arg(:agent_account_id) and
                 (is_nil(^arg(:status)) or status == ^arg(:status))
             )

      prepare build(load: [:from_account, :to_account])
      prepare build(sort: [timestamp: :desc])
    end

    # 查询待处理的退费请求
    read :pending_refund_requests do
      filter expr(transaction_type == :refund and status == :pending)
      prepare build(load: [:from_account, :to_account])
      prepare build(sort: [timestamp: :desc])
    end

    # 自定义转账创建操作
    create :transfer do
      accept [
        :amount,
        :from_account_id,
        :to_account_id,
        :timestamp,
        :transaction_type,
        :description,
        :metadata,
        :business_id,
        :status
      ]

      change fn changeset, _context ->
        # 设置默认时间戳
        case Ash.Changeset.get_attribute(changeset, :timestamp) do
          nil -> Ash.Changeset.change_attribute(changeset, :timestamp, DateTime.utc_now())
          _ -> changeset
        end
      end
    end

    # 取消转账
    update :cancel do
      change set_attribute(:status, :cancelled)

      validate fn changeset, _context ->
        current_status = changeset.data.status

        if current_status == :completed do
          {:error, field: :status, message: "已完成的转账不能取消"}
        else
          :ok
        end
      end
    end

    # 批准退费请求
    update :approve_refund do
      argument :approved_by, :string, allow_nil?: false
      require_atomic? false

      change set_attribute(:status, :completed)

      change fn changeset, _context ->
        approved_by = Ash.Changeset.get_argument(changeset, :approved_by)
        current_metadata = Ash.Changeset.get_attribute(changeset, :metadata) || %{}

        new_metadata =
          Map.merge(current_metadata, %{
            "approved_by" => approved_by,
            "approved_at" => DateTime.utc_now()
          })

        Ash.Changeset.change_attribute(changeset, :metadata, new_metadata)
      end

      validate fn changeset, _context ->
        current_status = changeset.data.status
        transaction_type = changeset.data.transaction_type

        cond do
          transaction_type != :refund ->
            {:error, field: :transaction_type, message: "只能批准退费请求"}

          current_status != :pending ->
            {:error, field: :status, message: "只能批准待审核的退费请求"}

          true ->
            :ok
        end
      end
    end

    # 撤销转账（创建反向转账）
    update :reverse_transfer do
      argument :reversed_by, :string, allow_nil?: false
      argument :reverse_reason, :string, allow_nil?: true
      require_atomic? false

      change set_attribute(:status, :cancelled)

      change fn changeset, _context ->
        reversed_by = Ash.Changeset.get_argument(changeset, :reversed_by)
        reverse_reason = Ash.Changeset.get_argument(changeset, :reverse_reason)
        current_metadata = Ash.Changeset.get_attribute(changeset, :metadata) || %{}

        new_metadata =
          Map.merge(current_metadata, %{
            "reversed_by" => reversed_by,
            "reversed_at" => DateTime.utc_now(),
            "reverse_reason" => reverse_reason,
            "is_reversed" => true
          })

        Ash.Changeset.change_attribute(changeset, :metadata, new_metadata)
      end

      validate fn changeset, _context ->
        current_status = changeset.data.status
        current_metadata = changeset.data.metadata || %{}
        transaction_type = changeset.data.transaction_type

        cond do
          # 对于退费请求，允许撤销pending状态的转账
          transaction_type == :refund and current_status == :pending ->
            :ok

          # 对于其他类型的转账，只能撤销已完成的转账
          transaction_type != :refund and current_status != :completed ->
            {:error, field: :status, message: "只能撤销已完成的转账"}

          Map.get(current_metadata, "is_reversed", false) ->
            {:error, field: :status, message: "该转账已经被撤销"}

          true ->
            :ok
        end
      end
    end
  end

  preparations do
    # 默认按时间倒序排列
    prepare build(sort: [timestamp: :desc])
  end

  attributes do
    attribute :id, AshDoubleEntry.ULID do
      primary_key? true
      allow_nil? false
      default &AshDoubleEntry.ULID.generate/0
    end

    attribute :amount, :money do
      allow_nil? false
      description "转账金额"
    end

    timestamps()

    # 交易类型
    attribute :transaction_type, TransactionType do
      allow_nil? false
      description "交易类型"
    end

    # 交易描述
    attribute :description, :string do
      allow_nil? true
      description "交易描述"
    end

    # 业务元数据
    attribute :metadata, :map do
      allow_nil? true
      description "业务元数据，包含游戏类型、房间ID等信息"
    end

    # 交易状态
    attribute :status, :atom do
      allow_nil? false
      default :completed
      constraints one_of: [:pending, :completed, :failed, :cancelled]
      description "交易状态"
    end

    # 关联的业务ID（如订单ID、游戏记录ID等）
    attribute :business_id, :string do
      allow_nil? true
      description "关联的业务ID"
    end
  end

  relationships do
    belongs_to :from_account, Cypridina.Ledger.Account do
      attribute_writable? true
    end

    belongs_to :to_account, Cypridina.Ledger.Account do
      attribute_writable? true
    end

    has_many :balances, Cypridina.Ledger.Balance
  end
end
