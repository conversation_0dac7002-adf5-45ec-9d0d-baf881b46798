defmodule Cypridina.Ledger.Account do
  @moduledoc """
  账户资源

  支持不同类型的账户：
  - 用户账户：每个用户的积分账户
  - 系统账户：用于系统操作（奖励、费用等）
  """

  use Ash.Resource,
    domain: Cypridina.Ledger,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshDoubleEntry.Account, AshAdmin.Resource]

  account do
    # 配置相关资源
    transfer_resource Cypridina.Ledger.Transfer
    balance_resource Cypridina.Ledger.Balance
    # 在open action中接受自定义属性
    open_action_accept [:description, :identifier, :currency]
  end

  admin do
    table_columns [:id, :identifier, :currency, :inserted_at]
  end

  postgres do
    table "ledger_accounts"
    repo Cypridina.Repo
  end

  code_interface do
    define :open, action: :open
    define :read
    define :get_by_identifier, action: :read, get_by_identity: :unique_identifier
    define :get_or_create_by_identifier, action: :upsert_by_identifier

    define :activate
    define :deactivate
  end

  # 账户类型现在通过 AccountIdentifier 模块管理
  # 所有账户操作都通过 identifier 进行，不再使用类型字段筛选

  @doc """
  通过账户标识符获取余额，如果账户不存在则自动创建并返回0余额
  """
  def get_balance_by_identifier(identifier) do
    case get_by_identifier(identifier) do
      {:ok, account} ->
        account
        |> Ash.load!([:balance_as_of])
        |> Map.get(:balance_as_of, Money.new(0, :XAA))
        |> Money.to_decimal()
        |> Decimal.to_integer()

      {:error, _reason} ->
        0
    end
  end

  actions do
    create :upsert_by_identifier do
      accept [:identifier, :currency]
      upsert? true
      upsert_identity :unique_identifier
    end

    read :lock_accounts do
      # Used to lock accounts while doing ledger operations
      prepare {AshDoubleEntry.Account.Preparations.LockForUpdate, []}
    end

    # 自定义读取操作
    read :read do
      primary? true
      pagination offset?: true, keyset?: true, countable: true
    end

    # 按用户ID查找账户（通过identifier）
    read :by_user_id do
      argument :user_id, :string, allow_nil?: false
      argument :currency, :atom, allow_nil?: false, default: :XAA

      prepare fn query, _context ->
        user_id = Ash.Query.get_argument(query, :user_id)
        currency = Ash.Query.get_argument(query, :currency)
        identifier_value = "user:#{currency}:#{user_id}"

        Ash.Query.filter(query, identifier: identifier_value)
      end

      get? true
    end

    # 激活账户
    update :activate do
      change set_attribute(:is_active, true)
    end

    # 停用账户
    update :deactivate do
      change set_attribute(:is_active, false)
    end
  end

  preparations do
    # 默认只显示激活的账户
    prepare build(filter: [is_active: true])
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :identifier, :string do
      allow_nil? false
    end

    attribute :currency, :string do
      allow_nil? false
    end

    timestamps()

    # 账户描述
    attribute :description, :string do
      allow_nil? true
      description "账户描述"
    end

    # 是否激活
    attribute :is_active, :boolean do
      allow_nil? false
      default true
      description "账户是否激活"
    end
  end

  relationships do
    has_many :balances, Cypridina.Ledger.Balance do
      destination_attribute :account_id
    end
  end

  calculations do
    calculate :balance_as_of_ulid, :money do
      calculation {AshDoubleEntry.Account.Calculations.BalanceAsOfUlid, resource: __MODULE__}

      argument :ulid, AshDoubleEntry.ULID do
        allow_nil? false
        allow_expr? true
      end
    end

    calculate :balance_as_of, :money do
      calculation {AshDoubleEntry.Account.Calculations.BalanceAsOf, resource: __MODULE__}

      argument :timestamp, :utc_datetime_usec do
        allow_nil? false
        allow_expr? true
        default &DateTime.utc_now/0
      end
    end
  end

  identities do
    identity :unique_identifier, [:identifier]
  end
end
