defmodule Cypridina.Ledger do
  @moduledoc """
  复式记账系统领域模块

  使用ash_double_entry实现的积分系统，提供：
  - 账户管理（用户账户、系统账户）
  - 转账记录（支持业务元数据）
  - 余额快照（支持历史查询）
  - 完整的审计追踪
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  require Ash.Query
  require Logger

  alias Cypridina.Ledger.AccountIdentifier
  alias Cypridina.Ledger.Account

  admin do
    show? true
  end

  resources do
    resource Cypridina.Ledger.Account
    resource Cypridina.Ledger.Transfer
    resource Cypridina.Ledger.Balance
  end

  def transfer(
        from_identifier,
        to_identifier,
        amount,
        opts \\ []
      ) do
    # 获取账户ID - 修复静默失败问题
    case Cypridina.Ledger.BalanceCache.get_account_ids_by_identifiers([
           from_identifier,
           to_identifier
         ]) do
      {:ok, [from_account_id, to_account_id]}
      when not is_nil(from_account_id) and not is_nil(to_account_id) ->
        transfer_opts = %{
          amount: Money.new!(amount, :XAA),
          from_account_id: from_account_id,
          to_account_id: to_account_id,
          timestamp: DateTime.utc_now(),
          transaction_type: Keyword.get(opts, :transaction_type, :transfer),
          description: Keyword.get(opts, :description, "用户间转账"),
          metadata: build_transfer_metadata(opts),
          business_id: Keyword.get(opts, :business_id)
        }

        Logger.info("创建转账: #{inspect(transfer_opts)}")

        # 在事务中需要返回通知
        Cypridina.Ledger.Transfer
        |> Ash.Changeset.for_create(:transfer, transfer_opts)
        |> Ash.create(return_notifications?: true)
        |> case do
          {:ok, transfer, notifications} ->
            # 在事务外发送通知
            Task.start(fn ->
              Ash.Notifier.notify(notifications)
            end)

            Cypridina.Ledger.BalanceCache.subtract_balance(from_identifier, amount)
            Cypridina.Ledger.BalanceCache.add_balance(to_identifier, amount)

            # 根据转账类型更新bonus money字段
            Task.start(fn ->
              update_bonus_money_for_transfer(
                from_identifier,
                to_identifier,
                amount,
                transfer_opts.transaction_type
              )
            end)

            # 如果是充值或奖励转账，发送金币变化通知
            if transfer_opts.transaction_type not in [:game_bet, :game_win] do
              Logger.info(
                "检测到充值/奖励转账，准备发送金币变化通知: to=#{to_identifier}, amount=#{inspect(amount)}, transaction_type=#{transfer_opts.transaction_type}"
              )

              Task.start(fn ->
                [from_identifier, to_identifier]
                |> Enum.each(fn identifier ->
                  case AccountIdentifier.parse_identifier(identifier) do
                    {:ok, %{type: :user, id: user_id}} ->
                      {:ok, balance} = Cypridina.Ledger.BalanceCache.get_balance(identifier)
                      Teen.Protocol.ProtocolSender.notify_money_change(user_id, balance)

                    _ ->
                      # 非用户账户，跳过通知
                      :ok
                  end
                end)
              end)
            else
              Logger.info("非充值/奖励转账，不发送通知: transaction_type=#{transfer_opts.transaction_type}")
            end

            {:ok, transfer}

          {:error, reason} ->
            {:error, reason}
        end

      {:ok, [from_account_id, to_account_id]} ->
        Logger.error(
          "💰 [LEDGER] 账户不存在: from_account=#{inspect(from_account_id)}, to_account=#{inspect(to_account_id)}"
        )

        {:error, "账户不存在"}

      {:error, reason} ->
        Logger.error("💰 [LEDGER] 获取账户ID失败: #{inspect(reason)}")
        {:error, "获取账户失败: #{inspect(reason)}"}
    end
  end

  def game_bet(0, user_id, amount, opts) do
    system_identifier = AccountIdentifier.system(:fees, :XAA)
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    # 直接使用Ledger系统
    transfer(user_identifier, system_identifier, amount, opts)
  end

  @doc """
  游戏投注
  """
  def game_bet(game_id, user_id, amount, opts) do
    # 生成账户标识符
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    game_identifier = AccountIdentifier.game(game_id, :XAA)

    opts = Keyword.put(opts, :transaction_type, :game_bet)
    transfer(user_identifier, game_identifier, amount, opts)
  end

  def game_win(0, user_id, amount, opts) do
    system_identifier = AccountIdentifier.system(:rewards, :XAA)
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    # 直接使用Ledger系统
    transfer(system_identifier, user_identifier, amount, opts)
  end

  @doc """
  游戏获奖
  """
  def game_win(game_id, user_id, amount, opts) do
    # 生成账户标识符
    game_identifier = AccountIdentifier.game(game_id, :XAA)
    user_identifier = AccountIdentifier.user(user_id, :XAA)

    opts = Keyword.put(opts, :transaction_type, :game_win)
    transfer(game_identifier, user_identifier, amount, opts)
  end

  def game_rake(game_id, user_id, amount, opts) when game_id != 0 do
    # 生成账户标识符
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    system_identifier = AccountIdentifier.system(:fees, :XAA)

    opts = Keyword.put(opts, :transaction_type, :game_rake)
    transfer(user_identifier, system_identifier, amount, opts)
  end

  def game_jackpot(game_id, jackpot_id, amount, opts) when game_id != 0 do
    # 生成账户标识符
    game_identifier = AccountIdentifier.game(game_id, :XAA)
    jackpot_identifier = AccountIdentifier.jackpot(game_id, jackpot_id, :XAA)

    opts = Keyword.put(opts, :transaction_type, :game_jackpot)
    transfer(game_identifier, jackpot_identifier, amount, opts)
  end

  @doc """
  奖池支付给用户
  """
  def jackpot_win(game_id, jackpot_id, user_id, amount, opts) when game_id != 0 do
    # 生成账户标识符
    jackpot_identifier = AccountIdentifier.jackpot(game_id, jackpot_id, :XAA)
    user_identifier = AccountIdentifier.user(user_id, :XAA)

    opts = Keyword.put(opts, :transaction_type, :jackpot_win)
    transfer(jackpot_identifier, user_identifier, amount, opts)
  end

  @doc """
  初始化奖池底金
  """
  def init_jackpot(game_id, jackpot_id, amount, opts \\ []) when game_id != 0 do
    # 生成账户标识符
    system_identifier = AccountIdentifier.system(:jackpot_fund, :XAA)
    jackpot_identifier = AccountIdentifier.jackpot(game_id, jackpot_id, :XAA)

    opts = Keyword.put(opts, :transaction_type, :jackpot_init)
    transfer(system_identifier, jackpot_identifier, amount, opts)
  end

  @doc """
  获取代理收到的退费请求
  """
  def get_agent_refund_requests(agent_id, status \\ nil) do
    identifier = AccountIdentifier.user(agent_id, :XAA)

    case Account.get_or_create_by_identifier(%{identifier: identifier, currency: :XAA}) do
      {:ok, agent_account} ->
        args = %{agent_account_id: agent_account.id}
        args = if status, do: Map.put(args, :status, status), else: args

        Cypridina.Ledger.Transfer
        |> Ash.Query.for_read(:agent_refund_requests, args)
        |> Ash.read()

      error ->
        error
    end
  end

  @doc """
  获取所有待处理的退费请求
  """
  def get_pending_refund_requests do
    Cypridina.Ledger.Transfer
    |> Ash.Query.for_read(:pending_refund_requests)
    |> Ash.read()
  end

  @doc """
  批准退费请求
  """
  def approve_refund_request(transfer_id, approved_by) do
    case Cypridina.Ledger.Transfer
         |> Ash.get!(transfer_id)
         |> Ash.Changeset.for_update(:approve_refund, %{approved_by: approved_by})
         |> Ash.update() do
      {:ok, transfer} ->
        Logger.info("退费请求 #{transfer_id} 已批准，批准人: #{approved_by}")
        {:ok, transfer}

      {:error, reason} ->
        Logger.error("批准退费请求失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  拒绝退费请求 - 通过撤销转账实现

  拒绝退费请求时，直接撤销原转账即可：
  - 原转账被标记为已撤销
  - 创建反向转账，将积分从代理账户返还给申请人
  - 简单可靠，避免复杂的状态管理
  """
  def reject_refund_request(transfer_id, rejected_by, reject_reason \\ nil) do
    # 直接使用撤销转账功能
    case reverse_transfer(transfer_id, rejected_by, reject_reason || "退费申请被拒绝") do
      {:ok, result} ->
        Logger.info("退费请求 #{transfer_id} 已拒绝，通过撤销转账实现，操作人: #{rejected_by}")
        {:ok, result.original_transfer}

      {:error, reason} ->
        Logger.error("拒绝退费请求失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  撤销转账

  创建一笔反向转账来撤销原转账的影响
  """
  def reverse_transfer(transfer_id, reversed_by, reverse_reason \\ nil) do
    Cypridina.Repo.transaction(fn ->
      # 获取原转账记录
      case Cypridina.Ledger.Transfer |> Ash.get(transfer_id) do
        {:ok, original_transfer} ->
          # 检查是否可以撤销
          # 对于退费请求，允许撤销pending状态的转账
          # 对于其他类型的转账，只能撤销已完成的转账
          cond do
            original_transfer.transaction_type == :refund and original_transfer.status == :pending ->
              :ok

            original_transfer.transaction_type != :refund and
                original_transfer.status != :completed ->
              Cypridina.Repo.rollback("只能撤销已完成的转账")

            true ->
              :ok
          end

          metadata = original_transfer.metadata || %{}

          if Map.get(metadata, "is_reversed", false) do
            Cypridina.Repo.rollback("该转账已经被撤销")
          end

          # 标记原转账为已撤销
          case Cypridina.Ledger.Transfer.reverse_transfer(transfer_id, %{
                 reversed_by: reversed_by,
                 reverse_reason: reverse_reason
               }) do
            {:ok, reversed_transfer} ->
              # 创建反向转账
              reverse_transfer_opts = %{
                amount: original_transfer.amount,
                # 反向
                from_account_id: original_transfer.to_account_id,
                # 反向
                to_account_id: original_transfer.from_account_id,
                timestamp: DateTime.utc_now(),
                transaction_type: :refund,
                description: "撤销转账: #{original_transfer.description || "无描述"}",
                metadata: %{
                  "operation" => "reverse_transfer",
                  "original_transfer_id" => transfer_id,
                  "reversed_by" => reversed_by,
                  "reverse_reason" => reverse_reason || "管理员撤销"
                }
              }

              case Cypridina.Ledger.Transfer
                   |> Ash.Changeset.for_create(:transfer, reverse_transfer_opts)
                   |> Ash.create() do
                {:ok, reverse_transfer} ->
                  Logger.info(
                    "转账 #{transfer_id} 已撤销，反向转账 #{reverse_transfer.id} 已创建，操作人: #{reversed_by}"
                  )

                  # 同步更新BalanceCache - 撤销转账需要恢复原来的余额状态
                  update_balance_cache_for_reverse_transfer(original_transfer, reverse_transfer)

                  %{
                    original_transfer: reversed_transfer,
                    reverse_transfer: reverse_transfer
                  }

                {:error, reason} ->
                  Logger.error("创建反向转账失败: #{inspect(reason)}")
                  Cypridina.Repo.rollback(reason)
              end

            {:error, reason} ->
              Logger.error("标记转账为已撤销失败: #{inspect(reason)}")
              Cypridina.Repo.rollback(reason)
          end

        {:error, reason} ->
          Logger.error("获取原转账记录失败: #{inspect(reason)}")
          Cypridina.Repo.rollback(reason)
      end
    end)
  end

  @doc """
  增强的撤销转账功能

  不仅撤销转账，还会处理相关的游戏内物品：
  - 如果是游戏投注，会撤销相关的下注记录
  - 如果是股票交易，会撤销相关的股票持仓变化
  - 如果是游戏获奖，会撤销相关的获奖记录
  """
  def reverse_transfer_with_game_items(transfer_id, reversed_by, reverse_reason \\ nil) do
    Cypridina.Repo.transaction(fn ->
      # 获取原转账记录
      case Cypridina.Ledger.Transfer |> Ash.get(transfer_id) do
        {:ok, original_transfer} ->
          # 检查是否可以撤销
          if original_transfer.status != :completed do
            Cypridina.Repo.rollback("只能撤销已完成的转账")
          end

          metadata = original_transfer.metadata || %{}

          if Map.get(metadata, "is_reversed", false) do
            Cypridina.Repo.rollback("该转账已经被撤销")
          end

          # 处理游戏内物品撤销
          game_items_result = reverse_game_items(original_transfer, reversed_by, reverse_reason)

          # 执行标准的转账撤销
          case reverse_transfer(transfer_id, reversed_by, reverse_reason) do
            {:ok, transfer_result} ->
              Logger.info("转账 #{transfer_id} 及相关游戏物品已撤销，操作人: #{reversed_by}")

              %{
                transfer_result: transfer_result,
                game_items_result: game_items_result
              }

            {:error, reason} ->
              Logger.error("撤销转账失败: #{inspect(reason)}")
              Cypridina.Repo.rollback(reason)
          end

        {:error, reason} ->
          Logger.error("获取原转账记录失败: #{inspect(reason)}")
          Cypridina.Repo.rollback(reason)
      end
    end)
  end

  @doc """
  获取用户转账历史
  """
  def get_user_transfer_history(user_id, opts \\ []) do
    Cypridina.Ledger.Transfer
    |> Ash.Query.for_read(:by_user, %{user_id: user_id})
    |> apply_pagination_opts(opts)
    |> Ash.read()
  end

  @doc """
  获取用户积分变动历史（用户友好格式）- 基于Balance数据模型

  返回格式化的积分变动记录，包括：
  - 变动时间
  - 变动类型（收入/支出）
  - 变动金额
  - 变动原因/描述
  - 变动后余额

  支持分页参数：
  - page: 页码（从1开始）
  - page_size: 每页记录数
  - limit: 限制记录数（兼容旧接口）
  - offset: 偏移量（兼容旧接口）
  """
  def get_user_points_history(user_id, opts \\ []) do
    identifier = AccountIdentifier.user(user_id, :XAA)

    with {:ok, user_account} <-
           Account.get_or_create_by_identifier(%{identifier: identifier, currency: :XAA}) do
      # 使用Ash分页机制
      case get_user_balance_history_with_transfers_paginated(user_account.id, opts) do
        {:ok, page_result} ->
          # 转换为用户友好的格式
          formatted_history =
            page_result.results
            |> Enum.map(fn balance ->
              transfer = balance.transfer

              # 判断是收入还是支出
              {change_type, amount} =
                if transfer.to_account_id == user_account.id do
                  {:income, Money.to_decimal(transfer.amount) |> Decimal.to_integer()}
                else
                  {:expense, -(Money.to_decimal(transfer.amount) |> Decimal.to_integer())}
                end

              # 获取变动原因
              reason = get_transfer_reason(transfer)

              # 获取变动后余额（这是准确的历史余额）
              balance_after = Money.to_decimal(balance.balance) |> Decimal.to_integer()

              %{
                id: transfer.id,
                timestamp: transfer.timestamp,
                change_type: change_type,
                amount: amount,
                reason: reason,
                description: transfer.description,
                transaction_type: transfer.transaction_type,
                status: transfer.status,
                metadata: transfer.metadata,
                balance_after: balance_after
              }
            end)
            |> Enum.sort_by(& &1.timestamp, {:desc, DateTime})

          # 返回分页结果
          {:ok,
           %{
             results: formatted_history,
             count: page_result.count,
             more?: page_result.more?
           }}

        error ->
          error
      end
    end
  end

  @doc """
  获取用户余额历史（包含转账信息）- 使用Ash分页
  """
  defp get_user_balance_history_with_transfers_paginated(account_id, opts) do
    # 处理分页参数
    page = Keyword.get(opts, :page, 1)
    page_size = Keyword.get(opts, :page_size, Keyword.get(opts, :limit, 20))

    # 兼容旧的offset/limit参数
    {page, page_size} =
      if Keyword.has_key?(opts, :offset) do
        offset = Keyword.get(opts, :offset, 0)
        limit = Keyword.get(opts, :limit, 20)
        calculated_page = div(offset, limit) + 1
        {calculated_page, limit}
      else
        {page, page_size}
      end

    # 使用Ash分页机制
    Cypridina.Ledger.Balance
    |> Ash.Query.for_read(:by_account, %{account_id: account_id})
    |> Ash.Query.load([:transfer])
    |> Ash.Query.sort(inserted_at: :desc)
    |> Ash.Query.page(count: true, limit: page_size, offset: (page - 1) * page_size)
    |> Ash.read()
  end

  # 获取转账原因的辅助函数
  defp get_transfer_reason(transfer) do
    # 使用集中管理的交易类型描述
    description = Cypridina.Types.TransactionType.description(transfer.transaction_type)

    # 如果是未知类型，尝试使用transfer的description字段
    if description == "未知交易类型" do
      transfer.description || "其他操作"
    else
      description
    end
  end

  @doc """
  获取用户余额历史
  """
  def get_user_balance_history(user_id, opts \\ []) do
    Cypridina.Ledger.Balance
    |> Ash.Query.for_read(:by_user, %{user_id: user_id})
    |> apply_pagination_opts(opts)
    |> Ash.read()
  end

  @doc """
  获取指定时间点的用户余额

  使用ash_double_entry的balance_as_of计算来获取特定时间点的余额
  """
  def get_user_balance_at_time(user_id, timestamp) do
    identifier = AccountIdentifier.user(user_id, :XAA)

    with {:ok, account} <-
           Account.get_or_create_by_identifier(%{identifier: identifier, currency: :XAA}) do
      case Cypridina.Ledger.Account
           |> Ash.get!(account.id, load: [balance_as_of: %{datetime: timestamp}]) do
        %{balance_as_of: nil} -> {:ok, Money.new!(0, :XAA)}
        %{balance_as_of: balance} -> {:ok, Money.new!(balance, :XAA)}
      end
    end
  end

  # 应用分页选项
  defp apply_pagination_opts(query, opts) do
    query
    |> then(fn q ->
      case Keyword.get(opts, :limit) do
        nil -> q
        limit -> Ash.Query.limit(q, limit)
      end
    end)
    |> then(fn q ->
      case Keyword.get(opts, :offset) do
        nil -> q
        offset -> Ash.Query.offset(q, offset)
      end
    end)
  end

  # ========== 辅助函数 ==========

  # 构建转账元数据
  defp build_transfer_metadata(opts) do
    # 获取传入的metadata，如果没有则使用空map
    base_metadata = Keyword.get(opts, :metadata, %{})

    # 添加系统字段
    system_metadata =
      %{
        game_type: Keyword.get(opts, :game_type),
        room_id: Keyword.get(opts, :room_id),
        race_issue: Keyword.get(opts, :race_issue),
        extra_data: Keyword.get(opts, :extra_data, %{})
      }
      |> Enum.reject(fn {_k, v} -> is_nil(v) end)
      |> Map.new()

    # 合并用户metadata和系统metadata
    Map.merge(base_metadata, system_metadata)
  end

  @doc """
  发送钱包余额变更通知

  目前系统还未实现钱包功能，但为了保持协议完整性，先实现此函数
  """
  def notify_wallet_change(user_id, wallet_balance) do
    Teen.Protocol.ProtocolSender.notify_wallet_change(user_id, wallet_balance)
  end

  # 更新撤销转账的BalanceCache
  defp update_balance_cache_for_reverse_transfer(original_transfer, reverse_transfer) do
    Logger.info("🔄 开始更新撤销转账缓存: 原转账#{original_transfer.id}, 反向转账#{reverse_transfer.id}")

    try do
      # 获取原转账涉及的账户信息
      with {:ok, from_account} <-
             Cypridina.Ledger.Account |> Ash.get(original_transfer.from_account_id),
           {:ok, to_account} <-
             Cypridina.Ledger.Account |> Ash.get(original_transfer.to_account_id) do
        amount = Money.to_decimal(original_transfer.amount) |> Decimal.to_integer()

        Logger.info(
          "💰 撤销转账金额: #{amount}, 从账户标识符: #{from_account.identifier}, 到账户标识符: #{to_account.identifier}"
        )

        # 根据账户类型更新对应的缓存
        # 撤销转账的逻辑：反向转账恢复原来的余额状态
        Logger.info("📈 恢复原转出账户余额: +#{amount}")
        # 原转出账户恢复余额
        update_account_balance_cache(from_account, amount)

        Logger.info("📉 减少原转入账户余额: -#{amount}")
        # 原转入账户减少余额
        update_account_balance_cache(to_account, -amount)

        Logger.info("✅ 撤销转账缓存更新成功: 原转账#{original_transfer.id}, 反向转账#{reverse_transfer.id}")
      else
        {:error, reason} ->
          Logger.warning("❌ 获取撤销转账账户信息失败: #{inspect(reason)}")
      end
    rescue
      error ->
        Logger.warning("❌ 更新撤销转账缓存失败: #{inspect(error)}")
    end
  end

  # 根据账户类型更新余额缓存
  defp update_account_balance_cache(account, amount) do
    Logger.info("🔧 更新账户缓存: identifier=#{account.identifier}, 金额=#{amount}")

    # 解析账户标识符来确定账户类型和相关信息
    case AccountIdentifier.parse_identifier(account.identifier) do
      {:ok, %{type: :user, id: user_id}} ->
        Logger.info("👤 更新用户账户缓存: user_id=#{user_id}, 金额=#{amount}")
        user_identifier = AccountIdentifier.user(user_id, :XAA)

        if amount > 0 do
          result = Cypridina.Ledger.BalanceCache.add_balance(user_identifier, amount)
          Logger.info("➕ 增加用户余额结果: #{inspect(result)}")
        else
          result = Cypridina.Ledger.BalanceCache.subtract_balance(user_identifier, abs(amount))
          Logger.info("➖ 减少用户余额结果: #{inspect(result)}")
        end

      {:ok, %{type: :system, account_type: account_type}} ->
        Logger.info("🏛️ 更新系统账户缓存: 类型=#{account_type}, 金额=#{amount}")
        system_identifier = AccountIdentifier.system(account_type, :XAA)

        if amount > 0 do
          Cypridina.Ledger.BalanceCache.add_balance(system_identifier, amount)
        else
          Cypridina.Ledger.BalanceCache.subtract_balance(system_identifier, abs(amount))
        end

      {:ok, %{type: :game, id: game_id}} ->
        Logger.info("🎮 更新游戏账户缓存: game_id=#{game_id}, 金额=#{amount}")
        game_identifier = AccountIdentifier.game(game_id, :XAA)

        if amount > 0 do
          Cypridina.Ledger.BalanceCache.add_balance(game_identifier, amount)
        else
          Cypridina.Ledger.BalanceCache.subtract_balance(game_identifier, abs(amount))
        end

      {:error, reason} ->
        Logger.warning("❌ 无法解析账户标识符: #{account.identifier}, 错误: #{inspect(reason)}")

      _ ->
        Logger.debug("忽略未知账户类型: #{account.identifier}")
    end
  end

  # 处理游戏内物品的撤销
  defp reverse_game_items(original_transfer, reversed_by, reverse_reason) do
    metadata = original_transfer.metadata || %{}
    transaction_type = original_transfer.transaction_type

    case {transaction_type, metadata} do
      # 处理游戏投注撤销
      {:game_bet, %{"bet_id" => bet_id}} ->
        reverse_game_bet(bet_id, reversed_by, reverse_reason)

      # 处理游戏获奖撤销
      {:game_win, %{"bet_id" => bet_id}} ->
        reverse_game_win(bet_id, reversed_by, reverse_reason)

      # 处理股票买入撤销
      {:buy_stock, %{"racer_id" => racer_id, "quantity" => quantity}} ->
        reverse_stock_purchase(original_transfer, racer_id, quantity, reversed_by, reverse_reason)

      # 处理股票卖出撤销
      {:sell_stock, %{"racer_id" => racer_id, "quantity" => quantity}} ->
        reverse_stock_sale(original_transfer, racer_id, quantity, reversed_by, reverse_reason)

      # 其他类型的转账不需要特殊处理
      _ ->
        %{type: :no_game_items, message: "该转账不涉及游戏内物品"}
    end
  end

  # 撤销游戏投注
  defp reverse_game_bet(bet_id, reversed_by, reverse_reason) do
    case RacingGame.Bet |> Ash.get(bet_id) do
      {:ok, bet} ->
        # 0: pending
        if bet.status == 0 do
          # 如果投注还在等待中，直接取消
          # 3: cancelled
          case bet |> Ash.Changeset.for_update(:set_bet_result, %{status: 3}) |> Ash.update() do
            {:ok, updated_bet} ->
              Logger.info("投注 #{bet_id} 已取消，撤销人: #{reversed_by}")
              %{type: :bet_cancelled, bet: updated_bet}

            {:error, reason} ->
              Logger.error("取消投注失败: #{inspect(reason)}")
              %{type: :error, message: "取消投注失败"}
          end
        else
          # 投注已结算，记录撤销信息但不改变状态

          Logger.info("投注 #{bet_id} 已结算，无法撤销投注状态，但转账已撤销")
          %{type: :bet_settled, message: "投注已结算，仅撤销转账"}
        end

      {:error, _} ->
        %{type: :bet_not_found, message: "找不到相关投注记录"}
    end
  end

  # 撤销游戏获奖
  defp reverse_game_win(bet_id, reversed_by, reverse_reason) do
    case RacingGame.Bet |> Ash.get(bet_id) do
      {:ok, bet} ->
        # 记录获奖撤销，但不改变投注状态（因为比赛结果已确定）

        Logger.info("获奖 #{bet_id} 的奖金已撤销，撤销人: #{reversed_by}")
        %{type: :win_reversed, bet: bet, message: "获奖奖金已撤销"}

      {:error, _} ->
        %{type: :bet_not_found, message: "找不到相关投注记录"}
    end
  end

  # 撤销股票买入
  defp reverse_stock_purchase(original_transfer, racer_id, quantity, reversed_by, reverse_reason) do
    # 从账户标识符中获取用户ID
    user_id =
      case AccountIdentifier.parse_identifier(original_transfer.from_account.identifier) do
        {:ok, %{type: :user, id: uid}} -> uid
        _ -> nil
      end

    if user_id do
      # 减少用户的股票持仓
      case RacingGame.Stock.subtract(%{
             user_id: user_id,
             racer_id: racer_id,
             amount: quantity,
             transaction_target_id: reversed_by,
             transaction_target_type: :system
           }) do
        {:ok, stock} ->
          Logger.info(
            "股票买入已撤销: 用户 #{user_id}, 动物 #{racer_id}, 数量 #{quantity}, 撤销人: #{reversed_by}"
          )

          %{type: :stock_purchase_reversed, stock: stock, message: "股票买入已撤销"}

        {:error, reason} ->
          Logger.error("撤销股票买入失败: #{inspect(reason)}")
          %{type: :error, message: "撤销股票买入失败: #{inspect(reason)}"}
      end
    else
      %{type: :error, message: "无法确定用户ID"}
    end
  end

  # 撤销股票卖出
  defp reverse_stock_sale(original_transfer, racer_id, quantity, reversed_by, reverse_reason) do
    # 从账户标识符中获取用户ID
    user_id =
      case AccountIdentifier.parse_identifier(original_transfer.to_account.identifier) do
        {:ok, %{type: :user, id: uid}} -> uid
        _ -> nil
      end

    if user_id do
      # 增加用户的股票持仓（因为是撤销卖出）
      case RacingGame.Stock.add(%{
             user_id: user_id,
             racer_id: racer_id,
             amount: quantity,
             # 撤销时不计算成本
             cost: Decimal.new(0),
             transaction_target_id: reversed_by,
             transaction_target_type: :system
           }) do
        {:ok, stock} ->
          Logger.info(
            "股票卖出已撤销: 用户 #{user_id}, 动物 #{racer_id}, 数量 #{quantity}, 撤销人: #{reversed_by}"
          )

          %{type: :stock_sale_reversed, stock: stock, message: "股票卖出已撤销"}

        {:error, reason} ->
          Logger.error("撤销股票卖出失败: #{inspect(reason)}")
          %{type: :error, message: "撤销股票卖出失败: #{inspect(reason)}"}
      end
    else
      %{type: :error, message: "无法确定用户ID"}
    end
  end

  @doc """
  根据转账类型更新bonus money字段

  处理规则：
  - bonus类型：增加接收方的bonusmoney
  - game_win类型：增加接收方的winningmoney
  - deposit类型：增加接收方的bonuscashmoney
  - game_bet类型：从发送方减少相应的bonus字段
  - withdrawal/withdrawal_request类型：从发送方减少相应的bonus字段
  """
  defp update_bonus_money_for_transfer(from_identifier, to_identifier, amount, transaction_type) do
    case transaction_type do
      :bonus ->
        # 奖励转账：增加接收方的bonusmoney
        with {:ok, %{type: :user, id: user_id}} <-
               AccountIdentifier.parse_identifier(to_identifier) do
          Teen.UserSystem.increment_bonusmoney(user_id, amount)
          Logger.info("增加用户 #{user_id} 的bonusmoney: #{amount}")
        end

      :game_win ->
        # 游戏获胜：增加接收方的winningmoney
        with {:ok, %{type: :user, id: user_id}} <-
               AccountIdentifier.parse_identifier(to_identifier) do
          Teen.UserSystem.increment_winningmoney(user_id, amount)
          Logger.info("增加用户 #{user_id} 的winningmoney: #{amount}")
        end

      :deposit ->
        # 充值：增加接收方的bonuscashmoney
        with {:ok, %{type: :user, id: user_id}} <-
               AccountIdentifier.parse_identifier(to_identifier) do
          Teen.UserSystem.increment_bonuscashmoney(user_id, amount)
          Logger.info("增加用户 #{user_id} 的bonuscashmoney: #{amount}")
        end

      :game_bet ->
        # 游戏投注：优先从bonus字段扣除
        with {:ok, %{type: :user, id: user_id}} <-
               AccountIdentifier.parse_identifier(from_identifier),
             {:ok, turnover} <- Teen.UserSystem.get_or_create_user_turnover(user_id) do
          # 按优先级扣除：bonusmoney -> winningmoney -> bonuscashmoney
          remaining =
            if turnover.bonusmoney > 0 and amount > 0 do
              deduct = min(turnover.bonusmoney, amount)
              Teen.UserSystem.decrement_bonusmoney(user_id, deduct)
              Logger.info("从用户 #{user_id} 的bonusmoney扣除: #{deduct}")
              amount - deduct
            else
              amount
            end

          remaining =
            if turnover.winningmoney > 0 and remaining > 0 do
              deduct = min(turnover.winningmoney, remaining)
              Teen.UserSystem.decrement_winningmoney(user_id, deduct)
              Logger.info("从用户 #{user_id} 的winningmoney扣除: #{deduct}")
              remaining - deduct
            else
              remaining
            end

          if turnover.bonuscashmoney > 0 and remaining > 0 do
            deduct = min(turnover.bonuscashmoney, remaining)
            Teen.UserSystem.decrement_bonuscashmoney(user_id, deduct)
            Logger.info("从用户 #{user_id} 的bonuscashmoney扣除: #{deduct}")
          end
        end

      withdrawal_type when withdrawal_type in [:withdrawal, :withdrawal_request] ->
        # 提现/提现请求：从发送方优先扣除bonus字段
        with {:ok, %{type: :user, id: user_id}} <-
               AccountIdentifier.parse_identifier(from_identifier),
             {:ok, turnover} <- Teen.UserSystem.get_or_create_user_turnover(user_id) do
          # 按优先级扣除：bonuscashmoney -> winningmoney -> bonusmoney
          withdrawal_desc = if withdrawal_type == :withdrawal, do: "提现", else: "提现请求"

          remaining =
            if turnover.bonuscashmoney > 0 and amount > 0 do
              deduct = min(turnover.bonuscashmoney, amount)
              Teen.UserSystem.decrement_bonuscashmoney(user_id, deduct)
              Logger.info("#{withdrawal_desc}从用户 #{user_id} 的bonuscashmoney扣除: #{deduct}")
              amount - deduct
            else
              amount
            end

          remaining =
            if turnover.winningmoney > 0 and remaining > 0 do
              deduct = min(turnover.winningmoney, remaining)
              Teen.UserSystem.decrement_winningmoney(user_id, deduct)
              Logger.info("#{withdrawal_desc}从用户 #{user_id} 的winningmoney扣除: #{deduct}")
              remaining - deduct
            else
              remaining
            end

          if turnover.bonusmoney > 0 and remaining > 0 do
            deduct = min(turnover.bonusmoney, remaining)
            Teen.UserSystem.decrement_bonusmoney(user_id, deduct)
            Logger.info("#{withdrawal_desc}从用户 #{user_id} 的bonusmoney扣除: #{deduct}")
          end
        end

      _ ->
        # 其他类型不处理
        :ok
    end
  end
end
