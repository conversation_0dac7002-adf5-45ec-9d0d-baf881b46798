defmodule Cypridina.Accounts.Strategies.PhoneVerification do
  @moduledoc """
  手机验证码认证策略

  支持手机验证码登录和注册
  """

  # 暂时简化实现，直接在用户资源中定义actions
  # 这个模块保留用于将来扩展

  def sign_in_with_phone(phone) do
    case Cypridina.Accounts.User.get_by_phone(phone) do
      {:ok, user} ->
        # 生成认证令牌
        case AshAuthentication.Jwt.token_for_user(user) do
          {:ok, token, _claims} ->
            {:ok, user, %{token: token}}

          {:error, reason} ->
            {:error, reason}
        end

      {:error, _} ->
        {:error, "用户不存在"}
    end
  end

  def request_verification(phone, purpose) do
    case Cypridina.Communications.VerificationCode.generate_and_send(%{
           phone_number: phone,
           purpose: purpose
         }) do
      {:ok, _verification_code} ->
        {:ok, %{message: "验证码已发送"}}

      {:error, reason} ->
        {:error, reason}
    end
  end
end
