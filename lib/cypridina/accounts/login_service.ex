defmodule <PERSON><PERSON><PERSON><PERSON>.Accounts.LoginService do
  @moduledoc """
  登录服务模块

  处理用户登录验证、设备记录、封禁检查等功能
  """

  alias Cypridina.Accounts.{User, UserDevice}
  alias Teen.BanSystem
  alias Teen.Events.EventPublisher
  alias Teen.ActivitySystem.{ActivityService, LoginTrackerService}

  require Logger

  @doc """
  处理用户登录

  参数:
  - user_id: 用户ID
  - device_id: 设备ID
  - login_ip: 登录IP地址
  - device_info: 设备信息（可选）

  返回:
  - {:ok, user} - 登录成功
  - {:error, reason} - 登录失败
  """
  def handle_user_login(user_id, device_id, login_ip, device_info \\ nil) do
    with {:ok, user} <- get_user(user_id),
         :ok <- check_user_ban(user_id),
         :ok <- check_ip_ban(login_ip),
         :ok <- check_device_ban(device_id),
         {:ok, _device} <- record_device_login(user_id, device_id, login_ip, device_info),
         {:ok, updated_user} <- set_user_online(user),
         :ok <- update_user_login_info(updated_user, login_ip, device_info) do
      Logger.info("用户登录成功", %{
        user_id: user_id,
        device_id: device_id,
        login_ip: login_ip
      })

      # 发布用户登录事件
      EventPublisher.publish_user_login(user_id,
        ip_address: login_ip,
        device_info: device_info || %{device_id: device_id},
        source: "login_service"
      )

      # 异步处理登录记录和奖励，避免阻塞登录流程
      Task.start(fn ->
        # 记录登录信息
        case LoginTrackerService.track_user_login(user_id, %{
               ip_address: login_ip,
               device_type: get_device_type(device_info),
               client_info: device_info || %{}
             }) do
          {:ok, login_info} ->
            Logger.info("用户登录记录成功", %{
              user_id: user_id,
              consecutive_days: login_info.consecutive_days,
              is_first_today: login_info.is_first_today
            })

          {:error, reason} ->
            Logger.error("用户登录记录失败", %{
              user_id: user_id,
              reason: inspect(reason)
            })
        end

        # 触发登录奖励（旧逻辑保留兼容性）
        try_trigger_login_bonus(updated_user)
      end)

      {:ok, updated_user}
    else
      {:error, :user_banned, ban_info} ->
        Logger.warning("用户被封禁尝试登录", %{
          user_id: user_id,
          ban_info: ban_info,
          device_id: device_id,
          login_ip: login_ip
        })

        {:error, :user_banned, ban_info}

      {:error, :ip_banned} ->
        Logger.warning("被封禁IP尝试登录", %{
          user_id: user_id,
          login_ip: login_ip,
          device_id: device_id
        })

        {:error, :ip_banned}

      {:error, :device_banned} ->
        Logger.warning("被封禁设备尝试登录", %{
          user_id: user_id,
          device_id: device_id,
          login_ip: login_ip
        })

        {:error, :device_banned}

      {:error, reason} = error ->
        Logger.error("用户登录失败", %{
          user_id: user_id,
          device_id: device_id,
          login_ip: login_ip,
          reason: reason
        })

        error
    end
  end

  @doc """
  处理用户登出
  """
  def handle_user_logout(user_id, device_id) do
    with {:ok, user} <- get_user(user_id),
         {:ok, _device} <- set_device_offline(user_id, device_id),
         {:ok, updated_user} <- set_user_offline(user) do
      Logger.info("用户登出成功", %{
        user_id: user_id,
        device_id: device_id
      })

      {:ok, updated_user}
    else
      {:error, reason} = error ->
        Logger.error("用户登出失败", %{
          user_id: user_id,
          device_id: device_id,
          reason: reason
        })

        error
    end
  end

  @doc """
  检查用户是否被封禁
  """
  def check_user_ban(user_id) do
    case BanSystem.is_user_banned?(user_id) do
      {:ok, false} ->
        :ok

      {:ok, true, ban_info} ->
        {:error, :user_banned, ban_info}

      {:error, reason} ->
        Logger.error("检查用户封禁状态失败", %{
          user_id: user_id,
          reason: reason
        })

        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  @doc """
  检查IP是否被封禁
  """
  def check_ip_ban(ip_address) do
    # 查询IP封禁记录
    case query_ip_ban(ip_address) do
      {:ok, false} ->
        :ok

      {:ok, true} ->
        {:error, :ip_banned}

      {:error, reason} ->
        Logger.error("检查IP封禁状态失败", %{
          ip_address: ip_address,
          reason: reason
        })

        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  @doc """
  检查设备是否被封禁
  """
  def check_device_ban(device_id) do
    # 查询设备封禁记录
    case query_device_ban(device_id) do
      {:ok, false} ->
        :ok

      {:ok, true} ->
        {:error, :device_banned}

      {:error, reason} ->
        Logger.error("检查设备封禁状态失败", %{
          device_id: device_id,
          reason: reason
        })

        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  # 私有函数

  defp get_user(user_id) do
    try do
      case Ash.get(User, user_id) do
        {:ok, user} when not is_nil(user) -> {:ok, user}
        {:ok, nil} -> {:error, :user_not_found}
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        {:error, error}
    end
  end

  defp record_device_login(user_id, device_id, login_ip, _device_info) do
    # 尝试查找现有设备记录
    try do
      case UserDevice.get_by_user_and_device(user_id, device_id) do
        {:ok, device} when not is_nil(device) ->
          # 更新现有设备的登录信息
          device
          |> Ash.Changeset.for_update(:update_login_info, %{login_ip: login_ip})
          |> Ash.update()

        {:ok, nil} ->
          # 创建新的设备记录
          UserDevice
          |> Ash.Changeset.for_create(:record_login, %{
            user_id: user_id,
            device_id: device_id,
            login_ip: login_ip
          })
          |> Ash.create()

        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error ->
        {:error, error}
    end
  end

  defp set_device_offline(user_id, device_id) do
    try do
      case UserDevice.get_by_user_and_device(user_id, device_id) do
        {:ok, device} when not is_nil(device) ->
          device
          |> Ash.Changeset.for_update(:set_offline, %{})
          |> Ash.update()

        {:ok, nil} ->
          Logger.warning("设置设备离线失败，设备不存在", %{
            user_id: user_id,
            device_id: device_id
          })

          # 设备不存在不算错误
          {:ok, nil}

        {:error, reason} ->
          Logger.warning("设置设备离线失败", %{
            user_id: user_id,
            device_id: device_id,
            reason: reason
          })

          # 设备查询失败不算错误
          {:ok, nil}
      end
    rescue
      error ->
        Logger.warning("设置设备离线异常", %{
          user_id: user_id,
          device_id: device_id,
          error: error
        })

        # 异常不算错误
        {:ok, nil}
    end
  end

  defp set_user_online(user) do
    user
    |> Ash.Changeset.for_update(:set_online, %{})
    |> Ash.update()
  end

  defp set_user_offline(user) do
    user
    |> Ash.Changeset.for_update(:set_offline, %{})
    |> Ash.update()
  end

  defp query_ip_ban(ip_address) do
    # 查询IP封禁记录（ban_type: 3）
    case BanSystem.UserBan.list_active_bans() do
      {:ok, bans} ->
        ip_banned =
          Enum.any?(bans, fn ban ->
            ban.ban_type == 3 and ban.ip_address == ip_address
          end)

        {:ok, ip_banned}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp query_device_ban(_device_id) do
    # 查询设备封禁记录（ban_type: 2）
    # 这里需要根据实际的设备封禁表结构来实现
    # 暂时返回false，表示设备未被封禁
    {:ok, false}
  end

  defp update_user_login_info(user, login_ip, device_info) do
    # 使用新的UserLoginRecord.update_login_info action
    case Teen.ActivitySystem.UserLoginRecord.update_login_info(%{
           user_id: user.id,
           ip_address: login_ip,
           device_type: get_device_type(device_info),
           client_info: device_info || %{}
         }) do
      :ok ->
        :ok

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_device_type(nil), do: "unknown"

  defp get_device_type(device_info) when is_map(device_info) do
    device_info
    |> Map.get("device_type", Map.get(device_info, :device_type, "unknown"))
    |> to_string()
  end

  defp get_device_type(_), do: "unknown"

  defp try_trigger_login_bonus(user) do
    try do
      # 检查是否为当日首次登录（通过对比last_login_at判断）
      is_first_login_today = is_first_login_today?(user)

      if is_first_login_today do
        # 从UserLoginRecord获取连续登录天数
        case Teen.ActivitySystem.LoginTrackerService.get_user_consecutive_days(user.id) do
          {:ok, consecutive_days} ->
            Logger.info("触发用户登录奖励", %{
              user_id: user.id,
              consecutive_days: consecutive_days
            })

            # 触发日常登录奖励
            ActivityService.trigger_daily_login_bonus(user.id, consecutive_days)

            # 检查并触发连续登录奖励
            ActivityService.check_consecutive_login_rewards(user.id, consecutive_days)

          {:error, reason} ->
            Logger.error("获取连续登录天数失败", %{
              user_id: user.id,
              reason: inspect(reason)
            })
        end
      end
    rescue
      error ->
        Logger.error("登录奖励处理失败", %{
          user_id: user.id,
          error: inspect(error)
        })
    end
  end

  defp is_first_login_today?(user) do
    # 现在通过UserLoginRecord来检查是否为今日首次登录
    case Teen.ActivitySystem.LoginTrackerService.has_logged_in_today?(user.id) do
      # 今天已经登录过
      true -> false
      # 今天还没登录
      false -> true
    end
  end
end
