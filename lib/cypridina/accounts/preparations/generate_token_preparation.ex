defmodule Cypridina.Accounts.Preparations.GenerateTokenPreparation do
  @moduledoc """
  为read action生成认证token的preparation

  这个preparation用于在查询用户时自动生成JWT token，
  类似于AshAuthentication.Strategy.Password.SignInPreparation的功能。
  """

  use Ash.Resource.Preparation
  require Logger

  @impl true
  def prepare(query, _opts, _context) do
    # 在after_action中生成token
    Ash.Query.after_action(query, &generate_token_for_user/2)
  end

  # 为用户生成token
  defp generate_token_for_user(query, [user]) when is_struct(user) do
    case generate_user_token(user) do
      {:ok, token} ->
        # 将token添加到metadata中
        Logger.info("生成token: #{inspect(token)}")

        # {query, [%{user | __metadata__: Map.put(user.__metadata__ || %{}, :token, token)}]}
        Ash.Resource.put_metadata(user, :token, token)

      {:error, reason} ->
        Logger.error("生成token失败: #{inspect(reason)}")
        {query, [user]}
    end
  end

  defp generate_token_for_user(query, users) when is_list(users) do
    # 处理多个用户的情况（虽然get_by_client_uniq_id应该只返回一个用户）
    updated_users =
      Enum.map(users, fn user ->
        case generate_user_token(user) do
          {:ok, token} ->
            # %{user | __metadata__: Map.put(user.__metadata__ || %{}, :token, token)}
            Ash.Resource.put_metadata(user, :token, token)

          {:error, reason} ->
            Logger.error("生成token失败: #{inspect(reason)}")
            user
        end
      end)

    {query, updated_users}
  end

  defp generate_token_for_user(query, result) do
    Logger.info("生成token3: #{inspect(result)}")
    # 如果结果不是用户结构体，直接返回
    {query, result}
  end

  # 生成用户token的辅助函数
  defp generate_user_token(user) do
    try do
      # 使用AshAuthentication的token生成功能
      # 参考AshAuthentication.GenerateTokenChange的实现
      case AshAuthentication.Jwt.token_for_user(user) do
        {:ok, token, _claims} -> {:ok, token}
        {:error, reason} -> {:error, reason}
      end
    rescue
      error ->
        Logger.error("Token生成异常: #{inspect(error)}")
        {:error, "Token生成失败"}
    end
  end
end
