defmodule Cypridina.Accounts.UserDevice do
  @moduledoc """
  用户设备资源

  管理用户的登录设备信息，包括设备ID、最后离线时间、最后登录IP等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.Accounts,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :user_id, :device_id, :last_login_ip, :last_offline_at, :updated_at]
  end

  postgres do
    table "user_devices"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :get_by_device_id, args: [:device_id]
    define :list_by_device_id
    define :update_login_info
    define :get_active_by_device_id
    define :get_by_user_and_device, args: [:user_id, :device_id]
    define :record_login
    define :set_offline
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :get_by_device_id do
      argument :device_id, :string, allow_nil?: false
      get? true
      filter expr(device_id == ^arg(:device_id))
      # 返回最近登录的用户设备记录
      prepare fn query, _context ->
        Ash.Query.sort(query, last_login_at: :desc)
      end
    end

    read :list_by_device_id do
      argument :device_id, :string, allow_nil?: false
      filter expr(device_id == ^arg(:device_id))
      # 按最近登录时间排序
      prepare fn query, _context ->
        Ash.Query.sort(query, last_login_at: :desc)
      end
    end

    read :get_active_by_device_id do
      argument :device_id, :string, allow_nil?: false
      get? true
      filter expr(device_id == ^arg(:device_id) and is_nil(last_offline_at))
      # 返回在线的用户设备记录
      prepare fn query, _context ->
        Ash.Query.sort(query, last_login_at: :desc)
      end
    end

    read :get_by_user_and_device do
      argument :user_id, :uuid, allow_nil?: false
      argument :device_id, :string, allow_nil?: false
      get? true
      filter expr(user_id == ^arg(:user_id) and device_id == ^arg(:device_id))
      # 返回特定用户的特定设备记录
      prepare fn query, _context ->
        Ash.Query.sort(query, last_login_at: :desc)
      end
    end

    create :record_login do
      argument :user_id, :uuid, allow_nil?: false
      argument :device_id, :string, allow_nil?: false
      argument :login_ip, :string, allow_nil?: false

      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:device_id, arg(:device_id))
      change set_attribute(:last_login_ip, arg(:login_ip))
      change set_attribute(:last_login_at, &DateTime.utc_now/0)
      change set_attribute(:last_offline_at, nil)
    end

    update :set_offline do
      change set_attribute(:last_offline_at, &DateTime.utc_now/0)
    end

    update :update_login_info do
      argument :login_ip, :string, allow_nil?: false

      change set_attribute(:last_login_ip, arg(:login_ip))
      change set_attribute(:last_login_at, &DateTime.utc_now/0)
      change set_attribute(:last_offline_at, nil)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :device_id, :string do
      allow_nil? false
      public? true
      description "设备唯一标识"
      constraints max_length: 255
    end

    attribute :last_login_ip, :string do
      allow_nil? true
      public? true
      description "最后登录IP地址"
      constraints max_length: 45
    end

    attribute :last_login_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后登录时间"
    end

    attribute :last_offline_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后离线时间"
    end

    attribute :device_info, :string do
      allow_nil? true
      public? true
      description "设备信息（JSON格式）"
      constraints max_length: 1000
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  calculations do
    # 设备是否在线
    calculate :is_online, :boolean, expr(is_nil(last_offline_at))
  end

  identities do
    identity :unique_user_device, [:user_id, :device_id]
  end
end
