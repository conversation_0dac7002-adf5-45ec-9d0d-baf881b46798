defmodule Cyprid<PERSON>.Accounts.UserRole do
  @moduledoc """
  Join table for User and Role many-to-many relationship
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Cypridina.Accounts

  postgres do
    table "user_roles"
    repo Cypridina.Repo
  end

  actions do
    defaults [:read, :destroy, update: :*]

    create :create do
      primary? true
      accept [:user_id, :role_id]
    end
  end

  attributes do
    uuid_primary_key :id

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      primary_key? true
      allow_nil? false
    end

    belongs_to :role, Teen.SystemSettings.Role do
      primary_key? true
      allow_nil? false
    end
  end

  identities do
    identity :unique_user_role, [:user_id, :role_id]
  end
end
