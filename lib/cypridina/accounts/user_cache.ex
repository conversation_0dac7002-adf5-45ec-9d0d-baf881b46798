defmodule Cypridina.UserCache do
  @moduledoc """
  用户数据的 ETS 缓存服务

  提供高性能的用户数据缓存，支持：
  - 通过 user_id 或 numeric_id 查询用户
  - 批量用户查询
  - 自动缓存过期和清理
  - 通过 Ash.Notifier 订阅用户变更，自动更新缓存
  - 缓存统计和监控

  ## 使用示例

      # 获取单个用户
      {:ok, user} = UserCache.get_user("user_id")
      {:ok, user} = UserCache.get_user_by_numeric_id(12345)

      # 批量获取用户
      users = UserCache.get_users(["id1", "id2", "id3"])

      # 检查缓存状态
      UserCache.cached?("user_id")
      UserCache.stats()

      # 缓存管理
      UserCache.invalidate_user("user_id")
      UserCache.clear_all()
      UserCache.warm_cache(["id1", "id2"])

  """
  use GenServer
  require Logger
  import Ash.Expr

  @table_name :user_cache
  # 默认缓存1小时
  @ttl :timer.hours(1)
  # 每5分钟清理过期数据
  @cleanup_interval :timer.minutes(5)

  # Client API

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  获取用户信息，优先从缓存读取
  """
  def get_user(user_id) when is_binary(user_id) do
    case lookup(user_id) do
      {:ok, user} ->
        {:ok, user}

      :not_found ->
        # 从数据库加载并缓存
        load_and_cache_user(user_id)
    end
  end

  def get_user(user_id) when is_integer(user_id) do
    get_user(to_string(user_id))
  end

  def get_user(nil), do: {:error, :invalid_user_id}

  def get_user!(user_id) do
    get_user(user_id)
    |> case do
      {:ok, user} -> user
      {:error, reason} -> raise "Failed to get user #{user_id}: #{reason}"
    end
  end

  @doc """
  通过 numeric_id 获取用户
  """
  def get_user_by_numeric_id(numeric_id) when is_integer(numeric_id) do
    case lookup_by_numeric_id(numeric_id) do
      {:ok, user} ->
        {:ok, user}

      :not_found ->
        load_and_cache_user_by_numeric_id(numeric_id)
    end
  end

  def get_user_by_numeric_id(numeric_id) when is_binary(numeric_id) do
    case Integer.parse(numeric_id) do
      {id, ""} -> get_user_by_numeric_id(id)
      _ -> {:error, :invalid_numeric_id}
    end
  end

  def get_user_by_numeric_id(nil), do: {:error, :invalid_numeric_id}

  @doc """
  批量获取用户
  """
  def get_users(user_ids) when is_list(user_ids) do
    # 过滤掉无效的ID
    valid_ids =
      Enum.filter(user_ids, fn
        id when is_binary(id) and id != "" -> true
        id when is_integer(id) -> true
        _ -> false
      end)

    # 标准化ID为字符串
    normalized_ids = Enum.map(valid_ids, &to_string/1)

    {cached, missing} =
      Enum.split_with(normalized_ids, fn id ->
        match?({:ok, _}, lookup(id))
      end)

    cached_users =
      Enum.map(cached, fn id ->
        {:ok, user} = lookup(id)
        user
      end)

    # 批量加载缺失的用户
    loaded_users =
      if missing != [] do
        load_and_cache_users(missing)
      else
        []
      end

    cached_users ++ loaded_users
  end

  def get_users([]), do: []
  def get_users(nil), do: []

  @doc """
  清除指定用户的缓存
  """
  def invalidate_user(user_id) when is_binary(user_id) do
    GenServer.cast(__MODULE__, {:invalidate, user_id})
  end

  @doc """
  清除所有缓存
  """
  def clear_all do
    GenServer.call(__MODULE__, :clear_all)
  end

  @doc """
  获取缓存统计信息
  """
  def stats do
    GenServer.call(__MODULE__, :stats)
  end

  @doc """
  预热缓存 - 批量加载常用用户
  """
  def warm_cache(user_ids) when is_list(user_ids) do
    GenServer.cast(__MODULE__, {:warm_cache, user_ids})
  end

  @doc """
  检查用户是否在缓存中
  """
  def cached?(user_id) when is_binary(user_id) do
    case :ets.lookup(@table_name, user_id) do
      [{^user_id, _user, expires_at, _numeric_id}] ->
        DateTime.compare(expires_at, DateTime.utc_now()) == :gt

      [] ->
        false
    end
  end

  def cached?(user_id) when is_integer(user_id), do: cached?(to_string(user_id))
  def cached?(nil), do: false

  @doc """
  健康检查 - 验证缓存服务状态
  """
  def health_check do
    # 检查进程是否运行
    case Process.whereis(__MODULE__) do
      nil ->
        {:error, :process_not_running}

      _pid ->
        # 检查ETS表是否存在
        if :ets.info(@table_name) != :undefined and
             :ets.info(:user_cache_numeric_index) != :undefined do
          {:ok,
           %{
             status: :healthy,
             cache_size: :ets.info(@table_name, :size),
             index_size: :ets.info(:user_cache_numeric_index, :size),
             memory_usage:
               :ets.info(@table_name, :memory) + :ets.info(:user_cache_numeric_index, :memory)
           }}
        else
          {:error, :tables_not_found}
        end
    end
  end

  # Server callbacks

  def init(opts) do
    # 创建 ETS 表
    table =
      :ets.new(@table_name, [
        :set,
        :public,
        :named_table,
        read_concurrency: true,
        write_concurrency: true
      ])

    # 创建 numeric_id 索引表
    index_table =
      :ets.new(:user_cache_numeric_index, [
        :set,
        :public,
        :named_table,
        read_concurrency: true
      ])

    # 订阅 Ash 通知
    subscribe_to_notifications()

    # 定期清理过期缓存
    schedule_cleanup()

    state = %{
      table: table,
      index_table: index_table,
      ttl: opts[:ttl] || @ttl,
      stats: %{
        hits: 0,
        misses: 0,
        evictions: 0
      }
    }

    {:ok, state}
  end

  def handle_cast({:invalidate, user_id}, state) do
    case :ets.lookup(@table_name, user_id) do
      [{^user_id, _user, _expires_at, numeric_id}] ->
        :ets.delete(@table_name, user_id)
        :ets.delete(:user_cache_numeric_index, numeric_id)
        Logger.debug("Invalidated cache for user #{user_id}")

      [] ->
        :ok
    end

    {:noreply, state}
  end

  def handle_cast({:warm_cache, user_ids}, state) do
    # 异步预热缓存
    Task.start(fn ->
      valid_ids = Enum.filter(user_ids, &is_binary/1)
      missing_ids = Enum.reject(valid_ids, &cached?/1)

      if missing_ids != [] do
        load_and_cache_users(missing_ids)
        Logger.info("Warmed cache with #{length(missing_ids)} users")
      end
    end)

    {:noreply, state}
  end

  def handle_call(:clear_all, _from, state) do
    :ets.delete_all_objects(@table_name)
    :ets.delete_all_objects(:user_cache_numeric_index)
    Logger.info("Cleared all user cache")

    {:reply, :ok, %{state | stats: %{hits: 0, misses: 0, evictions: 0}}}
  end

  def handle_call(:stats, _from, state) do
    cache_size = :ets.info(@table_name, :size)

    stats =
      Map.merge(state.stats, %{
        cache_size: cache_size,
        hit_rate: calculate_hit_rate(state.stats)
      })

    {:reply, stats, state}
  end

  # 处理 Phoenix.Socket.Broadcast 广播的 Ash 通知
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: topic, event: event, payload: notification},
        state
      ) do
    case topic do
      "user" ->
        handle_ash_notification(notification, state)
        {:noreply, state}

      # 处理特定用户的通知 "user:user_id"
      "user:" <> _user_id ->
        handle_ash_notification(notification, state)
        {:noreply, state}

      _ ->
        # 忽略其他主题的消息
        {:noreply, state}
    end
  end

  # 定期清理过期缓存
  def handle_info(:cleanup, state) do
    evicted = cleanup_expired()

    new_stats = update_in(state.stats, [:evictions], &(&1 + evicted))

    schedule_cleanup()
    {:noreply, %{state | stats: new_stats}}
  end

  # 私有函数

  defp lookup(user_id) do
    case :ets.lookup(@table_name, user_id) do
      [{^user_id, user, expires_at, _numeric_id}] ->
        if DateTime.compare(expires_at, DateTime.utc_now()) == :gt do
          update_stats(:hit)
          {:ok, user}
        else
          # 过期了，删除
          :ets.delete(@table_name, user_id)
          update_stats(:miss)
          :not_found
        end

      [] ->
        update_stats(:miss)
        :not_found
    end
  end

  defp lookup_by_numeric_id(numeric_id) do
    case :ets.lookup(:user_cache_numeric_index, numeric_id) do
      [{^numeric_id, user_id}] ->
        lookup(user_id)

      [] ->
        update_stats(:miss)
        :not_found
    end
  end

  defp load_and_cache_user(user_id) do
    case Cypridina.Accounts.User
         |> Ash.get(user_id) do
      {:ok, user} ->
        cache_user(user)
        Logger.debug("Loaded and cached user #{user_id}")
        {:ok, user}

      {:error, reason} = error ->
        Logger.debug("Failed to load user #{user_id}: #{inspect(reason)}")
        error

      error ->
        Logger.warning("Unexpected error loading user #{user_id}: #{inspect(error)}")
        {:error, :load_failed}
    end
  end

  defp load_and_cache_user_by_numeric_id(numeric_id) do
    case Cypridina.Accounts.User.get_by_numeric_id(numeric_id) do
      {:ok, user} ->
        cache_user(user)
        Logger.debug("Loaded and cached user by numeric_id #{numeric_id}")
        {:ok, user}

      {:error, reason} ->
        Logger.debug("Failed to load user by numeric_id #{numeric_id}: #{inspect(reason)}")
        {:error, :not_found}
    end
  end

  defp load_and_cache_users(user_ids) do
    case Cypridina.Accounts.User
         |> Ash.Query.filter(expr(id in ^user_ids))
         |> Cypridina.Accounts.read() do
      {:ok, users} ->
        cached_users = Enum.map(users, &cache_user/1)
        Logger.debug("Loaded and cached #{length(cached_users)} users")
        cached_users

      {:error, reason} ->
        Logger.warning("Failed to load users #{inspect(user_ids)}: #{inspect(reason)}")
        []
    end
  end

  defp cache_user(user) do
    expires_at = DateTime.add(DateTime.utc_now(), @ttl, :millisecond)

    # 确保用户有必要的字段
    if user.id && user.numeric_id do
      # 缓存用户数据
      :ets.insert(@table_name, {user.id, user, expires_at, user.numeric_id})

      # 更新 numeric_id 索引
      :ets.insert(:user_cache_numeric_index, {user.numeric_id, user.id})

      user
    else
      Logger.warning("User missing required fields: #{inspect(user)}")
      user
    end
  end

  defp subscribe_to_notifications do
    # 订阅用户资源的变更通知
    CypridinaWeb.Endpoint.subscribe("user")

    # 如果有其他用户相关的资源，也可以订阅
    # CypridinaWeb.Endpoint.subscribe("user_profile")

    Logger.info("UserCache subscribed to Ash notifications")
  end

  defp handle_ash_notification(notification, state) do
    case notification do
      %{resource: Cypridina.Accounts.User, action: action, data: user}
      when action in [:create, :update] ->
        # 更新缓存
        cache_user(user)
        Logger.debug("Updated cache for user #{user.id} after #{action}")

      %{resource: Cypridina.Accounts.User, action: :destroy, data: user} ->
        # 删除缓存
        :ets.delete(@table_name, user.id)
        :ets.delete(:user_cache_numeric_index, user.numeric_id)
        Logger.debug("Removed user #{user.id} from cache after destroy")

      _ ->
        # 忽略其他通知
        :ok
    end
  end

  defp cleanup_expired do
    now = DateTime.utc_now()

    # 使用 foldl 遍历表，找出过期的条目
    expired =
      :ets.foldl(
        fn {user_id, _user, expires_at, numeric_id}, acc ->
          if DateTime.compare(expires_at, now) == :lt do
            [{user_id, numeric_id} | acc]
          else
            acc
          end
        end,
        [],
        @table_name
      )

    # 删除过期条目
    Enum.each(expired, fn {user_id, numeric_id} ->
      :ets.delete(@table_name, user_id)
      :ets.delete(:user_cache_numeric_index, numeric_id)
    end)

    length(expired)
  end

  defp schedule_cleanup do
    Process.send_after(self(), :cleanup, @cleanup_interval)
  end

  defp update_stats(type) do
    case Process.whereis(__MODULE__) do
      nil -> :ok
      pid -> send(pid, {:update_stats, type})
    end
  end

  defp calculate_hit_rate(%{hits: hits, misses: misses}) do
    total = hits + misses

    if total > 0 do
      Float.round(hits / total * 100, 2)
    else
      0.0
    end
  end

  # 处理统计更新
  def handle_info({:update_stats, :hit}, state) do
    new_state = update_in(state, [:stats, :hits], &(&1 + 1))
    {:noreply, new_state}
  end

  def handle_info({:update_stats, :miss}, state) do
    new_state = update_in(state, [:stats, :misses], &(&1 + 1))
    {:noreply, new_state}
  end
end
