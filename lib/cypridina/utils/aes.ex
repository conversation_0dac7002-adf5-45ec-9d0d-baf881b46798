defmodule Cypridina.Utils.AES do
  @moduledoc """
  AES-128 ECB加密解密工具，使用Erlang的crypto库实现
  支持Base64编码输出，方便传输和存储
  """

  # AES-128的块大小为16字节
  @block_size 16

  @doc """
  使用AES-128 ECB模式加密数据，并返回Base64编码的结果

  ## 参数
    - plain_text: 待加密的明文数据（二进制或字符串）
    - key: 密钥（需要是16字节的二进制数据，对应128位密钥）
    - opts: 选项列表
      - padding: 使用的填充方式，默认为PKCS7，可选值为 :pkcs7, :none
      - encode: 是否进行Base64编码，默认为true

  ## 返回
    - 加密后的Base64编码字符串（当encode为true时）
    - 加密后的二进制数据（当encode为false时）

  ## 示例
    iex> Cypridina.Utils.AES.encrypt("Hello World", "1234567890123456")
    "L8cDIJxMvOnGjNA7O+3stw=="

    iex> Cypridina.Utils.AES.encrypt("Hello World", "1234567890123456", encode: false)
    <<47, 199, 3, 32, 156, 76, 188, 233, 198, 140, 208, 59, 59, 237, 236, 183>>
  """
  def encrypt(plain_text, key, opts \\ []) when is_binary(plain_text) and is_binary(key) do
    padding = Keyword.get(opts, :padding, :pkcs7)
    encode = Keyword.get(opts, :encode, true)

    # 确保密钥长度正确 (16字节 = 128位)
    key = normalize_key(key)

    # 应用填充
    padded_text = apply_padding(plain_text, padding)

    # 使用crypto库进行AES-128 ECB加密
    cipher_text = :crypto.crypto_one_time(:aes_128_ecb, key, "", padded_text, encrypt: true)

    if encode do
      # 应用Base64编码
      Base.encode64(cipher_text)
    else
      cipher_text
    end
  end

  @doc """
  解密AES-128 ECB模式加密的数据

  ## 参数
    - cipher_text: 待解密的数据（Base64编码的字符串或直接的二进制数据）
    - key: 密钥（需要是16字节的二进制数据，对应128位密钥）
    - opts: 选项列表
      - padding: 使用的填充方式，默认为PKCS7，可选值为 :pkcs7, :none
      - decode: 是否需要先进行Base64解码，默认为true

  ## 返回
    - 解密后的原始文本

  ## 示例
    iex> AES.decrypt("L8cDIJxMvOnGjNA7O+3stw==", "1234567890123456")
    "Hello World"

    iex> encrypted = AES.encrypt("Hello World", "1234567890123456", encode: false)
    iex> AES.decrypt(encrypted, "1234567890123456", decode: false)
    "Hello World"
  """
  def decrypt(cipher_text, key, opts \\ []) do
    padding = Keyword.get(opts, :padding, :pkcs7)
    decode = Keyword.get(opts, :decode, true)

    # 确保密钥长度正确 (16字节 = 128位)
    key = normalize_key(key)

    # 如果需要先进行Base64解码
    cipher_binary =
      if decode and is_binary(cipher_text) do
        case Base.decode64(cipher_text) do
          {:ok, decoded} -> decoded
          :error -> raise ArgumentError, "无效的Base64编码"
        end
      else
        cipher_text
      end

    # 使用crypto库进行AES-128 ECB解密
    decrypted = :crypto.crypto_one_time(:aes_128_ecb, key, "", cipher_binary, encrypt: false)

    # 移除填充
    remove_padding(decrypted, padding)
  end

  # 私有函数：规范化密钥到16字节（如果太短则填充，如果太长则截断）
  defp normalize_key(key) when byte_size(key) == 16, do: key

  defp normalize_key(key) when byte_size(key) < 16 do
    # 如果密钥太短，使用0填充
    padding_size = 16 - byte_size(key)
    key <> :binary.copy(<<0>>, padding_size)
  end

  defp normalize_key(key) when byte_size(key) > 16 do
    # 如果密钥太长，截断到16字节
    binary_part(key, 0, 16)
  end

  # 私有函数：应用PKCS7填充
  defp apply_padding(data, :none), do: data

  defp apply_padding(data, :pkcs7) do
    # 计算需要填充的字节数
    pad_size = @block_size - rem(byte_size(data), @block_size)
    pad_size = if pad_size == 0, do: @block_size, else: pad_size

    # 应用PKCS7填充（填充字节的值等于填充的字节数）
    data <> :binary.copy(<<pad_size>>, pad_size)
  end

  # 私有函数：移除PKCS7填充
  defp remove_padding(data, :none), do: data

  defp remove_padding(data, :pkcs7) do
    # 获取最后一个字节，它表示填充的字节数
    pad_size = :binary.last(data)

    if pad_size > 0 and pad_size <= @block_size do
      data_size = byte_size(data)
      # 检查所有填充字节是否相同
      padding_valid =
        binary_part(data, data_size - pad_size, pad_size)
        |> :binary.bin_to_list()
        |> Enum.all?(fn b -> b == pad_size end)

      if padding_valid do
        binary_part(data, 0, data_size - pad_size)
      else
        # 如果填充无效，返回原始数据
        data
      end
    else
      # 如果填充大小不合理，返回原始数据
      data
    end
  end
end
