defmodule <PERSON>prid<PERSON>.ProjectMode do
  @moduledoc """
  项目模式管理模块

  用于检查和管理当前项目运行的模式：
  - teen: Teen项目模式 (棋牌游戏平台)
  - race: Race项目模式 (赛马游戏平台)
  - self: 自用模式 (个人开发测试)
  """

  @type mode :: :teen | :race | :self

  @doc """
  获取当前项目模式

  ## Examples

      iex> Cypridina.ProjectMode.current()
      :teen

      iex> Cypridina.ProjectMode.current()
      :race
  """
  @spec current() :: mode()
  def current do
    # 优先使用环境变量，然后是应用配置，最后是默认值
    mode =
      System.get_env("PROJECT_MODE") ||
        Application.get_env(:cypridina, :project_mode, "teen")

    case String.downcase(to_string(mode)) do
      "teen" -> :teen
      "race" -> :race
      "self" -> :self
      # 默认为 teen 模式
      _ -> :teen
    end
  end

  @doc """
  检查当前是否为 Teen 项目模式

  ## Examples

      iex> <PERSON>prid<PERSON>.ProjectMode.teen?()
      true
  """
  @spec teen?() :: boolean()
  def teen?, do: current() == :teen

  @doc """
  检查当前是否为 Race 项目模式

  ## Examples

      iex> Cypridina.ProjectMode.race?()
      false
  """
  @spec race?() :: boolean()
  def race?, do: current() == :race

  @doc """
  检查当前是否为自用模式

  ## Examples

      iex> Cypridina.ProjectMode.self?()
      false
  """
  @spec self?() :: boolean()
  def self?, do: current() == :self

  @doc """
  获取当前模式的字符串表示

  ## Examples

      iex> Cypridina.ProjectMode.to_string()
      "teen"
  """
  @spec to_string() :: String.t()
  def to_string do
    case current() do
      :teen -> "teen"
      :race -> "race"
      :self -> "self"
    end
  end

  @doc """
  获取当前模式的显示名称

  ## Examples

      iex> Cypridina.ProjectMode.display_name()
      "Teen项目模式"
  """
  @spec display_name() :: String.t()
  def display_name do
    case current() do
      :teen -> "Teen项目模式"
      :race -> "Race项目模式"
      :self -> "自用模式"
    end
  end

  @doc """
  检查指定功能在当前模式下是否可用

  ## Examples

      iex> Cypridina.ProjectMode.feature_enabled?(:teen_games)
      true

      iex> Cypridina.ProjectMode.feature_enabled?(:racing_game)
      false
  """
  @spec feature_enabled?(atom()) :: boolean()
  def feature_enabled?(feature) do
    case {current(), feature} do
      # Teen 模式功能
      {:teen, :teen_games} -> true
      {:teen, :payment_system} -> true
      {:teen, :customer_service} -> true
      {:teen, :ban_system} -> true
      {:teen, :game_management} -> true
      {:teen, :activity_system} -> true
      {:teen, :statistics} -> true
      {:teen, :promotion_system} -> true
      # Race 模式功能
      {:race, :racing_game} -> true
      # 自用模式功能（基础功能）
      {:self, :accounts} -> true
      {:self, :ledger} -> true
      {:self, :communications} -> true
      {:self, :payments} -> true
      # 所有模式都支持的基础功能
      {_, :accounts} -> true
      {_, :ledger} -> true
      {_, :communications} -> true
      {_, :payments} -> true
      # 其他情况默认不支持
      _ -> false
    end
  end

  @doc """
  获取当前模式下可用的功能列表

  ## Examples

      iex> Cypridina.ProjectMode.available_features()
      [:accounts, :ledger, :communications, :payments, :teen_games, ...]
  """
  @spec available_features() :: [atom()]
  def available_features do
    base_features = [:accounts, :ledger, :communications, :payments]

    case current() do
      :teen ->
        base_features ++
          [
            :teen_games,
            :payment_system,
            :customer_service,
            :ban_system,
            :game_management,
            :activity_system,
            :statistics,
            :promotion_system
          ]

      :race ->
        base_features ++ [:racing_game]

      :self ->
        base_features
    end
  end

  @doc """
  根据当前模式执行不同的逻辑

  ## Examples

      iex> Cypridina.ProjectMode.case_mode(
      ...>   teen: fn -> "Teen logic" end,
      ...>   race: fn -> "Race logic" end,
      ...>   self: fn -> "Self logic" end
      ...> )
      "Teen logic"
  """
  @spec case_mode(keyword()) :: any()
  def case_mode(opts) do
    case current() do
      :teen -> if opts[:teen], do: opts[:teen].(), else: opts[:default] && opts[:default].()
      :race -> if opts[:race], do: opts[:race].(), else: opts[:default] && opts[:default].()
      :self -> if opts[:self], do: opts[:self].(), else: opts[:default] && opts[:default].()
    end
  end
end
