defmodule Cypridina.RoomSystem.GameFactory do
  @moduledoc """
  游戏工厂模块 - 动态加载和管理游戏逻辑

  功能：
  - 动态注册游戏类型
  - 获取游戏配置
  - 创建游戏房间实例
  - 支持插件式游戏扩展

  使用方式：
  1. 在应用启动时注册游戏
  2. 房间管理器通过工厂获取游戏信息
  3. 支持运行时动态添加新游戏
  """

  use GenServer
  require Logger

  @registry_name :game_factory_registry

  # 游戏行为定义
  @callback game_type() :: atom()
  @callback game_name() :: String.t()
  @callback room_module() :: module()
  @callback default_config() :: map()
  @callback is_lobby_game?() :: boolean()
  @callback supported_game_ids() :: [integer()]

  # 客户端API

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  @doc """
  注册游戏类型
  """
  def register_game(game_module) do
    GenServer.call(__MODULE__, {:register_game, game_module})
  end

  @doc """
  获取游戏类型映射
  """
  def get_game_types do
    GenServer.call(__MODULE__, :get_game_types)
  end

  @doc """
  根据游戏ID获取游戏类型
  """
  def get_game_type(game_id) do
    GenServer.call(__MODULE__, {:get_game_type, game_id})
  end

  @doc """
  获取游戏配置
  """
  def get_game_config(game_type) do
    GenServer.call(__MODULE__, {:get_game_config, game_type})
  end

  @doc """
  获取游戏房间模块
  """
  def get_room_module(game_type) do
    GenServer.call(__MODULE__, {:get_room_module, game_type})
  end

  @doc """
  获取所有百人场游戏
  """
  def get_lobby_games do
    GenServer.call(__MODULE__, :get_lobby_games)
  end

  @doc """
  检查游戏是否为百人场游戏
  """
  def is_lobby_game?(game_type) do
    GenServer.call(__MODULE__, {:is_lobby_game, game_type})
  end

  @doc """
  检查游戏是否为单人游戏
  """
  def is_single_player_game?(game_type) do
    GenServer.call(__MODULE__, {:is_single_player_game, game_type})
  end

  @doc """
  获取所有已注册的游戏
  """
  def list_games do
    GenServer.call(__MODULE__, :list_games)
  end

  @doc """
  根据游戏类型获取对应的游戏ID（取第一个支持的ID）
  """
  def get_game_id(game_type) do
    GenServer.call(__MODULE__, {:get_game_id, game_type})
  end

  # GenServer 回调

  @impl true
  def init(_opts) do
    Logger.info("🎮 [GAME_FACTORY] 游戏工厂启动")

    state = %{
      # game_type => game_module
      games: %{},
      # game_id => game_type
      game_id_mapping: %{},
      # game_type => config
      game_configs: %{},
      # game_type => room_module
      room_modules: %{},
      # [game_type] - 百人场游戏列表
      lobby_games: []
    }

    # 自动注册内置游戏
    final_state = register_builtin_games(state)

    {:ok, final_state}
  end

  @impl true
  def handle_call({:register_game, game_module}, _from, state) do
    case register_game_internal(state, game_module) do
      {:ok, new_state} ->
        {:reply, :ok, new_state}

      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call(:get_game_types, _from, state) do
    {:reply, state.game_id_mapping, state}
  end

  @impl true
  def handle_call({:get_game_type, game_id}, _from, state) do
    game_type = Map.get(state.game_id_mapping, game_id, :unknown)
    {:reply, game_type, state}
  end

  @impl true
  def handle_call({:get_game_config, game_type}, _from, state) do
    config = Map.get(state.game_configs, game_type)
    {:reply, config, state}
  end

  @impl true
  def handle_call({:get_room_module, game_type}, _from, state) do
    room_module = Map.get(state.room_modules, game_type)
    {:reply, room_module, state}
  end

  @impl true
  def handle_call(:get_lobby_games, _from, state) do
    {:reply, state.lobby_games, state}
  end

  @impl true
  def handle_call({:is_lobby_game, game_type}, _from, state) do
    is_lobby = game_type in state.lobby_games
    {:reply, is_lobby, state}
  end

  @impl true
  def handle_call({:is_single_player_game, game_type}, _from, state) do
    case Map.get(state.game_configs, game_type) do
      nil ->
        {:reply, false, state}

      config ->
        # 判断是否为单人游戏：max_players == 1
        is_single_player = Map.get(config, :max_players, 0) == 1
        {:reply, is_single_player, state}
    end
  end

  @impl true
  def handle_call(:list_games, _from, state) do
    games_info =
      Enum.map(state.games, fn {game_type, game_module} ->
        %{
          game_type: game_type,
          game_name: game_module.game_name(),
          room_module: game_module.room_module(),
          is_lobby_game: game_module.is_lobby_game?(),
          supported_game_ids: game_module.supported_game_ids()
        }
      end)

    {:reply, games_info, state}
  end

  @impl true
  def handle_call({:get_game_id, game_type}, _from, state) do
    case Map.get(state.games, game_type) do
      nil ->
        {:reply, nil, state}

      game_module ->
        # 获取第一个支持的游戏ID
        supported_ids = game_module.supported_game_ids()
        game_id = List.first(supported_ids)
        {:reply, game_id, state}
    end
  end

  # 私有函数

  defp register_builtin_games(state) do
    # 从配置文件获取要注册的游戏
    builtin_games = Application.get_env(:cypridina, :games, [])[:builtin]

    optional_games = Application.get_env(:cypridina, :games, [])[:optional]
    plugin_games = Application.get_env(:cypridina, :games, [])[:plugins]

    all_games = builtin_games ++ optional_games ++ plugin_games

    Enum.reduce(all_games, state, fn game_module, acc_state ->
      case register_game_internal(acc_state, game_module) do
        {:ok, new_state} ->
          Logger.info("🎮 [GAME_FACTORY] 注册游戏成功: #{game_module}")
          new_state

        {:error, reason} ->
          Logger.warning("🎮 [GAME_FACTORY] 注册游戏失败: #{game_module} - #{reason}")
          acc_state
      end
    end)
  end

  defp register_game_internal(state, game_module) do
    # 验证游戏模块是否实现了必要的回调
    game_type = game_module.game_type()
    game_name = game_module.game_name()
    room_module = game_module.room_module()
    default_config = game_module.default_config()
    is_lobby = game_module.is_lobby_game?()
    supported_ids = game_module.supported_game_ids()

    # 检查游戏类型是否已注册
    if Map.has_key?(state.games, game_type) do
      {:error, :game_already_registered}
    else
      # 更新状态
      new_games = Map.put(state.games, game_type, game_module)
      new_configs = Map.put(state.game_configs, game_type, default_config)
      new_room_modules = Map.put(state.room_modules, game_type, room_module)

      # 更新游戏ID映射
      new_id_mapping =
        Enum.reduce(supported_ids, state.game_id_mapping, fn game_id, acc ->
          Map.put(acc, game_id, game_type)
        end)

      # 更新百人场游戏列表
      new_lobby_games =
        if is_lobby do
          [game_type | state.lobby_games]
        else
          state.lobby_games
        end

      new_state = %{
        state
        | games: new_games,
          game_configs: new_configs,
          room_modules: new_room_modules,
          game_id_mapping: new_id_mapping,
          lobby_games: new_lobby_games
      }

      Logger.info("🎮 [GAME_FACTORY] 注册游戏成功: #{game_type} (#{game_name})")
      {:ok, new_state}
    end
  end
end
