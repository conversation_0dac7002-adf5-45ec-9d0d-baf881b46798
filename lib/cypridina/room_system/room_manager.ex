defmodule Cypridina.RoomSystem.RoomManager do
  @moduledoc """
  游戏房间管理器 - 负责房间的创建、销毁、匹配和管理

  参考IndiaGameServer的房间管理逻辑，实现：
  - 房间创建和销毁
  - 玩家匹配逻辑
  - 房间状态管理
  - 房间查找和路由
  """

  use GenServer
  require Logger

  @registry_name :game_room_registry

  # 使用游戏工厂来获取游戏类型映射
  alias Cypridina.RoomSystem.GameFactory

  # 移除硬编码的房间配置，改为从游戏工厂获取

  # 移除硬编码的百人场游戏列表，改为从游戏工厂获取

  # 客户端API

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  @doc """
  为玩家匹配房间
  """
  def match_room(user_id, game_id, match_data \\ %{}) do
    GenServer.call(__MODULE__, {:match_room, user_id, game_id, match_data})
  end

  @doc """
  创建新房间
  """
  def create_room(game_type, creator_id, server_id \\ %{}) do
    GenServer.call(__MODULE__, {:create_room, game_type, creator_id, server_id})
  end

  @doc """
  销毁房间
  """
  def destroy_room(room_id) do
    GenServer.call(__MODULE__, {:destroy_room, room_id})
  end

  @doc """
  获取房间信息
  """
  def get_room_info(room_id) do
    GenServer.call(__MODULE__, {:get_room_info, room_id})
  end

  @doc """
  获取所有房间列表
  """
  def list_rooms do
    GenServer.call(__MODULE__, :list_rooms)
  end

  @doc """
  获取所有房间统计
  """
  def get_room_stats do
    GenServer.call(__MODULE__, :get_room_stats)
  end

  @doc """
  向房间发送消息
  """
  def send_to_room(room_id, message) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        GenServer.cast(pid, message)
        :ok

      [] ->
        # 房间进程不存在，清理RoomManager中的数据
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        {:error, :room_not_found}
    end
  end

  @doc """
  向指定游戏类型的所有房间广播消息
  """
  def broadcast_to_game_type(game_type, message) do
    GenServer.call(__MODULE__, {:broadcast_to_game_type, game_type, message})
  end

  @doc """
  调用房间方法
  """
  def call_room(room_id, message) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        GenServer.call(pid, message)

      [] ->
        # 房间进程不存在，清理RoomManager中的数据
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        {:error, :room_not_found}
    end
  end

  @doc """
  检查房间是否存在
  """
  def room_exists?(room_id) do
    case Registry.lookup(@registry_name, room_id) do
      [{_pid, _}] ->
        true

      [] ->
        # 房间进程不存在，清理RoomManager中的数据
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        false
    end
  end

  # GenServer 回调

  @impl true
  def init(_opts) do
    Logger.info("🏠 [ROOM_MANAGER] 房间管理器启动")

    # 订阅房间事件
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "room_events")

    state = %{
      # room_id => room_info
      rooms: %{},
      # game_type => [room_ids]
      waiting_rooms: %{},
      # user_id => room_id
      player_rooms: %{},
      room_counter: 0,
      # 百人场房间映射 game_type => room_id
      lobby_rooms: %{}
    }

    # 为百人场游戏预创建房间
    final_state = create_lobby_rooms(state)

    {:ok, final_state}
  end

  @impl true
  def handle_call({:match_room, user_id, game_id, match_data}, _from, state) do
    Logger.info(
      "🎯 [MATCH] 玩家 #{user_id} 请求匹配游戏 #{game_id} server_id: #{Map.get(match_data, "server_id", 0)}"
    )

    game_type = GameFactory.get_game_type(game_id)

    if game_type == :unknown do
      {:reply, {:error, :unsupported_game}, state}
    else
      case find_or_create_room(state, user_id, game_type, match_data) do
        {:ok, room_id, new_state} ->
          # 将玩家加入房间
          case add_player_to_room(room_id, user_id) do
            {:ok, :joined} ->
              # 更新玩家房间映射
              updated_state = %{
                new_state
                | player_rooms: Map.put(new_state.player_rooms, user_id, room_id)
              }

              room_info = Map.get(updated_state.rooms, room_id)

              response = %{
                room_id: room_id,
                game_type: game_type,
                seat_id: get_player_seat(room_id, user_id),
                max_players: room_info.config.max_players,
                topic: room_info.topic,
                current_players: get_room_player_count(room_id)
              }

              Logger.info("🎯 [MATCH] 匹配成功: 玩家 #{user_id} -> 房间 #{room_id}")
              {:reply, {:ok, response}, updated_state}

            {:ok, :rejoined} ->
              room_info = Map.get(new_state.rooms, room_id)

              response = %{
                room_id: room_id,
                game_type: game_type,
                seat_id: get_player_seat(room_id, user_id),
                max_players: room_info.config.max_players,
                topic: room_info.topic,
                current_players: get_room_player_count(room_id)
              }

              Logger.info("🎯 [MATCH] 重连成功: 玩家 #{user_id} -> 房间 #{room_id}")
              {:reply, {:ok, response}, new_state}

            {:error, reason} ->
              Logger.error("🎯 [MATCH] 加入房间失败: #{reason}")
              {:reply, {:error, reason}, new_state}
          end

        {:error, reason} ->
          Logger.error("🎯 [MATCH] 匹配失败: #{reason}")
          {:reply, {:error, reason}, state}
      end
    end
  end

  @impl true
  def handle_call({:create_room, game_type, creator_id, server_id}, _from, state) do
    case create_new_room(state, game_type, creator_id, server_id) do
      {:ok, room_id, new_state} ->
        {:reply, {:ok, room_id}, new_state}

      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call({:destroy_room, room_id}, _from, state) do
    case destroy_room_internal(state, room_id) do
      {:ok, new_state} ->
        {:reply, :ok, new_state}

      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call({:get_room_info, room_id}, _from, state) do
    case Map.get(state.rooms, room_id) do
      nil ->
        {:reply, {:error, :room_not_found}, state}

      room_info ->
        {:reply, {:ok, room_info}, state}
    end
  end

  @impl true
  def handle_call(:list_rooms, _from, state) do
    rooms = Map.values(state.rooms)
    {:reply, rooms, state}
  end

  @impl true
  def handle_call(:get_room_stats, _from, state) do
    stats = %{
      total_rooms: map_size(state.rooms),
      waiting_rooms: state.waiting_rooms,
      active_players: map_size(state.player_rooms)
    }

    {:reply, stats, state}
  end

  @impl true
  def handle_call({:broadcast_to_game_type, game_type, message}, _from, state) do
    # 获取指定游戏类型的所有房间ID
    room_ids = get_rooms_by_game_type(state, game_type)

    # 向每个房间发送消息
    results =
      Enum.map(room_ids, fn room_id ->
        send_to_room(room_id, message)
      end)

    success_count = Enum.count(results, &(&1 == :ok))
    total_count = length(room_ids)

    Logger.info("🔊 [ROOM_MANAGER] 向 #{game_type} 游戏广播消息: #{success_count}/#{total_count} 房间成功")

    {:reply, {:ok, %{success: success_count, total: total_count}}, state}
  end

  @impl true
  def handle_info({:room_terminated, room_id, reason}, state) do
    Logger.info("🏠 [ROOM_MANAGER] 收到房间终止事件: #{room_id}, 原因: #{inspect(reason)}")

    case cleanup_terminated_room(state, room_id) do
      {:ok, new_state} ->
        Logger.info("🏠 [ROOM_MANAGER] 清理终止房间成功: #{room_id}")
        {:noreply, new_state}

      {:error, reason} ->
        Logger.warning("🏠 [ROOM_MANAGER] 清理终止房间失败: #{room_id}, 原因: #{reason}")
        {:noreply, state}
    end
  end

  @impl true
  def handle_info(msg, state) do
    Logger.debug("🏠 [ROOM_MANAGER] 未处理的info消息: #{inspect(msg)}")
    {:noreply, state}
  end

  @impl true
  def handle_cast({:cleanup_stale_room, room_id}, state) do
    Logger.info("🧹 [ROOM_MANAGER] 清理陈旧房间数据: #{room_id}")

    case cleanup_terminated_room(state, room_id) do
      {:ok, new_state} ->
        Logger.info("🧹 [ROOM_MANAGER] 清理陈旧房间成功: #{room_id}")
        {:noreply, new_state}

      {:error, reason} ->
        Logger.warning("🧹 [ROOM_MANAGER] 清理陈旧房间失败: #{room_id}, 原因: #{reason}")
        {:noreply, state}
    end
  end

  # 私有函数

  defp cleanup_terminated_room(state, room_id) do
    case Map.get(state.rooms, room_id) do
      nil ->
        # 房间已经不在管理器中，可能已经被清理过
        Logger.debug("🏠 [ROOM_MANAGER] 房间 #{room_id} 已不在管理器中")
        {:ok, state}

      room_info ->
        # 更新状态：从所有相关数据结构中移除房间
        new_rooms = Map.delete(state.rooms, room_id)

        new_waiting_rooms =
          remove_from_waiting_list(state.waiting_rooms, room_info.game_type, room_id)

        new_player_rooms = remove_room_from_players(state.player_rooms, room_id)

        # 如果是百人场房间，也要从lobby_rooms中移除
        new_lobby_rooms =
          Enum.reduce(state.lobby_rooms, %{}, fn {game_type, lobby_room_id}, acc ->
            if lobby_room_id == room_id do
              acc
            else
              Map.put(acc, game_type, lobby_room_id)
            end
          end)

        new_state = %{
          state
          | rooms: new_rooms,
            waiting_rooms: new_waiting_rooms,
            player_rooms: new_player_rooms,
            lobby_rooms: new_lobby_rooms
        }

        Logger.info("🏠 [ROOM_MANAGER] 清理终止房间: #{room_id}, 游戏类型: #{room_info.game_type}")
        {:ok, new_state}
    end
  end

  defp find_or_create_room(state, user_id, game_type, match_data) do
    cond do
      GameFactory.is_lobby_game?(game_type) ->
        # 百人场游戏：使用固定房间
        case Map.get(state.lobby_rooms, game_type) do
          nil ->
            Logger.info("🎯 [MATCH] 创建百人场房间: #{game_type}")
            create_lobby_room(state, game_type)

          room_id ->
            Logger.info("🎯 [MATCH] 使用现有百人场房间: #{room_id}")
            {:ok, room_id, state}
        end

      GameFactory.is_single_player_game?(game_type) ->
        # 单人游戏：直接创建独立房间，不走匹配逻辑
        Logger.info(
          "🎯 [MATCH] 单人游戏 #{game_type}，为玩家 #{user_id} 创建独立房间 server_id: #{Map.get(match_data, "server_id", 0)}"
        )

        create_new_room(state, game_type, user_id, Map.get(match_data, "server_id", 0))

      true ->
        # 多人游戏：查找或创建房间
        waiting_rooms = Map.get(state.waiting_rooms, game_type, [])

        case find_available_room(waiting_rooms, state.rooms) do
          {:ok, room_id} ->
            Logger.info("🎯 [MATCH] 找到等待房间: #{room_id}")
            {:ok, room_id, state}

          :not_found ->
            Logger.info("🎯 [MATCH] 创建新房间给玩家 #{user_id}")
            create_new_room(state, game_type, user_id, Map.get(match_data, "server_id", 0))
        end
    end
  end

  defp find_available_room([], _rooms), do: :not_found

  defp find_available_room([room_id | rest], rooms) do
    case Map.get(rooms, room_id) do
      nil ->
        find_available_room(rest, rooms)

      room_info ->
        current_players = get_room_player_count(room_id)

        if current_players < room_info.config.max_players do
          {:ok, room_id}
        else
          find_available_room(rest, rooms)
        end
    end
  end

  defp create_new_room(state, game_type, creator_id, server_id) do
    {room_id, trace_id} = generate_room_id(state)

    # 获取游戏ID用于topic
    game_id = GameFactory.get_game_id(game_type) || 1

    # 从游戏工厂获取默认配置
    default_config = GameFactory.get_game_config(game_type) || %{}
    # Logger.info("🎰 [SLOT777_CONFIG] 工厂: #{inspect(default_config, pretty: true)}")
    # 从后台管理系统加载房间配置
    backend_config = load_room_config_from_db(game_id, server_id)

    # Logger.info("🎰 [SLOT777_CONFIG] backend_config工厂: #{inspect(backend_config, pretty: true)}")
    # 合并配置（使用高级合并逻辑）
    merged_confi = merge_configs(default_config, backend_config)

    room_spec = %{
      id: room_id,
      trace_id: trace_id,
      game_type: game_type,
      game_id: game_id,
      server_id: server_id,
      creator_id: creator_id,
      config: merged_confi,
      created_at: System.system_time(:millisecond),
      status: :waiting,
      topic: "room:#{game_id}_#{room_id}"
    }

    # 从游戏工厂获取房间模块
    room_module = GameFactory.get_room_module(game_type)

    if room_module do
      case room_module.start_link(room_spec) do
        {:ok, _pid} ->
          # 更新状态
          new_rooms = Map.put(state.rooms, room_id, room_spec)

          # 单人游戏不添加到等待列表中
          new_waiting_rooms =
            if GameFactory.is_single_player_game?(game_type) do
              Logger.info("🏠 [ROOM_MANAGER] 单人游戏房间 #{room_id} 不添加到等待列表")
              state.waiting_rooms
            else
              add_to_waiting_list(state.waiting_rooms, game_type, room_id)
            end

          new_state = %{
            state
            | rooms: new_rooms,
              waiting_rooms: new_waiting_rooms,
              room_counter: state.room_counter + 1
          }

          Logger.info("🏠 [ROOM_MANAGER] 创建房间成功: #{room_id} (#{game_type}) 使用模块: #{room_module}")
          {:ok, room_id, new_state}

        {:error, reason} ->
          Logger.error("🏠 [ROOM_MANAGER] 创建房间失败: #{reason}")
          {:error, reason}
      end
    else
      Logger.error("🏠 [ROOM_MANAGER] 未找到游戏类型 #{game_type} 的房间模块")
      {:error, :room_module_not_found}
    end
  end

  defp destroy_room_internal(state, room_id) do
    case Map.get(state.rooms, room_id) do
      nil ->
        {:error, :room_not_found}

      room_info ->
        # 停止房间GenServer
        case Registry.lookup(@registry_name, room_id) do
          [{pid, _}] ->
            GenServer.stop(pid)

          [] ->
            :ok
        end

        # 更新状态
        new_rooms = Map.delete(state.rooms, room_id)

        new_waiting_rooms =
          remove_from_waiting_list(state.waiting_rooms, room_info.game_type, room_id)

        new_player_rooms = remove_room_from_players(state.player_rooms, room_id)

        new_state = %{
          state
          | rooms: new_rooms,
            waiting_rooms: new_waiting_rooms,
            player_rooms: new_player_rooms
        }

        Logger.info("🏠 [ROOM_MANAGER] 销毁房间: #{room_id}")
        {:ok, new_state}
    end
  end

  defp generate_room_id(state) do
    # 生成6位数房间码，确保不与现有房间冲突
    room_id = generate_unique_room_code(state)

    # 生成唯一溯源码
    trace_id = generate_trace_id()

    {room_id, trace_id}
  end

  # 生成6位数房间码，确保唯一性
  defp generate_unique_room_code(state, attempts \\ 0) do
    if attempts > 100 do
      # 如果尝试100次都没有找到唯一的房间码，使用时间戳后6位
      timestamp = System.system_time(:millisecond)
      Integer.to_string(timestamp) |> String.slice(-6, 6) |> String.pad_leading(6, "0")
    else
      # 生成100000-999999之间的6位数
      room_code = 100_000 + :rand.uniform(900_000) - 1
      room_id = Integer.to_string(room_code)

      # 检查是否已存在
      if Map.has_key?(state.rooms, room_id) do
        generate_unique_room_code(state, attempts + 1)
      else
        room_id
      end
    end
  end

  # 生成唯一溯源码：时间戳 + 随机字符串
  defp generate_trace_id do
    timestamp = System.system_time(:millisecond)
    random_part = :crypto.strong_rand_bytes(8) |> Base.encode16(case: :lower)
    "trace_#{timestamp}_#{random_part}"
  end

  defp add_to_waiting_list(waiting_rooms, game_type, room_id) do
    current_list = Map.get(waiting_rooms, game_type, [])
    Map.put(waiting_rooms, game_type, [room_id | current_list])
  end

  defp remove_from_waiting_list(waiting_rooms, game_type, room_id) do
    current_list = Map.get(waiting_rooms, game_type, [])
    new_list = List.delete(current_list, room_id)
    Map.put(waiting_rooms, game_type, new_list)
  end

  defp remove_room_from_players(player_rooms, room_id) do
    Enum.reject(player_rooms, fn {_user_id, r_id} -> r_id == room_id end)
    |> Enum.into(%{})
  end

  defp add_player_to_room(room_id, user_id) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        GenServer.call(pid, {:join_room, user_id, %{}})

      [] ->
        # 房间进程不存在，触发清理
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        {:error, :room_not_found}
    end
  end

  defp get_player_seat(room_id, user_id) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        case GenServer.call(pid, :get_state) do
          %{game_data: %{players: players}} ->
            case Map.get(players, user_id) do
              %{seat_id: seat_id} -> seat_id
              _ -> 1
            end

          %{players: players} ->
            case Map.get(players, user_id) do
              %{seat_id: seat_id} -> seat_id
              _ -> 1
            end

          _ ->
            1
        end

      [] ->
        # 房间进程不存在，触发清理
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        1
    end
  end

  defp get_room_player_count(room_id) do
    case Registry.lookup(@registry_name, room_id) do
      [{pid, _}] ->
        case GenServer.call(pid, :get_state) do
          %{game_data: %{players: players}} -> map_size(players)
          %{players: players} -> map_size(players)
          _ -> 0
        end

      [] ->
        # 房间进程不存在，触发清理
        GenServer.cast(__MODULE__, {:cleanup_stale_room, room_id})
        0
    end
  end

  # 百人场房间管理

  defp create_lobby_rooms(state) do
    lobby_games = GameFactory.get_lobby_games()

    Enum.reduce(lobby_games, state, fn game_type, acc_state ->
      case create_lobby_room(acc_state, game_type) do
        {:ok, _room_id, new_state} ->
          new_state

        {:error, reason} ->
          Logger.error("🏠 [ROOM_MANAGER] 创建百人场房间失败: #{game_type} - #{reason}")
          acc_state
      end
    end)
  end

  defp create_lobby_room(state, game_type) do
    # 百人场房间也使用统一的6位数房间号系统
    {room_id, trace_id} = generate_room_id(state)

    # 获取游戏ID用于topic
    game_id = GameFactory.get_game_id(game_type) || 1
    config = GameFactory.get_game_config(game_type) || %{}

    room_spec = %{
      id: room_id,
      trace_id: trace_id,
      game_type: game_type,
      game_id: game_id,
      # 系统创建的房间
      creator_id: "system",
      config: config,
      created_at: System.system_time(:millisecond),
      # 百人场房间始终处于游戏状态
      status: :playing,
      topic: "room:#{game_id}_#{room_id}"
    }

    # 从游戏工厂获取房间模块
    room_module = GameFactory.get_room_module(game_type)

    # 启动房间GenServer
    if room_module do
      case room_module.start_link(room_spec) do
        {:ok, _pid} ->
          # 更新状态
          new_rooms = Map.put(state.rooms, room_id, room_spec)
          new_lobby_rooms = Map.put(state.lobby_rooms, game_type, room_id)

          new_state = %{
            state
            | rooms: new_rooms,
              lobby_rooms: new_lobby_rooms,
              room_counter: state.room_counter + 1
          }

          Logger.info(
            "🏠 [ROOM_MANAGER] 创建百人场房间成功: #{room_id} (#{game_type}) 使用模块: #{room_module}"
          )

          {:ok, room_id, new_state}

        {:error, reason} ->
          Logger.error("🏠 [ROOM_MANAGER] 创建百人场房间失败: #{reason}")
          {:error, reason}
      end
    else
      Logger.error("🏠 [ROOM_MANAGER] 未找到游戏类型 #{game_type} 的房间模块")
      {:error, :room_module_not_found}
    end
  end

  @impl true
  def handle_info({:cleanup_stale_room, room_id}, state) do
    Logger.info("🏠 [ROOM_MANAGER] 清理过期房间数据: #{room_id}")

    case cleanup_terminated_room(state, room_id) do
      {:ok, new_state} ->
        Logger.info("🏠 [ROOM_MANAGER] 清理成功: #{room_id}")
        {:noreply, new_state}

      {:error, reason} ->
        Logger.warning("🏠 [ROOM_MANAGER] 清理失败: #{room_id}, 原因: #{reason}")
        {:noreply, state}
    end
  end

  # ==================== 房间配置加载和合并系统 ====================

  # 从数据库加载房间配置
  defp load_room_config_from_db(game_id, server_id)
       when is_integer(game_id) and is_integer(server_id) do
    try do
      case Teen.GameManagement.LeveRoomConfig.get_by_game_and_server(%{
             game_id: game_id,
             server_id: server_id
           }) do
        {:ok, [room_config | _]} ->
          Logger.info("🏠 [ROOM_MANAGER] 找到房间配置 - 游戏ID: #{game_id}, 服务器ID: #{server_id}")
          backend_config = room_config.unified_config || %{}

          # 智能标准化后台配置的键（字符串数字->数字，字符串->原子，数字原子->数字）
          normalized_config = normalize_config_keys(backend_config)
          Logger.debug("🔄 [KEY_NORMALIZE] 后台配置键标准化完成 - 配置项数: #{map_size(normalized_config)}")
          normalized_config

        {:ok, []} ->
          Logger.info("🏠 [ROOM_MANAGER] 未找到房间配置 (游戏ID: #{game_id}, 服务器ID: #{server_id})，使用默认配置")
          %{}

        {:error, reason} ->
          Logger.warning(
            "🏠 [ROOM_MANAGER] 查询房间配置失败 (游戏ID: #{game_id}, 服务器ID: #{server_id}): #{inspect(reason)}"
          )

          %{}
      end
    rescue
      error ->
        Logger.error(
          "🏠 [ROOM_MANAGER] 加载房间配置异常 (游戏ID: #{game_id}, 服务器ID: #{server_id}): #{inspect(error)}"
        )

        %{}
    end
  end

  defp load_room_config_from_db(_game_id, _server_id) do
    Logger.warning("🏠 [ROOM_MANAGER] 无效的游戏ID或服务器ID，使用默认配置")
    %{}
  end

  # 智能键标准化：优先转换为数字键，其次为原子键
  defp normalize_config_keys(map) when is_map(map) do
    map
    |> Enum.map(fn {key, value} ->
      normalized_key =
        cond do
          is_binary(key) ->
            # 尝试将字符串转换为数字
            case Integer.parse(key) do
              # 纯数字字符串转为数字键
              {num, ""} ->
                num

              _ ->
                # 非数字字符串转为原子键
                try do
                  String.to_existing_atom(key)
                rescue
                  ArgumentError ->
                    String.to_atom(key)
                end
            end

          is_integer(key) ->
            # 保持数字键不变
            key

          is_atom(key) ->
            # 检查原子是否表示数字
            key_str = Atom.to_string(key)

            case Integer.parse(key_str) do
              # 数字原子转为数字键
              {num, ""} -> num
              # 保持非数字原子不变
              _ -> key
            end

          true ->
            # 保持其他类型的键不变
            key
        end

      normalized_value =
        cond do
          # 递归处理嵌套Map
          is_map(value) -> normalize_config_keys(value)
          # 处理列表
          is_list(value) -> normalize_list_values(value)
          # 保持其他类型的值不变
          true -> value
        end

      {normalized_key, normalized_value}
    end)
    |> Enum.into(%{})
  end

  defp normalize_config_keys(value), do: value

  # 处理列表中的Map值
  defp normalize_list_values(list) when is_list(list) do
    Enum.map(list, fn item ->
      if is_map(item) do
        normalize_config_keys(item)
      else
        item
      end
    end)
  end

  defp normalize_list_values(value), do: value

  # 合并默认配置和后台配置（简化版本 - 后台配置已转换为原子键）
  defp merge_configs(default_config, backend_config)
       when is_map(default_config) and is_map(backend_config) do
    if map_size(backend_config) == 0 do
      Logger.info("🏠 [ROOM_MANAGER] 使用默认配置 - 后台配置为空")
      default_config
    else
      Logger.info("🏠 [ROOM_MANAGER] 合并配置 - 后台配置项数: #{map_size(backend_config)}")
      # 直接深度合并，因为键已经统一为原子格式
      deep_merge(default_config, backend_config)
    end
  end

  defp merge_configs(default_config, _backend_config) do
    Logger.warning("🏠 [ROOM_MANAGER] 配置格式错误，使用默认配置")
    default_config || %{}
  end

  # 深度合并Map - 支持列表、原子等特殊数据类型（参考 Slot777Config）
  defp deep_merge(left, right) when is_map(left) and is_map(right) do
    Map.merge(left, right, &merge_values/3)
  end

  defp deep_merge(_left, right), do: right

  # 获取指定游戏类型的所有房间ID
  defp get_rooms_by_game_type(state, game_type) do
    # 合并等待房间、百人场房间和常规房间
    waiting_rooms = Map.get(state.waiting_rooms, game_type, [])

    lobby_room =
      case Map.get(state.lobby_rooms, game_type) do
        nil -> []
        room_id -> [room_id]
      end

    # 从所有房间中筛选出指定游戏类型的房间
    regular_rooms =
      state.rooms
      |> Enum.filter(fn {_room_id, room_info} -> room_info.game_type == game_type end)
      |> Enum.map(fn {room_id, _room_info} -> room_id end)

    # 合并并去重
    (waiting_rooms ++ lobby_room ++ regular_rooms)
    |> Enum.uniq()
  end

  # 合并值的策略函数
  defp merge_values(key, left_val, right_val) do
    cond do
      # 两个都是Map：递归深度合并
      is_map(left_val) and is_map(right_val) ->
        deep_merge(left_val, right_val)

      # 两个都是列表：用右边的列表完全替换左边的列表
      is_list(left_val) and is_list(right_val) ->
        Logger.debug("🏠 [ROOM_MANAGER] 列表合并 #{key}: 使用后台配置")
        right_val

      # 其他类型（包括原子、字符串、数字等）：直接用右边的值覆盖左边
      true ->
        if String.contains?(to_string(key), "odds") or key in [:odds_config, "odds_config"] do
          Logger.info(
            "🏠 [ROOM_MANAGER] 重要配置项 #{key}: #{inspect(left_val)} -> #{inspect(right_val)}"
          )
        end

        right_val
    end
  end
end
