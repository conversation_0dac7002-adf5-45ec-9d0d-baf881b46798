defmodule Cypridina.RoomSystem.RoomBehaviour do
  @moduledoc """
  定义游戏房间的通用行为接口

  这个behaviour定义了所有游戏房间必须实现的回调函数，
  确保不同游戏类型的房间都有统一的接口和行为模式。

  ## 核心回调函数

  ### 房间生命周期
  - `init_game_logic/1` - 初始化游戏逻辑
  - `on_game_start/1` - 游戏开始时的处理
  - `handle_game_tick/1` - 游戏时钟处理

  ### 玩家管理
  - `handle_player_join/4` - 处理玩家加入请求（可完全自定义）
  - `handle_player_leave/2` - 处理玩家离开请求（可完全自定义）
  - `on_player_joined/2` - 玩家加入房间后的游戏逻辑处理
  - `on_player_rejoined/2` - 玩家重连后的游戏逻辑处理
  - `on_player_left/2` - 玩家离开房间后的游戏逻辑处理

  ### 消息处理
  - `handle_game_message/3` - 处理游戏消息

  ### 游戏状态查询
  - `game_type/0` - 获取游戏类型
  - `min_players/0` - 最小玩家数
  - `max_players/0` - 最大玩家数
  - `game_over?/1` - 判断游戏是否结束

  ## 使用示例

      defmodule MyGame.Room do
        @behaviour Cypridina.RoomSystem.RoomBehaviour

        @impl true
        def game_type, do: :my_game

        @impl true
        def min_players, do: 2

        @impl true
        def max_players, do: 8

        # ... 实现其他回调函数
      end
  """

  # ==================== 游戏基本信息 ====================

  @doc """
  获取游戏类型

  返回游戏的唯一标识符，用于区分不同的游戏类型。

  ## 返回值
  - `atom()` - 游戏类型标识符，如 `:longhu`, `:teen_patti`, `:pot_blind` 等

  ## 示例
      iex> MyGame.Room.game_type()
      :my_game
  """
  @callback game_type() :: atom()

  @doc """
  获取最小玩家数

  返回开始游戏所需的最小玩家数量。

  ## 返回值
  - `integer()` - 最小玩家数，必须大于0

  ## 示例
      iex> MyGame.Room.min_players()
      2
  """
  @callback min_players() :: integer()

  @doc """
  获取最大玩家数

  返回房间能容纳的最大玩家数量。

  ## 返回值
  - `integer()` - 最大玩家数，必须大于等于最小玩家数

  ## 示例
      iex> MyGame.Room.max_players()
      8
  """
  @callback max_players() :: integer()

  # ==================== 房间生命周期 ====================

  @doc """
  初始化游戏逻辑

  在房间创建后调用，用于初始化游戏特定的数据结构和配置。

  ## 参数
  - `state` - 房间状态map，包含基础房间信息

  ## 返回值
  - `map()` - 更新后的房间状态，通常会在 `game_data` 字段中添加游戏特定数据

  ## 示例
      def init_game_logic(state) do
        game_data = %{
          phase: :waiting,
          round: 0,
          cards: %{},
          bets: %{}
        }

        %{state | game_data: game_data}
      end
  """
  @callback init_game_logic(state :: map()) :: map()

  @doc """
  游戏开始处理

  当满足游戏开始条件时调用，用于启动游戏逻辑。

  ## 参数
  - `state` - 当前房间状态

  ## 返回值
  - `map()` - 更新后的房间状态

  ## 示例
      def on_game_start(state) do
        # 开始新一轮游戏
        new_game_data = %{
          state.game_data |
          phase: :betting,
          round: state.game_data.round + 1
        }

        %{state | game_data: new_game_data, room_state: :playing}
      end
  """
  @callback on_game_start(state :: map()) :: map()

  @doc """
  游戏时钟处理

  定期调用的游戏时钟处理函数，用于处理游戏中的定时逻辑。

  ## 参数
  - `state` - 当前房间状态

  ## 返回值
  - `map()` - 更新后的房间状态

  ## 示例
      def handle_game_tick(state) do
        # 检查阶段超时
        case check_phase_timeout(state) do
          {:timeout, new_state} -> transition_to_next_phase(new_state)
          {:ok, state} -> state
        end
      end
  """
  @callback handle_game_tick(state :: map()) :: map()

  # ==================== 玩家管理 ====================

  @doc """
  处理玩家加入请求

  当玩家请求加入房间时调用，具体游戏可以完全自定义加入逻辑，
  包括加入条件检查、房间状态验证等。

  ## 参数
  - `state` - 当前房间状态
  - `user_id` - 请求加入的用户ID
  - `bring_data` - 玩家携带的额外数据
  - `from` - GenServer.call 的 from 参数，用于回复客户端

  ## 返回值
  - `map()` - 更新后的房间状态

  ## 示例
      def handle_player_join(state, user_id, bring_data, from) do
        # 自定义加入条件检查
        if custom_join_condition?(state, user_id) do
          # 使用默认逻辑
          default_handle_player_join(state, user_id, bring_data, from)
        else
          # 拒绝加入
          GenServer.reply(from, {:error, :custom_rejection_reason})
          state
        end
      end
  """
  @callback handle_player_join(
              state :: map(),
              user_id :: integer(),
              bring_data :: map(),
              from :: GenServer.from()
            ) :: map()

  @doc """
  处理玩家离开请求

  当玩家请求离开房间时调用，具体游戏可以完全自定义离开逻辑，
  包括离开条件检查、游戏状态处理等。

  ## 参数
  - `state` - 当前房间状态
  - `user_id` - 请求离开的用户ID

  ## 返回值
  - `{result, new_state}` - 结果元组和更新后的房间状态
    - `result` - `{:ok, :left}` 表示成功离开，`{:error, reason}` 表示离开失败

  ## 示例
      def handle_player_leave(state, user_id) do
        # 自定义离开条件检查
        if can_leave_now?(state, user_id) do
          # 使用默认逻辑
          default_handle_player_leave(state, user_id)
        else
          # 拒绝离开
          {{:error, :cannot_leave_during_game}, state}
        end
      end
  """
  @callback handle_player_leave(state :: map(), user_id :: integer()) ::
              {{:ok, :left} | {:error, atom()}, map()}

  @doc """
  玩家加入房间后的游戏逻辑处理

  当新玩家成功加入房间后调用，用于处理游戏特定的加入逻辑。

  ## 参数
  - `state` - 当前房间状态
  - `player` - 加入的玩家信息map，包含 `user_id`, `numeric_id`, `nickname` 等字段

  ## 返回值
  - `map()` - 更新后的房间状态

  ## 示例
      def on_player_joined(state, player) do
        # 发送游戏配置给新玩家
        send_game_config(state, player)

        # 广播玩家加入消息
        broadcast_player_enter(state, player)

        state
      end
  """
  @callback on_player_joined(state :: map(), player :: map()) :: map()

  @doc """
  玩家重连处理

  当玩家重新连接到房间时调用，用于恢复玩家的游戏状态。

  ## 参数
  - `state` - 当前房间状态
  - `player` - 重连的玩家信息map

  ## 返回值
  - `map()` - 更新后的房间状态

  ## 示例
      def on_player_rejoined(state, player) do
        # 发送当前游戏状态
        send_current_game_state(state, player)

        # 如果游戏中，发送玩家手牌等私有信息
        if state.room_state == :playing do
          send_player_private_data(state, player)
        end

        state
      end
  """
  @callback on_player_rejoined(state :: map(), player :: map()) :: map()

  @doc """
  玩家离开房间后的游戏逻辑处理

  当玩家成功离开房间后调用，用于清理玩家相关数据和处理游戏逻辑。
  此函数在 handle_player_leave 的默认流程中被调用。

  ## 参数
  - `state` - 当前房间状态
  - `player` - 离开的玩家信息map

  ## 返回值
  - `map()` - 更新后的房间状态

  ## 示例
      def on_player_left(state, player) do
        # 处理游戏特定的离开逻辑
        new_state = state
        |> handle_player_quit_in_game(player)
        |> clear_player_game_data(player)
        |> check_game_end_conditions()

        # 调用默认的玩家移除逻辑
        default_on_player_left(new_state, player)
      end
  """
  @callback on_player_left(state :: map(), player :: map()) :: map()

  # ==================== 消息处理 ====================

  @doc """
  处理游戏消息

  处理来自客户端的游戏相关消息，如下注、操作等。

  ## 参数
  - `state` - 当前房间状态
  - `player` - 发送消息的玩家信息map
  - `message` - 客户端发送的消息map，通常包含 `mainId`, `subId`, `data` 等字段

  ## 返回值
  - `map()` - 更新后的房间状态

  ## 示例
      def handle_game_message(state, player, message) do
        case message do
          %{"mainId" => 5, "subId" => 1001} ->
            handle_bet_request(state, player, message)

          %{"mainId" => 5, "subId" => 1002} ->
            handle_fold_request(state, player, message)

          _ ->
            state
        end
      end
  """
  @callback handle_game_message(state :: map(), player :: map(), message :: map()) :: map()

  # ==================== 游戏状态查询 ====================

  @doc """
  判断游戏是否结束

  检查当前游戏状态是否满足结束条件。

  ## 参数
  - `state` - 当前房间状态

  ## 返回值
  - `boolean()` - `true` 表示游戏已结束，`false` 表示游戏继续

  ## 示例
      def game_over?(state) do
        case state.game_data.phase do
          :finished -> true
          :playing -> check_win_condition(state)
          _ -> false
        end
      end
  """
  @callback game_over?(state :: map()) :: boolean()
end
