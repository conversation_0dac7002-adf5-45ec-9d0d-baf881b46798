defmodule Cypridina.Communications.SmsService do
  @moduledoc """
  短信服务资源

  包装XunmaiCloud短信API，提供短信发送功能
  遵循"任其崩溃"原则，尽早发现问题
  """
  require Logger

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.Communications,
    data_layer: Ash.DataLayer.Ets,
    extensions: [AshAdmin.Resource, AshRateLimiter]

  admin do
    table_columns [:id, :phone_number, :content, :status, :sent_at]
  end

  code_interface do
    define :send_sms
    define :send_verification_code
  end

  actions do
    defaults [:read]

    action :send_sms, :struct do
      argument :phone_number, :string, allow_nil?: false
      argument :content, :string, allow_nil?: false
      argument :sender, :string, allow_nil?: true
      argument :ip_address, :string, allow_nil?: true

      run fn input, context ->
        phone_number = input.arguments.phone_number
        content = input.arguments.content
        sender = input.arguments.sender

        ip_address =
          input.arguments.ip_address || get_in(context, [:private, :ip_address]) || "unknown"

        # 基于IP的速率限制：每分钟最多5条短信
        ip_key = "sms_ip:#{ip_address}"

        case Cypridina.RateLimiter.hit(ip_key, :timer.minutes(1), 5) do
          {:allow, _count} ->
            # 基于手机号的速率限制：每分钟最多1条短信
            phone_key = "sms_phone:#{phone_number}"

            case Cypridina.RateLimiter.hit(phone_key, :timer.minutes(1), 1) do
              {:allow, _count} ->
                case send_sms_request(phone_number, content, sender) do
                  {:ok, response} ->
                    {:ok,
                     %{
                       id: Ash.UUID.generate(),
                       phone_number: phone_number,
                       content: content,
                       status: "sent",
                       batch_id: response["data"]["smsSendBatchId"],
                       sent_at: DateTime.utc_now()
                     }}

                  {:error, reason} ->
                    {:error, reason}
                end

              {:deny, _limit} ->
                {:error, "手机号发送频率过快，请稍后再试"}
            end

          {:deny, _limit} ->
            {:error, "IP发送频率过快，请稍后再试"}
        end
      end
    end

    action :send_verification_code, :struct do
      argument :phone_number, :string, allow_nil?: false
      argument :code, :string, allow_nil?: false
      argument :ip_address, :string, allow_nil?: true

      run fn input, context ->
        phone_number = input.arguments.phone_number
        code = input.arguments.code

        ip_address =
          input.arguments.ip_address || get_in(context, [:private, :ip_address]) || "unknown"

        content = "[Cypridina] 您的验证码是 #{code}，5分钟内有效，请勿泄露给他人。"
        content = "#{code}"

        # 基于IP的速率限制：每分钟最多3条验证码短信
        ip_key = "verification_sms_ip:#{ip_address}"

        case Cypridina.RateLimiter.hit(ip_key, :timer.minutes(1), 30) do
          {:allow, _count} ->
            # 基于手机号的速率限制：每分钟最多1条验证码短信
            phone_key = "verification_sms_phone:#{phone_number}"

            case Cypridina.RateLimiter.hit(phone_key, :timer.minutes(1), 10) do
              {:allow, _count} ->
                case send_sms_request(phone_number, content, nil) do
                  {:ok, response} ->
                    {:ok,
                     %{
                       id: Ash.UUID.generate(),
                       phone_number: phone_number,
                       content: content,
                       status: "sent",
                       batch_id: response["data"]["smsSendBatchId"],
                       sent_at: DateTime.utc_now()
                     }}

                  {:error, reason} ->
                    {:error, reason}
                end

              {:deny, _limit} ->
                {:error, "手机号验证码发送频率过快，请稍后再试"}
            end

          {:deny, _limit} ->
            {:error, "IP验证码发送频率过快，请稍后再试"}
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :phone_number, :string do
      allow_nil? false
      public? true
      description "手机号码"
    end

    attribute :content, :string do
      allow_nil? false
      public? true
      description "短信内容"
    end

    attribute :status, :string do
      allow_nil? false
      public? true
      description "发送状态"
      default "pending"
    end

    attribute :batch_id, :string do
      allow_nil? true
      public? true
      description "短信批次ID"
    end

    attribute :sent_at, :utc_datetime do
      allow_nil? true
      public? true
      description "发送时间"
    end
  end

  # 私有函数

  defp send_sms_request(phone_number, content, sender) do
    Logger.info("发送短信请求 - 手机号: #{phone_number}, 内容: #{content}")

    # 调用真实API发送短信
    config = get_sms_config()

    timestamp =
      DateTime.utc_now()
      |> DateTime.shift_zone!("Asia/Shanghai")
      |> Calendar.strftime("%Y%m%d%H%M%S")

    sign =
      generate_signature(
        config.app_key,
        timestamp,
        config.secret_key
      )

    fields = [
      appKey: config.app_key,
      timestamp: timestamp,
      sign: sign,
      phoneNumbers: phone_number,
      smsContent: content
      # sender: sender || ""
    ]

    Logger.info("发送短信请求体: #{inspect(fields)}")

    case Req.post(config.api_url, form_multipart: fields) do
      {:ok, %{status: 200, body: response}} ->
        # 添加调试日志
        Logger.info("SMS API Response: #{inspect(response)}")

        case response do
          %{"code" => code} when code == "" or code == "0" or code == 0 ->
            {:ok, response}

          %{"code" => code, "msg" => msg} ->
            {:error, "短信发送失败: #{code} - #{msg}"}

          %{"code" => code} ->
            {:error, "短信发送失败: #{code} - 系统异常"}

          _ ->
            {:error, "短信发送失败: 未知错误"}
        end

      {:ok, %{status: status}} ->
        {:error, "短信发送失败: HTTP #{status}"}

      {:error, reason} ->
        {:error, "短信发送失败: #{inspect(reason)}"}
    end
  end

  defp get_sms_config do
    %{
      api_url:
        Application.get_env(:cypridina, :sms)[:api_url] ||
          "https://xunmaicloud.com/admin-api/business/sms-send-batch/send",
      app_key:
        Application.get_env(:cypridina, :sms)[:app_key] || raise("SMS app_key not configured"),
      secret_key:
        Application.get_env(:cypridina, :sms)[:secret_key] ||
          raise("SMS secret_key not configured")
    }
  end

  defp generate_signature(app_key, timestamp, secret_key) do
    data =
      "#{app_key}#{timestamp}#{secret_key}"

    # |> dbg(label: "生成签名数据")

    :crypto.hash(:md5, data)
    |> Base.encode16(case: :lower)
  end
end
