defmodule Cypridina.Communications.VerificationCode do
  @moduledoc """
  验证码管理资源

  管理手机验证码的生成、存储、验证和过期
  """

  require Ash.Query
  require Logger
  import Ash.Expr

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Cypridina.Communications,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :phone_number, :code_type, :status, :expires_at, :inserted_at]
  end

  postgres do
    table "verification_codes"
    repo Cypridina.Repo
  end

  code_interface do
    define :generate_and_send
    define :verify_code
    define :cleanup_expired
  end

  actions do
    defaults [:read, :destroy]

    update :update do
      primary? true
      accept [:status, :sent_at, :used_at, :failure_reason]
    end

    create :generate_and_send do
      accept [:phone_number, :code_type, :ip_address]

      change fn changeset, _context ->
        phone_number = Ash.Changeset.get_attribute(changeset, :phone_number)
        code_type = Ash.Changeset.get_attribute(changeset, :code_type)
        ip_address = Ash.Changeset.get_attribute(changeset, :ip_address)

        # 生成6位数字验证码
        code = generate_code()

        # 设置过期时间（5分钟）
        expires_at = DateTime.add(DateTime.utc_now(), 5 * 60, :second)

        changeset
        |> Ash.Changeset.change_attribute(:code, code)
        |> Ash.Changeset.change_attribute(:expires_at, expires_at)
        |> Ash.Changeset.change_attribute(:status, 0)
        |> Ash.Changeset.change_attribute(:ip_address, ip_address)
        |> Ash.Changeset.after_action(fn _changeset, verification_code ->
          # 发送短信验证码
          case Cypridina.Communications.SmsService.send_verification_code(%{
                 phone_number: phone_number,
                 code: code,
                 ip_address: ip_address
               }) do
            {:ok, _sms_result} ->
              # 更新为已发送状态
              updated_record =
                Ash.update!(verification_code, %{status: 1, sent_at: DateTime.utc_now()})

              {:ok, updated_record}

            {:error, reason} ->
              # 更新为发送失败状态
              reason_str =
                case reason do
                  %Ash.Error.Unknown{} -> Exception.message(reason)
                  _ -> to_string(reason)
                end

              # 截断错误消息以符合数据库字段长度限制
              truncated_reason = String.slice(reason_str, 0, 450)

              updated_record =
                Ash.update!(verification_code, %{status: 2, failure_reason: truncated_reason})

              {:error, "短信发送失败: #{truncated_reason}"}
          end
        end)
      end
    end

    action :verify_code, :boolean do
      argument :phone_number, :string, allow_nil?: false
      argument :code, :string, allow_nil?: false
      argument :code_type, :integer, allow_nil?: false

      run fn input, _context ->
        phone_number = input.arguments.phone_number
        code = input.arguments.code
        code_type = input.arguments.code_type

        # 速率限制检查
        case check_verify_rate_limit(phone_number) do
          :ok ->
            case find_and_verify_code(phone_number, code, code_type) do
              {:ok, verification_code} ->
                # 标记验证码为已使用
                Ash.update!(verification_code, %{status: 3, used_at: DateTime.utc_now()})
                {:ok, true}

              {:error, reason} ->
                {:error, reason}
            end

          {:error, :rate_limited} ->
            {:error, "验证频率过快，请稍后再试"}
        end
      end
    end

    action :cleanup_expired, :struct do
      run fn _input, _context ->
        # 简化实现，暂时返回成功
        {:ok, %{cleaned_count: 0}}
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :phone_number, :string do
      allow_nil? false
      public? true
      description "手机号码"
      constraints max_length: 20
    end

    attribute :code, :string do
      allow_nil? false
      public? true
      description "验证码"
      constraints max_length: 10
    end

    attribute :code_type, :integer do
      allow_nil? false
      public? true
      description "验证码类型：1-注册，2-登录，3-找回密码，0-绑定手机"
      constraints min: 0, max: 3
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-待发送，1-已发送，2-发送失败，3-已使用"
      default 0
      constraints min: 0, max: 3
    end

    attribute :expires_at, :utc_datetime do
      allow_nil? false
      public? true
      description "过期时间"
    end

    attribute :sent_at, :utc_datetime do
      allow_nil? true
      public? true
      description "发送时间"
    end

    attribute :used_at, :utc_datetime do
      allow_nil? true
      public? true
      description "使用时间"
    end

    attribute :failure_reason, :string do
      allow_nil? true
      public? true
      description "发送失败原因"
      constraints max_length: 500
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "请求IP地址"
      constraints max_length: 45
    end

    timestamps()
  end

  identities do
    identity :unique_phone_code_time, [:phone_number, :code, :inserted_at]
  end

  # 私有函数

  defp generate_code do
    100_000..999_999
    |> Enum.random()
    |> to_string()
  end

  defp check_verify_rate_limit(phone_number) do
    # 每个手机号1分钟内最多验证5次
    case Cypridina.RateLimiter.hit("verify:#{phone_number}", 60_000, 5) do
      {:allow, _count} -> :ok
      {:deny, _limit} -> {:error, :rate_limited}
    end
  end

  defp find_and_verify_code(phone_number, code, code_type) do
    # 查找有效的验证码
    now = DateTime.utc_now()

    case __MODULE__
         |> Ash.Query.for_read(:read)
         |> Ash.Query.filter(
           expr(phone_number == ^phone_number and code_type == ^code_type and status == 1)
         )
         |> Ash.Query.filter(expr(expires_at > ^now))
         |> Ash.Query.sort(inserted_at: :desc)
         |> Ash.Query.limit(1)
         |> Ash.read() do
      {:ok, [verification_code]} ->
        # 验证验证码
        if verification_code.code == code do
          {:ok, verification_code}
        else
          {:error, "验证码错误"}
        end

      {:ok, []} ->
        {:error, "验证码不存在或已过期"}

      {:error, reason} ->
        {:error, "查询验证码失败: #{inspect(reason)}"}
    end
  end
end
