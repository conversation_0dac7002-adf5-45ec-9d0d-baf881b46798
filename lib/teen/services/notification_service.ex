defmodule Teen.Services.NotificationService do
  @moduledoc """
  统一通知服务

  处理所有系统的实时通知，包括：
  - 余额变动通知
  - 支付状态通知
  - 购买成功通知
  - 提现状态通知
  - 系统消息通知
  """

  require Logger

  @doc """
  发送余额变动通知
  """
  def send_balance_change_notification(user_id, balance_change) do
    notification = %{
      type: "balance_change",
      user_id: user_id,
      old_balance: balance_change.old_balance,
      new_balance: balance_change.new_balance,
      change_amount: balance_change.change_amount,
      # :increase, :decrease
      change_type: balance_change.change_type,
      reason: balance_change.reason,
      timestamp: DateTime.utc_now()
    }

    broadcast_to_user(user_id, "balance_update", notification)
  end

  @doc """
  发送支付状态通知
  """
  def send_payment_status_notification(user_id, payment_status) do
    notification = %{
      type: "payment_status",
      user_id: user_id,
      order_id: payment_status.order_id,
      # :recharge, :withdrawal, :purchase
      payment_type: payment_status.payment_type,
      # :pending, :success, :failed
      status: payment_status.status,
      amount: payment_status.amount,
      message: payment_status.message,
      timestamp: DateTime.utc_now()
    }

    broadcast_to_user(user_id, "payment_status", notification)
  end

  @doc """
  发送购买成功通知
  """
  def send_purchase_success_notification(user_id, purchase_info) do
    notification = %{
      type: "purchase_success",
      user_id: user_id,
      purchase_id: purchase_info.purchase_id,
      product_name: purchase_info.product_name,
      product_type: purchase_info.product_type,
      amount: purchase_info.amount,
      delivery_items: purchase_info.delivery_items,
      timestamp: DateTime.utc_now()
    }

    broadcast_to_user(user_id, "shop_purchase", notification)
  end

  @doc """
  发送提现状态通知
  """
  def send_withdrawal_status_notification(user_id, withdrawal_info) do
    notification = %{
      type: "withdrawal_status",
      user_id: user_id,
      withdrawal_id: withdrawal_info.withdrawal_id,
      # :pending, :processing, :completed, :failed
      status: withdrawal_info.status,
      amount: withdrawal_info.amount,
      fee_amount: withdrawal_info.fee_amount,
      actual_amount: withdrawal_info.actual_amount,
      estimated_time: withdrawal_info.estimated_time,
      message: withdrawal_info.message,
      timestamp: DateTime.utc_now()
    }

    broadcast_to_user(user_id, "withdrawal_status", notification)
  end

  @doc """
  发送系统消息通知
  """
  def send_system_message(user_id, message) do
    notification = %{
      type: "system_message",
      user_id: user_id,
      title: message.title,
      content: message.content,
      # :info, :warning, :error, :success
      category: message.category,
      action_required: message.action_required || false,
      timestamp: DateTime.utc_now()
    }

    broadcast_to_user(user_id, "system_message", notification)
  end

  @doc """
  发送充值成功通知
  """
  def send_recharge_success_notification(user_id, recharge_info) do
    notification = %{
      type: "recharge_success",
      user_id: user_id,
      recharge_id: recharge_info.recharge_id,
      amount: recharge_info.amount,
      bonus_amount: recharge_info.bonus_amount || 0,
      new_balance: recharge_info.new_balance,
      payment_method: recharge_info.payment_method,
      timestamp: DateTime.utc_now()
    }

    broadcast_to_user(user_id, "recharge_success", notification)
  end

  @doc """
  发送游戏事件通知
  """
  def send_game_event_notification(user_id, event_info) do
    notification = %{
      type: "game_event",
      user_id: user_id,
      # :win, :lose, :jackpot, :level_up
      event_type: event_info.event_type,
      game_id: event_info.game_id,
      amount: event_info.amount,
      balance_change: event_info.balance_change,
      message: event_info.message,
      timestamp: DateTime.utc_now()
    }

    broadcast_to_user(user_id, "game_event", notification)
  end

  @doc """
  发送VIP等级变更通知
  """
  def send_vip_level_change_notification(user_id, vip_info) do
    notification = %{
      type: "vip_level_change",
      user_id: user_id,
      old_level: vip_info.old_level,
      new_level: vip_info.new_level,
      benefits: vip_info.benefits,
      requirements: vip_info.requirements,
      timestamp: DateTime.utc_now()
    }

    broadcast_to_user(user_id, "vip_update", notification)
  end

  @doc """
  广播全局通知
  """
  def broadcast_global_notification(notification) do
    CypridinaWeb.Endpoint.broadcast("global", "notification", %{
      type: "global_notification",
      content: notification,
      timestamp: DateTime.utc_now()
    })

    Logger.info("发送全局通知: #{notification.title}")
  end

  @doc """
  广播系统维护通知
  """
  def broadcast_maintenance_notification(maintenance_info) do
    notification = %{
      type: "maintenance",
      title: maintenance_info.title,
      content: maintenance_info.content,
      start_time: maintenance_info.start_time,
      end_time: maintenance_info.end_time,
      affected_services: maintenance_info.affected_services,
      timestamp: DateTime.utc_now()
    }

    CypridinaWeb.Endpoint.broadcast("global", "maintenance", notification)

    Logger.info("发送系统维护通知: #{maintenance_info.title}")
  end

  @doc """
  处理余额变动事件（来自Ledger系统）
  """
  def handle_balance_change_event(balance_notification) do
    # 解析余额变动信息
    case parse_balance_notification(balance_notification) do
      {:ok, balance_change} ->
        send_balance_change_notification(balance_change.user_id, balance_change)

      {:error, reason} ->
        Logger.warning("处理余额变动通知失败: #{inspect(reason)}")
    end
  end

  @doc """
  处理支付事件（来自PaymentSystem）
  """
  def handle_payment_event(payment_notification) do
    case parse_payment_notification(payment_notification) do
      {:ok, payment_status} ->
        send_payment_status_notification(payment_status.user_id, payment_status)

      {:error, reason} ->
        Logger.warning("处理支付通知失败: #{inspect(reason)}")
    end
  end

  @doc """
  处理购买事件（来自ShopSystem）
  """
  def handle_purchase_event(purchase_notification) do
    case parse_purchase_notification(purchase_notification) do
      {:ok, purchase_info} ->
        send_purchase_success_notification(purchase_info.user_id, purchase_info)

      {:error, reason} ->
        Logger.warning("处理购买通知失败: #{inspect(reason)}")
    end
  end

  # ==================== 私有函数 ====================

  defp broadcast_to_user(user_id, event, notification) do
    user_channel = "user:#{user_id}"

    case CypridinaWeb.Endpoint.broadcast(user_channel, event, notification) do
      :ok ->
        Logger.debug("发送通知成功: #{event} -> #{user_id}")
        {:ok, notification}

      {:error, reason} ->
        Logger.error("发送通知失败: #{event} -> #{user_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp parse_balance_notification(notification) do
    try do
      balance_change = %{
        user_id: extract_user_id_from_account(notification.data.account_id),
        old_balance: get_previous_balance(notification.data.account_id),
        new_balance: Money.to_decimal(notification.data.balance) |> Decimal.to_integer(),
        change_amount: calculate_balance_change(notification.data),
        change_type: determine_change_type(notification.data),
        reason: extract_reason_from_transfer(notification.data.transfer_id)
      }

      {:ok, balance_change}
    rescue
      error ->
        {:error, error}
    end
  end

  defp parse_payment_notification(notification) do
    try do
      payment_status = %{
        user_id: notification.user_id,
        order_id: notification.order_id,
        payment_type: notification.payment_type,
        status: notification.status,
        amount: notification.amount,
        message: notification.message || get_status_message(notification.status)
      }

      {:ok, payment_status}
    rescue
      error ->
        {:error, error}
    end
  end

  defp parse_purchase_notification(notification) do
    try do
      purchase_info = %{
        user_id: notification.user_id,
        purchase_id: notification.purchase_id,
        product_name: notification.product_name,
        product_type: notification.product_type,
        amount: notification.amount,
        delivery_items: notification.delivery_items || []
      }

      {:ok, purchase_info}
    rescue
      error ->
        {:error, error}
    end
  end

  defp extract_user_id_from_account(account_id) do
    case Cypridina.Ledger.Account |> Ash.get(account_id) do
      {:ok, account} ->
        case Cypridina.Ledger.AccountIdentifier.parse_identifier(account.identifier) do
          {:ok, %{type: :user, id: user_id}} -> user_id
          _ -> nil
        end

      _ ->
        nil
    end
  end

  defp get_previous_balance(account_id) do
    # 从缓存或数据库获取之前的余额
    # 这里需要根据实际情况实现
    0
  end

  defp calculate_balance_change(balance_data) do
    # 计算余额变化量
    # 这里需要根据实际情况实现
    0
  end

  defp determine_change_type(balance_data) do
    # 判断余额是增加还是减少
    # 这里需要根据实际情况实现
    :increase
  end

  defp extract_reason_from_transfer(transfer_id) do
    case Cypridina.Ledger.Transfer |> Ash.get(transfer_id) do
      {:ok, transfer} -> transfer.description || "余额变动"
      _ -> "余额变动"
    end
  end

  defp get_status_message(status) do
    case status do
      :pending -> "处理中"
      :success -> "成功"
      :failed -> "失败"
      :cancelled -> "已取消"
      _ -> "未知状态"
    end
  end
end
