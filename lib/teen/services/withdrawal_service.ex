defmodule Teen.Services.WithdrawalService do
  @moduledoc """
  提现服务层

  提供提现相关的高级业务接口：
  - 创建提现申请（含积分扣除）
  - 管理员审核操作
  - 执行支付处理
  - 处理支付回调
  - 查询和统计功能

  所有复杂操作都通过Reactor实现，确保数据一致性
  """

  require Logger
  alias Teen.PaymentSystem

  # ========== 创建提现 ==========

  @doc """
  创建提现申请

  自动执行：
  1. 验证用户资格
  2. 创建提现记录
  3. 扣除积分到待审核账户
  """
  def create_withdrawal(user_id, params) do
    Logger.info("💰 创建提现申请 - 用户: #{user_id}")

    Logger.info(
      "💰 参数检查 - payment_method: #{inspect(params[:payment_method])}, bank_info: #{inspect(params[:bank_info])}"
    )

    reactor_params =
      Map.merge(params, %{
        user_id: user_id,
        ip_address: Map.get(params, :ip_address, "127.0.0.1")
      })

    Logger.info("💰 Reactor参数 - payment_method: #{inspect(reactor_params[:payment_method])}")

    case Reactor.run(PaymentSystem.CreateWithdrawalReactor, reactor_params) do
      {:ok, withdrawal} ->
        Logger.info("💰 提现申请创建成功: #{withdrawal.order_id}")
        {:ok, withdrawal}

      {:error, reason} ->
        Logger.error("💰 提现申请创建失败: #{inspect(reason)}")
        {:error, format_error(reason)}

      # 处理意外的返回值
      other ->
        Logger.error("💰 提现申请返回意外结果: #{inspect(other)}")
        {:error, "提现申请处理异常"}
    end
  end

  # ========== 审核操作 ==========

  @doc """
  审核通过提现申请

  审核通过后：
  - 积分保持在待审核账户
  - 自动触发支付流程（会调用 CompleteWithdrawalReactor）

  返回值包含：
  - withdrawal: 更新后的提现记录
  - payment_initiated: 是否成功启动支付
  - payment_result: 支付启动结果（如果成功）
  - payment_error: 支付启动错误（如果失败）
  """
  def approve_withdrawal(withdrawal_id, auditor_id) do
    Logger.info("💰 审核通过提现: #{withdrawal_id}")

    case Reactor.run(PaymentSystem.AuditWithdrawalReactor, %{
           withdrawal_id: withdrawal_id,
           auditor_id: auditor_id,
           action: :approve,
           feedback: "审核通过"
         }) do
      {:ok, result} ->
        Logger.info("💰 提现审核通过: #{result.withdrawal.order_id}")

        # 构建返回结果，包含支付流程状态
        response = %{
          withdrawal: result.withdrawal,
          audit_status: :approved,
          payment_initiated: Map.get(result, :payment_initiated, false),
          message: Map.get(result, :message, "审核通过")
        }

        # 添加支付相关信息
        response =
          if Map.get(result, :payment_initiated) do
            Map.merge(response, %{
              payment_result: Map.get(result, :payment_result),
              payment_status: :processing
            })
          else
            Map.merge(response, %{
              payment_error: Map.get(result, :payment_error),
              payment_status: :pending
            })
          end

        {:ok, response}

      {:error, reason} ->
        Logger.error("💰 提现审核失败: #{inspect(reason)}")
        {:error, format_error(reason)}
    end
  end

  @doc """
  审核拒绝提现申请

  自动将积分从待审核账户退回用户账户
  """
  def reject_withdrawal(withdrawal_id, auditor_id, feedback) do
    Logger.info("💰 审核拒绝提现: #{withdrawal_id}")

    case Reactor.run(PaymentSystem.AuditWithdrawalReactor, %{
           withdrawal_id: withdrawal_id,
           auditor_id: auditor_id,
           action: :reject,
           feedback: feedback
         }) do
      {:ok, result} ->
        Logger.info("💰 提现审核拒绝: #{result.withdrawal.order_id}")
        {:ok, result.withdrawal}

      {:error, reason} ->
        Logger.error("💰 提现拒绝失败: #{inspect(reason)}")
        {:error, format_error(reason)}
    end
  end

  # ========== 回调处理 ==========

  @doc """
  处理支付网关回调

  根据支付结果自动处理积分流转：
  - 成功：积分转入已支付账户
  - 失败：积分退回用户账户
  """
  def handle_withdrawal_callback(order_id, callback_data, signature \\ nil) do
    Logger.info("💰 处理提现回调: #{order_id}")

    case Reactor.run(PaymentSystem.WithdrawalCallbackReactor, %{
           order_id: order_id,
           callback_data: callback_data,
           gateway_signature: signature
         }) do
      {:ok, _result} ->
        Logger.info("💰 提现回调处理成功")
        {:ok, :processed}

      {:error, reason} ->
        Logger.error("💰 提现回调处理失败: #{inspect(reason)}")
        {:error, format_error(reason)}
    end
  end

  # ========== 查询功能 ==========

  @doc """
  获取用户提现统计
  """
  def get_user_withdrawal_stats(user_id) do
    case PaymentSystem.WithdrawalRecord.get_user_withdrawal_stats(user_id) do
      {:ok, stats} -> {:ok, format_stats(stats)}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取用户最近的提现记录
  """
  def get_user_recent_withdrawals(user_id, days \\ 30) do
    PaymentSystem.WithdrawalRecord.list_user_recent_withdrawals(user_id, days)
  end

  defp format_error(error) when is_binary(error), do: error
  defp format_error(error), do: inspect(error)

  defp format_stats(stats) do
    %{
      total_count: Map.get(stats, :total_count, 0),
      total_amount: Map.get(stats, :total_amount, Decimal.new(0)),
      success_count: Map.get(stats, :success_count, 0),
      success_amount: Map.get(stats, :success_amount, Decimal.new(0)),
      pending_count: Map.get(stats, :pending_count, 0),
      pending_amount: Map.get(stats, :pending_amount, Decimal.new(0))
    }
  end

  defp apply_pagination({:ok, records}, limit, offset) do
    paginated =
      records
      |> Enum.drop(offset)
      |> Enum.take(limit)

    {:ok, paginated}
  end

  defp apply_pagination(error, _, _), do: error
end
