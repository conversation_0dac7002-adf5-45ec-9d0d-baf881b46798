defmodule Teen.Services.ErrorHandler do
  @moduledoc """
  错误处理和回滚机制

  提供统一的错误处理和事务回滚机制，确保系统的一致性：
  - 支付失败回滚
  - 购买失败回滚
  - 提现失败回滚
  - 系统错误恢复
  - 数据一致性检查
  """

  require Logger
  alias <PERSON><PERSON>rid<PERSON>.Ledger
  alias <PERSON>pridina.Ledger.{BalanceCache, AccountIdentifier}
  alias Teen.Services.NotificationService

  @doc """
  处理支付失败回滚
  """
  def handle_payment_failure(payment_context, error_reason) do
    Logger.error("支付失败需要回滚: #{inspect(error_reason)}")

    case payment_context.type do
      :shop_purchase -> rollback_shop_purchase(payment_context, error_reason)
      :withdrawal -> rollback_withdrawal(payment_context, error_reason)
      :recharge -> rollback_recharge(payment_context, error_reason)
      _ -> {:error, "Unknown payment type for rollback"}
    end
  end

  @doc """
  处理商店购买失败回滚
  """
  def rollback_shop_purchase(purchase_context, error_reason) do
    Cypridina.Repo.transaction(fn ->
      with :ok <- refund_purchase_payment(purchase_context),
           :ok <- cancel_purchase_order(purchase_context),
           :ok <- notify_purchase_failure(purchase_context, error_reason) do
        Logger.info("商店购买回滚成功: #{purchase_context.purchase_id}")
        {:ok, :rollback_success}
      else
        {:error, rollback_error} ->
          Logger.error("商店购买回滚失败: #{inspect(rollback_error)}")
          Cypridina.Repo.rollback({:rollback_failed, rollback_error})
      end
    end)
  end

  @doc """
  处理提现失败回滚
  """
  def rollback_withdrawal(withdrawal_context, error_reason) do
    Cypridina.Repo.transaction(fn ->
      with :ok <- refund_withdrawal_amount(withdrawal_context),
           :ok <- update_withdrawal_status(withdrawal_context, :failed),
           :ok <- notify_withdrawal_failure(withdrawal_context, error_reason) do
        Logger.info("提现回滚成功: #{withdrawal_context.withdrawal_id}")
        {:ok, :rollback_success}
      else
        {:error, rollback_error} ->
          Logger.error("提现回滚失败: #{inspect(rollback_error)}")
          Cypridina.Repo.rollback({:rollback_failed, rollback_error})
      end
    end)
  end

  @doc """
  处理充值失败回滚
  """
  def rollback_recharge(recharge_context, error_reason) do
    Cypridina.Repo.transaction(fn ->
      with :ok <- cancel_recharge_order(recharge_context),
           :ok <- notify_recharge_failure(recharge_context, error_reason) do
        Logger.info("充值回滚成功: #{recharge_context.recharge_id}")
        {:ok, :rollback_success}
      else
        {:error, rollback_error} ->
          Logger.error("充值回滚失败: #{inspect(rollback_error)}")
          Cypridina.Repo.rollback({:rollback_failed, rollback_error})
      end
    end)
  end

  @doc """
  处理系统错误恢复
  """
  def handle_system_error_recovery(error_context) do
    case error_context.error_type do
      :balance_inconsistency -> recover_balance_inconsistency(error_context)
      :transaction_timeout -> recover_transaction_timeout(error_context)
      :network_failure -> recover_network_failure(error_context)
      :database_error -> recover_database_error(error_context)
      _ -> {:error, "Unknown error type for recovery"}
    end
  end

  @doc """
  数据一致性检查
  """
  def check_data_consistency(user_id) do
    with {:ok, ledger_balance} <- get_ledger_balance(user_id),
         {:ok, cache_balance} <- get_cache_balance(user_id),
         :ok <- validate_balance_consistency(ledger_balance, cache_balance),
         {:ok, transaction_integrity} <- check_transaction_integrity(user_id) do
      {:ok,
       %{
         ledger_balance: ledger_balance,
         cache_balance: cache_balance,
         transaction_integrity: transaction_integrity,
         status: :consistent
       }}
    else
      {:error, :balance_inconsistency} ->
        handle_balance_inconsistency(user_id)

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量数据一致性检查
  """
  def batch_consistency_check(user_ids) do
    results =
      Enum.map(user_ids, fn user_id ->
        {user_id, check_data_consistency(user_id)}
      end)

    {consistent, inconsistent} =
      Enum.split_with(results, fn {_user_id, result} ->
        match?({:ok, %{status: :consistent}}, result)
      end)

    if inconsistent != [] do
      Logger.warning("发现数据不一致的用户: #{length(inconsistent)} 个")

      # 自动修复不一致的数据
      Enum.each(inconsistent, fn {user_id, _error} ->
        Task.start(fn -> auto_fix_inconsistency(user_id) end)
      end)
    end

    %{
      total_checked: length(user_ids),
      consistent_count: length(consistent),
      inconsistent_count: length(inconsistent),
      inconsistent_users: Enum.map(inconsistent, fn {user_id, _} -> user_id end)
    }
  end

  @doc """
  自动修复数据不一致
  """
  def auto_fix_inconsistency(user_id) do
    Logger.info("开始自动修复用户 #{user_id} 的数据不一致")

    case check_data_consistency(user_id) do
      {:ok, %{status: :consistent}} ->
        Logger.info("用户 #{user_id} 数据已一致，无需修复")
        {:ok, :already_consistent}

      {:error, :balance_inconsistency} ->
        fix_balance_inconsistency(user_id)

      {:error, reason} ->
        Logger.error("用户 #{user_id} 数据修复失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== 私有函数 ====================

  defp refund_purchase_payment(purchase_context) do
    user_identifier = AccountIdentifier.user(purchase_context.user_id, :XAA)

    case BalanceCache.add_balance(user_identifier, purchase_context.amount) do
      {:ok, _new_balance} ->
        Logger.info("购买失败退款成功: 用户#{purchase_context.user_id}, 金额#{purchase_context.amount}")
        :ok

      {:error, reason} ->
        Logger.error("购买失败退款失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp cancel_purchase_order(purchase_context) do
    # 取消购买订单
    case Teen.ShopSystem.UserPurchase.refund_purchase(purchase_context.purchase_id, %{
           refund_reason: "支付失败自动退款"
         }) do
      {:ok, _} ->
        Logger.info("购买订单取消成功: #{purchase_context.purchase_id}")
        :ok

      {:error, reason} ->
        Logger.error("购买订单取消失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp notify_purchase_failure(purchase_context, error_reason) do
    NotificationService.send_system_message(purchase_context.user_id, %{
      title: "购买失败",
      content: "您的购买订单处理失败，已自动退款。原因: #{inspect(error_reason)}",
      category: :error,
      action_required: false
    })

    :ok
  end

  defp refund_withdrawal_amount(withdrawal_context) do
    user_identifier = AccountIdentifier.user(withdrawal_context.user_id, :XAA)
    withdrawal_identifier = AccountIdentifier.system(:withdrawal_pending, :XAA)

    case Ledger.transfer(withdrawal_identifier, user_identifier, withdrawal_context.amount,
           transaction_type: :refund,
           description: "提现失败退款"
         ) do
      {:ok, _transfer} ->
        Logger.info("提现失败退款成功: 用户#{withdrawal_context.user_id}, 金额#{withdrawal_context.amount}")
        :ok

      {:error, reason} ->
        Logger.error("提现失败退款失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp update_withdrawal_status(withdrawal_context, status) do
    # 更新提现状态
    case Teen.PaymentSystem.WithdrawalRecord.update_status(
           withdrawal_context.withdrawal_id,
           status
         ) do
      {:ok, _} ->
        Logger.info("提现状态更新成功: #{withdrawal_context.withdrawal_id} -> #{status}")
        :ok

      {:error, reason} ->
        Logger.error("提现状态更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp notify_withdrawal_failure(withdrawal_context, error_reason) do
    NotificationService.send_withdrawal_status_notification(withdrawal_context.user_id, %{
      withdrawal_id: withdrawal_context.withdrawal_id,
      status: :failed,
      amount: withdrawal_context.amount,
      fee_amount: withdrawal_context.fee_amount,
      actual_amount: withdrawal_context.actual_amount,
      message: "提现失败: #{inspect(error_reason)}",
      estimated_time: nil
    })

    :ok
  end

  defp cancel_recharge_order(recharge_context) do
    # 取消充值订单
    case Teen.PaymentSystem.RechargeRecord.cancel_order(recharge_context.recharge_id) do
      {:ok, _} ->
        Logger.info("充值订单取消成功: #{recharge_context.recharge_id}")
        :ok

      {:error, reason} ->
        Logger.error("充值订单取消失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp notify_recharge_failure(recharge_context, error_reason) do
    NotificationService.send_system_message(recharge_context.user_id, %{
      title: "充值失败",
      content: "您的充值订单处理失败。原因: #{inspect(error_reason)}",
      category: :error,
      action_required: false
    })

    :ok
  end

  defp recover_balance_inconsistency(error_context) do
    user_id = error_context.user_id

    case fix_balance_inconsistency(user_id) do
      {:ok, _} ->
        Logger.info("余额不一致恢复成功: 用户#{user_id}")
        {:ok, :recovered}

      {:error, reason} ->
        Logger.error("余额不一致恢复失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp recover_transaction_timeout(error_context) do
    # 处理事务超时恢复
    Logger.info("处理事务超时恢复: #{inspect(error_context)}")
    {:ok, :recovered}
  end

  defp recover_network_failure(error_context) do
    # 处理网络故障恢复
    Logger.info("处理网络故障恢复: #{inspect(error_context)}")
    {:ok, :recovered}
  end

  defp recover_database_error(error_context) do
    # 处理数据库错误恢复
    Logger.info("处理数据库错误恢复: #{inspect(error_context)}")
    {:ok, :recovered}
  end

  defp get_ledger_balance(user_id) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)

    case Cypridina.Ledger.Account.get_balance_by_identifier(user_identifier) do
      balance when is_integer(balance) ->
        {:ok, balance}

      _ ->
        {:error, :ledger_balance_not_found}
    end
  end

  defp get_cache_balance(user_id) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)

    case BalanceCache.get_balance(user_identifier) do
      {:ok, balance} ->
        {:ok, balance}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp validate_balance_consistency(ledger_balance, cache_balance) do
    if ledger_balance == cache_balance do
      :ok
    else
      Logger.warning("余额不一致: ledger=#{ledger_balance}, cache=#{cache_balance}")
      {:error, :balance_inconsistency}
    end
  end

  defp check_transaction_integrity(user_id) do
    # 检查交易完整性
    # 这里需要实现具体的交易完整性检查逻辑
    {:ok, %{status: :ok, last_check: DateTime.utc_now()}}
  end

  defp handle_balance_inconsistency(user_id) do
    Logger.warning("处理余额不一致: 用户#{user_id}")

    case fix_balance_inconsistency(user_id) do
      {:ok, fixed_balance} ->
        {:ok,
         %{
           ledger_balance: fixed_balance,
           cache_balance: fixed_balance,
           status: :fixed
         }}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp fix_balance_inconsistency(user_id) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)

    # 以ledger系统的余额为准，更新缓存
    case get_ledger_balance(user_id) do
      {:ok, ledger_balance} ->
        BalanceCache.set_balance(user_identifier, ledger_balance)
        Logger.info("余额不一致修复成功: 用户#{user_id}, 余额#{ledger_balance}")
        {:ok, ledger_balance}

      {:error, reason} ->
        Logger.error("获取ledger余额失败: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
