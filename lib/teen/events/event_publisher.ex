defmodule Teen.Events.EventPublisher do
  @moduledoc """
  优化的统一事件发布器

  提供统一的事件发布接口，支持异步事件处理和错误处理机制。
  整合了所有事件相关功能：
  - 异步事件处理
  - 统一错误处理和重试机制
  - 性能优化的事件队列管理
  - 兼容原有API接口
  """

  use GenServer
  require Logger
  alias Teen.Events.GameEvent
  alias Teen.ActivitySystem.ActivityTaskService
  alias Teen.GameManagement.GameRecord
  alias Teen.VipSystem.VipService

  @pubsub_server Cypridina.PubSub
  @max_retries 3
  @retry_delay 1000
  @task_timeout 30_000

  # GenServer API

  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @impl true
  def init(_opts) do
    Logger.info("🎯 [EVENT_PUBLISHER] 优化的事件发布器启动")
    {:ok, %{processed_events: 0, failed_events: 0}}
  end

  @doc """
  发布用户登录事件
  """
  def publish_user_login(user_id, opts \\ []) do
    event_data = %{
      login_time: DateTime.utc_now(),
      ip_address: Keyword.get(opts, :ip_address),
      device_info: Keyword.get(opts, :device_info, %{})
    }

    event =
      GameEvent.new(user_id, :user_login, event_data,
        source: Keyword.get(opts, :source, "auth_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)
  end

  @doc """
  发布用户充值事件
  """
  def publish_user_recharge(user_id, amount, opts \\ []) do
    event_data = %{
      amount: amount,
      currency: Keyword.get(opts, :currency, "XAA"),
      payment_method: Keyword.get(opts, :payment_method),
      order_id: Keyword.get(opts, :order_id),
      bonus_amount: Keyword.get(opts, :bonus_amount, 0)
    }

    event =
      GameEvent.new(user_id, :user_recharge, event_data,
        source: Keyword.get(opts, :source, "payment_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    # 发布事件到PubSub
    publish_event(event)

    # 同时调度Oban任务进行异步处理
    schedule_subsystem_updates(user_id, :recharge_completed, event_data)
  end

  @doc """
  发布游戏完成事件
  """
  def publish_game_completed(user_id, game_id, result, opts \\ []) do
    event_data = %{
      game_id: game_id,
      game_type: Keyword.get(opts, :game_type),
      result: result,
      is_win: result[:status] == :win || result["status"] == "win",
      bet_amount: result[:bet_amount] || result["bet_amount"],
      win_amount: result[:win_amount] || result["win_amount"] || 0,
      duration: Keyword.get(opts, :duration)
    }

    event =
      GameEvent.new(user_id, :game_completed, event_data,
        source: Keyword.get(opts, :source, "game_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)

    # 如果游戏获胜，同时发布获胜事件
    if event_data.is_win do
      win_event =
        GameEvent.new(user_id, :game_won, event_data,
          source: Keyword.get(opts, :source, "game_system"),
          metadata: Keyword.get(opts, :metadata, %{})
        )

      publish_event(win_event)
    end
  end

  @doc """
  发布用户绑定事件
  """
  def publish_user_binding(user_id, binding_type, opts \\ []) do
    event_data = %{
      # :phone, :email
      binding_type: binding_type,
      binding_value: Keyword.get(opts, :binding_value),
      verified: Keyword.get(opts, :verified, true)
    }

    event =
      GameEvent.new(user_id, :user_binding, event_data,
        source: Keyword.get(opts, :source, "user_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)
  end

  @doc """
  发布用户分享事件
  """
  def publish_user_share(user_id, opts \\ []) do
    event_data = %{
      share_type: Keyword.get(opts, :share_type),
      share_content: Keyword.get(opts, :share_content),
      platform: Keyword.get(opts, :platform)
    }

    event =
      GameEvent.new(user_id, :user_share, event_data,
        source: Keyword.get(opts, :source, "social_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)
  end

  @doc """
  发布用户提现事件
  """
  def publish_user_withdrawal(user_id, amount, opts \\ []) do
    event_data = %{
      amount: amount,
      currency: Keyword.get(opts, :currency, "XAA"),
      withdrawal_method: Keyword.get(opts, :withdrawal_method),
      order_id: Keyword.get(opts, :order_id),
      status: Keyword.get(opts, :status, :pending)
    }

    event =
      GameEvent.new(user_id, :user_withdrawal, event_data,
        source: Keyword.get(opts, :source, "payment_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)
  end

  @doc """
  发布VIP等级提升事件
  """
  def publish_vip_level_up(user_id, old_level, new_level, opts \\ []) do
    event_data = %{
      old_level: old_level,
      new_level: new_level,
      total_recharge: Keyword.get(opts, :total_recharge),
      benefits: Keyword.get(opts, :benefits, [])
    }

    event =
      GameEvent.new(user_id, :vip_level_up, event_data,
        source: Keyword.get(opts, :source, "vip_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)
  end

  @doc """
  发布任务完成事件
  """
  def publish_task_completed(user_id, task_id, task_type, opts \\ []) do
    event_data = %{
      task_id: task_id,
      task_type: task_type,
      reward_amount: Keyword.get(opts, :reward_amount),
      completion_time: DateTime.utc_now()
    }

    event =
      GameEvent.new(user_id, :task_completed, event_data,
        source: Keyword.get(opts, :source, "activity_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)
  end

  @doc """
  发布奖励领取事件
  """
  def publish_reward_claimed(user_id, reward_type, amount, opts \\ []) do
    event_data = %{
      reward_type: reward_type,
      amount: amount,
      currency: Keyword.get(opts, :currency, "coins"),
      source_id: Keyword.get(opts, :source_id),
      claim_time: DateTime.utc_now()
    }

    event =
      GameEvent.new(user_id, :reward_claimed, event_data,
        source: Keyword.get(opts, :source, "reward_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)
  end

  @doc """
  批量发布事件
  """
  def publish_events(events) when is_list(events) do
    Enum.each(events, &publish_event/1)
  end

  @doc """
  发布通用事件（异步处理）
  """
  def publish_event(%GameEvent{} = event) do
    if GameEvent.valid?(event) do
      GenServer.cast(__MODULE__, {:process_event, event, 0})
      {:ok, event}
    else
      Logger.error("🎯 [EVENT_PUBLISHER] 无效事件: #{inspect(event)}")
      {:error, :invalid_event}
    end
  end

  @doc """
  异步发布事件（兼容性API）
  """
  def publish_event_async(%GameEvent{} = event) do
    publish_event(event)
  end

  @doc """
  发布事件并等待确认
  """
  def publish_event_sync(%GameEvent{} = event, timeout \\ 5000) do
    if GameEvent.valid?(event) do
      GenServer.call(__MODULE__, {:process_event_sync, event}, timeout)
    else
      {:error, :invalid_event}
    end
  end

  @doc """
  获取事件发布统计信息
  """
  def get_stats do
    GenServer.call(__MODULE__, :get_stats)
  end

  # ==================== 兼容性处理接口 ====================
  # 保持与原有代码的兼容性

  @doc """
  直接处理用户登录事件（异步处理 + 广播）
  """
  def handle_user_login(user_id, opts \\ []) do
    publish_user_login(user_id, opts)
  end

  @doc """
  直接处理充值完成事件（异步处理 + 广播）
  """
  def handle_recharge_completed(user_id, amount, order_id, payment_method, opts \\ []) do
    publish_user_recharge(
      user_id,
      amount,
      Keyword.merge(opts, order_id: order_id, payment_method: payment_method)
    )
  end

  @doc """
  直接处理游戏完成事件（异步处理 + 广播）
  """
  def handle_game_completed(user_id, game_type, bet_amount, win_amount, result, opts \\ []) do
    publish_game_completed(
      user_id,
      game_type,
      result,
      Keyword.merge(opts, bet_amount: bet_amount, win_amount: win_amount)
    )
  end

  @doc """
  处理下注事件（新增，整合自GameSystem.EventPublisher）
  """
  def handle_game_bet_placed(user_id, game_id, bet_amount, opts \\ []) do
    event_data = %{
      game_id: game_id,
      bet_amount: bet_amount,
      game_type: Keyword.get(opts, :game_type),
      room_id: Keyword.get(opts, :room_id)
    }

    event =
      GameEvent.new(user_id, :game_bet_placed, event_data,
        source: Keyword.get(opts, :source, "game_system"),
        metadata: Keyword.get(opts, :metadata, %{})
      )

    publish_event(event)
  end

  @doc """
  批量发布游戏完成事件（整合自GameSystem.EventPublisher）
  """
  def publish_batch_game_completed(events) when is_list(events) do
    Enum.each(events, fn event_data ->
      user_id = event_data[:player_id] || event_data["player_id"]
      game_id = event_data[:game_round_id] || event_data["game_round_id"]
      result = event_data[:game_result] || event_data["game_result"] || %{}

      opts = [
        game_type: event_data[:game_type] || event_data["game_type"],
        room_id: event_data[:room_id] || event_data["room_id"],
        is_win: event_data[:is_win] || event_data["is_win"]
      ]

      publish_game_completed(user_id, game_id, result, opts)
    end)
  end

  # ==================== GenServer回调函数 ====================

  @impl true
  def handle_cast({:process_event, event, retry_count}, state) do
    task =
      Task.async(fn ->
        process_event_with_retries(event, retry_count)
      end)

    # 不等待任务完成，继续处理其他事件
    Task.shutdown(task, @task_timeout)

    {:noreply, %{state | processed_events: state.processed_events + 1}}
  end

  @impl true
  def handle_call({:process_event_sync, event}, _from, state) do
    result = process_event_with_retries(event, 0)

    new_state =
      case result do
        {:ok, _} -> %{state | processed_events: state.processed_events + 1}
        {:error, _} -> %{state | failed_events: state.failed_events + 1}
      end

    {:reply, result, new_state}
  end

  @impl true
  def handle_call(:get_stats, _from, state) do
    stats = %{
      processed_events: state.processed_events,
      failed_events: state.failed_events,
      status: :running
    }

    {:reply, stats, state}
  end

  # ==================== 异步事件处理函数 ====================

  defp process_event_with_retries(event, retry_count) do
    try do
      # 1. 发布到PubSub
      broadcast_event(event)

      # 2. 直接处理事件逻辑
      process_event_logic(event)

      Logger.debug("🎯 [EVENT_PUBLISHER] 事件处理成功: #{event.event_type} - 用户: #{event.user_id}")
      {:ok, event}
    rescue
      error ->
        Logger.error("🎯 [EVENT_PUBLISHER] 事件处理失败: #{inspect(error)}")

        if retry_count < @max_retries do
          Logger.info(
            "🎯 [EVENT_PUBLISHER] 重试事件处理: #{event.event_type} - 重试次数: #{retry_count + 1}"
          )

          Process.sleep(@retry_delay * (retry_count + 1))
          process_event_with_retries(event, retry_count + 1)
        else
          Logger.error("🎯 [EVENT_PUBLISHER] 事件处理最终失败: #{event.event_type} - 用户: #{event.user_id}")
          {:error, error}
        end
    end
  end

  defp broadcast_event(event) do
    # 发布到用户特定主题
    user_topic = GameEvent.topic(event)
    Phoenix.PubSub.broadcast(@pubsub_server, user_topic, {:game_event, event})

    # 发布到事件类型广播主题
    broadcast_topic = GameEvent.broadcast_topic(event.event_type)
    Phoenix.PubSub.broadcast(@pubsub_server, broadcast_topic, {:game_event, event})
  end

  defp process_event_logic(%GameEvent{event_type: :user_login} = event) do
    process_user_login(event.user_id, event.event_data)
  end

  defp process_event_logic(%GameEvent{event_type: :user_recharge} = event) do
    amount = event.event_data[:amount] || event.event_data["amount"]
    order_id = event.event_data[:order_id] || event.event_data["order_id"]
    payment_method = event.event_data[:payment_method] || event.event_data["payment_method"]
    process_recharge_completed(event.user_id, amount, order_id, payment_method, event.event_data)
  end

  defp process_event_logic(%GameEvent{event_type: :game_completed} = event) do
    game_id = event.event_data[:game_id] || event.event_data["game_id"]
    result = event.event_data[:result] || event.event_data["result"] || event.event_data
    process_game_completed_async(event.user_id, game_id, result, event.event_data)
  end

  defp process_event_logic(%GameEvent{event_type: :game_bet_placed} = event) do
    process_game_bet_placed(event.user_id, event.event_data)
  end

  defp process_event_logic(%GameEvent{event_type: :vip_level_up} = event) do
    process_vip_level_up(event.user_id, event.event_data)
  end

  defp process_event_logic(%GameEvent{event_type: :task_completed} = event) do
    process_task_completed(event.user_id, event.event_data)
  end

  defp process_event_logic(%GameEvent{event_type: :reward_claimed} = event) do
    process_reward_claimed(event.user_id, event.event_data)
  end

  defp process_event_logic(_event) do
    # 未知事件类型，仅记录日志
    :ok
  end

  # ==================== 具体事件处理逻辑 ====================

  defp process_user_login(user_id, event_data) do
    Logger.info("🎯 [EVENT_PROCESSOR] 处理用户登录: #{user_id}")

    # 更新登录相关任务
    ActivityTaskService.update_task_progress(user_id, :daily_login, %{
      login_time: DateTime.utc_now(),
      ip_address: event_data[:ip_address]
    })

    :ok
  end

  defp process_recharge_completed(user_id, amount, order_id, payment_method, event_data) do
    Logger.info("🎯 [EVENT_PROCESSOR] 处理充值完成: 用户#{user_id}, 金额#{amount}, 订单#{order_id}")

    # 更新充值任务进度
    ActivityTaskService.update_task_progress(user_id, :recharge_amount, %{
      amount: amount,
      order_id: order_id,
      payment_method: payment_method
    })

    # 更新VIP经验值
    if amount && amount > 0 do
      VipService.add_experience_from_source(user_id, :recharge, amount)
    end

    :ok
  end

  defp process_game_completed_async(user_id, game_id, result, event_data) do
    Logger.info("🎯 [EVENT_PROCESSOR] 处理游戏完成: 用户#{user_id}, 游戏#{game_id}")

    game_type = event_data[:game_type] || event_data["game_type"]
    is_win = result[:status] == :win || result["status"] == "win" || event_data[:is_win]
    bet_amount = result[:bet_amount] || result["bet_amount"] || event_data[:bet_amount]
    win_amount = result[:win_amount] || result["win_amount"] || event_data[:win_amount] || 0

    # 更新游戏任务进度
    ActivityTaskService.update_task_progress(user_id, :game_completed, %{
      game_id: game_id,
      game_type: game_type,
      is_win: is_win
    })

    # 如果获胜，更新胜利任务进度
    if is_win do
      ActivityTaskService.update_task_progress(user_id, :game_won, %{
        game_id: game_id,
        game_type: game_type,
        win_amount: win_amount
      })
    end

    # 更新VIP经验值
    if bet_amount && bet_amount > 0 do
      VipService.add_experience_from_source(user_id, :game_bet, bet_amount)
    end

    # 创建游戏记录
    create_game_record_async(user_id, game_id, result, event_data)

    :ok
  end

  defp process_game_bet_placed(user_id, event_data) do
    Logger.info("🎯 [EVENT_PROCESSOR] 处理游戏下注: 用户#{user_id}")

    bet_amount = event_data[:bet_amount] || event_data["bet_amount"]

    # 更新VIP经验值
    if bet_amount && bet_amount > 0 do
      VipService.add_experience_from_source(user_id, :game_bet, bet_amount)
    end

    :ok
  end

  defp process_vip_level_up(user_id, event_data) do
    Logger.info("🎯 [EVENT_PROCESSOR] 处理VIP等级提升: 用户#{user_id}")

    # 可以在这里添加VIP等级提升后的处理逻辑
    # 例如发放VIP奖励、通知用户等

    :ok
  end

  defp process_task_completed(user_id, event_data) do
    Logger.info("🎯 [EVENT_PROCESSOR] 处理任务完成: 用户#{user_id}")

    # 可以在这里添加任务完成后的处理逻辑
    # 例如发放奖励、检查其他任务等

    :ok
  end

  defp process_reward_claimed(user_id, event_data) do
    Logger.info("🎯 [EVENT_PROCESSOR] 处理奖励领取: 用户#{user_id}")

    # 可以在这里添加奖励领取后的处理逻辑
    # 例如更新用户余额、记录奖励历史等

    :ok
  end

  defp create_game_record_async(user_id, game_id, result, event_data) do
    bet_amount = result[:bet_amount] || result["bet_amount"] || Decimal.new(0)
    win_amount = result[:win_amount] || result["win_amount"] || Decimal.new(0)
    game_type = event_data[:game_type] || event_data["game_type"] || "unknown"

    # 确定游戏结果状态
    result_status =
      cond do
        result[:status] == :win || result["status"] == "win" -> :win
        bet_amount > 0 and win_amount == 0 -> :lose
        true -> :draw
      end

    # 计算净输赢
    net_amount =
      if is_number(bet_amount) and is_number(win_amount) do
        Decimal.sub(Decimal.new(win_amount), Decimal.new(bet_amount))
      else
        Decimal.sub(win_amount, bet_amount)
      end

    # 准备游戏数据
    game_data =
      Map.merge(result, %{
        net_amount: net_amount,
        room_id: event_data[:room_id] || event_data["room_id"],
        round: event_data[:round] || event_data["round"]
      })

    # 创建游戏记录
    case GameRecord.create(%{
           user_id: user_id,
           game_id: game_id,
           game_type: to_string(game_type),
           bet_amount: bet_amount,
           win_amount: win_amount,
           result_status: result_status,
           game_data: game_data
         }) do
      {:ok, record} ->
        Logger.info("🎯 [EVENT_PUBLISHER] 游戏记录创建成功: 用户#{user_id}, 游戏#{game_id}")
        {:ok, record}

      {:error, reason} ->
        Logger.error("🎯 [EVENT_PUBLISHER] 创建游戏记录失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # ==================== Oban任务调度函数（兼容性） ====================

  @doc """
  调度子系统更新任务（保持兼容性，现已整合到异步事件处理中）
  """
  def schedule_subsystem_updates(user_id, event_type, event_data) do
    Logger.info("🎯 [EVENT_PUBLISHER] 子系统更新已整合到异步事件处理中: 用户=#{user_id}, 事件=#{event_type}")
    
    # 处理充值完成事件
    if event_type == :recharge_completed do
      # 更新幸运值
      case Teen.Services.LuckService.handle_recharge_success(user_id) do
        {:ok, luck_record} ->
          Logger.info("🍀 [EVENT_PUBLISHER] 幸运值更新成功: 用户#{user_id}, 新幸运值#{luck_record.current_luck}")
        
        {:error, reason} ->
          Logger.error("🍀 [EVENT_PUBLISHER] 幸运值更新失败: 用户#{user_id}, 原因#{inspect(reason)}")
      end
    end
    
    :ok
  end

  @doc """
  调度VIP系统更新任务（保持兼容性，现已整合到异步事件处理中）
  """
  def schedule_vip_update(user_id, event_type, event_data) do
    Logger.info("🎯 [EVENT_PUBLISHER] VIP更新已整合到异步事件处理中: 用户=#{user_id}, 事件=#{event_type}")
    :ok
  end

  @doc """
  调度活动系统更新任务（保持兼容性，现已整合到异步事件处理中）
  """
  def schedule_activity_update(user_id, event_type, event_data) do
    Logger.info("🎯 [EVENT_PUBLISHER] 活动更新已整合到异步事件处理中: 用户=#{user_id}, 事件=#{event_type}")
    :ok
  end

  # 获取用户的UUID和numeric_id
  defp get_user_identifiers(user_id) do
    import Ash.Query

    # 判断user_id是UUID还是numeric_id
    cond do
      is_binary(user_id) and String.contains?(user_id, "-") ->
        # 是UUID，查询获取numeric_id
        case Cypridina.Accounts.User |> filter(id == ^user_id) |> limit(1) |> Ash.read_one() do
          {:ok, %{id: uuid, numeric_id: numeric_id}} -> {:ok, {uuid, numeric_id}}
          {:ok, nil} -> {:error, :user_not_found}
          {:error, reason} -> {:error, reason}
        end

      is_integer(user_id) or (is_binary(user_id) and String.match?(user_id, ~r/^\d+$/)) ->
        # 是numeric_id，查询获取UUID
        numeric_id = if is_binary(user_id), do: String.to_integer(user_id), else: user_id

        case Cypridina.Accounts.User
             |> filter(numeric_id == ^numeric_id)
             |> limit(1)
             |> Ash.read_one() do
          {:ok, %{id: uuid, numeric_id: ^numeric_id}} -> {:ok, {uuid, numeric_id}}
          {:ok, nil} -> {:error, :user_not_found}
          {:error, reason} -> {:error, reason}
        end

      true ->
        {:error, :invalid_user_id_format}
    end
  end
end
