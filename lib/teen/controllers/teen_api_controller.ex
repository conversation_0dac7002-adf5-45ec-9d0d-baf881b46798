defmodule CypridinaWeb.TeenApiController do
  use <PERSON><PERSON><PERSON>ina<PERSON><PERSON>, :controller

  # alias <PERSON><PERSON><PERSON><PERSON>.Utils.AES
  # alias <PERSON>pridina.Utils.AES
  # alias <PERSON><PERSON>ridina.Utils.XXTEA
  # alias <PERSON><PERSON><PERSON><PERSON>.Utils.CryptoUtils
  require Logger
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.User
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.LoginService
  alias Teen.GameManagement
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Cypridina.Utils.CryptoUtils

  @doc """
  请求手机验证码API接口
  与 WebSocket handle_request_phone_verification_code 对齐
  """
  def send_sms_code(conn, params) do
    Logger.info("📱 [PHONE_VERIFICATION] 请求手机验证码: #{inspect(params)}")

    # 使用与 WebSocket 相同的参数提取方式
    phone = Map.get(params, "phone", "")
    type = Map.get(params, "type", 2)
    siteid = Map.get(params, "siteid", 1)

    # 获取客户端IP地址用于速率限制（与 WebSocket 对齐）
    ip_address = get_client_ip(conn)

    # 使用与 WebSocket 相同的手机号处理方式
    normalized_phone =
      phone
      |> Cypridina.Utils.StringUtils.normalize_phone()
      |> Cypridina.Utils.StringUtils.add_default_country_code(91)

    # 验证手机号格式（与 WebSocket 对齐）
    cond do
      String.length(normalized_phone) == 0 ->
        Logger.warning("📱 [PHONE_VERIFICATION] 手机号为空")

        json(conn, %{
          code: 1,
          msg: "手机号不能为空",
          phone: normalized_phone
        })

      not Cypridina.Utils.StringUtils.valid_phone?(normalized_phone) ->
        Logger.warning("📱 [PHONE_VERIFICATION] 手机号格式错误: #{normalized_phone}")

        json(conn, %{
          code: 2,
          msg: "手机号格式不正确",
          phone: normalized_phone
        })

      true ->
        # 调用验证码发送服务（与 WebSocket 对齐）
        case Cypridina.Communications.VerificationCode.generate_and_send(%{
               phone_number: normalized_phone,
               code_type: type,
               ip_address: ip_address
             }) do
          {:ok, _verification_code} ->
            Logger.info("📱 [PHONE_VERIFICATION] 验证码发送成功 - 手机号: #{normalized_phone}, 类型: #{type}")

            json(conn, %{
              code: 0,
              msg: "验证码已发送",
              phone: normalized_phone,
              type: type,
              siteid: siteid
            })

          {:error, reason} ->
            # 安全地转换错误信息为字符串（与 WebSocket 对齐）
            reason_str =
              case reason do
                %Ash.Error.Invalid{} -> Exception.message(reason)
                %Ash.Error.Unknown{} -> Exception.message(reason)
                _ -> to_string(reason)
              end

            Logger.error(
              "📱 [PHONE_VERIFICATION] 验证码发送失败 - 手机号: #{normalized_phone}, 原因: #{reason_str}"
            )

            json(conn, %{
              code: 2,
              msg: "验证码发送失败: #{reason_str}",
              phone: normalized_phone,
              type: type,
              siteid: siteid
            })
        end
    end
  end

  @doc """
  用户登录API接口
  统一处理封禁检查，减少重复代码
  """
  def login(conn, params) do
    Logger.info("🔑 [LOGIN_API] 收到登录请求: #{inspect(params)}")

    # 提取登录参数
    account = Map.get(params, "account", "")
    password = Map.get(params, "password", "")
    accounttype = Map.get(params, "accounttype", 1)
    token = Map.get(params, "token", "")
    smscode = Map.get(params, "smscode", "")
    device_id = Map.get(params, "hduc", "")
    channel_id = Map.get(params, "siteid", "501") |> to_string()
    player_id = Map.get(params, "playerid", 0)

    # 统一进行IP和设备封禁检查
    ip_address = get_client_ip(conn)

    with :ok <- check_ip_ban(ip_address),
         :ok <- check_device_ban(device_id) do
      # 根据登录类型处理
      cond do
        # 手机验证码登录
        smscode != "" and account != "" ->
          handle_phone_verification_login(conn, account, smscode, device_id, channel_id, params)

        # Token登录
        token != "" ->
          handle_token_login(conn, token, device_id, params)

        # 游客登录
        account == "" or account == nil ->
          handle_guest_login(conn, device_id, channel_id, params)

        # 用户名密码登录
        account != "" and password != "" ->
          handle_username_login(conn, account, password, device_id, params)

        # 其他情况
        true ->
          send_login_error(conn, "登录参数错误", 4)
      end
    else
      {:error, :ip_banned} ->
        Logger.warning("🚫 [LOGIN_API] IP被封禁，拒绝登录 - IP: #{ip_address}")
        send_login_error(conn, "登录失败: IP地址已被封禁", 2)

      {:error, :device_banned} ->
        Logger.warning("🚫 [LOGIN_API] 设备被封禁，拒绝登录 - 设备ID: #{device_id}")
        send_login_error(conn, "登录失败: 设备已被封禁", 2)
    end
  end

  @doc """
  提供基本信息端点
  """
  def default(conn, data) do
    _xxtea_key = Application.get_env(:cypridina, :teen)[:DataCryptKey]

    # source_data = CryptoUtils.convert_from_hex(data)
    # 先解码十六进制字符串，再进行解密
    # decrypted_data = data |> Base.decode64!()
    # Logger.info("解密数据: #{inspect(decrypted_data |> URI.decode_query())}")
    base_url = Application.get_env(:cypridina, :base_url, "http://localhost:4000")

    encrypted_data =
      %{
        "connectserver" => %{
          1 => %{ip: "localhost", port: 4000, path: "Teen", protocol: "ws"}
        },
        "updateurl" => "",
        # APP版本号
        "installver" => "0",
        "installurl" => "https://download.india-game.com/app/install.apk",
        "weburl" => "https://www.india-game.com/",
        # 大厅版本
        "basever" => "0",
        "headUploadUrl" => "https://upload.india-game.com/avatar/",
        "gamewebapiurl" => "#{base_url}/api/teen/",
        "pmurl" => "https://pay.india-game.com/",
        "publicurl" => "https://public.india-game.com/",
        "downloadurl" => "https://download.india-game.com/",
        "downloadqrcodeurl" => "https://download.india-game.com/qrcode.png",
        "exchangestatus" => 1,
        "chargestatus" => 1,
        "registerstatus" => 1
      }
      |> Jason.encode!()
      # |> AES.encrypt(xxtea_key)
      |> Base.encode64()

    text(conn, encrypted_data)
  end

  @doc """
  Home/GetPayOrderList - 获取充值订单列表

  客户端通过加密的HTTP请求获取用户充值订单记录
  """
  def get_pay_order_list(conn, %{"data" => encrypted_data}) do
    Logger.info("💰 [HOME_API] 收到充值订单查询请求")

    # 解密请求数据
    case decrypt_request_data(encrypted_data) do
      {:ok, params} ->
        Logger.info("💰 [HOME_API] 请求参数: #{inspect(params)}")

        page_index = Map.get(params, "pageIndex", 1)
        page_size = Map.get(params, "pageSize", 50)
        user_id = conn.assigns[:current_user].id

        # 查询充值订单记录
        case Teen.PaymentSystem.PaymentOrder.list_by_user(%{user_id: user_id}) do
          {:ok, orders} ->
            # 只筛选充值类型的订单，并加载关联数据
            all_recharge_orders =
              orders
              |> Enum.filter(fn order -> order.order_type == "recharge" end)

            total_count = length(all_recharge_orders)

            # 分页并加载关联数据
            recharge_orders =
              all_recharge_orders
              |> Enum.slice((page_index - 1) * page_size, page_size)
              |> Ash.load!([:gateway_config])

            # 格式化数据
            formatted_orders = Enum.map(recharge_orders, &format_payment_order_for_client/1)

            Logger.info("💰 [HOME_API] 查询成功，共#{total_count}条记录")

            json(conn, %{
              code: 0,
              msg: "success",
              PageData: formatted_orders,
              TotalCount: total_count,
              PageIndex: page_index,
              PageSize: page_size
            })

          {:error, reason} ->
            Logger.error("💰 [HOME_API] 查询失败: #{inspect(reason)}")
            json(conn, %{code: 1, msg: "查询失败"})
        end

      {:error, reason} ->
        Logger.error("💰 [HOME_API] 解密失败: #{inspect(reason)}")
        json(conn, %{code: 1, msg: "请求数据解密失败"})
    end
  end

  @doc """
  Home/GetMoneyExLogList - 获取提现记录列表

  客户端通过加密的HTTP请求获取用户提现记录
  """
  def get_money_ex_log_list(conn, %{"data" => encrypted_data}) do
    Logger.info("💰 [HOME_API] 收到提现记录查询请求")

    # 解密请求数据
    case decrypt_request_data(encrypted_data) do
      {:ok, params} ->
        Logger.info("💰 [HOME_API] 请求参数: #{inspect(params)}")

        # 提取参数
        # numeric_id = Map.get(params, "userID")
        # user = Cypridina.Accounts.User.get_by_numeric_id(%{numeric_id: numeric_id})
        # user_id = user.id
        user_id = conn.assigns[:current_user].id

        page_index = Map.get(params, "pageIndex", 1)

        page_size = Map.get(params, "pageSize", 50)

        # 验证用户ID
        # 查询提现记录
        case Teen.PaymentSystem.WithdrawalRecord.list_user_records(%{
               user_id: user_id,
               page: page_index,
               page_size: page_size
             }) do
          {:ok, records} ->
            # 计算总数（如果需要的话）
            total_count = length(records)

            # 格式化数据
            formatted_records = Enum.map(records, &format_withdrawal_record_for_client/1)

            Logger.info("💰 [HOME_API] 查询成功，共#{total_count}条记录")

            json(conn, %{
              code: 0,
              msg: "success",
              PageData: formatted_records,
              TotalCount: total_count,
              PageIndex: page_index,
              PageSize: page_size
            })

          {:error, reason} ->
            Logger.error("💰 [HOME_API] 查询失败: #{inspect(reason)}")
            json(conn, %{code: 1, msg: "查询失败"})
        end

      {:error, reason} ->
        Logger.error("💰 [HOME_API] 解密失败: #{inspect(reason)}")
        json(conn, %{code: 1, msg: "请求数据解密失败"})
    end
  end

  # 处理Token登录（IP和设备封禁已在login函数中检查）
  defp handle_token_login(conn, token, device_id, params) do
    Logger.info("🔑 [TOKEN_LOGIN] 使用Token登录")

    ip_address = get_client_ip(conn)

    with {:ok, %{"sub" => sub, "tenant" => tenant} = claims, _} <-
           AshAuthentication.Jwt.verify(token, User),
         :ok <-
           Logger.info("🔑 [TOKEN_LOGIN] Token验证成功 - Sub: #{sub}, Tenant: #{inspect(claims)}"),
         {:ok, user} <- AshAuthentication.subject_to_user(sub, User, tenant: tenant),
         :ok <- check_user_ban(user.id) do
      Logger.info("🔑 [TOKEN_LOGIN] 找到用户 - 用户ID: #{user.id}, 用户名: #{user.username}")

      # 处理登录逻辑
      case LoginService.handle_user_login(user.id, device_id, ip_address) do
        {:ok, updated_user} ->
          send_login_success(conn, updated_user, 3, params, token)

        {:error, reason} ->
          Logger.error("🔑 [TOKEN_LOGIN] 登录处理失败: #{inspect(reason)}")
          send_login_error(conn, "登录失败", 4)
      end
    else
      {:error, :user_banned} ->
        Logger.warning("🚫 [TOKEN_LOGIN] 用户被封禁，拒绝登录")
        send_login_error(conn, "登录失败: 账号已被封禁", 2)

      :error ->
        Logger.error("🔑 [TOKEN_LOGIN] Token验证失败")
        send_login_error(conn, "Token验证失败", 4)

      {:error, reason} ->
        Logger.error("🔑 [TOKEN_LOGIN] Token验证失败, 原因: #{inspect(reason)}")
        send_login_error(conn, "Token验证失败", 4)
    end
  end

  # 处理游客登录（IP和设备封禁已在login函数中检查）
  defp handle_guest_login(conn, device_id, channel_id, params) do
    Logger.info("🎮 [GUEST_LOGIN] 游客登录")

    ip_address = get_client_ip(conn)

    # 先尝试通过设备ID查找现有用户
    case Cypridina.Accounts.UserDevice.get_by_device_id(device_id) do
      {:ok, device} ->
        Logger.info("🎮 [GUEST_LOGIN] 找到现有游客用户: #{device.user_id}")
        # 检查用户封禁状态
        case check_user_ban(device.user_id) do
          :ok ->
            # 更新登录信息
            Cypridina.Accounts.UserDevice.update_login_info(device, %{login_ip: ip_address})
            # 获取用户
            case Ash.get(Cypridina.Accounts.User, device.user_id) do
              {:ok, user} ->
                send_login_success(conn, user, 1, params)

              {:error, reason} ->
                Logger.error("🎮 [GUEST_LOGIN] 获取用户失败: #{inspect(reason)}")
                send_login_error(conn, "获取用户失败", 4)
            end

          {:error, reason} ->
            {:error, reason}
        end

      {:error, _} ->
        # 创建新的游客用户
        Logger.info("🎮 [GUEST_LOGIN] 创建新游客用户")

        case User.create_guest_user(%{}, tenant: channel_id) do
          {:ok, user} ->
            # 记录设备信息
            case Cypridina.Accounts.UserDevice.record_login(%{
                   user_id: user.id,
                   device_id: device_id,
                   login_ip: get_client_ip(conn)
                 }) do
              {:ok, _device} ->
                send_login_success(conn, user, 1, params)

              {:error, reason} ->
                Logger.error("🎮 [GUEST_LOGIN] 设备记录失败: #{inspect(reason)}")
                send_login_success(conn, user, 1, params)
            end

          {:error, reason} ->
            Logger.error("🎮 [GUEST_LOGIN] 创建游客用户失败: #{inspect(reason)}")
            send_login_error(conn, "创建用户失败", 4)
        end
    end
  end

  # 处理手机验证码登录（IP和设备封禁已在login函数中检查）
  defp handle_phone_verification_login(conn, phone, smscode, device_id, channel_id, params) do
    Logger.info("📱 [PHONE_LOGIN] 手机验证码登录: #{phone}")

    ip_address = get_client_ip(conn)

    # 标准化手机号格式
    normalized_phone = normalize_phone_number(phone)

    # 验证验证码
    case Cypridina.Communications.VerificationCode.verify_code(%{
           phone_number: normalized_phone,
           code: smscode,
           # 登录验证码类型
           code_type: 2
         }) do
      {:ok, _verification} ->
        Logger.info("📱 [PHONE_LOGIN] 验证码验证成功 - 手机号: #{normalized_phone}")

        # 查找或创建用户
        case find_or_create_phone_user(
               normalized_phone,
               channel_id,
               smscode,
               device_id,
               ip_address
             ) do
          {:ok, user} ->
            Logger.info("📱 [PHONE_LOGIN] 手机登录成功 - 用户ID: #{user.id}, 手机号: #{normalized_phone}")
            send_login_success(conn, user, 7, params)

          {:error, :user_banned} ->
            Logger.warning("🚫 [PHONE_LOGIN] 用户被封禁，拒绝登录 - 手机号: #{normalized_phone}")
            send_login_error(conn, "登录失败: 账号已被封禁", 2)

          {:error, reason} ->
            Logger.error(
              "📱 [PHONE_LOGIN] 创建用户失败 - 手机号: #{normalized_phone}, 原因: #{inspect(reason)}"
            )

            send_login_error(conn, "创建用户失败", 4)
        end

      {:error, reason} ->
        Logger.error("📱 [PHONE_LOGIN] 验证码验证失败 - 手机号: #{normalized_phone}, 原因: #{inspect(reason)}")
        send_login_error(conn, "验证码验证失败", 2)
    end
  end

  # 处理用户名密码登录（IP和设备封禁已在login函数中检查）
  defp handle_username_login(conn, username, password, device_id, params) do
    Logger.info("👤 [USERNAME_LOGIN] 用户名登录: #{username}")

    ip_address = get_client_ip(conn)

    case AshAuthentication.authenticate(User, :username, %{
           username: username,
           password: password
         }) do
      {:ok, user} ->
        Logger.info("👤 [USERNAME_LOGIN] 认证成功: #{user.id}")

        case check_user_ban(user.id) do
          :ok ->
            case LoginService.handle_user_login(user.id, device_id, ip_address) do
              {:ok, updated_user} ->
                send_login_success(conn, updated_user, 2, params)

              {:error, reason} ->
                Logger.error("👤 [USERNAME_LOGIN] 登录处理失败: #{inspect(reason)}")
                send_login_error(conn, "登录失败", 4)
            end

          {:error, :user_banned} ->
            Logger.warning("🚫 [USERNAME_LOGIN] 用户被封禁，拒绝登录 - 用户ID: #{user.id}")
            send_login_error(conn, "登录失败: 账号已被封禁", 2)
        end

      {:error, reason} ->
        Logger.error("👤 [USERNAME_LOGIN] 认证失败: #{inspect(reason)}")
        send_login_error(conn, "用户名或密码错误", 2)
    end
  end

  # 发送登录成功响应（与 WebSocket build_login_success_response 对齐）
  defp send_login_success(conn, user, accounttype, params, token \\ nil) do
    # 从用户对象获取信息
    player_id = user.numeric_id
    user_id = user.id
    username = user.username

    # 加载用户profile信息
    user_with_profile =
      user
      |> Ash.load!([:profile])

    final_token =
      case AshAuthentication.Jwt.token_for_user(user, %{"tenant" => nil}) do
        {:ok, new_token, claims} ->
          Logger.info("生成token #{inspect(claims)}")

          new_token

        {:error, _} ->
          ""
      end

    Logger.info(
      "🚀 [LOGIN_SUCCESS] 登录成功 - 用户ID: #{user_id}, 用户名: #{username}, PlayerID: #{player_id}"
    )

    profile = user_with_profile.profile || %{}
    isbindaccount = if user.phone != nil && user.email != nil, do: 1, else: 0
    # 获取用户金币余额
    user_money = Cypridina.Accounts.get_user_points(user_id) || 0
    nickname = profile.nickname || username || "游客#{player_id}"
    headid = profile.head_id || 0

    # 构建完整的登录响应数据，符合Protocol.ts SC_LOGIN_P的格式
    response = %{
      # 必需的顶层字段
      # 登录成功码
      code: 0,
      # 空消息表示成功
      msg: "",
      # 玩家ID
      playerid: player_id,
      # 昵称（顶层必需）
      nickname: nickname,
      # 游戏币余额（顶层必需）
      money: user_money,
      # 钱包余额（顶层必需）
      walletmoney: 0,
      # 头像ID（顶层必需）
      headid: headid,
      firstlogin: if(user.inserted_at == user.updated_at, do: 1, else: 0),
      # 注册赠送金额
      regsendmoney: 0,
      # 是否绑定账号å
      isbindaccount: isbindaccount,
      Function: %{
        # BaseInfo 功能数据
        "6" => %{
          nickname: nickname,
          headid: headid,
          headframeid: 1,
          phoneval: user.phone,
          email: user.email,
          sex:
            case profile.gender do
              :male -> 1
              :female -> 2
              _ -> 1
            end,
          money: user_money,
          walletmoney: 0,
          GameWinAmount: 0,
          AduitStatus: 0,
          CustomHeadUrl: profile.avatar_url || "",
          wxheadurl: ""
        },
        # Money 功能数据
        "7" => %{
          walletmoney: 0,
          money: user_money,
          winningmoney: 0,
          bonusmoney: 0,
          BankName: "",
          BankAccountNum: "",
          BankAccountName: "",
          VipLevel: 0,
          VipExp: 0
        },
        # AccountInfo 功能数据
        "33" => %{
          account: username,
          phone: user.phone || "",
          email: user.email || ""
        }
      },
      loginparam: %{
        account: if(accounttype == 3, do: "#{player_id}", else: username),
        userToken: final_token,
        password: "",
        accounttype: accounttype,
        openid: "",
        nickname: "",
        headimgurl: "",
        city: "",
        sex: 1
      },

      # 游戏房间列表 - 从配置数据动态生成
      gamelist: get_dynamic_gamelist(),
      # 站点游戏列表 - 从配置数据动态生成
      sitegamelist1: get_dynamic_sitegamelist(),

      # 其他配置信息
      agentextips: "",
      bankextips: "",
      regsendmoney: 0,
      officalwebdisplay: 1,
      IsShowCode: 0,
      promotion: 0,
      styleid: 1,
      Url: ""
    }

    json(conn, response)
  end

  # 发送登录错误响应
  defp send_login_error(conn, message, code) do
    response = %{
      code: code,
      msg: message
    }

    json(conn, response)
  end

  # 标准化手机号格式（使用 StringUtils 统一处理）
  defp normalize_phone_number(phone) do
    phone
    |> Cypridina.Utils.StringUtils.normalize_phone()
    |> Cypridina.Utils.StringUtils.add_default_country_code(91)
  end

  # 查找或创建手机用户
  defp find_or_create_phone_user(phone, channel_id, sms_code, device_id, ip_address) do
    case User.get_by_phone(phone) do
      {:ok, user} ->
        Logger.info("📱 [PHONE_USER] 找到现有用户 - 手机号: #{phone}, 用户ID: #{user.id}")

        # 检查用户封禁状态
        case check_user_ban(user.id) do
          :ok ->
            case LoginService.handle_user_login(user.id, device_id, ip_address) do
              {:ok, updated_user} ->
                {:ok, updated_user}

              {:error, reason} ->
                {:error, reason}
            end

          {:error, :user_banned} ->
            Logger.warning("🚫 [PHONE_USER] 用户被封禁 - 手机号: #{phone}, 用户ID: #{user.id}")
            {:error, :user_banned}
        end

      {:error, _} ->
        # 创建新用户并绑定手机号
        Logger.info("📱 [PHONE_USER] 创建新用户 - 手机号: #{phone}")

        random_password = :crypto.strong_rand_bytes(8) |> Base.encode64() |> binary_part(0, 8)

        case User.register_with_phone(
               %{
                 phone: phone,
                 verification_code: sms_code,
                 # 默认密码，可以根据需要修改
                 password: random_password,
                 password_confirmation: random_password
               },
               tenant: channel_id
             ) do
          {:ok, user} ->
            Cypridina.Accounts.UserDevice.record_login(%{
              user_id: user.id,
              device_id: device_id,
              login_ip: ip_address
            })

            Logger.info("📱 [PHONE_USER] 用户创建成功 - 手机号: #{phone}, 用户ID: #{user.id}")
            {:ok, user}

          {:error, reason} ->
            Logger.error("📱 [PHONE_USER] 用户创建失败 - 手机号: #{phone}, 原因: #{inspect(reason)}")
            {:error, reason}
        end
    end
  end

  # 获取客户端IP
  defp get_client_ip(conn) do
    case Plug.Conn.get_req_header(conn, "x-forwarded-for") do
      [ip | _] -> ip
      [] -> to_string(:inet_parse.ntoa(conn.remote_ip))
    end
  end

  # ==================== 封禁检查功能 ====================

  # 检查用户封禁状态
  defp check_user_ban(user_id) do
    case Teen.BanSystem.is_user_banned?(user_id) do
      {:ok, false} ->
        :ok

      {:ok, true, ban_info} ->
        Logger.warning("🚫 [BAN_CHECK] 用户被封禁 - 用户ID: #{user_id}, 封禁信息: #{inspect(ban_info)}")
        {:error, :user_banned}

      {:error, reason} ->
        Logger.error("🚫 [BAN_CHECK] 检查用户封禁状态失败 - 用户ID: #{user_id}, 原因: #{inspect(reason)}")
        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  # 检查IP封禁状态
  defp check_ip_ban(ip_address) do
    case query_ip_ban(ip_address) do
      {:ok, false} ->
        :ok

      {:ok, true} ->
        Logger.warning("🚫 [BAN_CHECK] IP被封禁 - IP: #{ip_address}")
        {:error, :ip_banned}

      {:error, reason} ->
        Logger.error("🚫 [BAN_CHECK] 检查IP封禁状态失败 - IP: #{ip_address}, 原因: #{inspect(reason)}")
        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  # 检查设备封禁状态
  defp check_device_ban(device_id) do
    case query_device_ban(device_id) do
      {:ok, false} ->
        :ok

      {:ok, true} ->
        Logger.warning("🚫 [BAN_CHECK] 设备被封禁 - 设备ID: #{device_id}")
        {:error, :device_banned}

      {:error, reason} ->
        Logger.error("🚫 [BAN_CHECK] 检查设备封禁状态失败 - 设备ID: #{device_id}, 原因: #{inspect(reason)}")
        # 如果检查失败，为了安全起见，允许登录但记录错误
        :ok
    end
  end

  # 查询IP封禁记录
  defp query_ip_ban(ip_address) do
    try do
      case Teen.BanSystem.UserBan.list_active_bans() do
        {:ok, bans} ->
          ip_banned =
            Enum.any?(bans, fn ban ->
              ban.ban_type == 3 and ban.ip_address == ip_address
            end)

          {:ok, ip_banned}

        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("🚫 [BAN_QUERY] 查询IP封禁记录异常 - IP: #{ip_address}, 错误: #{inspect(error)}")
        {:error, error}
    end
  end

  # 查询设备封禁记录
  defp query_device_ban(device_id) do
    try do
      case Teen.BanSystem.UserBan.list_active_bans() do
        {:ok, bans} ->
          device_banned =
            Enum.any?(bans, fn ban ->
              ban.ban_type == 2 and ban.device_id == device_id
            end)

          {:ok, device_banned}

        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error ->
        Logger.error("🚫 [BAN_QUERY] 查询设备封禁记录异常 - 设备ID: #{device_id}, 错误: #{inspect(error)}")
        {:error, error}
    end
  end

  # 获取动态游戏房间列表
  defp get_dynamic_gamelist() do
    try do
      Teen.GameManagement.get_enabled_rooms()
    rescue
      error ->
        Logger.error("获取动态游戏房间列表失败: #{inspect(error)}")
        # 返回空列表作为fallback
        %{}
    end
  end

  # 获取动态站点游戏列表
  defp get_dynamic_sitegamelist() do
    try do
      Teen.GameManagement.get_enabled_games()
    rescue
      error ->
        Logger.error("获取动态站点游戏列表失败: #{inspect(error)}")
        # 返回空列表作为fallback
        %{}
    end
  end

  # ==================== Home API 辅助函数 ====================
  # 解密请求数据
  defp decrypt_request_data(encrypted_hex_data) do
    try do
      # 先尝试作为简单的Base64解码（用于测试）
      case Base.decode64(encrypted_hex_data) do
        {:ok, decoded_data} ->
          case Jason.decode(decoded_data) do
            {:ok, params} ->
              Logger.info("💰 [HOME_API] 使用Base64解码成功")
              {:ok, params}

            {:error, _} ->
              # 如果Base64解码后JSON解析失败，尝试XXTEA解密
              decrypt_with_xxtea(encrypted_hex_data)
          end

        :error ->
          # 如果Base64解码失败，尝试XXTEA解密
          decrypt_with_xxtea(encrypted_hex_data)
      end
    rescue
      e -> {:error, "解密异常: #{inspect(e)}"}
    end
  end

  # 使用XXTEA解密
  defp decrypt_with_xxtea(encrypted_hex_data) do
    try do
      # 获取加密密钥
      crypto_key = get_crypto_key()

      # 十六进制解码
      encrypted_data = CryptoUtils.convert_from_hex(encrypted_hex_data)

      # XXTEA解密
      decrypted_data = CryptoUtils.decrypt_xxtea(encrypted_data, crypto_key)

      # JSON解析
      case Jason.decode(decrypted_data) do
        {:ok, params} ->
          Logger.info("💰 [HOME_API] 使用XXTEA解密成功")
          {:ok, params}

        {:error, reason} ->
          {:error, "JSON解析失败: #{inspect(reason)}"}
      end
    rescue
      e -> {:error, "XXTEA解密失败: #{inspect(e)}"}
    end
  end

  # 格式化充值订单给客户端
  defp format_payment_order_for_client(order) do
    %{
      "Id" => order.id,
      "OrderCode" => order.order_id,
      "Amount" => Decimal.to_integer(order.amount),
      "ActualAmount" => Decimal.to_integer(order.actual_amount),
      "FeeAmount" => Decimal.to_integer(order.fee_amount),
      "Status" => map_payment_status(order.status),
      "PaymentMethod" => order.payment_method || "",
      "GatewayName" => get_gateway_name(order),
      "CreateTime" => format_datetime_for_client(order.inserted_at),
      "CompleteTime" => format_datetime_for_client(order.completed_at),
      "PaymentUrl" => order.payment_url || "",
      "Remark" => order.error_message || ""
    }
  end

  # 格式化提现记录给客户端
  defp format_withdrawal_record_for_client(record) do
    %{
      "Id" => record.id,
      "OrderCode" => record.order_id,
      "Amount" => Decimal.to_integer(record.withdrawal_amount),
      "ActualAmount" => Decimal.to_integer(record.actual_amount),
      "FeeAmount" => Decimal.to_integer(record.fee_amount),
      "TFStatus" => record.progress_status,
      "AduitStatus" => record.audit_status,
      "PaymentMethod" => record.payment_method,
      "CreateTime" => format_datetime_for_client(record.inserted_at),
      "FinishTime" => format_datetime_for_client(record.completed_time),
      "BankName" => extract_bank_name(record.bank_info),
      "BankAccountName" => extract_bank_account_name(record.bank_info),
      "BankAccountNum" => extract_bank_account_number(record.bank_info),
      "Feedback" => record.feedback
    }
  end

  # 格式化日期时间给客户端
  defp format_datetime_for_client(nil), do: nil

  defp format_datetime_for_client(datetime) do
    DateTime.to_iso8601(datetime)
  end

  # 从银行信息JSON中提取银行名称
  defp extract_bank_name(bank_info) do
    case parse_bank_info(bank_info) do
      {:ok, info} -> Map.get(info, "bankName", "")
      _ -> ""
    end
  end

  # 从银行信息JSON中提取账户名称
  defp extract_bank_account_name(bank_info) do
    case parse_bank_info(bank_info) do
      {:ok, info} -> Map.get(info, "accountName", "")
      _ -> ""
    end
  end

  # 从银行信息JSON中提取账户号码
  defp extract_bank_account_number(bank_info) do
    case parse_bank_info(bank_info) do
      {:ok, info} -> Map.get(info, "accountNumber", "")
      _ -> ""
    end
  end

  # 解析银行信息JSON
  defp parse_bank_info(nil), do: {:error, "空信息"}
  defp parse_bank_info(""), do: {:error, "空信息"}

  defp parse_bank_info(bank_info) when is_binary(bank_info) do
    case Jason.decode(bank_info) do
      {:ok, info} -> {:ok, info}
      {:error, _} -> {:error, "JSON解析失败"}
    end
  end

  defp parse_bank_info(_), do: {:error, "无效格式"}

  # 获取加密密钥
  defp get_crypto_key do
    # 从配置中获取密钥，与客户端的DataCryptKey保持一致
    Application.get_env(:cypridina, :teen)[:DataCryptKey] || "12sldakj~@!#!@ew"
  end

  # 映射支付状态到客户端期望的数值
  defp map_payment_status(status) do
    case status do
      # 待支付
      "pending" -> 0
      # 处理中
      "processing" -> 1
      # 已完成
      "completed" -> 2
      # 失败
      "failed" -> 3
      # 已取消
      "cancelled" -> 4
      _ -> 0
    end
  end

  # 获取网关名称
  defp get_gateway_name(order) do
    # 如果order已经加载了gateway_config关系
    case Map.get(order, :gateway_config) do
      nil -> ""
      gateway_config -> gateway_config.gateway_name || ""
    end
  end
end
