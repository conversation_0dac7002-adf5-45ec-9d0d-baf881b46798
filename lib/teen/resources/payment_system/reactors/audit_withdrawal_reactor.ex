defmodule Teen.PaymentSystem.AuditWithdrawalReactor do
  @moduledoc """
  处理提现审核的Reactor

  支持两种审核操作：
  1. 审核通过：保持积分在待审核账户，准备进入支付流程
  2. 审核拒绝：将积分从待审核账户退回用户账户

  补偿机制：
  - 审核拒绝时如果退款失败，记录异常并通知管理员
  - 确保审核状态和积分流转的一致性
  """
  use Ash.Reactor

  require Logger

  # 输入参数
  input :withdrawal_id
  input :auditor_id
  # :approve 或 :reject
  input :action
  # 拒绝时的原因说明，默认为 nil
  input :feedback

  # 步骤1: 验证提现记录和审核权限
  step :validate_audit_request do
    argument :withdrawal_id, input(:withdrawal_id)
    argument :auditor_id, input(:auditor_id)
    argument :action, input(:action)

    run fn %{withdrawal_id: withdrawal_id, auditor_id: auditor_id, action: action}, _context ->
      Logger.info("💸 [AUDIT_WITHDRAWAL] 验证审核请求 - ID: #{withdrawal_id}, 操作: #{action}")

      with {:ok, withdrawal} <- Ash.get(Teen.PaymentSystem.WithdrawalRecord, withdrawal_id),
           :ok <- validate_withdrawal_status(withdrawal),
           {:ok, auditor} <- validate_auditor_permission(auditor_id),
           :ok <- validate_action(action) do
        Logger.info("💸 [AUDIT_WITHDRAWAL] 审核验证通过")
        {:ok, %{withdrawal: withdrawal, auditor: auditor}}
      else
        {:error, reason} ->
          Logger.error("💸 [AUDIT_WITHDRAWAL] 审核验证失败: #{inspect(reason)}")
          {:error, reason}
      end
    end
  end

  # 步骤2: 根据审核决定分流处理
  step :route_audit_action do
    argument :action, input(:action)
    argument :validation_result, result(:validate_audit_request)

    run fn %{action: action, validation_result: validation_result}, _context ->
      Logger.info("💸 [AUDIT_WITHDRAWAL] 路由审核操作: #{action}")

      case action do
        :approve ->
          {:ok, %{route: :approve, data: validation_result}}

        :reject ->
          {:ok, %{route: :reject, data: validation_result}}

        _ ->
          {:error, "无效的审核操作: #{action}"}
      end
    end
  end

  # 步骤3A: 处理审核通过
  step :handle_approval do
    argument :route_result, result(:route_audit_action)
    argument :auditor_id, input(:auditor_id)

    run fn %{
             route_result: %{route: route, data: %{withdrawal: withdrawal}},
             auditor_id: auditor_id
           },
           _context ->
      if route == :approve do
        Logger.info("💸 [AUDIT_WITHDRAWAL] 处理审核通过 - 订单: #{withdrawal.order_id}")

        case Teen.PaymentSystem.WithdrawalRecord.approve_withdrawal(
               withdrawal,
               %{auditor_id: auditor_id}
             ) do
          {:ok, updated_withdrawal} ->
            Logger.info("💸 [AUDIT_WITHDRAWAL] 审核通过状态更新成功")
            {:ok, %{withdrawal: updated_withdrawal, action: :approved}}

          {:error, reason} ->
            Logger.error("💸 [AUDIT_WITHDRAWAL] 审核通过状态更新失败: #{inspect(reason)}")
            {:error, reason}
        end
      else
        {:ok, :skip}
      end
    end
  end

  # 步骤3B: 处理审核拒绝
  step :handle_rejection do
    argument :route_result, result(:route_audit_action)
    argument :auditor_id, input(:auditor_id)
    argument :feedback, input(:feedback)

    run fn args, _context ->
      if args.route_result.route == :reject do
        withdrawal = args.route_result.data.withdrawal
        Logger.info("💸 [AUDIT_WITHDRAWAL] 处理审核拒绝 - 订单: #{withdrawal.order_id}")

        # 首先更新状态
        case Teen.PaymentSystem.WithdrawalRecord.reject_withdrawal(
               withdrawal,
               %{
                 auditor_id: args.auditor_id,
                 feedback: args.feedback || "管理员拒绝"
               }
             ) do
          {:ok, updated_withdrawal} ->
            Logger.info("💸 [AUDIT_WITHDRAWAL] 拒绝状态更新成功，准备退款")
            {:ok, %{withdrawal: updated_withdrawal, action: :rejected, need_refund: true}}

          {:error, reason} ->
            Logger.error("💸 [AUDIT_WITHDRAWAL] 拒绝状态更新失败: #{inspect(reason)}")
            {:error, reason}
        end
      else
        {:ok, :skip}
      end
    end
  end

  # 步骤4: 处理拒绝后的积分退回
  step :refund_points_on_rejection do
    argument :rejection_result, result(:handle_rejection)

    run fn %{rejection_result: result}, _context ->
      case result do
        %{need_refund: true, withdrawal: withdrawal} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 执行积分退回 - 金额: #{withdrawal.withdrawal_amount}")

          pending_identifier =
            Cypridina.Ledger.AccountIdentifier.system(:withdrawal_pending, :XAA)

          user_identifier = Cypridina.Ledger.AccountIdentifier.user(withdrawal.user_id, :XAA)
          amount = Decimal.to_integer(withdrawal.withdrawal_amount)

          case Cypridina.Ledger.transfer(
                 pending_identifier,
                 user_identifier,
                 amount,
                 transaction_type: :refund,
                 description: "提现审核拒绝退款: #{withdrawal.order_id}",
                 metadata: %{
                   withdrawal_id: withdrawal.id,
                   order_id: withdrawal.order_id,
                   reason: "audit_rejected",
                   feedback: withdrawal.feedback
                 }
               ) do
            {:ok, transfer} ->
              Logger.info("💸 [AUDIT_WITHDRAWAL] 积分退回成功，交易ID: #{transfer.id}")
              {:ok, %{withdrawal: withdrawal, refund_transfer: transfer}}

            {:error, reason} ->
              Logger.error("💸 [AUDIT_WITHDRAWAL] 积分退回失败: #{inspect(reason)}")
              {:error, "积分退回失败: #{inspect(reason)}"}
          end

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 步骤5: 发布审核结果事件
  step :publish_audit_event do
    argument :approval_result, result(:handle_approval)
    argument :refund_result, result(:refund_points_on_rejection)
    argument :payment_result, result(:update_withdrawal_status)

    run fn args, _context ->
      approval_result = args.approval_result
      refund_result = args.refund_result
      payment_result = args.payment_result

      result = approval_result || refund_result

      case result do
        %{withdrawal: withdrawal, action: action} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 发布审核事件: #{action}")

          event_data = %{
            event: :"withdrawal_#{action}",
            withdrawal_id: withdrawal.id,
            order_id: withdrawal.order_id,
            user_id: withdrawal.user_id,
            amount: withdrawal.withdrawal_amount,
            auditor_id: withdrawal.auditor_id,
            audit_time: withdrawal.audit_time,
            action: action
          }

          # 如果是审核通过且有支付结果，添加支付信息
          event_data =
            if action == :approved and payment_result != :skip do
              Map.merge(event_data, %{
                payment_initiated: true,
                payment_status: :processing
              })
            else
              event_data
            end

          # 如果是拒绝，添加反馈信息
          event_data =
            if action == :rejected do
              Map.put(event_data, :feedback, withdrawal.feedback)
            else
              event_data
            end

          Phoenix.PubSub.broadcast(
            Cypridina.PubSub,
            "withdrawal:#{withdrawal.user_id}",
            event_data
          )

          # 同时发布给管理员频道
          Phoenix.PubSub.broadcast(
            Cypridina.PubSub,
            "withdrawal:admin",
            Map.put(event_data, :channel, :admin)
          )

          {:ok, withdrawal}

        _ ->
          {:ok, :no_event}
      end
    end
  end

  # 步骤6: 验证待审核账户余额（仅限审核通过）
  step :verify_pending_balance do
    argument :approval_result, result(:handle_approval)

    run fn %{approval_result: result}, _context ->
      case result do
        %{withdrawal: withdrawal, action: :approved} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 验证待审核账户余额")

          pending_identifier =
            Cypridina.Ledger.AccountIdentifier.system(:withdrawal_pending, :XAA)

          withdrawal_amount = Decimal.to_integer(withdrawal.withdrawal_amount)

          {:ok,
           %{
             withdrawal: withdrawal,
             pending_identifier: pending_identifier,
             withdrawal_amount: withdrawal_amount
           }}

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 步骤7: 选择支付网关（仅限审核通过）
  step :select_gateway do
    argument :balance_info, result(:verify_pending_balance)

    run fn %{balance_info: balance_info}, _context ->
      case balance_info do
        %{withdrawal: withdrawal} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 选择支付网关")

          case auto_select_gateway(withdrawal) do
            {:ok, config} ->
              Logger.info("💸 [AUDIT_WITHDRAWAL] 自动选择网关成功: #{config.gateway_name}")
              {:ok, %{withdrawal: withdrawal, gateway_config: config}}

            {:error, reason} ->
              Logger.error("💸 [AUDIT_WITHDRAWAL] 自动选择网关失败: #{inspect(reason)}")
              {:error, "Failed to auto-select gateway: #{inspect(reason)}"}
          end

        :skip ->
          {:ok, :skip}

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 步骤8: 提交到支付网关（仅限审核通过）
  step :submit_to_gateway do
    argument :gateway_info, result(:select_gateway)

    run fn %{gateway_info: gateway_info}, _context ->
      case gateway_info do
        %{withdrawal: withdrawal, gateway_config: gateway_config} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 提交到支付网关: #{gateway_config.gateway_name}")

          case submit_withdrawal_to_gateway(withdrawal, gateway_config) do
            {:ok, gateway_response} ->
              Logger.info("💸 [AUDIT_WITHDRAWAL] 网关提交成功: #{inspect(gateway_response)}")
              {:ok, %{withdrawal: withdrawal, gateway_response: gateway_response}}

            {:error, reason} ->
              Logger.error("💸 [AUDIT_WITHDRAWAL] 网关提交失败: #{inspect(reason)}")
              {:error, "Gateway submission failed: #{inspect(reason)}"}
          end

        :skip ->
          {:ok, :skip}

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 步骤9: 更新提现记录状态（仅限审核通过且网关成功）
  step :update_withdrawal_status do
    argument :gateway_result, result(:submit_to_gateway)

    run fn %{gateway_result: gateway_result}, _context ->
      case gateway_result do
        %{withdrawal: withdrawal, gateway_response: gateway_response} ->
          Logger.info("💸 [AUDIT_WITHDRAWAL] 更新提现记录状态为处理中")

          case Teen.PaymentSystem.WithdrawalRecord.update_progress(withdrawal, %{
                 # 处理中
                 progress_status: 1
               }) do
            {:ok, updated_withdrawal} ->
              Logger.info("💸 [AUDIT_WITHDRAWAL] 状态更新成功")
              {:ok, %{withdrawal: updated_withdrawal, gateway_response: gateway_response}}

            {:error, reason} ->
              Logger.error("💸 [AUDIT_WITHDRAWAL] 状态更新失败: #{inspect(reason)}")
              {:error, "Failed to update withdrawal status: #{inspect(reason)}"}
          end

        :skip ->
          {:ok, :skip}

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 步骤4B: 处理退款失败的情况
  step :handle_refund_failure do
    argument :refund_result, result(:refund_points_on_rejection)

    run fn %{refund_result: result}, _context ->
      case result do
        {:error, reason} ->
          # 从rejection_result中获取withdrawal信息
          Logger.error("💸 [AUDIT_WITHDRAWAL] 退款失败，发送告警")

          # 发送告警通知
          Phoenix.PubSub.broadcast(
            Cypridina.PubSub,
            "system:alerts",
            %{
              alert: :withdrawal_refund_failed,
              message: "提现拒绝退款失败: #{reason}",
              severity: :high,
              timestamp: DateTime.utc_now()
            }
          )

          {:error, reason}

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 最终返回步骤
  step :return_result do
    argument :approval_result, result(:handle_approval)
    argument :refund_result, result(:refund_points_on_rejection)
    argument :payment_result, result(:update_withdrawal_status)
    argument :event_result, result(:publish_audit_event)

    run fn args, _context ->
      # 优先返回审核通过的结果
      case args.approval_result do
        %{withdrawal: withdrawal, action: :approved} ->
          # 如果有支付结果，包含支付信息
          case args.payment_result do
            %{withdrawal: updated_withdrawal} ->
              {:ok,
               %{
                 withdrawal: updated_withdrawal,
                 action: :approved,
                 payment_initiated: true,
                 payment_status: :processing
               }}

            :skip ->
              {:ok,
               %{
                 withdrawal: withdrawal,
                 action: :approved,
                 payment_initiated: false,
                 payment_status: :pending
               }}

            _ ->
              {:ok,
               %{
                 withdrawal: withdrawal,
                 action: :approved,
                 payment_initiated: false,
                 payment_status: :failed
               }}
          end

        _ ->
          # 返回拒绝结果
          case args.refund_result do
            %{withdrawal: withdrawal} ->
              {:ok,
               %{
                 withdrawal: withdrawal,
                 action: :rejected,
                 refund_completed: true
               }}

            _ ->
              {:error, "Unknown audit result"}
          end
      end
    end
  end

  # 私有辅助函数
  defp validate_withdrawal_status(withdrawal) do
    cond do
      withdrawal.audit_status != 0 ->
        {:error, "提现记录已被审核，当前状态: #{audit_status_text(withdrawal.audit_status)}"}

      withdrawal.result_status != 0 ->
        {:error, "提现已完成处理，不能再审核"}

      true ->
        :ok
    end
  end

  defp validate_auditor_permission(auditor_id) do
    # 验证审核员权限
    case Cypridina.Accounts.User.get_by_id(auditor_id) do
      {:ok, user} ->
        # 使用 permission_level 或 role_name 来判断权限
        if user.permission_level >= 1 or user.role_name in ["超级管理员", "财务", "审核员"] do
          {:ok, user}
        else
          {:error, "用户没有审核权限"}
        end

      {:error, _} ->
        {:error, "审核员不存在"}
    end
  end

  defp validate_action(action) when action in [:approve, :reject], do: :ok
  defp validate_action(action), do: {:error, "无效的审核操作: #{action}"}

  defp audit_status_text(0), do: "待审核"
  defp audit_status_text(1), do: "已通过"
  defp audit_status_text(2), do: "已拒绝"
  defp audit_status_text(_), do: "未知"

  defp auto_select_gateway(withdrawal) do
    # 根据提现方式和金额自动选择网关
    case withdrawal.payment_method do
      "bank_card" ->
        {:ok,
         %{
           gateway_name: "YidunPay",
           merchant_id: "default_merchant",
           callback_url: "http://localhost:4000/api/payment",
           notify_url: "http://localhost:4000/api/payment/notify"
         }}

      "alipay" ->
        {:ok,
         %{
           gateway_name: "SanQiPay",
           merchant_id: "default_merchant",
           callback_url: "http://localhost:4000/api/payment",
           notify_url: "http://localhost:4000/api/payment/notify"
         }}

      "upi" ->
        {:ok,
         %{
           gateway_name: "DuoDuoPay",
           merchant_id: "default_merchant",
           callback_url: "http://localhost:4000/api/payment",
           notify_url: "http://localhost:4000/api/payment/notify"
         }}

      _ ->
        # 默认使用模拟网关
        {:ok,
         %{
           gateway_name: "SimulatedGateway",
           merchant_id: "test_merchant",
           callback_url: "http://localhost:4000/api/payment",
           notify_url: "http://localhost:4000/api/payment/notify"
         }}
    end
  end

  defp submit_withdrawal_to_gateway(withdrawal, gateway_config) do
    params = build_gateway_params(withdrawal, gateway_config)

    # 调用实际的支付网关API
    case call_payment_gateway(gateway_config, params) do
      {:ok, response} ->
        {:ok, response}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp build_gateway_params(withdrawal, gateway_config) do
    # 解析银行信息
    bank_info =
      case Jason.decode(withdrawal.bank_info || "{}") do
        {:ok, info} -> info
        _ -> %{}
      end

    %{
      order_id: withdrawal.order_id,
      amount: Decimal.to_float(withdrawal.withdrawal_amount),
      notify_url: gateway_config.notify_url,
      callback_url: gateway_config.callback_url,
      bank_info: Jason.encode!(bank_info),
      payment_method: withdrawal.payment_method
    }
  end

  defp call_payment_gateway(gateway_config, params) do
    # 根据不同的网关调用相应的API
    case gateway_config.gateway_name do
      "YidunPay" ->
        Teen.PaymentSystem.PaymentService.create_withdrawal_order(params, gateway_config)

      "SanQiPay" ->
        Teen.PaymentSystem.PaymentService.create_withdrawal_order(params, gateway_config)

      "DuoDuoPay" ->
        Teen.PaymentSystem.PaymentService.create_withdrawal_order(params, gateway_config)

      _ ->
        # 开发环境模拟
        simulate_gateway_call(gateway_config, params)
    end
  end

  defp simulate_gateway_call(_gateway_config, params) do
    # 模拟网关响应
    {:ok,
     %{
       status: "processing",
       gateway_order_id: "GW#{System.unique_integer([:positive])}",
       message: "Withdrawal request submitted successfully",
       order_id: params.order_id,
       processing_time: "1-2 business days"
     }}
  end
end
