defmodule Teen.PaymentSystem.UserBankCard do
  @moduledoc """
  用户绑定银行卡资源

  管理用户绑定的银行卡信息
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PaymentSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :user_id, :bank_id, :account_name, :account_number, :status]
  end

  postgres do
    table "user_bank_cards"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :get_user_default_card
    define :enable
    define :disable
  end

  actions do
    defaults [:update, :destroy]

    read :read do
      primary? true
      prepare build(load: [:user, :bank])
    end

    create :create do
      primary? true
      accept [
        :user_id,
        :bank_id,
        :account_name,
        :account_number,
        :is_default,
        :status,
        :verified_at
      ]
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and status == 1)
    end

    read :get_user_default_card do
      argument :user_id, :uuid, allow_nil?: false
      get? true
      filter expr(user_id == ^arg(:user_id) and status == 1 and is_default == true)
    end

    update :set_as_default do
      change set_attribute(:is_default, true)
    end

    update :unset_default do
      change set_attribute(:is_default, false)
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :bank_id, :uuid do
      allow_nil? false
      public? true
      description "银行配置ID"
    end

    attribute :account_name, :string do
      allow_nil? false
      public? true
      description "账户姓名"
      constraints max_length: 100
    end

    attribute :account_number, :string do
      allow_nil? false
      public? true
      description "银行卡号"
      constraints max_length: 50
    end

    attribute :is_default, :boolean do
      allow_nil? false
      public? true
      description "是否为默认银行卡"
      default false
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :verified_at, :utc_datetime do
      allow_nil? true
      public? true
      description "验证时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :bank, Teen.PaymentSystem.BankConfig do
      public? true
      source_attribute :bank_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_user_account, [:user_id, :account_number]
  end
end
