defmodule Teen.PaymentSystem.WithdrawalRecord do
  @moduledoc """
  提现记录资源

  管理用户的提现申请，包括：
  - 提现金额和支付方式
  - 流水验证和审核状态
  - 支付网关集成
  - 处理进度跟踪
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :order_id,
      :user_id,
      :withdrawal_amount,
      :payment_method,
      :audit_status,
      :progress_status,
      :inserted_at
    ]
  end

  postgres do
    table "withdrawal_records"
    repo Cypridina.Repo
  end

  code_interface do
    # define :create
    # define :read
    # define :update
    # define :destroy
    define :get_by_id, action: :read, get_by: :id
    define :list_by_audit_status
    define :list_by_progress_status
    define :list_by_payment_method
    define :list_by_user
    define :list_pending_withdrawals
    define :approve_withdrawal
    define :reject_withdrawal
    define :update_progress
    define :get_by_order_id
    define :create_withdrawal
    define :validate_withdrawal_eligibility
    define :list_user_recent_withdrawals
    define :get_user_withdrawal_stats
    define :list_user_records
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    read :list_by_audit_status do
      argument :audit_status, :integer, allow_nil?: false
      filter expr(audit_status == ^arg(:audit_status))
    end

    read :list_by_progress_status do
      argument :progress_status, :integer, allow_nil?: false
      filter expr(progress_status == ^arg(:progress_status))
    end

    read :list_by_payment_method do
      argument :payment_method, :string, allow_nil?: false
      filter expr(payment_method == ^arg(:payment_method))
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :list_pending_withdrawals do
      filter expr(audit_status == 0 or progress_status == 0)
    end

    read :get_by_order_id do
      argument :order_id, :string, allow_nil?: false
      filter expr(order_id == ^arg(:order_id))
      prepare build(limit: 1)
    end

    update :approve_withdrawal do
      argument :auditor_id, :uuid, allow_nil?: false
      change set_attribute(:audit_status, 1)
      change set_attribute(:auditor_id, arg(:auditor_id))
      change set_attribute(:audit_time, &DateTime.utc_now/0)
    end

    update :reject_withdrawal do
      argument :auditor_id, :uuid, allow_nil?: false
      argument :feedback, :string, allow_nil?: false
      change set_attribute(:audit_status, 2)
      change set_attribute(:auditor_id, arg(:auditor_id))
      change set_attribute(:audit_time, &DateTime.utc_now/0)
      change set_attribute(:feedback, arg(:feedback))
    end

    update :update_progress do
      argument :progress_status, :integer, allow_nil?: false
      change set_attribute(:progress_status, arg(:progress_status))
      change set_attribute(:process_time, &DateTime.utc_now/0)
    end

    create :create_withdrawal do
      accept [
        :user_id,
        :withdrawal_amount,
        :payment_method,
        :bank_info,
        :alipay_info,
        :upi_info,
        :ip_address,
        :current_balance,
        :withdrawable_balance,
        :required_turnover,
        :completed_turnover,
        :fee_amount,
        :tax_amount,
        :actual_amount
      ]

      change fn changeset, _context ->
        # 生成订单号
        order_id = "WD#{DateTime.utc_now() |> DateTime.to_unix(:millisecond)}"

        changeset
        |> Ash.Changeset.change_attribute(:order_id, order_id)
        |> Ash.Changeset.change_attribute(:audit_status, 0)
        |> Ash.Changeset.change_attribute(:progress_status, 0)
        |> Ash.Changeset.change_attribute(:result_status, 0)
      end

      # 验证流水要求
      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_attribute(changeset, :user_id)
        withdrawal_amount = Ash.Changeset.get_attribute(changeset, :withdrawal_amount)

        case validate_turnover_requirement(user_id, withdrawal_amount) do
          :ok -> :ok
          {:error, reason} -> {:error, field: :withdrawal_amount, message: reason}
        end
      end

      # 验证支付方式信息
      validate fn changeset, _context ->
        payment_method = Ash.Changeset.get_attribute(changeset, :payment_method)
        bank_info = Ash.Changeset.get_attribute(changeset, :bank_info)
        alipay_info = Ash.Changeset.get_attribute(changeset, :alipay_info)
        upi_info = Ash.Changeset.get_attribute(changeset, :upi_info)

        case validate_payment_method_info(payment_method, bank_info, alipay_info, upi_info) do
          :ok -> :ok
          {:error, reason} -> {:error, field: :payment_method, message: reason}
        end
      end

      # 验证支付封禁状态
      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_attribute(changeset, :user_id)

        case validate_payment_ban_status(user_id) do
          :ok -> :ok
          {:error, reason} -> {:error, field: :user_id, message: reason}
        end
      end
    end

    read :validate_withdrawal_eligibility do
      argument :user_id, :uuid, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false

      filter expr(user_id == ^arg(:user_id))

      prepare fn query, _context ->
        query
        |> Ash.Query.load([:withdrawal_validation])
      end
    end

    read :list_user_recent_withdrawals do
      argument :user_id, :uuid, allow_nil?: false
      argument :days, :integer, default: 30

      filter expr(user_id == ^arg(:user_id))

      prepare fn query, _context ->
        days = Ash.Query.get_argument(query, :days) || 30
        days_ago = Date.utc_today() |> Date.add(-days)

        query
        |> Ash.Query.filter(expr(inserted_at >= ^days_ago))
        |> Ash.Query.sort(inserted_at: :desc)
      end
    end

    read :get_user_withdrawal_stats do
      argument :user_id, :uuid, allow_nil?: false

      filter expr(user_id == ^arg(:user_id))

      prepare fn query, _context ->
        query
        |> Ash.Query.load([:daily_stats, :monthly_stats])
      end
    end

    read :list_user_records do
      argument :user_id, :uuid, allow_nil?: false
      argument :page, :integer, default: 1
      argument :page_size, :integer, default: 50

      filter expr(user_id == ^arg(:user_id))

      prepare fn query, _context ->
        page = Ash.Query.get_argument(query, :page) || 1
        page_size = Ash.Query.get_argument(query, :page_size) || 50
        offset = (page - 1) * page_size

        query
        |> Ash.Query.sort(inserted_at: :desc)
        |> Ash.Query.limit(page_size)
        |> Ash.Query.offset(offset)
      end
    end
  end

  preparations do
    prepare build(load: [:user])
  end

  attributes do
    uuid_primary_key :id

    attribute :order_id, :string do
      allow_nil? false
      public? true
      description "提现订单编号"
      constraints max_length: 50
    end

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :platform, :string do
      allow_nil? false
      public? true
      description "平台名称"
      default "web"
      constraints max_length: 50
    end

    attribute :withdrawal_amount, :decimal do
      allow_nil? false
      public? true
      description "提现金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :payment_method, :string do
      allow_nil? false
      public? true
      description "支付方式：bank_card-银行卡，alipay-支付宝，upi-UPI"
      constraints max_length: 20
    end

    attribute :current_balance, :decimal do
      allow_nil? false
      public? true
      description "当前余额"
      constraints min: Decimal.new("0")
    end

    attribute :withdrawable_balance, :decimal do
      allow_nil? false
      public? true
      description "可提现余额（扣除流水要求后）"
      constraints min: Decimal.new("0")
    end

    attribute :required_turnover, :decimal do
      allow_nil? false
      public? true
      description "所需流水金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :completed_turnover, :decimal do
      allow_nil? false
      public? true
      description "已完成流水金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :fee_amount, :decimal do
      allow_nil? false
      public? true
      description "手续费金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :tax_amount, :decimal do
      allow_nil? false
      public? true
      description "税收金额"
      default Decimal.new("0")
      constraints min: Decimal.new("0")
    end

    attribute :actual_amount, :decimal do
      allow_nil? false
      public? true
      description "实际到账金额"
      constraints min: Decimal.new("0")
    end

    # 支付信息
    attribute :bank_info, :string do
      allow_nil? true
      public? true
      description "银行卡信息（JSON格式）"
      constraints max_length: 1000
    end

    attribute :alipay_info, :string do
      allow_nil? true
      public? true
      description "支付宝信息（JSON格式）"
      constraints max_length: 500
    end

    attribute :upi_info, :string do
      allow_nil? true
      public? true
      description "UPI信息（JSON格式）"
      constraints max_length: 500
    end

    # 状态字段
    attribute :audit_status, :integer do
      allow_nil? false
      public? true
      description "审核状态：0-待审核，1-审核通过，2-审核拒绝"
      default 0
      constraints min: 0, max: 2
    end

    attribute :progress_status, :integer do
      allow_nil? false
      public? true
      description "处理状态：0-排队中，1-处理中，2-支付成功，3-支付失败，4-人工处理，5-已取消"
      default 0
      constraints min: 0, max: 5
    end

    attribute :result_status, :integer do
      allow_nil? false
      public? true
      description "最终结果：0-处理中，1-成功，2-失败"
      default 0
      constraints min: 0, max: 2
    end

    # 支付网关相关
    attribute :gateway_order_id, :string do
      allow_nil? true
      public? true
      description "支付网关订单ID"
      constraints max_length: 100
    end

    attribute :gateway_response, :string do
      allow_nil? true
      public? true
      description "支付网关响应（JSON格式）"
      constraints max_length: 2000
    end

    # 审核和处理信息
    attribute :feedback, :string do
      allow_nil? true
      public? true
      description "审核反馈或处理说明"
      constraints max_length: 1000
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "用户IP地址"
      constraints max_length: 45
    end

    attribute :auditor_id, :uuid do
      allow_nil? true
      public? true
      description "审核人ID"
    end

    attribute :audit_time, :utc_datetime do
      allow_nil? true
      public? true
      description "审核时间"
    end

    attribute :process_time, :utc_datetime do
      allow_nil? true
      public? true
      description "处理时间"
    end

    attribute :completed_time, :utc_datetime do
      allow_nil? true
      public? true
      description "完成时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :auditor, Cypridina.Accounts.User do
      public? true
      source_attribute :auditor_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :withdrawal_validation, :map do
      public? true
      description "验证提现请求"
      argument :amount, :decimal

      calculation fn records, context ->
        amount = context.arguments[:amount] || Decimal.new(0)

        Enum.map(records, fn record ->
          user_id = record.user_id

          # 验证流水要求
          turnover_validation = validate_turnover_requirement(user_id, amount)

          # 验证支付封禁
          ban_validation = validate_payment_ban_status(user_id)

          # 验证VIP限额
          vip_validation = validate_vip_withdrawal_limits(user_id, amount)

          %{
            turnover_valid: turnover_validation == :ok,
            turnover_message: elem(turnover_validation, 1),
            ban_valid: ban_validation == :ok,
            ban_message: elem(ban_validation, 1),
            vip_valid: vip_validation == :ok,
            vip_message: elem(vip_validation, 1),
            overall_valid:
              turnover_validation == :ok && ban_validation == :ok && vip_validation == :ok
          }
        end)
      end
    end

    calculate :daily_stats, :map do
      public? true
      description "今日提现统计"

      calculation fn records, _ ->
        today = Date.utc_today()

        Enum.map(records, fn _record ->
          today_records =
            Enum.filter(records, fn r ->
              Date.compare(DateTime.to_date(r.inserted_at), today) == :eq
            end)

          %{
            count: length(today_records),
            total_amount:
              Enum.reduce(today_records, Decimal.new(0), fn r, acc ->
                Decimal.add(acc, r.withdrawal_amount)
              end),
            successful_count: Enum.count(today_records, fn r -> r.result_status == 1 end),
            pending_count: Enum.count(today_records, fn r -> r.result_status == 0 end)
          }
        end)
      end
    end

    calculate :monthly_stats, :map do
      public? true
      description "本月提现统计"

      calculation fn records, _ ->
        today = Date.utc_today()
        month_start = Date.beginning_of_month(today)

        Enum.map(records, fn _record ->
          month_records =
            Enum.filter(records, fn r ->
              date = DateTime.to_date(r.inserted_at)
              Date.compare(date, month_start) != :lt
            end)

          %{
            count: length(month_records),
            total_amount:
              Enum.reduce(month_records, Decimal.new(0), fn r, acc ->
                Decimal.add(acc, r.withdrawal_amount)
              end),
            successful_count: Enum.count(month_records, fn r -> r.result_status == 1 end),
            fee_total:
              Enum.reduce(month_records, Decimal.new(0), fn r, acc ->
                Decimal.add(acc, r.fee_amount)
              end)
          }
        end)
      end
    end
  end

  identities do
    identity :unique_order_id, [:order_id]
  end

  # 私有函数用于验证流水要求
  defp validate_turnover_requirement(user_id, withdrawal_amount) do
    case Teen.Services.TurnoverService.validate_withdrawal_eligibility(user_id, withdrawal_amount) do
      {:ok, _info} -> :ok
      {:error, reason} -> {:error, reason}
    end
  end

  # 验证支付方式信息
  defp validate_payment_method_info(payment_method, bank_info, alipay_info, upi_info) do
    case payment_method do
      "bank_card" ->
        if bank_info && String.length(bank_info || "") > 0 do
          :ok
        else
          {:error, "银行卡信息不能为空"}
        end

      "alipay" ->
        if alipay_info && String.length(alipay_info || "") > 0 do
          :ok
        else
          {:error, "支付宝信息不能为空"}
        end

      "upi" ->
        if upi_info && String.length(upi_info || "") > 0 do
          :ok
        else
          {:error, "UPI信息不能为空"}
        end

      _ ->
        {:error, "不支持的支付方式"}
    end
  end

  # 验证支付封禁状态
  defp validate_payment_ban_status(user_id) do
    case Teen.BanSystem.is_user_payment_banned?(user_id) do
      {:ok, false} ->
        :ok

      {:ok, true, ban_info} ->
        require Logger
        Logger.warning("用户被支付封禁，无法提现 - 用户ID: #{user_id}, 封禁信息: #{inspect(ban_info)}")
        {:error, "您已被限制支付功能，无法进行提现操作"}

      {:error, reason} ->
        require Logger
        Logger.error("检查用户支付封禁状态失败 - 用户ID: #{user_id}, 原因: #{inspect(reason)}")
        {:error, "系统繁忙，请稍后再试"}
    end
  end

  # 验证VIP提现限额
  defp validate_vip_withdrawal_limits(user_id, amount) do
    case Teen.VipSystem.UserVipInfo.check_withdrawal_permission(user_id, amount) do
      {:ok, [info | _]} ->
        permission = List.first(info.withdrawal_permission)

        if permission.can_withdraw do
          :ok
        else
          {:error, permission.reason}
        end

      _ ->
        :ok
    end
  end

  @doc """
  获取用户提现统计信息
  """
  def get_user_withdrawal_summary(user_id) do
    with {:ok, recent_withdrawals} <- list_user_recent_withdrawals(%{user_id: user_id}),
         {:ok, stats} <- get_user_withdrawal_stats(%{user_id: user_id}) do
      {:ok,
       %{
         recent_withdrawals: recent_withdrawals,
         daily_stats: List.first(stats).daily_stats,
         monthly_stats: List.first(stats).monthly_stats
       }}
    end
  end
end
