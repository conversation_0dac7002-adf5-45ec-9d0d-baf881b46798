defmodule Teen.ShopSystem.UserPurchase do
  @moduledoc """
  用户购买记录资源

  记录用户购买商品的详细信息，包括：
  - 购买的商品信息
  - 支付状态和金额
  - 商品发放状态
  - 购买时间等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ShopSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :product_name,
      :paid_amount,
      :payment_status,
      :delivery_status,
      :purchased_at
    ]
  end

  postgres do
    table "user_purchases"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_by_product
    define :complete_purchase
    define :deliver_product
    define :refund_purchase
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true
      accept [
        :user_id,
        :product_id,
        :product_name,
        :product_type,
        :product_config,
        :original_price,
        :paid_amount,
        :currency,
        :payment_order_id
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:payment_status, :pending)
        |> Ash.Changeset.change_attribute(:delivery_status, :pending)
        |> Ash.Changeset.change_attribute(:purchased_at, DateTime.utc_now())
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :purchased_at])
    end

    read :list_by_product do
      argument :product_id, :uuid, allow_nil?: false
      filter expr(product_id == ^arg(:product_id))
      prepare build(sort: [desc: :purchased_at])
    end

    update :complete_purchase do
      require_atomic? false
      accept [:transaction_id]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:payment_status, :completed)
        |> Ash.Changeset.change_attribute(:paid_at, DateTime.utc_now())
      end
    end

    update :deliver_product do
      require_atomic? false
      accept [:delivery_data]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:delivery_status, :delivered)
        |> Ash.Changeset.change_attribute(:delivered_at, DateTime.utc_now())
      end
    end

    update :refund_purchase do
      require_atomic? false
      accept [:refund_reason]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:payment_status, :refunded)
        |> Ash.Changeset.change_attribute(:delivery_status, :cancelled)
        |> Ash.Changeset.change_attribute(:refunded_at, DateTime.utc_now())
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :product_id, :uuid do
      allow_nil? false
      public? true
      description "商品ID"
    end

    attribute :product_name, :string do
      allow_nil? false
      public? true
      description "商品名称（购买时快照）"
      constraints max_length: 100
    end

    attribute :product_type, :atom do
      allow_nil? false
      public? true
      description "商品类型"

      constraints one_of: [
                    :monthly_card,
                    :weekly_card,
                    :play_card,
                    :coin_package,
                    :vip_package,
                    :special_item,
                    :recharge_bonus
                  ]
    end

    attribute :product_config, :map do
      allow_nil? false
      public? true
      description "商品配置（购买时快照）"
      default %{}
    end

    attribute :original_price, :decimal do
      allow_nil? false
      public? true
      description "商品原价（分）"
      constraints min: Decimal.new("0")
    end

    attribute :paid_amount, :decimal do
      allow_nil? false
      public? true
      description "实际支付金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :currency, :atom do
      allow_nil? false
      public? true
      description "货币类型"
      constraints one_of: [:inr, :usd, :cny]
      default :inr
    end

    attribute :payment_order_id, :string do
      allow_nil? true
      public? true
      description "支付订单ID"
      constraints max_length: 100
    end

    attribute :transaction_id, :string do
      allow_nil? true
      public? true
      description "交易ID"
      constraints max_length: 100
    end

    attribute :payment_status, :atom do
      allow_nil? false
      public? true
      description "支付状态"
      constraints one_of: [:pending, :completed, :failed, :refunded]
      default :pending
    end

    attribute :delivery_status, :atom do
      allow_nil? false
      public? true
      description "发放状态"
      constraints one_of: [:pending, :delivered, :failed, :cancelled]
      default :pending
    end

    attribute :delivery_data, :map do
      allow_nil? true
      public? true
      description "发放数据"
      default %{}
    end

    attribute :refund_reason, :string do
      allow_nil? true
      public? true
      description "退款原因"
      constraints max_length: 500
    end

    attribute :purchased_at, :utc_datetime do
      allow_nil? false
      public? true
      description "购买时间"
    end

    attribute :paid_at, :utc_datetime do
      allow_nil? true
      public? true
      description "支付完成时间"
    end

    attribute :delivered_at, :utc_datetime do
      allow_nil? true
      public? true
      description "发放完成时间"
    end

    attribute :refunded_at, :utc_datetime do
      allow_nil? true
      public? true
      description "退款时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :product, Teen.ShopSystem.Product do
      public? true
      source_attribute :product_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :discount_amount, :decimal do
      public? true
      description "优惠金额"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          Decimal.sub(record.original_price, record.paid_amount)
        end)
      end
    end

    calculate :discount_percentage, :float do
      public? true
      description "优惠百分比"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.compare(record.original_price, Decimal.new("0")) == :gt do
            discount = Decimal.sub(record.original_price, record.paid_amount)
            percentage = Decimal.div(discount, record.original_price)
            percentage |> Decimal.mult(Decimal.new("100")) |> Decimal.to_float()
          else
            0.0
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case {record.payment_status, record.delivery_status} do
            {:pending, _} -> "待支付"
            {:completed, :pending} -> "待发放"
            {:completed, :delivered} -> "已完成"
            {:failed, _} -> "支付失败"
            {:refunded, _} -> "已退款"
            {_, :failed} -> "发放失败"
            {_, :cancelled} -> "已取消"
            _ -> "未知状态"
          end
        end)
      end
    end
  end
end
