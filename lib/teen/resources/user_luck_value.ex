defmodule Teen.UserLuckValue do
  @moduledoc """
  用户幸运值管理资源
  """

  use Ash.Resource,
    domain: Teen.UserSystem,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "user_luck_values"
    repo Cypridina.Repo
  end

  code_interface do
    define :get_by_user, action: :read, get_by: [:user_id]
    define :update_luck, args: [:new_luck]
    define :admin_reset, args: [:new_value]
    define :create_for_user, args: [:user_id]
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [:user_id, :current_luck, :last_updated_at]
    end

    # 为用户创建幸运值记录
    create :create_for_user do
      argument :user_id, :uuid, allow_nil?: false

      change set_attribute(:user_id, arg(:user_id))
      change set_attribute(:current_luck, -1)
      change set_attribute(:last_updated_at, &DateTime.utc_now/0)
    end

    # 更新幸运值
    update :update_luck do
      argument :new_luck, :integer, allow_nil?: false

      change set_attribute(:current_luck, arg(:new_luck))
      change set_attribute(:last_updated_at, &DateTime.utc_now/0)
    end

    # 管理员重置幸运值
    update :admin_reset do
      argument :new_value, :integer, allow_nil?: false

      change set_attribute(:current_luck, arg(:new_value))
      change set_attribute(:last_updated_at, &DateTime.utc_now/0)
    end
  end

  validations do
    validate numericality(:current_luck,
               greater_than_or_equal_to: -1,
               less_than_or_equal_to: 1000
             )

    validate present(:user_id)
  end

  attributes do
    uuid_primary_key :id

    # 用户标识 - 使用UUID引用用户表的主键
    attribute :user_id, :uuid, allow_nil?: false
    attribute :current_luck, :integer, default: -1
    attribute :last_updated_at, :utc_datetime_usec

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      source_attribute :user_id
      destination_attribute :id
      allow_nil? false
    end
  end

  # 虚拟字段：从充值记录计算充值次数
  calculations do
    calculate :recharge_count,
              :integer,
              expr(
                fragment(
                  "(SELECT COUNT(*) FROM recharge_records WHERE user_id = ? AND status = 'completed')",
                  user_id
                )
              )
  end

  identities do
    identity :unique_user_id, [:user_id]
  end
end
