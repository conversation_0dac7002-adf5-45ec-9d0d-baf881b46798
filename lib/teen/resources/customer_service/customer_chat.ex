defmodule Teen.CustomerService.CustomerChat do
  @moduledoc """
  客服聊天记录资源

  管理客服与用户的聊天记录，包括问题处理状态、回复内容等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.CustomerService,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :user_id, :platform, :question, :reply_content, :status, :inserted_at]
  end

  postgres do
    table "customer_chats"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_status
    define :list_by_platform
    define :list_by_user
  end

  actions do
    defaults [:read, :update, :destroy]

    # 自定义创建动作，接受所有必要的参数
    create :create do
      primary? true

      accept [
        :user_id,
        :platform,
        :question,
        :reply_content,
        :status,
        :customer_service_id,
        :processed_at,
        :vip_level,
        :svip_level,
        :question_image,
        :ip_address
      ]
    end

    read :list_by_status do
      argument :status, :integer, allow_nil?: false
      filter expr(status == ^arg(:status))
    end

    read :list_by_platform do
      argument :platform, :string, allow_nil?: false
      filter expr(platform == ^arg(:platform))
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    update :mark_as_processed do
      accept [:reply_content]
      change set_attribute(:status, 1)
      change set_attribute(:processed_at, &DateTime.utc_now/0)
    end

    update :batch_reply do
      argument :reply_content, :string, allow_nil?: false
      change set_attribute(:reply_content, arg(:reply_content))
      change set_attribute(:status, 1)
      change set_attribute(:processed_at, &DateTime.utc_now/0)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :platform, :string do
      allow_nil? false
      public? true
      description "平台名称"
      constraints max_length: 50
    end

    attribute :vip_level, :integer do
      allow_nil? true
      public? true
      description "VIP等级"
      default 0
    end

    attribute :svip_level, :integer do
      allow_nil? true
      public? true
      description "SVIP等级"
      default 0
    end

    attribute :question, :string do
      allow_nil? false
      public? true
      description "用户问题"
      constraints max_length: 2000
    end

    attribute :question_image, :string do
      allow_nil? true
      public? true
      description "问题图片URL"
      constraints max_length: 500
    end

    attribute :reply_content, :string do
      allow_nil? true
      public? true
      description "客服回复内容"
      constraints max_length: 2000
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "处理状态：0-未处理，1-已处理"
      default 0
      constraints min: 0, max: 1
    end

    attribute :ip_address, :string do
      allow_nil? true
      public? true
      description "用户IP地址"
      constraints max_length: 45
    end

    attribute :processed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "处理时间"
    end

    attribute :customer_service_id, :uuid do
      allow_nil? true
      public? true
      description "处理客服ID"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :customer_service, Cypridina.Accounts.User do
      public? true
      source_attribute :customer_service_id
      destination_attribute :id
    end
  end

  identities do
    identity :unique_user_question_time, [:user_id, :inserted_at]
  end
end
