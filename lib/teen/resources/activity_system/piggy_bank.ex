defmodule Teen.ActivitySystem.PiggyBank do
  @moduledoc """
  储钱罐资源

  管理用户的储钱罐功能，包括：
  - 储钱罐类型和配置
  - 累积金额和目标金额
  - 解锁条件和奖励倍率
  - 储钱罐状态管理
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource, AshOban]

  admin do
    table_columns [
      :id,
      :user_id,
      :bank_type,
      :target_amount,
      :current_amount,
      :bonus_rate,
      :status,
      :created_at
    ]
  end

  postgres do
    table "piggy_banks"
    repo Cypridina.Repo
  end

  # AshOban 配置 - 储钱罐状态检查
  oban do
    domain Teen.ActivitySystem

    scheduled_actions do
      schedule :check_piggy_bank_status, "0 1 * * *" do
        action :check_full_banks
        worker_module_name(Teen.Workers.PiggyBankChecker)
        max_attempts(2)
        queue(:piggy_bank_tasks)
      end
    end
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_active_banks
    define :add_amount
    define :break_bank
    define :check_full_banks
    define :get_user_total_savings
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true
      accept [:user_id, :bank_type, :target_amount, :bonus_rate, :unlock_condition]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :active)
        |> Ash.Changeset.change_attribute(:current_amount, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:created_at, DateTime.utc_now())
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :created_at])
    end

    read :list_active_banks do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and status == :active)
    end

    update :add_amount do
      argument :amount, :decimal, allow_nil?: false
      require_atomic? false

      change fn changeset, context ->
        amount = Ash.Changeset.get_argument(changeset, :amount)
        current = changeset.data.current_amount
        new_amount = Decimal.add(current, amount)

        changeset
        |> Ash.Changeset.change_attribute(:current_amount, new_amount)
        |> Ash.Changeset.change_attribute(:last_deposit_at, DateTime.utc_now())
      end
    end

    update :break_bank do
      require_atomic? false
      accept []

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :broken)
        |> Ash.Changeset.change_attribute(:broken_at, DateTime.utc_now())
      end
    end

    read :get_user_total_savings do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and status == :active)
    end

    # 检查储钱罐状态的定时任务
    create :check_full_banks do
      accept []

      change fn changeset, _context ->
        case check_all_full_banks() do
          {:ok, result} ->
            require Logger
            Logger.info("储钱罐状态检查完成: #{inspect(result)}")
            changeset

          {:error, reason} ->
            require Logger
            Logger.error("储钱罐状态检查失败: #{inspect(reason)}")

            changeset
            |> Ash.Changeset.add_error(field: :base, message: "储钱罐状态检查失败: #{inspect(reason)}")
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :bank_type, :atom do
      allow_nil? false
      public? true
      description "储钱罐类型"
      constraints one_of: [:loss_rebate, :deposit_bonus, :game_savings, :vip_savings]
    end

    attribute :target_amount, :decimal do
      allow_nil? false
      public? true
      description "目标金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :current_amount, :decimal do
      allow_nil? false
      public? true
      description "当前金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :bonus_rate, :decimal do
      allow_nil? false
      public? true
      description "奖励倍率（如1.2表示120%）"
      constraints min: Decimal.new("1.0")
      default Decimal.new("1.0")
    end

    attribute :unlock_condition, :map do
      allow_nil? true
      public? true
      description "解锁条件（JSON格式）"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "储钱罐状态"
      constraints one_of: [:active, :full, :broken, :expired]
      default :active
    end

    attribute :created_at, :utc_datetime do
      allow_nil? false
      public? true
      description "创建时间"
    end

    attribute :last_deposit_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后存入时间"
    end

    attribute :broken_at, :utc_datetime do
      allow_nil? true
      public? true
      description "打破时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :progress_percentage, :float do
      public? true
      description "储蓄进度百分比"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.compare(record.target_amount, Decimal.new("0")) == :gt do
            progress = Decimal.div(record.current_amount, record.target_amount)
            progress |> Decimal.mult(Decimal.new("100")) |> Decimal.to_float()
          else
            0.0
          end
        end)
      end
    end

    calculate :is_full, :boolean do
      public? true
      description "是否已满"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          Decimal.compare(record.current_amount, record.target_amount) != :lt
        end)
      end
    end

    calculate :potential_reward, :decimal do
      public? true
      description "潜在奖励金额"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          Decimal.mult(record.current_amount, record.bonus_rate)
        end)
      end
    end

    calculate :remaining_amount, :decimal do
      public? true
      description "距离目标还需金额"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          remaining = Decimal.sub(record.target_amount, record.current_amount)

          if Decimal.compare(remaining, Decimal.new("0")) == :gt do
            remaining
          else
            Decimal.new("0")
          end
        end)
      end
    end
  end

  # 私有函数 - 储钱罐状态检查逻辑

  defp check_all_full_banks do
    require Logger
    Logger.info("🐷 开始检查储钱罐状态...")

    # 查找所有活跃的储钱罐
    case Ash.read(__MODULE__, filter: [status: :active]) do
      {:ok, active_banks} ->
        results =
          Enum.map(active_banks, fn bank ->
            if bank.is_full do
              generate_piggy_bank_reward(bank)
            else
              {:skipped, "储钱罐未满"}
            end
          end)

        successful = Enum.count(results, fn {status, _} -> status == :ok end)
        total = length(results)

        Logger.info("🐷 储钱罐状态检查完成 - 处理: #{total}个储钱罐, 生成奖励: #{successful}个")
        {:ok, %{total: total, successful: successful, results: results}}

      {:error, reason} ->
        Logger.error("🐷 获取储钱罐列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp generate_piggy_bank_reward(bank) do
    # 为储钱罐生成奖励
    reward_amount = bank.potential_reward

    reward_data = %{
      user_id: bank.user_id,
      reward_type: :coins,
      source_type: :piggy_bank,
      source_id: bank.id,
      reward_amount: reward_amount,
      reward_data: %{
        original_amount: bank.current_amount,
        bonus_rate: bank.bonus_rate,
        bank_type: bank.bank_type
      },
      description: "储钱罐奖励",
      is_pending: true
    }

    case Teen.ActivitySystem.UserReward.create(reward_data) do
      {:ok, reward} ->
        # 标记储钱罐为已满状态
        case break_bank(bank) do
          {:ok, _} ->
            Logger.debug("为用户 #{bank.user_id} 生成储钱罐奖励: #{reward_amount}")
            {:ok, reward}

          {:error, reason} ->
            Logger.error("更新储钱罐状态失败 - 储钱罐ID: #{bank.id}, 原因: #{inspect(reason)}")
            {:error, reason}
        end

      {:error, reason} ->
        Logger.error("生成储钱罐奖励失败 - 用户: #{bank.user_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
