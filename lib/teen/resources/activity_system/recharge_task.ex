defmodule Teen.ActivitySystem.RechargeTask do
  @moduledoc """
  充值任务资源

  管理充值奖励配置
  规则：充值X金额奖励Y金币
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :recharge_amount, :reward_amount, :reward_rate, :status, :updated_at]
  end

  postgres do
    table "recharge_tasks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_amount_range
    define :enable_task
    define :disable_task
    define :get_reward_for_amount
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true
      accept [:recharge_amount, :reward_amount, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_amount_range do
      argument :min_amount, :decimal, allow_nil?: false
      argument :max_amount, :decimal, allow_nil?: false

      filter expr(
               recharge_amount >= ^arg(:min_amount) and recharge_amount <= ^arg(:max_amount) and
                 status == :enabled
             )
    end

    read :get_reward_for_amount do
      argument :recharge_amount, :decimal, allow_nil?: false
      filter expr(recharge_amount <= ^arg(:recharge_amount) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  validations do
    validate compare(:recharge_amount, greater_than: Decimal.new("0")), message: "充值金额必须大于0"

    validate compare(:reward_amount, greater_than_or_equal_to: Decimal.new("0")),
      message: "奖励金额不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :recharge_amount, :decimal do
      allow_nil? false
      public? true
      description "充值金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金币（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :reward_rate, :decimal do
      public? true
      description "奖励比例（奖励金额/充值金额）"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if Decimal.compare(record.recharge_amount, Decimal.new("0")) == :gt do
            Decimal.div(record.reward_amount, record.recharge_amount)
            |> Decimal.mult(Decimal.new("100"))
            |> Decimal.round(2)
          else
            Decimal.new("0")
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :task_description, :string do
      public? true
      description "任务描述"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          recharge_yuan = Decimal.div(record.recharge_amount, Decimal.new("100"))
          reward_yuan = Decimal.div(record.reward_amount, Decimal.new("100"))
          "充值#{recharge_yuan}元奖励#{reward_yuan}元"
        end)
      end
    end
  end

  identities do
    identity :unique_recharge_amount, [:recharge_amount]
  end
end
