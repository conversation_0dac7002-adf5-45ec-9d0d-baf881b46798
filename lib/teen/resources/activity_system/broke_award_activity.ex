defmodule Teen.ActivitySystem.BrokeAwardActivity do
  @moduledoc """
  破产补助活动资源

  当玩家资金低于一定阈值时，可领取破产补助
  对应原Node.js系统的BrokeAward功能

  参数说明：
  - isfetch: 0获取状态，1领取奖励
  返回：
  - fetchcount: 已领取次数
  - awardcash: 可领取金额
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :activity_name,
      :min_balance_threshold,
      :award_amount,
      :max_claims_per_day,
      :status
    ]
  end

  postgres do
    table "broke_award_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_activities
    define :check_eligibility
    define :claim_award
    define :get_claim_status
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true
      accept :*
    end

    read :list_active_activities do
      filter expr(
               status == :active and
                 start_date <= ^DateTime.utc_now() and
                 end_date >= ^DateTime.utc_now()
             )
    end

    read :check_eligibility do
      argument :user_id, :uuid, allow_nil?: false

      prepare fn query, context ->
        user_id = context.arguments[:user_id]

        query
        |> Ash.Query.filter(expr(status == :active))
        |> Ash.Query.load(eligibility_status: %{user_id: user_id})
      end
    end

    read :get_claim_status do
      argument :user_id, :uuid, allow_nil?: false

      prepare fn query, context ->
        user_id = context.arguments[:user_id]

        query
        |> Ash.Query.filter(expr(status == :active))
        |> Ash.Query.load(claim_status: %{user_id: user_id})
      end
    end

    update :claim_award do
      argument :user_id, :uuid, allow_nil?: false
      require_atomic? false

      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        activity = changeset.data

        case check_claim_eligibility(user_id, activity) do
          :ok -> :ok
          {:error, reason} -> {:error, field: :user_id, message: reason}
        end
      end

      change fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        activity = changeset.data

        case process_broke_award_claim(user_id, activity) do
          {:ok, award_amount} ->
            # 记录领取信息
            record_broke_award_claim(user_id, activity.id, award_amount)
            changeset

          {:error, reason} ->
            Ash.Changeset.add_error(changeset, field: :award, message: reason)
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :activity_name, :string do
      allow_nil? false
      public? true
      description "活动名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    attribute :min_balance_threshold, :decimal do
      allow_nil? false
      public? true
      description "最低余额阈值（低于此值可申请补助）"
      constraints min: Decimal.new("0")
      # 默认1000分以下可申请
      default Decimal.new("1000")
    end

    attribute :award_amount, :decimal do
      allow_nil? false
      public? true
      description "补助金额"
      constraints min: Decimal.new("0")
      # 默认补助500分
      default Decimal.new("500")
    end

    attribute :max_claims_per_day, :integer do
      allow_nil? false
      public? true
      description "每日最大领取次数"
      constraints min: 1
      default 3
    end

    attribute :max_claims_total, :integer do
      allow_nil? false
      public? true
      description "总最大领取次数"
      constraints min: 1
      default 10
    end

    attribute :cooldown_hours, :integer do
      allow_nil? false
      public? true
      description "领取间隔时间（小时）"
      constraints min: 1
      # 默认8小时间隔
      default 8
    end

    attribute :start_date, :utc_datetime do
      allow_nil? false
      public? true
      description "开始时间"
    end

    attribute :end_date, :utc_datetime do
      allow_nil? false
      public? true
      description "结束时间"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :inactive]
      default :active
    end

    timestamps()
  end

  calculations do
    calculate :eligibility_status, :map do
      public? true
      description "用户资格状态"
      argument :user_id, :uuid

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn activity ->
          check_user_eligibility(user_id, activity)
        end)
      end
    end

    calculate :claim_status, :map do
      public? true
      description "用户领取状态"
      argument :user_id, :uuid

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn activity ->
          get_user_claim_status(user_id, activity)
        end)
      end
    end
  end

  # Helper functions

  defp check_claim_eligibility(user_id, activity) do
    with {:ok, balance} <- get_user_balance(user_id),
         :ok <- check_balance_threshold(balance, activity.min_balance_threshold),
         :ok <- check_daily_claim_limit(user_id, activity),
         :ok <- check_total_claim_limit(user_id, activity),
         :ok <- check_cooldown_period(user_id, activity) do
      :ok
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp get_user_balance(user_id) do
    # 通过账本系统获取用户余额
    account = "user:XAA:#{user_id}"

    case Cypridina.Ledger.get_balance(account) do
      {:ok, balance} -> {:ok, balance}
      {:error, _} -> {:error, "获取用户余额失败"}
    end
  end

  defp check_balance_threshold(balance, threshold) do
    if Decimal.compare(balance, threshold) == :lt do
      :ok
    else
      {:error, "余额不符合申请条件"}
    end
  end

  defp check_daily_claim_limit(user_id, activity) do
    today = Date.utc_today()

    case get_daily_claim_count(user_id, activity.id, today) do
      count when count < activity.max_claims_per_day -> :ok
      _ -> {:error, "今日领取次数已达上限"}
    end
  end

  defp check_total_claim_limit(user_id, activity) do
    case get_total_claim_count(user_id, activity.id) do
      count when count < activity.max_claims_total -> :ok
      _ -> {:error, "总领取次数已达上限"}
    end
  end

  defp check_cooldown_period(user_id, activity) do
    case get_last_claim_time(user_id, activity.id) do
      nil ->
        :ok

      last_claim_time ->
        cooldown_until = DateTime.add(last_claim_time, activity.cooldown_hours * 3600, :second)

        if DateTime.compare(DateTime.utc_now(), cooldown_until) == :gt do
          :ok
        else
          {:error, "领取间隔时间未到"}
        end
    end
  end

  defp process_broke_award_claim(user_id, activity) do
    case distribute_broke_award(user_id, activity.award_amount) do
      {:ok, _} -> {:ok, activity.award_amount}
      {:error, reason} -> {:error, "补助发放失败: #{inspect(reason)}"}
    end
  end

  defp distribute_broke_award(user_id, amount) do
    from_account = "system:XAA:broke_awards"
    to_account = "user:XAA:#{user_id}"

    Cypridina.Ledger.game_win(from_account, to_account, Decimal.to_integer(amount), "破产补助")
  end

  defp record_broke_award_claim(user_id, activity_id, award_amount) do
    claim_data = %{
      "claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "award_amount" => Decimal.to_string(award_amount),
      "user_balance_before" => get_user_balance_string(user_id)
    }

    case get_user_broke_award_record(user_id, activity_id) do
      nil ->
        # 创建新记录
        Teen.ActivitySystem.UserActivityParticipation.create(%{
          user_id: user_id,
          activity_id: activity_id,
          activity_type: :broke_award,
          participation_data: %{
            "total_claims" => 1,
            "last_claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
            "claims_history" => [claim_data]
          }
        })

      record ->
        # 更新现有记录
        current_claims = Map.get(record.participation_data, "total_claims", 0)
        claims_history = Map.get(record.participation_data, "claims_history", [])

        Teen.ActivitySystem.UserActivityParticipation.update(record, %{
          participation_data: %{
            "total_claims" => current_claims + 1,
            "last_claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
            "claims_history" => [claim_data | claims_history]
          }
        })
    end
  end

  defp get_daily_claim_count(user_id, activity_id, date) do
    case get_user_broke_award_record(user_id, activity_id) do
      nil ->
        0

      record ->
        claims_history = Map.get(record.participation_data, "claims_history", [])
        date_string = Date.to_string(date)

        claims_history
        |> Enum.count(fn claim ->
          claim_time = Map.get(claim, "claim_time", "")
          String.starts_with?(claim_time, date_string)
        end)
    end
  end

  defp get_total_claim_count(user_id, activity_id) do
    case get_user_broke_award_record(user_id, activity_id) do
      nil -> 0
      record -> Map.get(record.participation_data, "total_claims", 0)
    end
  end

  defp get_last_claim_time(user_id, activity_id) do
    case get_user_broke_award_record(user_id, activity_id) do
      nil ->
        nil

      record ->
        case Map.get(record.participation_data, "last_claim_time") do
          nil ->
            nil

          time_string ->
            case DateTime.from_iso8601(time_string) do
              {:ok, datetime, _} -> datetime
              _ -> nil
            end
        end
    end
  end

  defp get_user_broke_award_record(user_id, activity_id) do
    case Teen.ActivitySystem.UserActivityParticipation.list_by_user_and_activity(
           user_id,
           :broke_award
         ) do
      {:ok, participations} ->
        Enum.find(participations, fn p -> p.activity_id == activity_id end)

      _ ->
        nil
    end
  end

  defp get_user_balance_string(user_id) do
    case get_user_balance(user_id) do
      {:ok, balance} -> Decimal.to_string(balance)
      _ -> "0"
    end
  end

  defp check_user_eligibility(user_id, activity) do
    case check_claim_eligibility(user_id, activity) do
      :ok -> %{"eligible" => true, "reason" => "符合申请条件"}
      {:error, reason} -> %{"eligible" => false, "reason" => reason}
    end
  end

  defp get_user_claim_status(user_id, activity) do
    total_claims = get_total_claim_count(user_id, activity.id)
    daily_claims = get_daily_claim_count(user_id, activity.id, Date.utc_today())
    last_claim_time = get_last_claim_time(user_id, activity.id)

    %{
      "total_claims" => total_claims,
      "daily_claims" => daily_claims,
      "max_daily_claims" => activity.max_claims_per_day,
      "max_total_claims" => activity.max_claims_total,
      "last_claim_time" =>
        if(last_claim_time, do: DateTime.to_iso8601(last_claim_time), else: nil),
      "award_amount" => Decimal.to_string(activity.award_amount)
    }
  end

  @doc """
  获取破产补助数据
  """
  def get_broke_award_data(user_id, is_fetch \\ 0) do
    try do
      case list_active_activities() do
        {:ok, [activity | _]} ->
          case is_fetch do
            0 ->
              # 获取状态
              case get_claim_status(activity, %{user_id: user_id}) do
                {:ok, [activity_with_status]} ->
                  status = activity_with_status.claim_status

                  {:ok,
                   %{
                     fetchcount: Map.get(status, "total_claims", 0),
                     awardcash: String.to_integer(Map.get(status, "award_amount", "0")),
                     canfetch:
                       if(check_claim_eligibility(user_id, activity) == :ok, do: 1, else: 0)
                   }}

                _ ->
                  {:ok, build_default_broke_award_data()}
              end

            1 ->
              # 领取奖励
              case claim_award(activity, %{user_id: user_id}) do
                {:ok, _} ->
                  {:ok,
                   %{
                     code: 0,
                     fetchaward: Decimal.to_integer(activity.award_amount),
                     msg: "破产补助领取成功"
                   }}

                {:error, error} ->
                  message =
                    case error do
                      %Ash.Error.Invalid{errors: [%{message: msg} | _]} -> msg
                      _ -> "领取失败"
                    end

                  {:error, %{code: 1, msg: message}}
              end
          end

        _ ->
          {:ok, build_default_broke_award_data()}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception getting broke award data: #{inspect(e)}")
        {:ok, build_default_broke_award_data()}
    end
  end

  defp build_default_broke_award_data do
    %{
      fetchcount: 0,
      awardcash: 500,
      canfetch: 0
    }
  end

  @doc """
  处理破产补助请求
  """
  def process_broke_award_request(user_id, data) do
    is_fetch = Map.get(data, "isfetch", 0)
    get_broke_award_data(user_id, is_fetch)
  end
end
