defmodule Teen.ActivitySystem.UserGameTaskProgress do
  @moduledoc """
  用户游戏任务进度资源

  记录用户在各种游戏任务中的进度情况，包括：
  - 当前进度值
  - 是否已完成
  - 是否已领取奖励
  - 每日重置机制
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :game_task_id,
      :current_progress,
      :is_completed,
      :reward_claimed,
      :claimed_at,
      :task_date,
      :updated_at
    ]
  end

  postgres do
    table "user_game_task_progress"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :get_user_progress
    define :list_by_user
    define :list_by_task
    define :update_progress
    define :claim_reward
    define :reset_daily_progress
  end

  actions do
    defaults [:read, :destroy, update: :*]

    create :create do
      primary? true
      accept [
        :user_id,
        :game_task_id,
        :current_progress,
        :is_completed,
        :reward_claimed,
        :task_date
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:current_progress, 0)
        |> Ash.Changeset.change_attribute(:is_completed, false)
        |> Ash.Changeset.change_attribute(:reward_claimed, false)
        |> Ash.Changeset.change_attribute(:task_date, Date.utc_today())
      end
    end

    read :get_user_progress do
      argument :user_id, :uuid, allow_nil?: false
      argument :game_task_id, :uuid, allow_nil?: false
      argument :task_date, :date, allow_nil?: true

      filter expr(
        user_id == ^arg(:user_id) and
        game_task_id == ^arg(:game_task_id) and
        (is_nil(^arg(:task_date)) or task_date == ^arg(:task_date))
      )
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      argument :task_date, :date, allow_nil?: true

      filter expr(
        user_id == ^arg(:user_id) and
        (is_nil(^arg(:task_date)) or task_date == ^arg(:task_date))
      )
    end

    read :list_by_task do
      argument :game_task_id, :uuid, allow_nil?: false
      argument :task_date, :date, allow_nil?: true

      filter expr(
        game_task_id == ^arg(:game_task_id) and
        (is_nil(^arg(:task_date)) or task_date == ^arg(:task_date))
      )
    end

    update :update_progress do
      accept [:current_progress, :is_completed]
      require_atomic? false

      change fn changeset, _context ->
        current_progress = Ash.Changeset.get_attribute(changeset, :current_progress)

        # 如果有游戏任务关联，检查是否完成
        case changeset.data do
          %{game_task: %{required_count: required_count}} ->
            if current_progress >= required_count do
              changeset
              |> Ash.Changeset.change_attribute(:is_completed, true)
            else
              changeset
            end
          _ ->
            changeset
        end
      end
    end

    update :claim_reward do
      require_atomic? false

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:reward_claimed, true)
        |> Ash.Changeset.change_attribute(:claimed_at, DateTime.utc_now())
      end
    end

    create :reset_daily_progress do
      accept []

      change fn changeset, context ->
        # 这个action用于每日重置
        changeset
        |> Ash.Changeset.change_attribute(:current_progress, 0)
        |> Ash.Changeset.change_attribute(:is_completed, false)
        |> Ash.Changeset.change_attribute(:reward_claimed, false)
        |> Ash.Changeset.change_attribute(:task_date, Date.utc_today())
      end
    end
  end

  validations do
    validate compare(:current_progress, greater_than_or_equal_to: 0),
      message: "进度不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :game_task_id, :uuid do
      allow_nil? false
      public? true
      description "游戏任务ID"
    end

    attribute :current_progress, :integer do
      allow_nil? false
      public? true
      description "当前进度"
      default 0
      constraints min: 0
    end

    attribute :is_completed, :boolean do
      allow_nil? false
      public? true
      description "是否已完成"
      default false
    end

    attribute :reward_claimed, :boolean do
      allow_nil? false
      public? true
      description "奖励是否已领取"
      default false
    end

    attribute :claimed_at, :utc_datetime do
      allow_nil? true
      public? true
      description "奖励领取时间"
    end

    attribute :task_date, :date do
      allow_nil? false
      public? true
      description "任务日期（用于每日重置）"
      default &Date.utc_today/0
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end

    belongs_to :game_task, Teen.ActivitySystem.GameTask do
      public? true
      source_attribute :game_task_id
      destination_attribute :id
    end
  end

  calculations do
    calculate :progress_percentage, :integer do
      public? true
      description "完成百分比"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.game_task do
            %{required_count: required_count} when required_count > 0 ->
              min(100, round(record.current_progress / required_count * 100))
            _ ->
              0
          end
        end)
      end
    end

    calculate :can_claim_reward, :boolean do
      public? true
      description "是否可以领取奖励"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          record.is_completed and not record.reward_claimed
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          cond do
            record.reward_claimed -> "已领取"
            record.is_completed -> "可领取"
            true -> "进行中"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_user_task_date, [:user_id, :game_task_id, :task_date]
  end

  # 便捷函数

  @doc """
  获取或创建用户今日的任务进度
  """
  def get_or_create_today_progress(user_id, game_task_id) do
    today = Date.utc_today()

    case get_user_progress(%{user_id: user_id, game_task_id: game_task_id, task_date: today}) do
      {:ok, [progress]} ->
        {:ok, progress}

      {:ok, []} ->
        # 创建今日进度记录
        create(%{
          user_id: user_id,
          game_task_id: game_task_id,
          task_date: today
        })

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  增加用户任务进度
  """
  def increment_progress(user_id, game_task_id, increment \\ 1) do
    case get_or_create_today_progress(user_id, game_task_id) do
      {:ok, progress} ->
        new_progress = progress.current_progress + increment
        update_progress(progress, %{current_progress: new_progress})

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  增加用户任务进度并检查完成状态
  """
  def increment_progress_with_completion_check(user_id, game_task_id, required_count, increment \\ 1) do
    case get_or_create_today_progress(user_id, game_task_id) do
      {:ok, progress} ->
        # 确保进度不会超过要求值
        new_progress = min(progress.current_progress + increment, required_count)
        is_completed = new_progress >= required_count

        update_progress(progress, %{
          current_progress: new_progress,
          is_completed: is_completed
        })

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查任务是否可以领取奖励
  """
  def check_claimable(user_id, game_task_id) do
    case get_or_create_today_progress(user_id, game_task_id) do
      {:ok, progress} ->
        {:ok, progress.is_completed and not progress.reward_claimed}

      {:error, reason} ->
        {:error, reason}
    end
  end
end
