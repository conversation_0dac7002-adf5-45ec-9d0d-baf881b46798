defmodule Teen.ActivitySystem.GameTask.Changes.CreateGameTask do
  @moduledoc """
  创建游戏任务时的处理逻辑

  包括：
  - 唯一性验证
  - 自动填充游戏名称
  - 设置默认状态
  """

  use Ash.Resource.Change

  @impl true
  def change(changeset, opts, _context) do
    # 获取游戏ID和任务类型
    game_id = Ash.Changeset.get_attribute(changeset, :game_id)
    task_type = Ash.Changeset.get_attribute(changeset, :task_type)

    # 检查唯一性（如果启用验证）
    validate_uniqueness = Ash.Changeset.get_argument(changeset, :validate_uniqueness)

    changeset = if validate_uniqueness != false and not is_nil(game_id) and not is_nil(task_type) do
      case check_game_task_uniqueness(game_id, task_type) do
        :ok ->
          changeset
        {:error, existing_task} ->
          Ash.Changeset.add_error(changeset,
            field: :game_id,
            message: "该游戏(ID: #{game_id})的#{get_task_type_display(task_type)}任务已存在。现有任务: \"#{existing_task.task_name}\"。请选择其他游戏或任务类型，或编辑现有任务。"
          )
      end
    else
      changeset
    end

    # 根据游戏ID自动填充游戏名称（如果没有手动设置）
    current_game_name = Ash.Changeset.get_attribute(changeset, :game_name)

    game_name =
      if current_game_name == "未知游戏名称" or is_nil(current_game_name) do
        case game_id do
          game_id when is_integer(game_id) and game_id > 0 ->
            get_game_name_by_id(game_id)

          _ ->
            "未知游戏名称"
        end
      else
        current_game_name
      end

    changeset
    |> Ash.Changeset.change_attribute(:status, :enabled)
    |> Ash.Changeset.change_attribute(:game_name, game_name)
  end

  @impl true
  def atomic(changeset, _opts, context) do
    # 对于原子操作，我们不进行复杂的验证和数据库查询
    # 返回 {:not_atomic, reason} 来禁用原子操作，使用常规的 change/3
    {:not_atomic, "Game task creation requires uniqueness validation and database lookup"}
  end

  # 检查游戏任务唯一性
  defp check_game_task_uniqueness(game_id, task_type) do
    case Teen.ActivitySystem.GameTask
         |> Ash.Query.filter(game_id == ^game_id and task_type == ^task_type)
         |> Ash.read_one() do
      {:ok, nil} -> :ok
      {:ok, existing_task} -> {:error, existing_task}
      {:error, _} -> :ok
    end
  end

  # 获取任务类型显示名称
  defp get_task_type_display(task_type) do
    case task_type do
      :game_rounds -> "游戏局数"
      :recharge_amount -> "充值金额"
      :win_rounds -> "游戏胜利"
      :wheel_spins -> "转盘次数"
      :task_completion -> "完成任务"
      _ -> "未知类型"
    end
  end

  # 根据游戏ID获取游戏名称
  defp get_game_name_by_id(game_id) when is_integer(game_id) do
    try do
      case Teen.GameManagement.ManageGameConfig.get_by_game_id(game_id) do
        {:ok, game} -> game.display_name
        {:error, _} -> get_fallback_game_name(game_id)
      end
    rescue
      _error -> get_fallback_game_name(game_id)
    end
  end

  defp get_game_name_by_id(_), do: "未知游戏名称"

  # 备选游戏名称映射
  defp get_fallback_game_name(game_id) do
    case game_id do
      1 -> "Teen Patti"
      22 -> "Dragon Tiger"
      23 -> "Crash"
      30 -> "Andar Bahar"
      31 -> "Jhandi Munda"
      32 -> "AK47 Teen Patti"
      33 -> "Pot Blind"
      34 -> "Safari of Wealth"
      40 -> "Slot 777"
      41 -> "Slot Niu"
      42 -> "Slot Cat"
      _ -> "未知游戏 (ID: #{game_id})"
    end
  end
end
