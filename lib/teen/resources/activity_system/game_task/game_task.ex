defmodule Teen.ActivitySystem.GameTask do
  @moduledoc """
  游戏任务资源

  管理每日游戏任务配置，包括：
  - 游戏局数任务
  - 游戏赢的局数任务
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource, AshOban]

  admin do
    table_columns [
      :id,
      :task_name,
      :game_name,
      :task_type,
      :required_count,
      :max_claims,
      :reward_amount,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "game_tasks"
    repo Cypridina.Repo
  end

  # AshOban 配置 - 每日任务重置
  oban do
    domain Teen.ActivitySystem

    scheduled_actions do
      schedule :reset_daily_tasks, "@daily" do
        worker_module_name(Teen.ActivitySystem.GameTask.AshOban.ActionWorker.ResetDailyTasks)
      end
    end
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_game
    define :enable_task
    define :disable_task
    define :reset_daily_tasks
  end

  actions do
    defaults [:read, :destroy]

    update :update do
      primary? true

      accept [
        :task_name,
        :game_id,
        :game_name,
        :task_type,
        :required_count,
        :max_claims,
        :target_value,
        :reward_amount,
        :reward_type,
        :status,
        :game_config_id,
        :is_active,
        :start_date,
        :end_date
      ]

      # 自动填充游戏名称
      change fn changeset, _context ->
        game_id = Ash.Changeset.get_attribute(changeset, :game_id)
        current_game_name = Ash.Changeset.get_attribute(changeset, :game_name)

        # 如果游戏ID发生变化或游戏名称为空/默认值，则自动填充
        game_name =
          if is_integer(game_id) and game_id > 0 and
             (is_nil(current_game_name) or current_game_name == "未知游戏名称" or current_game_name == "") do
            get_game_name_by_id(game_id)
          else
            current_game_name || "未知游戏名称"
          end

        Ash.Changeset.change_attribute(changeset, :game_name, game_name)
      end
    end

    create :create do
      primary? true

      accept [
        :task_name,
        :game_id,
        :game_name,
        :task_type,
        :required_count,
        :max_claims,
        :target_value,
        :reward_amount,
        :reward_type,
        :status,
        :game_config_id,
        :is_active,
        :start_date,
        :end_date
      ]

      # 添加参数验证
      argument :validate_uniqueness, :boolean, default: true

      # 预处理：设置默认值和验证
      change fn changeset, _context ->
        # 获取游戏ID和任务类型
        game_id = Ash.Changeset.get_attribute(changeset, :game_id)
        task_type = Ash.Changeset.get_attribute(changeset, :task_type)

        # 检查唯一性（如果启用验证）
        validate_uniqueness = Ash.Changeset.get_argument(changeset, :validate_uniqueness)

        changeset = if validate_uniqueness != false and not is_nil(game_id) and not is_nil(task_type) do
          case check_game_task_uniqueness(game_id, task_type) do
            :ok ->
              changeset
            {:error, existing_task} ->
              Ash.Changeset.add_error(changeset,
                field: :game_id,
                message: "该游戏(ID: #{game_id})的#{get_task_type_display(task_type)}任务已存在。现有任务: \"#{existing_task.task_name}\"。请选择其他游戏或任务类型，或编辑现有任务。"
              )
          end
        else
          changeset
        end

        # 根据游戏ID自动填充游戏名称（如果没有手动设置）
        current_game_name = Ash.Changeset.get_attribute(changeset, :game_name)

        game_name =
          if current_game_name == "未知游戏名称" or is_nil(current_game_name) do
            case game_id do
              game_id when is_integer(game_id) and game_id > 0 ->
                get_game_name_by_id(game_id)

              _ ->
                "未知游戏名称"
            end
          else
            current_game_name
          end

        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:game_name, game_name)
      end
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_game do
      argument :game_id, :integer, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    # 每日任务重置的定时任务
    create :reset_daily_tasks do
      accept []

      change fn changeset, _context ->
        case reset_all_daily_tasks() do
          {:ok, result} ->
            require Logger
            Logger.info("每日任务重置完成: #{inspect(result)}")
            changeset

          {:error, reason} ->
            require Logger
            Logger.error("每日任务重置失败: #{inspect(reason)}")

            changeset
            |> Ash.Changeset.add_error(field: :base, message: "每日任务重置失败: #{inspect(reason)}")
        end
      end
    end
  end

  validations do
    validate compare(:required_count, greater_than: 0), message: "所需局数必须大于0"
    validate compare(:max_claims, greater_than: 0), message: "最大领取次数必须大于0"

    validate compare(:reward_amount, greater_than_or_equal_to: Decimal.new("0")),
      message: "奖励金额不能为负数"

    # 验证必填字段
    validate present([:task_name, :game_id, :task_type]), message: "任务名称、游戏ID和任务类型为必填项"
  end

  attributes do
    uuid_primary_key :id

    attribute :task_name, :string do
      allow_nil? false
      public? true
      description "任务名称"
      constraints max_length: 100
      default "新游戏任务"
    end

    attribute :game_id, :integer do
      allow_nil? false
      public? true
      description "游戏ID"
      constraints min: 1
      default 1
    end

    attribute :game_name, :string do
      allow_nil? false
      public? true
      description "游戏名称"
      constraints max_length: 100
      default "未知游戏名称"
    end

    attribute :task_type, :atom do
      allow_nil? false
      public? true
      description "任务类型"
      default :game_rounds

      constraints one_of: [
                    :game_rounds,
                    :recharge_amount,
                    :win_rounds,
                    :wheel_spins,
                    :task_completion
                  ]
    end

    attribute :required_count, :integer do
      allow_nil? false
      public? true
      description "所需局数"
      constraints min: 1
      default 1
    end

    attribute :max_claims, :integer do
      allow_nil? false
      public? true
      description "每日最大领取次数"
      constraints min: 1
      default 1
    end

    attribute :target_value, :integer do
      allow_nil? true
      public? true
      description "目标值"
      constraints min: 1
      default 1
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :reward_type, :atom do
      allow_nil? true
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :points, :items]
      default :coins
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    attribute :game_config_id, :uuid do
      allow_nil? true
      public? true
      description "关联的游戏配置ID"
    end

    attribute :is_active, :boolean do
      allow_nil? true
      public? true
      description "是否激活"
      default true
    end

    attribute :start_date, :date do
      allow_nil? true
      public? true
      description "开始日期"
      default &Date.utc_today/0
    end

    attribute :end_date, :date do
      allow_nil? true
      public? true
      description "结束日期"
      default fn -> Date.add(Date.utc_today(), 30) end
    end

    # attribute :inserted_at, :utc_datetime_usec do
    #   allow_nil? false
    #   public? true
    #   description "创建时间"
    #   writable? false
    #   default &DateTime.utc_now/0
    # end

    # attribute :updated_at, :utc_datetime_usec do
    #   allow_nil? false
    #   public? true
    #   description "更新时间"
    #   writable? false
    #   default &DateTime.utc_now/0
    #   update_default &DateTime.utc_now/0
    # end
    timestamps()
  end

  calculations do
    calculate :task_type_display, :string do
      public? true
      description "任务类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.task_type do
            :game_rounds -> "游戏局数"
            :recharge_amount -> "充值金额"
            :win_rounds -> "游戏赢的局数"
            :wheel_spins -> "转盘次数"
            :task_completion -> "完成任务"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :reward_type_display, :string do
      public? true
      description "奖励类型显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.reward_type do
            :coins -> "金币"
            :points -> "积分"
            :items -> "道具"
            _ -> "未知"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_game_task, [:game_id, :task_type]
  end

  # 私有函数 - 游戏任务唯一性检查

  defp check_game_task_uniqueness(game_id, task_type) do
    require Ash.Query
    import Ash.Expr

    case __MODULE__
         |> Ash.Query.filter(expr(game_id == ^game_id and task_type == ^task_type))
         |> Ash.read_one() do
      {:ok, nil} ->
        :ok
      {:ok, existing_task} ->
        {:error, existing_task}
      {:error, _reason} ->
        :ok  # 如果查询失败，允许继续（可能是数据库问题）
    end
  end

  defp get_task_type_display(:game_rounds), do: "游戏局数"
  defp get_task_type_display(:win_rounds), do: "胜利局数"
  defp get_task_type_display(:bet_amount), do: "投注金额"
  defp get_task_type_display(:consecutive_wins), do: "连续胜利"
  defp get_task_type_display(task_type), do: "#{task_type}"

  # 私有函数 - 每日任务重置逻辑

  defp reset_all_daily_tasks do
    require Logger
    Logger.info("📝 开始重置每日任务...")

    # 这里可以实现具体的任务重置逻辑
    # 例如：重置用户任务进度、生成新的每日任务等

    # 示例：重置所有用户的每日任务进度
    # 实际实现需要根据具体的任务进度表结构来调整

    Logger.info("📝 每日任务重置完成")
    {:ok, %{message: "每日任务已重置", reset_time: DateTime.utc_now()}}
  end

  # 根据游戏ID获取游戏名称
  defp get_game_name_by_id(game_id) when is_integer(game_id) do
    try do
      case Teen.GameManagement.ManageGameConfig.get_by_game_id(game_id) do
        {:ok, game} -> game.display_name
        {:error, _} -> get_fallback_game_name(game_id)
      end
    rescue
      _error -> get_fallback_game_name(game_id)
    end
  end

  defp get_game_name_by_id(_), do: "未知游戏名称"

  # 备选游戏名称映射
  defp get_fallback_game_name(game_id) do
    case game_id do
      1 -> "Teen Patti"
      22 -> "Dragon Tiger"
      23 -> "Crash"
      30 -> "Andar Bahar"
      31 -> "Jhandi Munda"
      32 -> "AK47 Teen Patti"
      33 -> "Pot Blind"
      34 -> "Safari of Wealth"
      40 -> "Slot 777"
      41 -> "Slot Niu"
      42 -> "Slot Cat"
      _ -> "未知游戏 (ID: #{game_id})"
    end
  end
end
