defmodule Teen.ActivitySystem.SevenDayTask do
  @moduledoc """
  七次任务资源（连续登录任务）

  管理连续登录7天的任务配置
  规则：连续登录7天可循环，中断则重置
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :day_number, :reward_amount, :is_cyclic, :status, :updated_at]
  end

  postgres do
    table "seven_day_tasks"
    repo Cy<PERSON><PERSON>ina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :list_by_day
    define :enable_task
    define :disable_task
    define :get_day_reward
  end

  actions do
    defaults [:read, :destroy, update: :*]

    create :create do
      primary? true
      accept [:day_number, :reward_amount, :is_cyclic, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
        |> Ash.Changeset.change_attribute(:is_cyclic, true)
      end
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :list_by_day do
      argument :day_number, :integer, allow_nil?: false
      filter expr(day_number == ^arg(:day_number) and status == :enabled)
    end

    read :get_day_reward do
      argument :day_number, :integer, allow_nil?: false
      get? true
      filter expr(day_number == ^arg(:day_number) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  validations do
    validate compare(:day_number, greater_than: 0), message: "天数必须大于0"
    validate compare(:day_number, less_than_or_equal_to: 7), message: "天数不能超过7"

    validate compare(:reward_amount, greater_than_or_equal_to: Decimal.new("0")),
      message: "奖励金额不能为负数"
  end

  attributes do
    uuid_primary_key :id

    attribute :day_number, :integer do
      allow_nil? false
      public? true
      description "第几天（1-7）"
      constraints min: 1, max: 7
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金币（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :is_cyclic, :boolean do
      allow_nil? false
      public? true
      description "是否循环"
      default true
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :day_display, :string do
      public? true
      description "天数显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          "第#{record.day_number}天"
        end)
      end
    end

    calculate :status_display, :string do
      public? true
      description "状态显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.status do
            :enabled -> "启用"
            :disabled -> "禁用"
            _ -> "未知"
          end
        end)
      end
    end

    calculate :cyclic_display, :string do
      public? true
      description "循环状态显示"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          if record.is_cyclic, do: "是", else: "否"
        end)
      end
    end
  end

  identities do
    identity :unique_day_number, [:day_number]
  end
end
