defmodule Teen.ActivitySystem.LoginCashActivity do
  @moduledoc """
  登录现金奖励活动资源

  对应原Node.js系统的LoginCash活动，支持多种触发类型：
  - 0: 登录奖励
  - 1: 充值奖励
  - 2: 游戏局数奖励
  - 3: 转盘奖励
  - 4: 游戏赢分奖励
  - 10: 完成所有任务奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :activity_name,
      :reward_type,
      :total_reward,
      :status,
      :start_date,
      :end_date
    ]
  end

  postgres do
    table "login_cash_activities"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_activities
    define :get_user_progress
    define :claim_reward
    define :complete_task
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true
      accept :*
    end

    read :list_active_activities do
      filter expr(
               status == :active and
                 start_date <= ^DateTime.utc_now() and
                 end_date >= ^DateTime.utc_now()
             )
    end

    read :get_user_progress do
      argument :user_id, :uuid, allow_nil?: false
      argument :activity_id, :uuid, allow_nil?: false

      prepare fn query, context ->
        user_id = context.arguments[:user_id]
        activity_id = context.arguments[:activity_id]

        query
        |> Ash.Query.filter(expr(id == ^activity_id))
        |> Ash.Query.load(user_progress: %{user_id: user_id})
      end
    end

    update :claim_reward do
      argument :user_id, :uuid, allow_nil?: false
      argument :fetch_type, :integer, allow_nil?: false
      require_atomic? false

      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        fetch_type = Ash.Changeset.get_argument(changeset, :fetch_type)
        activity = changeset.data

        case check_claim_eligibility(user_id, activity, fetch_type) do
          :ok -> :ok
          {:error, reason} -> {:error, field: :fetch_type, message: reason}
        end
      end

      change fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        fetch_type = Ash.Changeset.get_argument(changeset, :fetch_type)
        activity = changeset.data

        case process_reward_claim(user_id, activity, fetch_type) do
          {:ok, reward_amount} ->
            record_reward_claim(user_id, activity.id, fetch_type, reward_amount)
            changeset

          {:error, reason} ->
            Ash.Changeset.add_error(changeset, field: :reward, message: reason)
        end
      end
    end

    update :complete_task do
      argument :user_id, :uuid, allow_nil?: false
      argument :task_type, :integer, allow_nil?: false
      argument :progress_value, :decimal, allow_nil?: false
      require_atomic? false

      change fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        task_type = Ash.Changeset.get_argument(changeset, :task_type)
        progress_value = Ash.Changeset.get_argument(changeset, :progress_value)
        activity = changeset.data

        update_user_progress(user_id, activity, task_type, progress_value)
        changeset
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :activity_name, :string do
      allow_nil? false
      public? true
      description "活动名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "活动描述"
      constraints max_length: 500
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:cash, :coins, :items]
      default :cash
    end

    attribute :total_reward, :decimal do
      allow_nil? false
      public? true
      description "总奖励金额"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :task_config, :map do
      allow_nil? false
      public? true
      description "任务配置"
      default %{}
    end

    attribute :reward_config, :map do
      allow_nil? false
      public? true
      description "奖励配置"
      default %{}
    end

    attribute :start_date, :utc_datetime do
      allow_nil? false
      public? true
      description "开始时间"
    end

    attribute :end_date, :utc_datetime do
      allow_nil? false
      public? true
      description "结束时间"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :inactive, :completed]
      default :active
    end

    timestamps()
  end

  calculations do
    calculate :user_progress, :map do
      public? true
      description "用户进度"
      argument :user_id, :uuid

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn activity ->
          case get_user_activity_progress(user_id, activity.id) do
            nil -> build_default_progress(activity)
            progress -> progress.progress_data
          end
        end)
      end
    end
  end

  # Helper functions

  defp check_claim_eligibility(user_id, activity, fetch_type) do
    case get_user_activity_progress(user_id, activity.id) do
      nil ->
        {:error, "用户尚未参与活动"}

      progress ->
        case reward_already_claimed?(progress, fetch_type) do
          true -> {:error, "奖励已领取"}
          false -> :ok
        end
    end
  end

  defp reward_already_claimed?(progress, fetch_type) do
    claimed_rewards = Map.get(progress.progress_data, "claimed_rewards", [])
    Enum.member?(claimed_rewards, fetch_type)
  end

  defp process_reward_claim(user_id, activity, fetch_type) do
    reward_amount = calculate_reward_amount(activity, fetch_type)

    case distribute_login_cash_reward(user_id, reward_amount, "登录现金奖励") do
      {:ok, _} -> {:ok, reward_amount}
      {:error, reason} -> {:error, "奖励发放失败: #{inspect(reason)}"}
    end
  end

  defp calculate_reward_amount(activity, fetch_type) do
    reward_config = activity.reward_config

    case fetch_type do
      0 -> Decimal.new(Map.get(reward_config, "login_reward", "100"))
      1 -> Decimal.new(Map.get(reward_config, "recharge_reward", "200"))
      2 -> Decimal.new(Map.get(reward_config, "game_count_reward", "150"))
      3 -> Decimal.new(Map.get(reward_config, "wheel_reward", "300"))
      4 -> Decimal.new(Map.get(reward_config, "win_reward", "250"))
      10 -> Decimal.new(Map.get(reward_config, "complete_all_reward", "1000"))
      _ -> Decimal.new("0")
    end
  end

  defp distribute_login_cash_reward(user_id, amount, description) do
    from_account = "system:XAA:login_rewards"
    to_account = "user:XAA:#{user_id}"

    Cypridina.Ledger.game_win(from_account, to_account, Decimal.to_integer(amount), description)
  end

  defp record_reward_claim(user_id, activity_id, fetch_type, reward_amount) do
    case get_user_activity_progress(user_id, activity_id) do
      nil ->
        Teen.ActivitySystem.UserActivityParticipation.create(%{
          user_id: user_id,
          activity_id: activity_id,
          activity_type: :login_cash,
          participation_data: %{
            "claimed_rewards" => [fetch_type],
            "last_claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
            "total_claimed" => Decimal.to_string(reward_amount)
          }
        })

      progress ->
        claimed_rewards = Map.get(progress.participation_data, "claimed_rewards", [])
        total_claimed = Decimal.new(Map.get(progress.participation_data, "total_claimed", "0"))

        Teen.ActivitySystem.UserActivityParticipation.update(progress, %{
          participation_data: %{
            progress.participation_data
            | "claimed_rewards" => [fetch_type | claimed_rewards],
              "last_claim_time" => DateTime.utc_now() |> DateTime.to_iso8601(),
              "total_claimed" => Decimal.add(total_claimed, reward_amount) |> Decimal.to_string()
          }
        })
    end
  end

  defp update_user_progress(user_id, activity, task_type, progress_value) do
    case get_user_activity_progress(user_id, activity.id) do
      nil ->
        Teen.ActivitySystem.UserActivityParticipation.create(%{
          user_id: user_id,
          activity_id: activity.id,
          activity_type: :login_cash,
          participation_data: build_initial_progress(activity, task_type, progress_value)
        })

      progress ->
        updated_data =
          update_progress_data(progress.participation_data, task_type, progress_value)

        Teen.ActivitySystem.UserActivityParticipation.update(progress, %{
          participation_data: updated_data
        })
    end
  end

  defp build_initial_progress(activity, task_type, progress_value) do
    %{
      "login_count" => if(task_type == 0, do: Decimal.to_string(progress_value), else: "0"),
      "recharge_amount" => if(task_type == 1, do: Decimal.to_string(progress_value), else: "0"),
      "game_count" => if(task_type == 2, do: Decimal.to_string(progress_value), else: "0"),
      "wheel_count" => if(task_type == 3, do: Decimal.to_string(progress_value), else: "0"),
      "win_amount" => if(task_type == 4, do: Decimal.to_string(progress_value), else: "0"),
      "targets" => activity.task_config,
      "claimed_rewards" => [],
      "last_update_time" => DateTime.utc_now() |> DateTime.to_iso8601()
    }
  end

  defp update_progress_data(current_data, task_type, progress_value) do
    field_name =
      case task_type do
        0 -> "login_count"
        1 -> "recharge_amount"
        2 -> "game_count"
        3 -> "wheel_count"
        4 -> "win_amount"
        _ -> nil
      end

    if field_name do
      current_value = Decimal.new(Map.get(current_data, field_name, "0"))
      new_value = Decimal.add(current_value, progress_value)

      %{
        current_data
        | field_name => Decimal.to_string(new_value),
          "last_update_time" => DateTime.utc_now() |> DateTime.to_iso8601()
      }
    else
      current_data
    end
  end

  defp get_user_activity_progress(user_id, activity_id) do
    case Teen.ActivitySystem.UserActivityParticipation.list_by_user_and_activity(
           user_id,
           :login_cash
         ) do
      {:ok, participations} ->
        Enum.find(participations, fn p -> p.activity_id == activity_id end)

      _ ->
        nil
    end
  end

  defp build_default_progress(activity) do
    %{
      "login_count" => "0",
      "recharge_amount" => "0",
      "game_count" => "0",
      "wheel_count" => "0",
      "win_amount" => "0",
      "targets" => activity.task_config,
      "claimed_rewards" => [],
      "last_update_time" => DateTime.utc_now() |> DateTime.to_iso8601()
    }
  end

  @doc """
  获取用户登录现金活动数据
  """
  def get_login_cash_data(user_id) do
    try do
      case list_active_activities() do
        {:ok, activities} when length(activities) > 0 ->
          activity = List.first(activities)

          case get_user_progress(activity, %{user_id: user_id, activity_id: activity.id}) do
            {:ok, [activity_with_progress]} ->
              progress = activity_with_progress.user_progress

              task_data = %{
                "logincount" => String.to_integer(Map.get(progress, "login_count", "0")),
                "chargeamount" => String.to_integer(Map.get(progress, "recharge_amount", "0")),
                "gamecount" => String.to_integer(Map.get(progress, "game_count", "0")),
                "wheelcount" => String.to_integer(Map.get(progress, "wheel_count", "0")),
                "winamount" => String.to_integer(Map.get(progress, "win_amount", "0")),
                "targets" => activity.task_config,
                "claimed" => Map.get(progress, "claimed_rewards", [])
              }

              {:ok, %{logincash: task_data}}

            _ ->
              {:ok, build_default_login_cash_data()}
          end

        _ ->
          {:ok, build_default_login_cash_data()}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception getting login cash data: #{inspect(e)}")
        {:ok, build_default_login_cash_data()}
    end
  end

  defp build_default_login_cash_data do
    %{
      logincash: %{
        "logincount" => 0,
        "chargeamount" => 0,
        "gamecount" => 0,
        "wheelcount" => 0,
        "winamount" => 0,
        "targets" => %{
          "login_target" => 1,
          "recharge_target" => 100,
          "game_target" => 10,
          "wheel_target" => 3,
          "win_target" => 1000
        },
        "claimed" => []
      }
    }
  end

  @doc """
  处理登录现金奖励领取
  """
  def process_login_cash_claim(user_id, data) do
    fetch_type = Map.get(data, "fetchtype", 0)

    try do
      case list_active_activities() do
        {:ok, [activity | _]} ->
          case claim_reward(activity, %{user_id: user_id, fetch_type: fetch_type}) do
            {:ok, _} ->
              reward_amount = calculate_reward_amount(activity, fetch_type)
              {:ok, %{code: 0, fetchaward: Decimal.to_integer(reward_amount), msg: "奖励领取成功"}}

            {:error, error} ->
              message =
                case error do
                  %Ash.Error.Invalid{errors: [%{message: msg} | _]} -> msg
                  _ -> "奖励领取失败"
                end

              {:error, %{code: 1, msg: message}}
          end

        _ ->
          {:error, %{code: 1, msg: "暂无活动"}}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception processing login cash claim: #{inspect(e)}")
        {:error, %{code: 1, msg: "系统错误"}}
    end
  end
end
