defmodule Teen.ActivitySystem.FreeBonusTask do
  @moduledoc """
  免费任务（Free bonus）资源

  管理分享任务配置
  包括分享次数、游戏要求、需要赢金币、提现次数等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :title,
      :share_count,
      :game_id,
      :required_win_coins,
      :withdraw_count,
      :reward_amount,
      :status,
      :updated_at
    ]
  end

  postgres do
    table "free_bonus_tasks"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_tasks
    define :get_by_game, args: [:game_id]
    define :enable_task
    define :disable_task
    define :check_task_completed
    define :check_reward_claimed
    define :claim_reward
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true
      accept [
        :title,
        :share_count,
        :game_id,
        :game_name,
        :required_win_coins,
        :withdraw_count,
        :reward_amount,
        :status
      ]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    read :list_active_tasks do
      filter expr(status == :enabled)
    end

    read :get_by_game do
      argument :game_id, :string, allow_nil?: false
      filter expr(game_id == ^arg(:game_id) and status == :enabled)
    end

    update :enable_task do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_task do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end

    read :check_task_completed do
      argument :user_id, :string, allow_nil?: false
      argument :task_id, :uuid, allow_nil?: false

      prepare fn query, context ->
        task_id = context.arguments[:task_id]
        user_id = context.arguments[:user_id]

        query
        |> Ash.Query.filter(expr(id == ^task_id))
        |> Ash.Query.load(task_completed: %{user_id: user_id})
      end
    end

    read :check_reward_claimed do
      argument :user_id, :string, allow_nil?: false
      argument :task_id, :uuid, allow_nil?: false

      prepare fn query, context ->
        task_id = context.arguments[:task_id]
        user_id = context.arguments[:user_id]

        query
        |> Ash.Query.filter(expr(id == ^task_id))
        |> Ash.Query.load(reward_claimed: %{user_id: user_id})
      end
    end

    update :claim_reward do
      argument :user_id, :string, allow_nil?: false
      require_atomic? false

      validate fn changeset, _context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        task = changeset.data

        # Check if task is completed
        case check_bonus_task_completed(user_id, task) do
          false ->
            {:error, field: :user_id, message: "任务尚未完成"}

          true ->
            # Check if already claimed
            case check_bonus_reward_claimed(user_id, task.id) do
              true -> {:error, field: :user_id, message: "任务奖励已领取"}
              false -> :ok
            end
        end
      end

      change fn changeset, context ->
        user_id = Ash.Changeset.get_argument(changeset, :user_id)
        task = changeset.data

        # Distribute reward
        reward_amount = Decimal.to_integer(task.reward_amount)

        case distribute_bonus_reward(user_id, reward_amount, "免费积分任务奖励") do
          {:ok, _} ->
            # Record claim
            record_bonus_claim(user_id, task.id)
            changeset

          {:error, reason} ->
            Ash.Changeset.add_error(changeset,
              field: :reward,
              message: "奖励发放失败: #{inspect(reason)}"
            )
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :share_count, :integer do
      allow_nil? false
      public? true
      description "分享次数"
      constraints min: 1
      default 1
    end

    attribute :game_id, :string do
      allow_nil? true
      public? true
      description "游戏ID"
      constraints max_length: 50
    end

    attribute :game_name, :string do
      allow_nil? true
      public? true
      description "游戏名称"
      constraints max_length: 100
    end

    attribute :required_win_coins, :decimal do
      allow_nil? false
      public? true
      description "需要赢金币（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :withdraw_count, :integer do
      allow_nil? false
      public? true
      description "提现次数"
      constraints min: 1
      default 1
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end

  calculations do
    calculate :task_progress, :map do
      public? true
      description "任务进度"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn record ->
          case get_user_participation(user_id, record.id) do
            nil ->
              %{shares: 0, win_coins: 0, withdraws: 0}

            participation ->
              task_progress =
                get_in(participation.participation_data, ["task_#{record.id}"]) || %{}

              %{
                shares: Map.get(task_progress, "shares", 0),
                win_coins: Map.get(task_progress, "win_coins", 0),
                withdraws: Map.get(task_progress, "withdraws", 0)
              }
          end
        end)
      end
    end

    calculate :task_completed, :boolean do
      public? true
      description "任务是否完成"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn record ->
          progress = get_task_progress(user_id, record.id)

          progress.shares >= record.share_count &&
            progress.win_coins >= Decimal.to_integer(record.required_win_coins) &&
            progress.withdraws >= record.withdraw_count
        end)
      end
    end

    calculate :reward_claimed, :boolean do
      public? true
      description "奖励是否已领取"
      argument :user_id, :string

      calculation fn records, context ->
        user_id = context.arguments[:user_id]

        Enum.map(records, fn record ->
          case get_user_participation(user_id, record.id) do
            nil ->
              false

            participation ->
              claimed_tasks = get_in(participation.participation_data, ["claimed_tasks"]) || []
              Enum.member?(claimed_tasks, record.id)
          end
        end)
      end
    end
  end

  # Helper functions for free bonus task operations

  defp check_bonus_task_completed(user_id, task) do
    progress = get_task_progress(user_id, task.id)

    progress.shares >= task.share_count &&
      progress.win_coins >= Decimal.to_integer(task.required_win_coins) &&
      progress.withdraws >= task.withdraw_count
  end

  defp check_bonus_reward_claimed(user_id, task_id) do
    case get_user_participation(user_id, task_id) do
      nil ->
        false

      participation ->
        claimed_tasks = get_in(participation.participation_data, ["claimed_tasks"]) || []
        Enum.member?(claimed_tasks, task_id)
    end
  end

  defp get_task_progress(user_id, task_id) do
    case get_user_participation(user_id, task_id) do
      nil ->
        %{shares: 0, win_coins: 0, withdraws: 0}

      participation ->
        task_progress = get_in(participation.participation_data, ["task_#{task_id}"]) || %{}

        %{
          shares: Map.get(task_progress, "shares", 0),
          win_coins: Map.get(task_progress, "win_coins", 0),
          withdraws: Map.get(task_progress, "withdraws", 0)
        }
    end
  end

  defp distribute_bonus_reward(user_id, amount, description) do
    require Logger

    try do
      # Use ledger system to distribute reward
      from_account = "system:XAA:rewards"
      to_account = "user:XAA:#{user_id}"

      Logger.info("🎯 [FREE_BONUS] 开始发放奖励 - 用户: #{user_id}, 金额: #{amount}, 描述: #{description}")

      case Cypridina.Ledger.game_win(from_account, to_account, amount, description) do
        {:ok, result} ->
          Logger.info("🎯 [FREE_BONUS] 奖励发放成功 - 用户: #{user_id}, 金额: #{amount}")
          {:ok, result}

        {:error, reason} ->
          Logger.error(
            "🎯 [FREE_BONUS] 奖励发放失败 - 用户: #{user_id}, 金额: #{amount}, 原因: #{inspect(reason)}"
          )

          {:error, reason}
      end
    rescue
      e ->
        Logger.error("🎯 [FREE_BONUS] 奖励发放异常 - 用户: #{user_id}, 金额: #{amount}, 异常: #{inspect(e)}")
        {:error, :ledger_error}
    end
  end

  defp record_bonus_claim(user_id, task_id) do
    case get_user_participation(user_id, task_id) do
      nil ->
        Teen.ActivitySystem.UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: :free_bonus_task,
          progress: 1,
          status: :active,
          participation_data: %{"claimed_tasks" => [task_id]}
        })

      participation ->
        claimed_tasks = get_in(participation.participation_data, ["claimed_tasks"]) || []
        new_claimed_tasks = [task_id | claimed_tasks] |> Enum.uniq()

        new_data =
          Map.put(participation.participation_data || %{}, "claimed_tasks", new_claimed_tasks)

        Teen.ActivitySystem.UserActivityParticipation.update_progress(participation, %{
          participation_data: new_data
        })
    end
  end

  defp get_user_participation(user_id, task_id) do
    case Teen.ActivitySystem.UserActivityParticipation.list_by_user_and_activity(
           user_id,
           :free_bonus_task
         ) do
      {:ok, participations} ->
        # For free bonus tasks, we use a single participation record for all tasks
        List.first(participations)

      _ ->
        nil
    end
  end

  # New helper functions for enhanced functionality

  defp build_user_task_data(tasks, user_id) do
    tasks
    |> Enum.with_index()
    |> Enum.map(fn {task, index} ->
      task_key = "task_#{task.id}"
      progress = get_enhanced_task_progress(user_id, task.id)
      completion_status = check_task_completion_status(user_id, task)

      {
        task_key,
        %{
          "id" => task.id,
          "title" => task.title,
          "description" => generate_task_description(task),
          "required_shares" => task.share_count,
          "current_shares" => progress.shares || 0,
          "required_win_coins" => Decimal.to_integer(task.required_win_coins),
          "current_win_coins" => progress.win_coins || 0,
          "withdraw_count" => task.withdraw_count,
          "current_withdraws" => progress.withdraws || 0,
          "reward" => Decimal.to_integer(task.reward_amount),
          "status" => if(task.status == :enabled, do: 1, else: 0),
          "completed" => completion_status.completed,
          "claimed" => completion_status.claimed,
          "progress_percentage" => calculate_progress_percentage(task, progress),
          "category" => categorize_task_type(task),
          "difficulty" => assess_task_difficulty(task),
          "estimated_time" => estimate_completion_time(task, progress),
          "next_milestone" => get_next_milestone(task, progress)
        }
      }
    end)
    |> Enum.into(%{})
  end

  defp get_enhanced_task_progress(user_id, task_id) do
    base_progress = get_task_progress(user_id, task_id)

    # Add additional progress tracking
    %{
      shares: base_progress.shares,
      win_coins: base_progress.win_coins,
      withdraws: base_progress.withdraws,
      last_activity: get_last_activity_time(user_id, task_id),
      streak_days: calculate_activity_streak(user_id, task_id)
    }
  end

  defp check_task_completion_status(user_id, task) do
    completed = check_bonus_task_completed(user_id, task)
    claimed = check_bonus_reward_claimed(user_id, task.id)

    %{
      completed: completed,
      claimed: claimed,
      can_claim: completed and not claimed
    }
  end

  defp generate_task_description(task) do
    parts = []

    parts =
      if task.share_count > 0 do
        parts ++ ["分享#{task.share_count}次"]
      else
        parts
      end

    parts =
      if Decimal.to_integer(task.required_win_coins) > 0 do
        parts ++ ["赢取#{Decimal.to_integer(task.required_win_coins)}金币"]
      else
        parts
      end

    parts =
      if task.withdraw_count > 0 do
        parts ++ ["完成#{task.withdraw_count}次提现"]
      else
        parts
      end

    case parts do
      [] -> "完成任务要求"
      _ -> Enum.join(parts, "、")
    end
  end

  defp calculate_progress_percentage(task, progress) do
    total_requirements = 0
    completed_requirements = 0.0

    # Check share progress
    if task.share_count > 0 do
      total_requirements = total_requirements + 1

      completed_requirements =
        completed_requirements + min(1.0, progress.shares / task.share_count)
    end

    # Check win coins progress
    required_coins = Decimal.to_integer(task.required_win_coins)

    if required_coins > 0 do
      total_requirements = total_requirements + 1

      completed_requirements =
        completed_requirements + min(1.0, progress.win_coins / required_coins)
    end

    # Check withdrawal progress
    if task.withdraw_count > 0 do
      total_requirements = total_requirements + 1

      completed_requirements =
        completed_requirements + min(1.0, progress.withdraws / task.withdraw_count)
    end

    if total_requirements > 0 do
      round(completed_requirements / total_requirements * 100)
    else
      100
    end
  end

  defp categorize_task_type(task) do
    cond do
      task.share_count > 0 && Decimal.to_integer(task.required_win_coins) == 0 &&
          task.withdraw_count == 0 ->
        "social"

      task.share_count == 0 && Decimal.to_integer(task.required_win_coins) > 0 &&
          task.withdraw_count == 0 ->
        "gaming"

      task.share_count == 0 && Decimal.to_integer(task.required_win_coins) == 0 &&
          task.withdraw_count > 0 ->
        "financial"

      true ->
        "comprehensive"
    end
  end

  defp assess_task_difficulty(task) do
    difficulty_score = 0

    # Share difficulty
    difficulty_score = difficulty_score + min(task.share_count * 10, 30)

    # Win coins difficulty
    win_coins = Decimal.to_integer(task.required_win_coins)
    difficulty_score = difficulty_score + min(div(win_coins, 100), 40)

    # Withdrawal difficulty
    difficulty_score = difficulty_score + task.withdraw_count * 20

    cond do
      difficulty_score <= 20 -> "easy"
      difficulty_score <= 40 -> "medium"
      difficulty_score <= 70 -> "hard"
      true -> "expert"
    end
  end

  defp estimate_completion_time(task, progress) do
    # Simple estimation logic
    remaining_shares = max(0, task.share_count - progress.shares)
    remaining_coins = max(0, Decimal.to_integer(task.required_win_coins) - progress.win_coins)
    remaining_withdraws = max(0, task.withdraw_count - progress.withdraws)

    total_time_minutes =
      remaining_shares * 5 + div(remaining_coins, 100) * 10 + remaining_withdraws * 60

    cond do
      total_time_minutes <= 30 -> "30分钟内"
      total_time_minutes <= 120 -> "1-2小时"
      total_time_minutes <= 480 -> "几小时内"
      true -> "需要几天"
    end
  end

  defp get_next_milestone(task, progress) do
    cond do
      progress.shares < task.share_count ->
        "再分享#{task.share_count - progress.shares}次"

      progress.win_coins < Decimal.to_integer(task.required_win_coins) ->
        "再赢取#{Decimal.to_integer(task.required_win_coins) - progress.win_coins}金币"

      progress.withdraws < task.withdraw_count ->
        "再完成#{task.withdraw_count - progress.withdraws}次提现"

      true ->
        "可以领取奖励"
    end
  end

  defp calculate_user_task_stats(user_id, tasks) do
    total_tasks = length(tasks)

    completed_count =
      Enum.count(tasks, fn task ->
        check_bonus_task_completed(user_id, task)
      end)

    claimed_count =
      Enum.count(tasks, fn task ->
        check_bonus_reward_claimed(user_id, task.id)
      end)

    total_rewards =
      tasks
      |> Enum.filter(fn task -> check_bonus_reward_claimed(user_id, task.id) end)
      |> Enum.map(&Decimal.to_integer(&1.reward_amount))
      |> Enum.sum()

    %{
      "total_completed" => completed_count,
      "total_claimed" => claimed_count,
      "total_rewards_earned" => total_rewards,
      "completion_rate" =>
        if(total_tasks > 0, do: round(completed_count / total_tasks * 100), else: 0),
      "claim_rate" =>
        if(completed_count > 0, do: round(claimed_count / completed_count * 100), else: 0)
    }
  end

  # Stub implementations for new functions
  defp get_last_activity_time(_user_id, _task_id), do: DateTime.utc_now()
  defp calculate_activity_streak(_user_id, _task_id), do: 1

  @doc """
  Get free bonus task data for a user
  """
  def get_free_bonus_data(user_id) do
    try do
      # Get active free bonus tasks
      case list_active_tasks() do
        {:ok, tasks} when length(tasks) > 0 ->
          # Build task data with better error handling
          task_data = build_user_task_data(tasks, user_id)

          # Add user statistics
          user_stats = calculate_user_task_stats(user_id, tasks)

          {:ok,
           %{
             bonustask: task_data,
             stats: user_stats,
             total_tasks: length(tasks),
             active_tasks: Enum.count(tasks, &(&1.status == :enabled))
           }}

        {:ok, []} ->
          {:ok, build_default_bonus_data()}

        {:error, reason} ->
          require Logger
          Logger.error("Failed to get free bonus tasks: #{inspect(reason)}")
          {:ok, build_default_bonus_data()}
      end
    rescue
      e ->
        require Logger
        Logger.error("Exception getting free bonus data: #{inspect(e)}")
        {:ok, build_default_bonus_data()}
    end
  end

  defp build_default_bonus_data do
    %{
      bonustask: %{
        "default_share_task" => %{
          "id" => "default_1",
          "title" => "分享任务",
          "description" => "分享游戏给朋友获得奖励",
          "required_shares" => 3,
          "current_shares" => 0,
          "required_win_coins" => 0,
          "current_win_coins" => 0,
          "withdraw_count" => 0,
          "current_withdraws" => 0,
          "reward" => 50,
          "status" => 1,
          "completed" => false,
          "progress_percentage" => 0,
          "category" => "social",
          "difficulty" => "easy"
        }
      },
      stats: %{
        "total_completed" => 0,
        "total_rewards_earned" => 0,
        "completion_rate" => 0
      },
      total_tasks: 1,
      active_tasks: 1
    }
  end

  @doc """
  Process free bonus task reward claim
  """
  def process_free_bonus_claim(user_id, data) do
    require Logger

    # 参数验证
    task_id = Map.get(data, "task_id")

    cond do
      is_nil(task_id) or task_id == "" ->
        Logger.warning("🎯 [FREE_BONUS] 缺少task_id参数 - 用户: #{user_id}")
        {:error, %{code: 1, msg: "缺少任务ID参数"}}

      not is_binary(task_id) ->
        Logger.warning(
          "🎯 [FREE_BONUS] task_id参数格式错误 - 用户: #{user_id}, task_id: #{inspect(task_id)}"
        )

        {:error, %{code: 1, msg: "任务ID格式错误"}}

      true ->
        try do
          Logger.info("🎯 [FREE_BONUS] 开始处理免费积分领取 - 用户: #{user_id}, 任务: #{task_id}")

          # Get task config
          case read(task_id) do
            {:ok, task} ->
              Logger.info("🎯 [FREE_BONUS] 找到任务配置 - 用户: #{user_id}, 奖励: #{task.reward_amount}")

              # Use the claim_reward action
              case claim_reward(task, %{user_id: user_id}) do
                {:ok, _updated_task} ->
                  reward_amount =
                    try do
                      Decimal.to_integer(task.reward_amount)
                    rescue
                      e ->
                        Logger.error(
                          "🎯 [FREE_BONUS] Decimal转换失败 - 用户: #{user_id}, 奖励: #{task.reward_amount}, 错误: #{inspect(e)}"
                        )

                        0
                    end

                  Logger.info("🎯 [FREE_BONUS] 任务奖励领取成功 - 用户: #{user_id}, 金额: #{reward_amount}")
                  {:ok, %{code: 0, fetchaward: reward_amount, msg: "任务奖励领取成功"}}

                {:error, error} ->
                  Logger.warning(
                    "🎯 [FREE_BONUS] 任务奖励领取失败 - 用户: #{user_id}, 错误: #{inspect(error)}"
                  )

                  message =
                    case error do
                      %Ash.Error.Invalid{errors: [%{message: msg} | _]} -> msg
                      %{message: msg} when is_binary(msg) -> msg
                      _ -> "奖励领取失败"
                    end

                  {:error, %{code: 1, msg: message}}
              end

            {:error, %Ash.Error.Query.NotFound{}} ->
              Logger.warning("🎯 [FREE_BONUS] 任务不存在 - 用户: #{user_id}, task_id: #{task_id}")
              {:error, %{code: 1, msg: "任务不存在"}}

            {:error, error} ->
              Logger.error(
                "🎯 [FREE_BONUS] 查询任务失败 - 用户: #{user_id}, task_id: #{task_id}, 错误: #{inspect(error)}"
              )

              {:error, %{code: 1, msg: "查询任务失败"}}
          end
        rescue
          e ->
            Logger.error(
              "🎯 [FREE_BONUS] 处理免费积分领取异常 - 用户: #{user_id}, task_id: #{task_id}, 异常: #{inspect(e)}, 堆栈: #{Exception.format_stacktrace(__STACKTRACE__)}"
            )

            {:error, %{code: 1, msg: "服务暂时不可用，请稍后重试"}}
        end
    end
  end
end
