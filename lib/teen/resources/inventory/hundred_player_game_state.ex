defmodule Teen.Resources.Inventory.HundredPlayerGameState do
  @moduledoc """
  百人场游戏状态资源

  管理百人场游戏的实时状态数据：
  - 库存管理
  - 中心线计算
  - 暗税累计
  - 游戏结果历史
  """

  use Ash.Resource,
    domain: Teen.GameManagement,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "hundred_player_game_states"
    repo Cypridina.Repo
  end

  code_interface do
    define :create, action: :create
    define :update, action: :update
    define :read_all, action: :read

    define :get_by_game_id_and_type,
      action: :get_by_game_id_and_type,
      args: [:game_id, :game_type]

    define :list_by_game_type, action: :list_by_game_type, args: [:game_type]
    define :list_all_hundred_player_games, action: :list_all_hundred_player_games
  end

  actions do
    defaults [:read]

    create :create do
      primary? true

      accept [
        :game_id,
        :game_type,
        :current_inventory,
        :center_line,
        :dark_tax_accumulated,
        :last_result,
        :last_update
      ]
    end

    update :update do
      primary? true

      accept [
        :game_type,
        :current_inventory,
        :center_line,
        :dark_tax_accumulated,
        :last_result,
        :last_update
      ]
    end

    read :get_by_game_id_and_type do
      argument :game_id, :integer, allow_nil?: false
      argument :game_type, :string, allow_nil?: false

      filter expr(game_id == ^arg(:game_id) and game_type == ^arg(:game_type))
      get? true
    end

    read :list_by_game_type do
      argument :game_type, :string, allow_nil?: false
      filter expr(game_type == ^arg(:game_type))
    end

    read :list_all_hundred_player_games do
      description "获取所有百人场游戏状态"
    end
  end

  validations do
    validate match(:game_type, ~r/^(longhu|jhandi_munda|crash)$/) do
      message "游戏类型必须是 longhu、jhandi_munda 或 crash"
    end

    # 注意：根据文档说明，以下数值都可以为负：
    # 1. 当前库存：玩家总输赢值的总和，玩家赢了就是负数
    # 2. 中心线：当暗税比例为负值时，中心线会下降为负值
    # 3. 暗税累计：按文档说明，暗税可以为负值
    # 因此移除了这些数值的非负约束
  end

  attributes do
    uuid_primary_key :id

    attribute :game_id, :integer do
      allow_nil? false
      description "游戏ID"
    end

    attribute :game_type, :string do
      allow_nil? false
      description "游戏类型：longhu, jhandi_munda, crash"
    end

    attribute :current_inventory, :decimal do
      allow_nil? false
      default 0
      description "当前库存"
    end

    attribute :center_line, :decimal do
      allow_nil? false
      default 0
      description "中心线"
    end

    attribute :dark_tax_accumulated, :decimal do
      allow_nil? false
      default 0
      description "暗税累计"
    end

    attribute :last_result, :string do
      description "最近游戏结果"
    end

    attribute :last_update, :utc_datetime_usec do
      allow_nil? false
      default &DateTime.utc_now/0
      description "最后更新时间"
    end

    timestamps()
  end

  calculations do
    calculate :inventory_percentage, :decimal, expr(current_inventory / center_line * 100) do
      description "库存百分比"
    end

    calculate :control_level, :string do
      description "控制级别"

      calculation fn records, _ ->
        Enum.map(records, fn record ->
          percentage =
            if record.center_line > 0 do
              Decimal.to_float(record.current_inventory) / Decimal.to_float(record.center_line) *
                100
            else
              100
            end

          cond do
            percentage <= 40 -> "绝对收分"
            percentage <= 70 -> "预备收分"
            percentage >= 160 -> "绝对放分"
            percentage >= 130 -> "预备放分"
            true -> "随机"
          end
        end)
      end
    end
  end

  identities do
    identity :unique_game, [:game_id, :game_type]
  end
end
