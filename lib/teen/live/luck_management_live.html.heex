<div class="min-h-screen bg-base-200 py-6">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-base-content">幸运值管理</h1>
      <p class="mt-2 text-base-content/70">管理用户的 Teen Patti 游戏幸运值</p>
    </div>
    
<!-- 统计卡片 -->
    <div class="mb-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div class="bg-base-100 overflow-hidden shadow-lg rounded-lg border border-primary/20">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-primary-content" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-primary/70 truncate">总用户数</dt>
                <dd class="text-lg font-medium text-primary">{@stats.total_users || 0}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-base-100 overflow-hidden shadow-lg rounded-lg border border-neutral/20">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-neutral rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-neutral-content" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-neutral/70 truncate">未充值用户</dt>
                <dd class="text-lg font-medium text-neutral">{@stats.never_recharged || 0}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-base-100 overflow-hidden shadow-lg rounded-lg border border-success/20">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-success rounded-full flex items-center justify-center">
                <svg class="w-5 h-5 text-success-content" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-success/70 truncate">活跃用户</dt>
                <dd class="text-lg font-medium text-success">{@stats.active_users || 0}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-base-100 overflow-hidden shadow-lg rounded-lg border border-secondary/20">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                <svg
                  class="w-5 h-5 text-secondary-content"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-secondary/70 truncate">平均幸运值</dt>
                <dd class="text-lg font-medium text-secondary">{@stats.average_luck || 0}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
    
<!-- 搜索和筛选 -->
    <div class="mb-6 bg-base-100 shadow-lg rounded-lg p-6">
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <!-- 搜索用户 -->
        <div>
          <label class="block text-sm font-medium text-base-content">搜索用户</label>
          <form phx-submit="search" class="flex items-end space-x-2">
            <div class="flex-1">
              <input
                type="text"
                name="query"
                value={@search_query}
                placeholder="输入用户ID、昵称或手机号"
                class="mt-1 input input-bordered w-full"
              />
            </div>
            <button type="submit" class="btn btn-primary">
              搜索
            </button>
          </form>
        </div>
        
<!-- 幸运值范围筛选 -->
        <div class="col-span-2">
          <label class="block text-sm font-medium text-base-content mb-2">幸运值范围筛选</label>
          <form phx-submit="filter_luck" class="flex items-center space-x-2">
            <div class="flex-1">
              <input
                type="number"
                name="min"
                value={@filter_min_luck}
                min="-1"
                max="1000"
                placeholder="最小值"
                class="input input-bordered w-full"
              />
            </div>
            <span class="text-base-content/70">至</span>
            <div class="flex-1">
              <input
                type="number"
                name="max"
                value={@filter_max_luck}
                min="-1"
                max="1000"
                placeholder="最大值"
                class="input input-bordered w-full"
              />
            </div>
            <button type="submit" class="btn btn-success">
              筛选
            </button>
          </form>
        </div>

        <div class="flex items-end">
          <button phx-click="clear_filters" class="btn btn-ghost w-full">
            清除筛选
          </button>
        </div>
      </div>
    </div>
    
<!-- 操作按钮 -->
    <div class="mb-6 bg-base-100 shadow-lg rounded-lg p-6">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex flex-wrap gap-2">
          <button phx-click="select_all" class="btn btn-outline btn-sm">
            全选
          </button>

          <button phx-click="clear_selection" class="btn btn-ghost btn-sm">
            清除选择
          </button>
        </div>

        <div class="flex flex-wrap gap-2">
          <span class="text-sm text-base-content/70">
            已选择 {MapSet.size(@selected_users)} 个用户
          </span>

          <button phx-click="toggle_batch_form" class="btn btn-primary btn-sm">
            {if @show_batch_form, do: "取消批量操作", else: "批量重置幸运值"}
          </button>
        </div>
      </div>
      
<!-- 批量操作表单 -->
      <%= if @show_batch_form do %>
        <div class="mt-6 border-t pt-6">
          <h3 class="text-lg font-medium text-base-content mb-4">批量重置幸运值</h3>

          <form phx-submit="batch_reset" class="flex items-center space-x-4">
            <div>
              <label class="block text-sm font-medium text-base-content">新幸运值</label>
              <input
                type="number"
                name="value"
                value={@batch_value}
                min="-1"
                max="1000"
                placeholder="输入幸运值 (-1 到 1000)"
                required
                class="mt-1 input input-bordered"
              />
              <p class="mt-1 text-sm text-base-content/70">-1: 未充值, 0-1000: 幸运值</p>
            </div>

            <div class="flex items-end">
              <button type="submit" class="btn btn-error">
                确认批量重置 ({MapSet.size(@selected_users)})
              </button>
            </div>
          </form>
        </div>
      <% end %>
    </div>
    
<!-- 幸运值列表 -->
    <div class="bg-base-100 shadow-lg overflow-hidden sm:rounded-md">
      <%= if @loading do %>
        <div class="p-8 text-center">
          <div class="inline-flex items-center">
            <span class="loading loading-spinner loading-md text-primary mr-3"></span>
            <span class="text-base-content/70">加载中...</span>
          </div>
        </div>
      <% else %>
        <%= if length(@luck_records) > 0 do %>
          <ul class="divide-y divide-base-300">
            <%= for record <- @luck_records do %>
              <li class="px-6 py-4 hover:bg-base-200/50 transition-colors duration-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      phx-click="select_user"
                      phx-value-user_id={record.user.numeric_id}
                      checked={MapSet.member?(@selected_users, record.user.numeric_id)}
                      class="checkbox checkbox-sm"
                    />

                    <div class="ml-4">
                      <div class="flex items-center space-x-3">
                        <div>
                          <p class="text-sm font-medium text-base-content">
                            <%= if record.user do %>
                              <%= if record.user.profile && record.user.profile.nickname do %>
                                {record.user.profile.nickname}
                              <% else %>
                                {record.user.username || "未知用户"}
                              <% end %>
                            <% else %>
                              用户ID: {record.user.numeric_id}
                            <% end %>
                          </p>
                          <p class="text-xs text-base-content/70">
                            ID: {record.user.numeric_id}
                            <%= if record.user && record.user.phone do %>
                              | 手机: {String.slice(record.user.phone, 0..2) <>
                                "****" <> String.slice(record.user.phone, -4..-1)}
                            <% end %>
                          </p>
                        </div>
                        <span class={[
                          "text-lg font-semibold",
                          luck_value_class(record.current_luck)
                        ]}>
                          {format_luck_value(record.current_luck)}
                        </span>
                      </div>
                      <div class="flex items-center space-x-4 mt-1">
                        <p class="text-sm text-base-content/70">
                          充值次数: {format_recharge_count(Map.get(record, :recharge_count, 0))}
                        </p>
                        <p class="text-sm text-base-content/70">
                          最后更新: {format_time(record.last_updated_at)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div class="flex items-center space-x-2">
                    <!-- 单个重置表单 -->
                    <form phx-submit="reset_single">
                      <input type="hidden" name="user_id" value={record.user.numeric_id} />
                      <div class="flex items-center space-x-2">
                        <input
                          type="number"
                          name="value"
                          min="-1"
                          max="1000"
                          placeholder="新值"
                          class="input input-bordered input-xs w-20"
                        />
                        <button type="submit" class="btn btn-primary btn-xs">
                          重置
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        <% else %>
          <div class="p-8 text-center">
            <p class="text-base-content/70">暂无幸运值数据</p>
          </div>
        <% end %>
      <% end %>
    </div>
    
<!-- 分页控制 -->
    <%= if assigns[:total_records] && @total_records > @per_page do %>
      <div class="mt-6 flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <button
            phx-click="change_page"
            phx-value-page={@page - 1}
            disabled={@page <= 1}
            class="btn btn-outline btn-sm"
          >
            上一页
          </button>

          <span class="px-3 py-2 text-sm text-base-content">
            第 {@page} 页 / 共 {ceil(@total_records / @per_page)} 页
          </span>

          <button
            phx-click="change_page"
            phx-value-page={@page + 1}
            disabled={@page >= ceil(@total_records / @per_page)}
            class="btn btn-outline btn-sm"
          >
            下一页
          </button>
        </div>

        <div class="text-sm text-base-content/70">
          共 {@total_records} 条记录
        </div>
      </div>
    <% end %>
  </div>
</div>
