defmodule Teen.Live.SystemConfigLive do
  @moduledoc """
  系统配置管理页面

  提供系统级配置的查看和管理功能
  """

  use CypridinaWeb, :live_view
  require Logger

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:page_title, "系统配置")
      |> assign(:fluid?, true)
      |> assign(:current_tab, "general")
      |> assign(:config_sections, get_config_sections())
      |> assign(:show_edit_modal, false)
      |> assign(:editing_config, nil)

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :current_tab, tab)}
  end

  def handle_event("edit_config", %{"section" => section, "key" => key}, socket) do
    config_value = get_config_value(section, key)

    socket =
      socket
      |> assign(:show_edit_modal, true)
      |> assign(:editing_config, %{
        section: section,
        key: key,
        value: config_value,
        original_value: config_value
      })

    {:noreply, socket}
  end

  def handle_event("close_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_edit_modal, false)
      |> assign(:editing_config, nil)

    {:noreply, socket}
  end

  def handle_event("save_config", %{"config" => config_params}, socket) do
    editing_config = socket.assigns.editing_config

    case save_config_value(editing_config.section, editing_config.key, config_params["value"]) do
      :ok ->
        socket =
          socket
          |> assign(:show_edit_modal, false)
          |> assign(:editing_config, nil)
          |> assign(:config_sections, get_config_sections())
          |> put_flash(:info, "配置已更新")

        {:noreply, socket}

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "保存失败: #{reason}")}
    end
  end

  # 获取配置分类
  defp get_config_sections do
    %{
      "general" => %{
        name: "常规设置",
        icon: "hero-cog-6-tooth",
        configs: [
          %{
            key: "app_name",
            name: "应用名称",
            value: Application.get_env(:cypridina, :app_name, "Teen Patti Admin"),
            description: "系统显示的应用名称",
            type: :string
          },
          %{
            key: "maintenance_mode",
            name: "维护模式",
            value: Application.get_env(:cypridina, :maintenance_mode, false),
            description: "启用后系统将进入维护模式",
            type: :boolean
          },
          %{
            key: "max_concurrent_users",
            name: "最大并发用户数",
            value: Application.get_env(:cypridina, :max_concurrent_users, 10000),
            description: "系统允许的最大并发用户数",
            type: :integer
          }
        ]
      },
      "game" => %{
        name: "游戏设置",
        icon: "hero-puzzle-piece",
        configs: [
          %{
            key: "default_luck_value",
            name: "默认幸运值",
            value: Application.get_env(:teen, :default_luck_value, 750),
            description: "新用户的默认幸运值",
            type: :integer
          },
          %{
            key: "max_luck_value",
            name: "最大幸运值",
            value: Application.get_env(:teen, :max_luck_value, 1000),
            description: "幸运值的最大限制",
            type: :integer
          },
          %{
            key: "game_enabled",
            name: "游戏功能启用",
            value: Application.get_env(:teen, :game_enabled, true),
            description: "是否启用游戏功能",
            type: :boolean
          }
        ]
      },
      "payment" => %{
        name: "支付设置",
        icon: "hero-credit-card",
        configs: [
          %{
            key: "payment_enabled",
            name: "支付功能启用",
            value: Application.get_env(:teen, :payment_enabled, true),
            description: "是否启用支付功能",
            type: :boolean
          },
          %{
            key: "min_recharge_amount",
            name: "最小充值金额",
            value: Application.get_env(:teen, :min_recharge_amount, 10),
            description: "用户最小充值金额限制",
            type: :integer
          },
          %{
            key: "max_recharge_amount",
            name: "最大充值金额",
            value: Application.get_env(:teen, :max_recharge_amount, 50000),
            description: "用户最大充值金额限制",
            type: :integer
          }
        ]
      },
      "security" => %{
        name: "安全设置",
        icon: "hero-shield-check",
        configs: [
          %{
            key: "session_timeout",
            name: "会话超时时间",
            value: Application.get_env(:cypridina, :session_timeout, 3600),
            description: "用户会话超时时间（秒）",
            type: :integer
          },
          %{
            key: "max_login_attempts",
            name: "最大登录尝试次数",
            value: Application.get_env(:cypridina, :max_login_attempts, 5),
            description: "用户最大登录失败尝试次数",
            type: :integer
          },
          %{
            key: "ip_whitelist_enabled",
            name: "IP白名单启用",
            value: Application.get_env(:cypridina, :ip_whitelist_enabled, false),
            description: "是否启用管理员IP白名单",
            type: :boolean
          }
        ]
      }
    }
  end

  # 获取配置值
  defp get_config_value(section, key) do
    case section do
      "general" -> Application.get_env(:cypridina, String.to_atom(key))
      "game" -> Application.get_env(:teen, String.to_atom(key))
      "payment" -> Application.get_env(:teen, String.to_atom(key))
      "security" -> Application.get_env(:cypridina, String.to_atom(key))
      _ -> nil
    end
  end

  # 保存配置值
  defp save_config_value(section, key, value) do
    try do
      app_name =
        case section do
          "general" -> :cypridina
          "game" -> :teen
          "payment" -> :teen
          "security" -> :cypridina
          _ -> :cypridina
        end

      # 转换值类型
      converted_value = convert_value_type(value, get_config_type(section, key))

      # 更新运行时配置
      Application.put_env(app_name, String.to_atom(key), converted_value)

      Logger.info("系统配置已更新: #{section}.#{key} = #{inspect(converted_value)}")
      :ok
    rescue
      error ->
        Logger.error("保存配置失败: #{inspect(error)}")
        {:error, "配置保存失败"}
    end
  end

  # 获取配置类型
  defp get_config_type(section, key) do
    sections = get_config_sections()
    section_config = sections[section]

    if section_config do
      config = Enum.find(section_config.configs, &(&1.key == key))
      (config && config.type) || :string
    else
      :string
    end
  end

  # 转换值类型
  defp convert_value_type(value, :boolean) when is_binary(value) do
    value in ["true", "1", "on", "yes"]
  end

  defp convert_value_type(value, :integer) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> raise "Invalid integer value"
    end
  end

  defp convert_value_type(value, :string) when is_binary(value), do: value
  defp convert_value_type(value, _type), do: value

  # 格式化配置值显示
  defp format_config_value(value, :boolean), do: if(value, do: "启用", else: "禁用")
  defp format_config_value(value, :integer), do: to_string(value)
  defp format_config_value(value, :string), do: value
  defp format_config_value(value, _), do: inspect(value)

  # 获取配置名称
  defp get_config_name(section, key) do
    sections = get_config_sections()
    section_config = sections[section]

    if section_config do
      config = Enum.find(section_config.configs, &(&1.key == key))
      (config && config.name) || key
    else
      key
    end
  end

  # 获取配置描述
  defp get_config_description(section, key) do
    sections = get_config_sections()
    section_config = sections[section]

    if section_config do
      config = Enum.find(section_config.configs, &(&1.key == key))
      (config && config.description) || ""
    else
      ""
    end
  end
end
