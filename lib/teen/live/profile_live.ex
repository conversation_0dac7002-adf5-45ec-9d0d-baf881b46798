defmodule Teen.Live.ProfileLive do
  @moduledoc """
  个人信息管理页面 - 移植自RacingGame.Live.AdminPanel.ProfileComponent

  提供用户个人信息查看、密码修改等功能
  """

  use CypridinaWeb, :live_view

  alias CypridinaWeb.AuthHelper
  alias RacingGame.Bet
  alias <PERSON>pridina.Accounts.{User, AgentRelationship}
  alias Cypridina.Utils.TimeHelper
  require Ash.Query
  require Logger

  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(:page_title, "个人信息")
      |> assign(:fluid?, true)
      |> assign(:show_change_password_modal, false)
      |> assign(:show_refund_modal, false)
      |> assign(:change_password_form, %{})
      |> assign(:refund_form, %{})
      |> load_profile_data()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  def handle_event("refresh", _params, socket) do
    socket = load_profile_data(socket)
    {:noreply, socket}
  end

  def handle_event("show_change_password_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_change_password_modal, true)
      |> assign(:change_password_form, %{
        "current_password" => "",
        "new_password" => "",
        "confirm_password" => ""
      })

    {:noreply, socket}
  end

  def handle_event("hide_change_password_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_change_password_modal, false)
      |> assign(:change_password_form, %{})

    {:noreply, socket}
  end

  def handle_event("change_password", %{"password" => password_params}, socket) do
    user = socket.assigns.current_user

    case change_user_password(user, password_params) do
      {:ok, _user} ->
        socket =
          socket
          |> assign(:show_change_password_modal, false)
          |> put_flash(:info, "密码修改成功")

        {:noreply, socket}

      {:error, _reason} ->
        socket = put_flash(socket, :error, "密码修改失败，请检查当前密码是否正确")
        {:noreply, socket}
    end
  end

  def handle_event("show_refund_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_refund_modal, true)
      |> assign(:refund_form, %{
        "amount" => "",
        "reason" => ""
      })

    {:noreply, socket}
  end

  def handle_event("hide_refund_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_refund_modal, false)
      |> assign(:refund_form, %{})

    {:noreply, socket}
  end

  def handle_event("submit_refund", %{"refund" => refund_params}, socket) do
    user = socket.assigns.current_user

    case create_refund_request(user.id, refund_params) do
      {:ok, _request} ->
        socket =
          socket
          |> assign(:show_refund_modal, false)
          |> put_flash(:info, "退款申请提交成功，请等待审核")

        {:noreply, socket}

      {:error, _reason} ->
        socket = put_flash(socket, :error, "退款申请提交失败，请检查输入信息")
        {:noreply, socket}
    end
  end

  defp load_profile_data(socket) do
    user = socket.assigns.current_user

    profile_data = %{
      user: user,
      role: AuthHelper.get_permission_level_name(user),
      permissions: get_user_permissions(user),
      stats: get_user_stats(user),
      agent_info: get_agent_info(user.id)
    }

    assign(socket, :profile_data, profile_data)
  end

  defp get_user_permissions(user) do
    [
      %{name: "管理员权限", has: AuthHelper.has_permission?(user, :admin)},
      %{name: "超级管理员", has: AuthHelper.has_permission?(user, :super_admin)}
    ]
  end

  defp get_user_stats(user) do
    %{
      total_bets: get_user_bet_count(user.id),
      total_stock_transactions: get_user_stock_transaction_count(user.id),
      current_points: get_user_current_points(user.id),
      subordinates_count: get_subordinates_count(user.id)
    }
  end

  defp get_user_bet_count(user_id) do
    try do
      Bet
      |> Ash.Query.filter(user_id == ^user_id)
      |> Ash.count!()
    rescue
      _ -> 0
    end
  end

  defp get_user_stock_transaction_count(user_id) do
    # 这里需要根据实际的股票交易模型来实现
    0
  end

  defp get_user_current_points(user_id) do
    try do
      case User
           |> Ash.Query.filter(id == ^user_id)
           |> Ash.read_one() do
        {:ok, user} when not is_nil(user) ->
          # 这里需要根据实际的积分系统来获取当前积分
          0

        _ ->
          0
      end
    rescue
      _ -> 0
    end
  end

  defp get_subordinates_count(user_id) do
    try do
      AgentRelationship
      |> Ash.Query.filter(agent_id == ^user_id)
      |> Ash.count!()
    rescue
      _ -> 0
    end
  end

  defp get_agent_info(user_id) do
    try do
      case AgentRelationship
           |> Ash.Query.filter(user_id == ^user_id)
           |> Ash.read_one() do
        {:ok, relationship} when not is_nil(relationship) ->
          %{
            agent_id: relationship.agent_id,
            commission_rate: relationship.commission_rate || 0.0,
            created_at: relationship.inserted_at
          }

        _ ->
          nil
      end
    rescue
      _ -> nil
    end
  end

  defp change_user_password(user, password_params) do
    # 这里需要实现密码修改逻辑
    # 暂时返回成功，实际需要调用User的密码修改action
    {:ok, user}
  end

  defp create_refund_request(user_id, refund_params) do
    # 这里需要实现退款申请逻辑
    # 暂时返回成功，实际需要创建退款申请记录
    {:ok, %{}}
  end
end
