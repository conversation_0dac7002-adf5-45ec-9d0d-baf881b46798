<div id="user-management-container" class="h-full flex flex-col" phx-hook="BackpexDynamicLayout">
  <!-- 页面头部  -->
  <div class="flex-shrink-0 flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">用户管理</h1>
    <button phx-click="show_create_modal" class="btn btn-primary">
      <.icon name="hero-plus" class="w-4 h-4 mr-2" /> 创建用户
    </button>
  </div>
  
<!-- 搜索区域  -->
  <div class="flex-shrink-0 card bg-base-100 shadow-xl mb-6">
    <div class="card-body">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- 普通搜索 -->
        <form phx-submit="search" class="form-control">
          <label class="label">
            <span class="label-text">搜索用户</span>
          </label>
          <div class="input-group">
            <input
              type="text"
              name="search[query]"
              value={@search_query}
              placeholder="输入用户名搜索..."
              class="input input-bordered flex-1"
            />
            <button type="submit" class="btn btn-square btn-primary">
              <.icon name="hero-magnifying-glass" class="w-4 h-4" />
            </button>
          </div>
        </form>
        
<!-- 代理搜索 -->
        <form phx-submit="search_agents" class="form-control">
          <label class="label">
            <span class="label-text">搜索代理</span>
          </label>
          <div class="input-group">
            <input
              type="text"
              name="search[query]"
              value={@search_query}
              placeholder="输入代理用户名搜索..."
              class="input input-bordered flex-1"
            />
            <button type="submit" class="btn btn-square btn-secondary">
              <.icon name="hero-magnifying-glass" class="w-4 h-4" />
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  
<!-- 用户列表  -->
  <div class="flex-1 card bg-base-100 shadow-xl flex flex-col min-h-0">
    <div class="card-body flex-1 flex flex-col min-h-0">
      <%= if @users_data && length(@users_data) > 0 do %>
        <div class="flex-1 overflow-auto min-h-0">
          <table class="table table-zebra w-full">
            <thead class="sticky top-0 bg-base-100 z-10">
              <tr>
                <th>用户名</th>
                <th>数字ID</th>
                <th>权限等级</th>
                <th>代理等级</th>
                <th>注册时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <%= for user <- @users_data do %>
                <tr>
                  <td class="font-medium">{to_string(user.username || "N/A")}</td>
                  <td>{user.numeric_id || "N/A"}</td>
                  <td>
                    <span class="badge badge-outline">
                      {user.role_name || "普通用户"}
                    </span>
                  </td>
                  <td>
                    <%= if user.agent_level && user.agent_level >= 0 do %>
                      <span class="badge badge-info">代理L{user.agent_level}</span>
                    <% else %>
                      <span class="badge badge-ghost">普通用户</span>
                    <% end %>
                  </td>
                  <td class="text-sm">
                    {TimeHelper.format_local_datetime(user.inserted_at)}
                  </td>
                  <td>
                    <div class="flex flex-wrap gap-1">
                      <button
                        phx-click="show_edit_modal"
                        phx-value-user_id={to_string(user.id)}
                        class="btn btn-ghost btn-xs"
                      >
                        编辑
                      </button>
                      <.live_component
                        module={PointsHistoryComponent}
                        id={"points_history_#{user.id}"}
                        user_id={user.id}
                        current_user={@current_user}
                        show_admin_actions={true}
                        user_info={
                          %{
                            username: user.username,
                            numeric_id: user.numeric_id,
                            # 需要从实际的积分系统获取
                            current_points: 0
                          }
                        }
                      />
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
<!-- 分页 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex-shrink-0 flex justify-center mt-6 pt-4 border-t border-base-300">
            <div class="join">
              <%= for page_num <- 1..ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  class={[
                    "join-item btn",
                    if(@page_info.page == page_num, do: "btn-active", else: "")
                  ]}
                >
                  {page_num}
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="flex-1 flex items-center justify-center">
          <div class="text-center">
            <div class="text-4xl mb-4">👥</div>
            <p class="text-lg text-base-content/70">暂无用户数据</p>
            <p class="text-sm text-base-content/50">尝试调整搜索条件或创建新用户</p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- 创建用户模态框 -->
<%= if @show_create_modal do %>
  <div class="modal modal-open">
    <div class="modal-backdrop" phx-click="hide_create_modal"></div>
    <div class="modal-box max-w-2xl">
      <h3 class="font-bold text-lg mb-4">创建新用户</h3>
      <form phx-submit="create_user">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">用户名</span>
            </label>
            <input
              type="text"
              name="user[username]"
              value={@create_form["username"] || ""}
              class="input input-bordered"
              required
              minlength="3"
              maxlength="20"
              placeholder="3-20个字符"
            />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">密码</span>
            </label>
            <input
              type="password"
              name="user[password]"
              value={@create_form["password"] || ""}
              class="input input-bordered"
              required
              minlength="6"
              placeholder="至少6个字符"
            />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">用户角色</span>
            </label>
            <select name="user[role]" class="select select-bordered">
              <option value="user" selected={(@create_form["role"] || "user") == "user"}>
                普通用户
              </option>
              <option value="admin" selected={@create_form["role"] == "admin"}>管理员</option>
              <option value="super_admin" selected={@create_form["role"] == "super_admin"}>
                超级管理员
              </option>
            </select>
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">用户类型</span>
            </label>
            <select name="user[user_type]" class="select select-bordered">
              <option
                value="normal"
                selected={(@create_form["user_type"] || "normal") == "normal"}
              >
                普通用户
              </option>
              <option value="agent" selected={@create_form["user_type"] == "agent"}>代理用户</option>
            </select>
          </div>
          <div class="form-control md:col-span-2">
            <label class="label">
              <span class="label-text">初始积分</span>
            </label>
            <input
              type="number"
              name="user[initial_points]"
              class="input input-bordered"
              value={@create_form["initial_points"] || "0"}
              min="0"
              step="1"
            />
          </div>
        </div>
        <div class="modal-action">
          <button type="button" phx-click="hide_create_modal" class="btn btn-ghost">
            取消
          </button>
          <button type="submit" class="btn btn-primary">
            创建用户
          </button>
        </div>
      </form>
    </div>
  </div>
<% end %>

<!-- 编辑用户模态框 -->
<%= if @show_edit_modal && @selected_user do %>
  <div class="modal modal-open">
    <div class="modal-backdrop" phx-click="hide_edit_modal"></div>
    <div class="modal-box max-w-2xl">
      <h3 class="font-bold text-lg mb-4">编辑用户: {to_string(@selected_user.username)}</h3>
      <form phx-submit="update_user">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">用户角色</span>
            </label>
            <select name="user[role]" class="select select-bordered">
              <option value="user" selected={@edit_form["role"] == "user"}>普通用户</option>
              <option value="admin" selected={@edit_form["role"] == "admin"}>管理员</option>
              <option value="super_admin" selected={@edit_form["role"] == "super_admin"}>
                超级管理员
              </option>
            </select>
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">用户类型</span>
            </label>
            <select name="user[user_type]" class="select select-bordered">
              <option value="normal" selected={@edit_form["user_type"] == "normal"}>普通用户</option>
              <option value="agent" selected={@edit_form["user_type"] == "agent"}>代理用户</option>
            </select>
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">积分调整</span>
            </label>
            <input
              type="number"
              name="user[points_adjustment]"
              class="input input-bordered"
              value={@edit_form["points_adjustment"]}
              placeholder="正数增加，负数减少"
            />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">调整原因</span>
            </label>
            <input
              type="text"
              name="user[adjustment_reason]"
              class="input input-bordered"
              value={@edit_form["adjustment_reason"]}
              placeholder="请输入调整原因"
            />
          </div>
        </div>
        <div class="modal-action">
          <button type="button" phx-click="hide_edit_modal" class="btn btn-ghost">
            取消
          </button>
          <button type="submit" class="btn btn-primary">
            保存更改
          </button>
        </div>
      </form>
    </div>
  </div>
<% end %>
