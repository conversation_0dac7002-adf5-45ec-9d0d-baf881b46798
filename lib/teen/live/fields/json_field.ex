defmodule Teen.Live.Fields.JsonField do
  @moduledoc """
  JSON/Map 字段显示组件
  """

  use Backpex.Field
  import Phoenix.HTML.Form

  @impl Backpex.Field
  def render_value(assigns) do
    json_string =
      case assigns.value do
        nil -> "{}"
        value when is_map(value) -> Jason.encode!(value, pretty: true)
        value when is_binary(value) -> value
        _ -> inspect(assigns.value)
      end

    assigns = assign(assigns, :json_string, json_string)

    ~H"""
    <div class="json-display">
      <pre class="bg-gray-50 p-3 rounded border text-xs overflow-auto max-h-48 font-mono">
        <%= @json_string %>
      </pre>
    </div>
    """
  end

  @impl Backpex.Field
  def render_form(assigns) do
    # 将值转换为JSON字符串用于表单显示
    form_value =
      case input_value(assigns.form, assigns.name) do
        nil -> ""
        value when is_map(value) -> Jason.encode!(value, pretty: true)
        value when is_binary(value) -> value
        value -> inspect(value)
      end

    assigns = assign(assigns, :form_value, form_value)

    ~H"""
    <textarea
      id={@id}
      name={input_name(@form, @name)}
      class="textarea textarea-bordered w-full font-mono text-sm"
      rows="10"
      placeholder="请输入有效的JSON格式"
    ><%= @form_value %></textarea>
    """
  end
end
