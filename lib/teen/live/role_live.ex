defmodule Teen.Live.RoleLive do
  @moduledoc """
  角色管理页面

  提供角色的创建、查看、编辑和管理功能
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.SystemSettings.Role
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :name do
        module Backpex.Fields.Text
        label("角色名称")
        searchable(true)
        orderable(true)
        help_text("角色的显示名称，如：管理员、普通用户")
      end

      field :code do
        module Backpex.Fields.Text
        label("角色代码")
        searchable(true)
        orderable(true)
        help_text("角色的唯一标识符，如：admin、user")
      end

      field :level do
        module Backpex.Fields.Number
        label("权限级别")
        orderable(true)
        help_text("数字越大权限越高，0为最低权限")
        default(fn _assigns -> 0 end)
      end

      field :status do
        module Backpex.Fields.Select
        label("状态")
        orderable(true)
        options([
          {"启用", "active"},
          {"禁用", "inactive"}
        ])
        default(fn _assigns -> "active" end)
      end

      field :description do
        module Backpex.Fields.Textarea
        label("描述")
        help_text("角色的详细描述和权限说明")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        only([:index, :show])
        orderable(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        only([:show])
      end
    end

    # filters do
    #   filter :status do
    #     module Backpex.Filters.Select
    #     label("状态")
    #     options([
    #       {"全部", nil},
    #       {"启用", "active"},
    #       {"禁用", "inactive"}
    #     ])
    #   end

    #   filter :level do
    #     module Backpex.Filters.Range
    #     label("权限级别")
    #   end
    # end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "角色"

  @impl Backpex.LiveResource
  def plural_name, do: "角色管理"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, _item) do
    case assigns.current_user do
      %{permission_level: level} when level >= 2 ->
        # 超级管理员可以执行所有操作
        action in [:index, :show, :new, :edit, :delete]

      %{permission_level: level} when level >= 1 ->
        # 管理员只能查看和编辑，不能删除
        action in [:index, :show, :edit]

      _ ->
        # 普通用户只能查看
        action in [:index, :show]
    end
  end

  @impl Backpex.LiveResource
  def return_to(_socket, _action, _item, _live_action, _params) do
    "/admin/roles"
  end

  # 表单提交前验证
  @impl Backpex.LiveResource
  def before_create(socket, params) do
    # 确保角色代码唯一且格式正确
    params =
      params
      |> Map.update("code", "", &String.downcase/1)
      |> Map.update("code", "", &String.replace(&1, ~r/[^a-z0-9_]/, ""))

    {:ok, socket, params}
  end

  @impl Backpex.LiveResource
  def before_update(socket, params, _item) do
    # 更新时也要验证代码格式
    params =
      params
      |> Map.update("code", "", &String.downcase/1)
      |> Map.update("code", "", &String.replace(&1, ~r/[^a-z0-9_]/, ""))

    {:ok, socket, params}
  end
end
