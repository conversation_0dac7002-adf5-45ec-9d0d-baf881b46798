defmodule Teen.Live.PaymentGatewayLive do
  @moduledoc """
  支付网关管理页面

  提供支付网关的创建、查看、编辑和管理功能
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.PaymentSystem.PaymentGateway
    layout({Teen.Layouts, :admin})

    fields do
      field :name do
        searchable(true)
      end

      field :gateway_type do
        module Backpex.Fields.Select
        label("网关类型")

        options([
          {"代收", "recharge"},
          {"代付", "withdraw"}
        ])

        searchable(true)
      end

      field :gateway_url

      field :create_order_path do
        label("下单接口路径")
      end

      field :query_order_path do
        label("查询接口路径")
      end

      field :priority do
        label("优先级")
      end

      field :status do
        label("是否启用")
        searchable(true)
      end

      field :min_amount do
        label("最小金额")
      end

      field :max_amount do
        label("最大金额")
      end

      field :supported_currencies do
        module Backpex.Fields.MultiSelect
        # type :embed
        label("支持币种")
        options(fn _assigns -> [{"印度卢比", :inr}] end)

        # child_fields(
        #   currency: %{
        #     module: Backpex.Fields.Text,
        #     label: "币种"
        #   }
        # )
      end

      field :timeout_seconds do
        label("超时时间（秒）")
      end

      # field :config_data do
      #   module Backpex.Fields.InlineCRUD
      #   type :embed
      #   label("配置数据")
      #   only([:index, :show])

      #   child_fields(
      #     key: %{
      #       module: Backpex.Fields.Text,
      #       label: "键"
      #     },
      #     value: %{
      #       module: Backpex.Fields.Text,
      #       label: "值"
      #     }
      #   )
      # end

      # label "配置数据"
      # only [:index, :show]
      # render fn assigns ->
      #   value =
      #     case assigns.value do
      #       nil -> ""
      #       map when is_map(map) -> Jason.encode!(map, pretty: true)
      #       str when is_binary(str) -> str
      #       _ -> ""
      #     end

      #   assigns = assign(assigns, :formatted_value, value)

      #   ~H"""
      #   <pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto max-h-32"><%= @formatted_value %></pre>
      #   """
      # end
      # end

      # field :last_test_at do
      #   label "最后测试时间"
      #   only [:index, :show]
      # end

      # field :inserted_at do
      #   label "创建时间"
      #   only [:index, :show]
      # end

      # field :updated_at do
      #   label "更新时间"
      #   only [:show]
      # end
    end
  end

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      },
      enable_gateway: %{
        module: Teen.ItemActions.EnableGateway,
        only: [:index]
      },
      disable_gateway: %{
        module: Teen.ItemActions.DisableGateway,
        only: [:index]
      },
      test_connection: %{
        module: Teen.ItemActions.TestGatewayConnection,
        only: [:index]
      }
    ]
  end

  @impl Backpex.LiveResource
  def singular_name, do: "支付网关"

  @impl Backpex.LiveResource
  def plural_name, do: "支付网关"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end
end
