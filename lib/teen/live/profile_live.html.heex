<div class="container mx-auto p-6 max-w-6xl">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">个人信息</h1>
    <button phx-click="refresh" class="btn btn-outline btn-primary">
      <.icon name="hero-arrow-path" class="w-4 h-4 mr-2" /> 刷新
    </button>
  </div>

  <%= if @profile_data do %>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 基本信息卡片 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-header bg-base-200 p-4 rounded-t-xl">
          <h2 class="card-title text-lg font-semibold">基本信息</h2>
          <div class="flex gap-2">
            <.live_component
              module={CypridinaWeb.Components.PointsHistoryComponent}
              id="profile-points-history"
              user_id={@profile_data.user.id}
              user_info={
                %{
                  username: @profile_data.user.username,
                  numeric_id: @profile_data.user.numeric_id,
                  current_points: @profile_data.stats.current_points
                }
              }
            />
            <button
              phx-click="show_change_password_modal"
              class="btn btn-sm btn-outline btn-primary"
            >
              修改密码
            </button>
          </div>
        </div>
        <div class="card-body p-6">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-base-content/70">用户名:</span>
              <span class="font-medium">{to_string(@profile_data.user.username)}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-base-content/70">数字ID:</span>
              <span class="font-medium">{@profile_data.user.numeric_id}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-base-content/70">角色:</span>
              <span class="badge badge-primary">{@profile_data.role}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-base-content/70">注册时间:</span>
              <span class="font-medium">
                {TimeHelper.format_local_datetime(@profile_data.user.inserted_at)}
              </span>
            </div>
          </div>
        </div>
      </div>
      
<!-- 权限信息卡片 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-header bg-base-200 p-4 rounded-t-xl">
          <h2 class="card-title text-lg font-semibold">权限信息</h2>
        </div>
        <div class="card-body p-6">
          <div class="space-y-3">
            <%= for permission <- @profile_data.permissions do %>
              <div class="flex justify-between items-center">
                <span class="text-base-content/70">{permission.name}:</span>
                <%= if permission.has do %>
                  <span class="badge badge-success">
                    <.icon name="hero-check" class="w-3 h-3 mr-1" /> 已授权
                  </span>
                <% else %>
                  <span class="badge badge-ghost">
                    <.icon name="hero-x-mark" class="w-3 h-3 mr-1" /> 未授权
                  </span>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
      
<!-- 代理信息卡片 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-header bg-base-200 p-4 rounded-t-xl">
          <h2 class="card-title text-lg font-semibold">代理信息</h2>
        </div>
        <div class="card-body p-6">
          <%= if @profile_data.agent_info do %>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-base-content/70">上级代理ID:</span>
                <span class="font-medium">{@profile_data.agent_info.agent_id}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-base-content/70">抽水比例:</span>
                <span class="font-medium text-success">
                  {@profile_data.agent_info.commission_rate}%
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-base-content/70">加入时间:</span>
                <span class="font-medium">
                  {TimeHelper.format_local_datetime(@profile_data.agent_info.created_at)}
                </span>
              </div>
            </div>
          <% else %>
            <div class="text-center py-8 text-base-content/50">
              <div class="text-4xl mb-2">🏢</div>
              <p class="text-lg mb-1">您暂无上级代理</p>
              <p class="text-sm">如需加入代理体系，请联系管理员</p>
            </div>
          <% end %>
        </div>
      </div>
      
<!-- 统计信息卡片 -->
      <div class="card bg-base-100 shadow-xl lg:col-span-2">
        <div class="card-header bg-base-200 p-4 rounded-t-xl">
          <h2 class="card-title text-lg font-semibold">统计信息</h2>
        </div>
        <div class="card-body p-6">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="stat bg-primary/10 rounded-lg p-4">
              <div class="stat-title text-primary">当前积分</div>
              <div class="stat-value text-primary text-2xl">
                {@profile_data.stats.current_points}
              </div>
            </div>
            <div class="stat bg-secondary/10 rounded-lg p-4">
              <div class="stat-title text-secondary">下注次数</div>
              <div class="stat-value text-secondary text-2xl">
                {@profile_data.stats.total_bets}
              </div>
            </div>
            <div class="stat bg-accent/10 rounded-lg p-4">
              <div class="stat-title text-accent">股票交易</div>
              <div class="stat-value text-accent text-2xl">
                {@profile_data.stats.total_stock_transactions}
              </div>
            </div>
            <div class="stat bg-info/10 rounded-lg p-4">
              <div class="stat-title text-info">下线数量</div>
              <div class="stat-value text-info text-2xl">
                {@profile_data.stats.subordinates_count}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% else %>
    <div class="flex justify-center items-center h-64">
      <div class="loading loading-spinner loading-lg text-primary"></div>
      <span class="ml-4 text-base-content/70">加载中...</span>
    </div>
  <% end %>
</div>

<!-- 修改密码模态框 -->
<%= if @show_change_password_modal do %>
  <div class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">修改密码</h3>
      <form phx-submit="change_password">
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">当前密码</span>
          </label>
          <input
            type="password"
            name="password[current_password]"
            class="input input-bordered"
            required
          />
        </div>
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">新密码</span>
          </label>
          <input
            type="password"
            name="password[new_password]"
            class="input input-bordered"
            required
          />
        </div>
        <div class="form-control mb-6">
          <label class="label">
            <span class="label-text">确认新密码</span>
          </label>
          <input
            type="password"
            name="password[confirm_password]"
            class="input input-bordered"
            required
          />
        </div>
        <div class="modal-action">
          <button type="button" phx-click="hide_change_password_modal" class="btn btn-ghost">
            取消
          </button>
          <button type="submit" class="btn btn-primary">
            确认修改
          </button>
        </div>
      </form>
    </div>
  </div>
<% end %>

<!-- 退款申请模态框 -->
<%= if @show_refund_modal do %>
  <div class="modal modal-open">
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">申请退款</h3>
      <form phx-submit="submit_refund">
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">退款金额</span>
          </label>
          <input
            type="number"
            name="refund[amount]"
            class="input input-bordered"
            placeholder="请输入退款金额"
            required
          />
        </div>
        <div class="form-control mb-6">
          <label class="label">
            <span class="label-text">退款原因</span>
          </label>
          <textarea
            name="refund[reason]"
            class="textarea textarea-bordered"
            placeholder="请说明退款原因"
            required
          ></textarea>
        </div>
        <div class="modal-action">
          <button type="button" phx-click="hide_refund_modal" class="btn btn-ghost">
            取消
          </button>
          <button type="submit" class="btn btn-primary">
            提交申请
          </button>
        </div>
      </form>
    </div>
  </div>
<% end %>
