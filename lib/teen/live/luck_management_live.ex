defmodule Teen.Live.LuckManagementLive do
  @moduledoc """
  幸运值管理后台界面
  """

  use <PERSON><PERSON><PERSON>ina<PERSON><PERSON>, :live_view
  require Logger

  alias Teen.Services.LuckService
  alias Teen.UserLuckValue

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 定期更新数据
      :timer.send_interval(10000, self(), :refresh_stats)
    end

    # 初始化默认统计数据以避免KeyError
    default_stats = %{
      total_users: 0,
      never_recharged: 0,
      active_users: 0,
      average_luck: 0,
      max_luck: 0,
      min_luck: 0
    }

    socket =
      socket
      |> assign(:page_title, "幸运值管理")
      |> assign(:current_url, "/admin/luck-management")
      |> assign(:fluid?, true)
      |> assign(:luck_records, [])
      |> assign(:stats, default_stats)
      |> assign(:loading, true)
      |> assign(:selected_users, MapSet.new())
      |> assign(:search_query, "")
      |> assign(:filter_min_luck, nil)
      |> assign(:filter_max_luck, nil)
      |> assign(:page, 1)
      |> assign(:per_page, 10)
      |> assign(:show_batch_form, false)
      |> assign(:batch_value, "")
      |> assign(:total_records, 0)
      |> load_data()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("search", %{"query" => query}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("filter_luck", %{"min" => min_str, "max" => max_str}, socket) do
    min_luck =
      case Integer.parse(min_str) do
        {min, ""} -> min
        _ -> nil
      end

    max_luck =
      case Integer.parse(max_str) do
        {max, ""} -> max
        _ -> nil
      end

    socket =
      socket
      |> assign(:filter_min_luck, min_luck)
      |> assign(:filter_max_luck, max_luck)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_filters", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> assign(:filter_min_luck, nil)
      |> assign(:filter_max_luck, nil)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("select_user", %{"user_id" => user_id_str}, socket) do
    user_id = String.to_integer(user_id_str)
    selected = socket.assigns.selected_users

    new_selected =
      if MapSet.member?(selected, user_id) do
        MapSet.delete(selected, user_id)
      else
        MapSet.put(selected, user_id)
      end

    {:noreply, assign(socket, :selected_users, new_selected)}
  end

  @impl true
  def handle_event("select_all", _params, socket) do
    all_user_ids =
      socket.assigns.luck_records
      |> Enum.map(& &1.user.numeric_id)
      |> MapSet.new()

    {:noreply, assign(socket, :selected_users, all_user_ids)}
  end

  @impl true
  def handle_event("clear_selection", _params, socket) do
    {:noreply, assign(socket, :selected_users, MapSet.new())}
  end

  @impl true
  def handle_event("reset_single", %{"user_id" => user_id_str, "value" => value_str}, socket) do
    user_id = String.to_integer(user_id_str)

    case Integer.parse(value_str) do
      {value, ""} when value >= -1 and value <= 1000 ->
        # LuckService现在支持numeric_id，直接使用
        case LuckService.admin_reset_luck(user_id, value) do
          {:ok, _} ->
            Logger.info("🍀 [ADMIN] 重置用户 #{user_id} 幸运值为 #{value}")

            socket =
              socket
              |> put_flash(:info, "用户 #{user_id} 幸运值已重置为 #{value}")
              |> load_data()

            {:noreply, socket}

          {:error, reason} ->
            Logger.error("🍀 [ADMIN] 重置用户 #{user_id} 幸运值失败: #{inspect(reason)}")
            {:noreply, put_flash(socket, :error, "重置失败: #{inspect(reason)}")}
        end

      _ ->
        {:noreply, put_flash(socket, :error, "幸运值必须是 -1 到 1000 之间的整数")}
    end
  end

  @impl true
  def handle_event("toggle_batch_form", _params, socket) do
    {:noreply, assign(socket, :show_batch_form, not socket.assigns.show_batch_form)}
  end

  @impl true
  def handle_event("batch_reset", %{"value" => value_str}, socket) do
    selected = MapSet.to_list(socket.assigns.selected_users)

    if Enum.empty?(selected) do
      {:noreply, put_flash(socket, :warning, "请先选择要操作的用户")}
    else
      case Integer.parse(value_str) do
        {value, ""} when value >= -1 and value <= 1000 ->
          Logger.info("🍀 [ADMIN] 批量重置 #{length(selected)} 个用户的幸运值为 #{value}")

          # LuckService现在支持numeric_id，直接使用
          case LuckService.batch_reset_luck(selected, value) do
            {:ok, results} ->
              success_count =
                Enum.count(results, fn
                  {:ok, _} -> true
                  _ -> false
                end)

              Logger.info("🍀 [ADMIN] 批量重置完成: #{success_count}/#{length(selected)} 成功")

              socket =
                socket
                |> put_flash(:info, "批量重置完成: #{success_count}/#{length(selected)} 成功")
                |> assign(:selected_users, MapSet.new())
                |> assign(:show_batch_form, false)
                |> assign(:batch_value, "")
                |> load_data()

              {:noreply, socket}

            {:error, reason} ->
              Logger.error("🍀 [ADMIN] 批量重置失败: #{inspect(reason)}")
              {:noreply, put_flash(socket, :error, "批量重置失败: #{inspect(reason)}")}
          end

        _ ->
          {:noreply, put_flash(socket, :error, "幸运值必须是 -1 到 1000 之间的整数")}
      end
    end
  end

  @impl true
  def handle_event("change_page", %{"page" => page_str}, socket) do
    case Integer.parse(page_str) do
      {page, ""} when page > 0 ->
        socket =
          socket
          |> assign(:page, page)
          |> load_data()

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info(:refresh_stats, socket) do
    # 只更新统计数据，不更新列表
    case LuckService.get_luck_statistics() do
      {:ok, stats} ->
        {:noreply, assign(socket, :stats, stats)}

      {:error, reason} ->
        Logger.error("🍀 [ADMIN] 刷新统计数据失败: #{inspect(reason)}")
        {:noreply, socket}
    end
  end

  # 加载数据
  defp load_data(socket) do
    try do
      Logger.info("🍀 [ADMIN] 开始加载幸运值数据...")

      # 获取统计数据
      stats =
        case LuckService.get_luck_statistics() do
          {:ok, stats} ->
            Logger.info("🍀 [ADMIN] 统计数据加载成功: #{inspect(stats)}")
            stats

          {:error, reason} ->
            Logger.error("🍀 [ADMIN] 获取统计数据失败: #{inspect(reason)}")

            %{
              total_users: 0,
              never_recharged: 0,
              active_users: 0,
              average_luck: 0,
              max_luck: 0,
              min_luck: 0
            }
        end

      # 获取幸运值记录列表
      opts = build_filter_opts(socket)
      Logger.info("🍀 [ADMIN] 查询选项: #{inspect(opts)}")

      {all_records, total_count} =
        case LuckService.get_all_luck_records(opts) do
          {:ok, records} ->
            Logger.info("🍀 [ADMIN] 获取到 #{length(records)} 条幸运值记录")
            {records, length(records)}

          {:error, reason} ->
            Logger.error("🍀 [ADMIN] 获取幸运值记录失败: #{inspect(reason)}")
            {[], 0}
        end

      # 应用搜索过滤
      filtered_records = apply_search_filter(all_records, socket.assigns.search_query)
      Logger.info("🍀 [ADMIN] 搜索过滤后: #{length(filtered_records)} 条记录")

      # 分页
      page = socket.assigns.page
      per_page = socket.assigns.per_page
      offset = (page - 1) * per_page
      paginated_records = Enum.slice(filtered_records, offset, per_page)
      Logger.info("🍀 [ADMIN] 分页后: #{length(paginated_records)} 条记录 (页码: #{page})")

      # 为每个记录添加充值次数信息
      records_with_recharge_count =
        Enum.map(paginated_records, fn record ->
          recharge_count =
            case LuckService.get_user_recharge_count(record.user_id) do
              {:ok, count} -> count
              {:error, _} -> 0
            end

          Map.put(record, :recharge_count, recharge_count)
        end)

      Logger.info("🍀 [ADMIN] 数据加载完成，返回 #{length(records_with_recharge_count)} 条记录")

      socket
      |> assign(:luck_records, records_with_recharge_count)
      |> assign(:stats, stats)
      |> assign(:total_records, length(filtered_records))
      |> assign(:loading, false)
    rescue
      error ->
        Logger.error("🍀 [ADMIN] 加载数据失败: #{inspect(error)}")
        Logger.error("🍀 [ADMIN] 错误堆栈: #{inspect(__STACKTRACE__)}")

        # 设置默认统计数据以避免KeyError
        default_stats = %{
          total_users: 0,
          never_recharged: 0,
          active_users: 0,
          average_luck: 0,
          max_luck: 0,
          min_luck: 0
        }

        socket
        |> put_flash(:error, "加载数据失败: #{inspect(error)}")
        |> assign(:luck_records, [])
        |> assign(:stats, default_stats)
        |> assign(:total_records, 0)
        |> assign(:loading, false)
    end
  end

  defp build_filter_opts(socket) do
    opts = []

    opts =
      if socket.assigns.filter_min_luck,
        do: Keyword.put(opts, :min_luck, socket.assigns.filter_min_luck),
        else: opts

    opts =
      if socket.assigns.filter_max_luck,
        do: Keyword.put(opts, :max_luck, socket.assigns.filter_max_luck),
        else: opts

    opts
  end

  defp apply_search_filter(records, "") do
    records
  end

  defp apply_search_filter(records, query) do
    query_lower = String.downcase(query)

    Enum.filter(records, fn record ->
      # 安全地获取用户ID字符串
      user_id_str =
        if record.user && record.user.numeric_id do
          Integer.to_string(record.user.numeric_id)
        else
          ""
        end

      # 搜索用户ID
      id_match = String.contains?(user_id_str, query_lower)

      # 搜索用户昵称或用户名
      name_match =
        cond do
          # 确保用户信息存在
          is_nil(record.user) ->
            false

          # 检查用户名
          true ->
            # 安全地获取昵称
            nickname =
              cond do
                is_nil(record.user.profile) -> ""
                is_nil(record.user.profile.nickname) -> ""
                true -> String.downcase(record.user.profile.nickname)
              end

            # 安全地获取用户名
            username =
              cond do
                is_nil(record.user.username) -> ""
                true -> String.downcase(to_string(record.user.username))
              end

            String.contains?(nickname, query_lower) || String.contains?(username, query_lower)
        end

      # 搜索手机号
      phone_match =
        cond do
          is_nil(record.user) -> false
          is_nil(record.user.phone) -> false
          true -> String.contains?(record.user.phone, query_lower)
        end

      id_match || name_match || phone_match
    end)
  end

  # 格式化幸运值显示
  defp format_luck_value(-1), do: "未充值"
  defp format_luck_value(value), do: Integer.to_string(value)

  # 格式化充值次数显示
  defp format_recharge_count(0), do: "未充值"
  defp format_recharge_count(count), do: "#{count}次"

  # 幸运值颜色样式
  defp luck_value_class(-1), do: "text-gray-500"
  defp luck_value_class(value) when value >= 800, do: "text-green-600 font-bold"
  defp luck_value_class(value) when value >= 500, do: "text-blue-600"
  defp luck_value_class(value) when value >= 200, do: "text-yellow-600"
  defp luck_value_class(_), do: "text-red-600"

  # 格式化时间
  defp format_time(nil), do: "从未"

  defp format_time(datetime) do
    Calendar.strftime(datetime, "%Y-%m-%d %H:%M:%S")
  end

  # 通过numeric_id获取用户UUID的辅助函数

  # 辅助函数用于模板渲染
  defp luck_value_class(luck_value) do
    case luck_value do
      -1 -> "text-neutral"
      value when value >= 0 and value <= 100 -> "text-error"
      value when value > 100 and value <= 500 -> "text-warning"
      value when value > 500 and value <= 800 -> "text-success"
      value when value > 800 -> "text-primary"
      _ -> "text-base-content"
    end
  end

  defp format_luck_value(luck_value) do
    case luck_value do
      -1 -> "未充值"
      value -> to_string(value)
    end
  end

  defp format_recharge_count(count) when is_integer(count) and count > 0, do: to_string(count)
  defp format_recharge_count(_), do: "0"

  defp format_time(nil), do: "未知"

  defp format_time(datetime) do
    case DateTime.from_naive(datetime, "Etc/UTC") do
      {:ok, dt} ->
        dt
        |> DateTime.shift_zone!("Asia/Shanghai")
        |> Calendar.strftime("%Y-%m-%d %H:%M")

      _ ->
        "格式错误"
    end
  end
end
