defmodule Teen.Live.Admin.AllIconsLive do
  @moduledoc """
  显示所有可用 Heroicons 图标的页面
  """
  use CypridinaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(page_title: "所有图标")
      |> assign(search_query: "")
      |> assign(:current_url, "/admin/all-icons")
      |> assign(:fluid?, true)
      |> assign(all_icons: get_all_heroicons())
      |> assign(filtered_icons: get_all_heroicons())

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    filtered_icons = filter_icons(socket.assigns.all_icons, query)

    socket =
      socket
      |> assign(search_query: query)
      |> assign(filtered_icons: filtered_icons)

    {:ok, socket}
  end

  @impl true
  def handle_event("copy_icon", %{"icon" => icon_name}, socket) do
    socket =
      socket
      |> push_event("copy-to-clipboard", %{text: icon_name})
      |> put_flash(:info, "已复制: #{icon_name}")

    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <!-- 页面标题和搜索 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div>
              <h1 class="text-2xl font-bold flex items-center gap-2">
                <.icon name="hero-swatch" class="size-6 text-primary" /> 所有 Heroicons 图标
              </h1>
              <p class="text-base-content/70 mt-1">
                共 {length(@all_icons)} 个图标，当前显示 {length(@filtered_icons)} 个
              </p>
            </div>
            
    <!-- 搜索框 -->
            <div class="lg:w-80">
              <form phx-submit="search" phx-change="search">
                <div class="input-group">
                  <input
                    type="text"
                    name="search[query]"
                    value={@search_query}
                    placeholder="搜索图标名称..."
                    class="input input-bordered flex-1"
                  />
                  <button type="submit" class="btn btn-square btn-primary">
                    <.icon name="hero-magnifying-glass" class="size-4" />
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      
    <!-- 图标网格 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <%= if length(@filtered_icons) > 0 do %>
            <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-3">
              <%= for icon_name <- @filtered_icons do %>
                <div
                  class="flex flex-col items-center p-3 bg-base-200/30 hover:bg-base-200 rounded-lg cursor-pointer transition-all duration-200 group hover:shadow-md"
                  phx-click="copy_icon"
                  phx-value-icon={icon_name}
                  title={"点击复制: #{icon_name}"}
                >
                  <.icon
                    name={icon_name}
                    class="size-6 mb-2 text-base-content group-hover:text-primary transition-colors"
                  />
                  <code class="text-xs text-center font-mono text-base-content/60 group-hover:text-base-content transition-colors break-all leading-tight">
                    {String.replace_prefix(icon_name, "hero-", "")}
                  </code>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-12">
              <.icon name="hero-magnifying-glass" class="size-16 text-base-content/30 mx-auto mb-4" />
              <h3 class="text-lg font-semibold text-base-content/70">未找到匹配的图标</h3>
              <p class="text-base-content/50">尝试调整搜索条件</p>
            </div>
          <% end %>
        </div>
      </div>
      
    <!-- 使用说明 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title text-info">
            <.icon name="hero-information-circle" class="size-5" /> 使用说明
          </h2>
          <div class="grid md:grid-cols-2 gap-4">
            <div class="alert alert-info">
              <.icon name="hero-code-bracket" class="size-5" />
              <div>
                <h3 class="font-bold">在 LiveView 中使用</h3>
                <code class="text-sm bg-base-200 px-2 py-1 rounded block mt-1">
                  &lt;.icon name="hero-图标名" class="size-5" /&gt;
                </code>
              </div>
            </div>

            <div class="alert alert-success">
              <.icon name="hero-squares-2x2" class="size-5" />
              <div>
                <h3 class="font-bold">在 Backpex 侧边栏中使用</h3>
                <code class="text-sm bg-base-200 px-2 py-1 rounded block mt-1">
                  icon="hero-图标名"
                </code>
              </div>
            </div>
          </div>

          <div class="mt-4 p-4 bg-base-200/50 rounded-lg">
            <h4 class="font-semibold mb-2">💡 提示</h4>
            <ul class="text-sm space-y-1 text-base-content/80">
              <li>• 点击任意图标可复制其名称到剪贴板</li>
              <li>• 所有图标名称都以 "hero-" 开头</li>
              <li>• 使用 TailwindCSS 类控制图标大小：size-4, size-5, size-6 等</li>
              <li>• 使用 text-primary, text-success 等类控制图标颜色</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <script>
      window.addEventListener("phx:copy-to-clipboard", (e) => {
        const text = e.detail.text;
        if (navigator.clipboard && window.isSecureContext) {
          navigator.clipboard.writeText(text);
        } else {
          const textArea = document.createElement("textarea");
          textArea.value = text;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
        }
      });
    </script>
    """
  end

  # 获取所有 Heroicons 图标
  defp get_all_heroicons do
    [
      # A
      "hero-academic-cap",
      "hero-adjustments-horizontal",
      "hero-adjustments-vertical",
      "hero-archive-box",
      "hero-archive-box-arrow-down",
      "hero-archive-box-x-mark",
      "hero-arrow-down",
      "hero-arrow-down-circle",
      "hero-arrow-down-left",
      "hero-arrow-down-on-square",
      "hero-arrow-down-on-square-stack",
      "hero-arrow-down-right",
      "hero-arrow-down-tray",
      "hero-arrow-left",
      "hero-arrow-left-circle",
      "hero-arrow-left-on-rectangle",
      "hero-arrow-long-down",
      "hero-arrow-long-left",
      "hero-arrow-long-right",
      "hero-arrow-long-up",
      "hero-arrow-path",
      "hero-arrow-path-rounded-square",
      "hero-arrow-right",
      "hero-arrow-right-circle",
      "hero-arrow-right-on-rectangle",
      "hero-arrow-small-down",
      "hero-arrow-small-left",
      "hero-arrow-small-right",
      "hero-arrow-small-up",
      "hero-arrow-top-right-on-square",
      "hero-arrow-trending-down",
      "hero-arrow-trending-up",
      "hero-arrow-up",
      "hero-arrow-up-circle",
      "hero-arrow-up-left",
      "hero-arrow-up-on-square",
      "hero-arrow-up-on-square-stack",
      "hero-arrow-up-right",
      "hero-arrow-up-tray",
      "hero-arrow-uturn-down",
      "hero-arrow-uturn-left",
      "hero-arrow-uturn-right",
      "hero-arrow-uturn-up",
      "hero-arrows-pointing-in",
      "hero-arrows-pointing-out",
      "hero-arrows-right-left",
      "hero-arrows-up-down",
      "hero-at-symbol",

      # B
      "hero-backspace",
      "hero-backward",
      "hero-banknotes",
      "hero-bars-2",
      "hero-bars-3",
      "hero-bars-3-bottom-left",
      "hero-bars-3-bottom-right",
      "hero-bars-3-center-left",
      "hero-bars-arrow-down",
      "hero-bars-arrow-up",
      "hero-battery-0",
      "hero-battery-50",
      "hero-battery-100",
      "hero-beaker",
      "hero-bell",
      "hero-bell-alert",
      "hero-bell-slash",
      "hero-bell-snooze",
      "hero-bolt",
      "hero-bolt-slash",
      "hero-book-open",
      "hero-bookmark",
      "hero-bookmark-slash",
      "hero-bookmark-square",
      "hero-briefcase",
      "hero-bug-ant",
      "hero-building-library",
      "hero-building-office",
      "hero-building-office-2",
      "hero-building-storefront",

      # C
      "hero-cake",
      "hero-calculator",
      "hero-calendar",
      "hero-calendar-days",
      "hero-camera",
      "hero-chart-bar",
      "hero-chart-bar-square",
      "hero-chart-pie",
      "hero-chat-bubble-bottom-center",
      "hero-chat-bubble-bottom-center-text",
      "hero-chat-bubble-left",
      "hero-chat-bubble-left-ellipsis",
      "hero-chat-bubble-left-right",
      "hero-chat-bubble-oval-left",
      "hero-chat-bubble-oval-left-ellipsis",
      "hero-check",
      "hero-check-badge",
      "hero-check-circle",
      "hero-chevron-double-down",
      "hero-chevron-double-left",
      "hero-chevron-double-right",
      "hero-chevron-double-up",
      "hero-chevron-down",
      "hero-chevron-left",
      "hero-chevron-right",
      "hero-chevron-up",
      "hero-chevron-up-down",
      "hero-circle-stack",
      "hero-clipboard",
      "hero-clipboard-document",
      "hero-clipboard-document-check",
      "hero-clipboard-document-list",
      "hero-clock",
      "hero-cloud",
      "hero-cloud-arrow-down",
      "hero-cloud-arrow-up",
      "hero-code-bracket",
      "hero-code-bracket-square",
      "hero-cog",
      "hero-cog-6-tooth",
      "hero-cog-8-tooth",
      "hero-command-line",
      "hero-computer-desktop",
      "hero-cpu-chip",
      "hero-credit-card",
      "hero-cube",
      "hero-cube-transparent",
      "hero-currency-bangladeshi",
      "hero-currency-dollar",
      "hero-currency-euro",
      "hero-currency-pound",
      "hero-currency-rupee",
      "hero-currency-yen",
      "hero-cursor-arrow-rays",
      "hero-cursor-arrow-ripple",

      # D
      "hero-device-phone-mobile",
      "hero-device-tablet",
      "hero-document",
      "hero-document-arrow-down",
      "hero-document-arrow-up",
      "hero-document-chart-bar",
      "hero-document-check",
      "hero-document-duplicate",
      "hero-document-magnifying-glass",
      "hero-document-minus",
      "hero-document-plus",
      "hero-document-text",

      # E
      "hero-ellipsis-horizontal",
      "hero-ellipsis-horizontal-circle",
      "hero-ellipsis-vertical",
      "hero-envelope",
      "hero-envelope-open",
      "hero-exclamation-circle",
      "hero-exclamation-triangle",
      "hero-eye",
      "hero-eye-dropper",
      "hero-eye-slash",

      # F
      "hero-face-frown",
      "hero-face-smile",
      "hero-film",
      "hero-finger-print",
      "hero-fire",
      "hero-flag",
      "hero-folder",
      "hero-folder-arrow-down",
      "hero-folder-minus",
      "hero-folder-open",
      "hero-folder-plus",
      "hero-forward",
      "hero-funnel",
      "hero-gif",

      # G
      "hero-gift",
      "hero-gift-top",
      "hero-globe-alt",
      "hero-globe-americas",
      "hero-globe-asia-australia",
      "hero-globe-europe-africa",

      # H
      "hero-hand-raised",
      "hero-hand-thumb-down",
      "hero-hand-thumb-up",
      "hero-hashtag",
      "hero-heart",
      "hero-home",
      "hero-home-modern",

      # I
      "hero-identification",
      "hero-inbox",
      "hero-inbox-arrow-down",
      "hero-inbox-stack",
      "hero-information-circle",

      # K
      "hero-key",

      # L
      "hero-language",
      "hero-lifebuoy",
      "hero-light-bulb",
      "hero-link",
      "hero-list-bullet",
      "hero-lock-closed",
      "hero-lock-open",

      # M
      "hero-magnifying-glass",
      "hero-magnifying-glass-circle",
      "hero-magnifying-glass-minus",
      "hero-magnifying-glass-plus",
      "hero-map",
      "hero-map-pin",
      "hero-megaphone",
      "hero-microphone",
      "hero-minus",
      "hero-minus-circle",
      "hero-minus-small",
      "hero-moon",
      "hero-musical-note",

      # N
      "hero-newspaper",
      "hero-no-symbol",

      # P
      "hero-paint-brush",
      "hero-paper-airplane",
      "hero-paper-clip",
      "hero-pause",
      "hero-pause-circle",
      "hero-pencil",
      "hero-pencil-square",
      "hero-phone",
      "hero-phone-arrow-down-left",
      "hero-phone-arrow-up-right",
      "hero-phone-x-mark",
      "hero-photo",
      "hero-play",
      "hero-play-circle",
      "hero-play-pause",
      "hero-plus",
      "hero-plus-circle",
      "hero-plus-small",
      "hero-power",
      "hero-presentation-chart-bar",
      "hero-presentation-chart-line",
      "hero-printer",
      "hero-puzzle-piece",

      # Q
      "hero-qr-code",
      "hero-question-mark-circle",

      # R
      "hero-radio",
      "hero-receipt-percent",
      "hero-receipt-refund",
      "hero-rectangle-group",
      "hero-rectangle-stack",
      "hero-rocket-launch",
      "hero-rss",

      # S
      "hero-scale",
      "hero-scissors",
      "hero-server",
      "hero-server-stack",
      "hero-share",
      "hero-shield-check",
      "hero-shield-exclamation",
      "hero-shopping-bag",
      "hero-shopping-cart",
      "hero-signal",
      "hero-signal-slash",
      "hero-sparkles",
      "hero-speaker-wave",
      "hero-speaker-x-mark",
      "hero-square-2-stack",
      "hero-square-3-stack-3d",
      "hero-squares-2x2",
      "hero-squares-plus",
      "hero-star",
      "hero-stop",
      "hero-stop-circle",
      "hero-sun",
      "hero-swatch",

      # T
      "hero-table-cells",
      "hero-tag",
      "hero-ticket",
      "hero-trash",
      "hero-trophy",
      "hero-truck",
      "hero-tv",

      # U
      "hero-users",
      "hero-user-circle",
      "hero-user-group",
      "hero-user-minus",
      "hero-user-plus",
      "hero-user",

      # V
      "hero-variable",
      "hero-video-camera",
      "hero-video-camera-slash",
      "hero-view-columns",
      "hero-view-finder-circle",

      # W
      "hero-wallet",
      "hero-wifi",
      "hero-window",
      "hero-wrench",
      "hero-wrench-screwdriver",

      # X
      "hero-x-circle",
      "hero-x-mark"
    ]
    |> Enum.sort()
  end

  # 筛选图标
  defp filter_icons(icons, ""), do: icons

  defp filter_icons(icons, query) do
    query_lower = String.downcase(query)

    Enum.filter(icons, fn icon ->
      String.contains?(String.downcase(icon), query_lower)
    end)
  end
end
