defmodule Teen.Live.UserBanLive do
  @moduledoc """
  用户封禁管理页面

  提供用户封禁的创建、查看、编辑和解封功能
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.BanSystem.UserBan
    layout({Teen.Layouts, :admin})

    fields do
      field :user_id do
        module Backpex.Fields.Select
        label("用户")
        options(fn _assigns -> get_user_options() end)
        prompt("选择要封禁的用户...")
        help_text("搜索并选择要封禁的用户账号")
        searchable(true)
      end

      field :ban_type do
        module Backpex.Fields.Select
        label("封禁类型")

        options([
          {"账号封禁", 1},
          {"设备封禁", 2},
          {"IP封禁", 3},
          {"支付封禁", 4}
        ])

        searchable(true)
      end

      field :reason do
        module Backpex.Fields.Textarea
        label("封禁原因")
        searchable(true)
        help_text("详细说明封禁原因")
      end

      field :status do
        module Backpex.Fields.Select
        label("状态")

        options([
          {"已解封", 0},
          {"封禁中", 1}
        ])

        searchable(true)
      end

      field :cash_amount do
        module Backpex.Fields.Number
        label("封号时现金数")
        only([:index, :show])
        help_text("封号时用户的现金余额")
      end

      field :bank_amount do
        module Backpex.Fields.Number
        label("封号时银行数")
        only([:index, :show])
        help_text("封号时用户的银行余额")
      end

      field :ip_address do
        module Backpex.Fields.Text
        label("IP地址")
        help_text("用户封禁时的IP地址")
      end

      field :operator_id do
        module Backpex.Fields.BelongsTo
        label("操作员")
        display_field(:username)
        live_resource(Teen.Live.UserLive)
        only([:index, :show])
        help_text("执行封禁操作的管理员")
      end

      field :banned_at do
        module Backpex.Fields.DateTime
        label("封禁时间")
        only([:index, :show])
      end

      field :expires_at do
        module Backpex.Fields.DateTime
        label("到期时间")
        help_text("留空表示永久封禁")
      end

      field :unbanned_at do
        module Backpex.Fields.DateTime
        label("解封时间")
        only([:index, :show])
      end

      field :unban_operator_id do
        module Backpex.Fields.BelongsTo
        label("解封操作员")
        display_field(:username)
        live_resource(Teen.Live.UserLive)
        only([:index, :show])
      end

      field :unban_reason do
        module Backpex.Fields.Textarea
        label("解封原因")
        only([:edit, :show])
        help_text("解封时填写的原因")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        only([:show])
      end
    end

    filters do
      filter :status do
        module Teen.Live.Filters.BanStatusSelect
      end

      filter :ban_type do
        module Teen.Live.Filters.BanTypeSelect
      end
    end
  end

  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      unban_user: %{
        module: Teen.ItemActions.UnbanUser,
        only: [:index]
      }
    ]
  end

  @impl Backpex.LiveResource
  def singular_name, do: "封禁记录"

  @impl Backpex.LiveResource
  def plural_name, do: "封禁管理"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  # 表单提交前处理 - 自动设置操作员
  @impl Backpex.LiveResource
  def before_create(socket, params) do
    current_user = socket.assigns.current_user
    params = Map.put(params, "operator_id", current_user.id)
    {:ok, socket, params}
  end

  # 为新建表单提供默认值
  @impl Backpex.LiveResource
  def new(_assigns) do
    struct(Teen.BanSystem.UserBan, %{
      status: 1,
      ban_type: 1,
      banned_at: DateTime.utc_now()
    })
  end

  # 获取用户选项
  defp get_user_options do
    # 使用 Cypridina.Accounts 模块的函数
    case Cypridina.Accounts.list_all_users() do
      users when is_list(users) ->
        users
        # 限制返回数量
        |> Enum.take(100)
        |> Enum.map(fn user ->
          {"#{user.username} (ID: #{user.numeric_id})", user.id}
        end)

      _ ->
        [{"暂无用户", nil}]
    end
  end
end
