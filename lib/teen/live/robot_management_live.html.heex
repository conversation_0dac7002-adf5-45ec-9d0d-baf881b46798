<div class="min-h-screen bg-base-200">
  <div class="container mx-auto p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-base-content mb-2">机器人管理</h1>
      <p class="text-base-content/70">管理游戏机器人的创建、分配和状态</p>
    </div>
    
<!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5 mb-6">
      <!-- 总计卡片 -->
      <div class="card bg-base-100 shadow-lg border border-primary/20">
        <div class="card-body p-4">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
              <span class="text-primary-content font-bold text-sm">{@stats.total || 0}</span>
            </div>
            <div>
              <div class="text-sm text-primary font-medium">总计</div>
              <div class="text-xl font-bold text-primary">{@stats.total || 0}</div>
            </div>
          </div>
        </div>
      </div>
      
<!-- 空闲卡片 -->
      <div class="card bg-base-100 shadow-lg border border-success/20">
        <div class="card-body p-4">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-success rounded-full flex items-center justify-center mr-4">
              <span class="text-success-content font-bold text-sm">{@stats.idle || 0}</span>
            </div>
            <div>
              <div class="text-sm text-success font-medium">空闲</div>
              <div class="text-xl font-bold text-success">{@stats.idle || 0}</div>
            </div>
          </div>
        </div>
      </div>
      
<!-- 游戏中卡片 -->
      <div class="card bg-base-100 shadow-lg border border-secondary/20">
        <div class="card-body p-4">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-secondary rounded-full flex items-center justify-center mr-4">
              <span class="text-secondary-content font-bold text-sm">
                {(@stats.assigned || 0) + (@stats.in_game || 0) + (@stats.in_round || 0)}
              </span>
            </div>
            <div>
              <div class="text-sm text-secondary font-medium">游戏中</div>
              <div class="text-xl font-bold text-secondary">
                {(@stats.assigned || 0) + (@stats.in_game || 0) + (@stats.in_round || 0)}
              </div>
            </div>
          </div>
        </div>
      </div>
      
<!-- 需处理卡片 -->
      <div class="card bg-base-100 shadow-lg border border-warning/20">
        <div class="card-body p-4">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-warning rounded-full flex items-center justify-center mr-4">
              <span class="text-warning-content font-bold text-sm">
                {(@stats.recycling || 0) + (@stats.insufficient_funds || 0)}
              </span>
            </div>
            <div>
              <div class="text-sm text-warning font-medium">需处理</div>
              <div class="text-xl font-bold text-warning">
                {(@stats.recycling || 0) + (@stats.insufficient_funds || 0)}
              </div>
            </div>
          </div>
        </div>
      </div>
      
<!-- 禁用卡片 -->
      <div class="card bg-base-100 shadow-lg border border-neutral/20">
        <div class="card-body p-4">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-neutral rounded-full flex items-center justify-center mr-4">
              <span class="text-neutral-content font-bold text-sm">{@stats.disabled || 0}</span>
            </div>
            <div>
              <div class="text-sm text-neutral font-medium">禁用</div>
              <div class="text-xl font-bold text-neutral">{@stats.disabled || 0}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
<!-- 操作按钮区域 -->
    <div class="card bg-base-100 shadow-lg mb-6">
      <div class="card-body">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex flex-wrap gap-2">
            <button phx-click="toggle_create_form" class="btn btn-primary btn-sm">
              <.icon name="hero-plus" class="size-4" />
              <%= if @show_create_form do %>
                取消创建
              <% else %>
                创建机器人
              <% end %>
            </button>
            <button phx-click="select_all" class="btn btn-outline btn-sm">
              <.icon name="hero-check-circle" class="size-4" /> 全选
            </button>
            <button phx-click="clear_selection" class="btn btn-ghost btn-sm">
              <.icon name="hero-x-circle" class="size-4" /> 清除选择
            </button>
          </div>

          <div class="flex flex-wrap gap-2">
            <button phx-click="release_selected" class="btn btn-success btn-sm">
              <.icon name="hero-lock-open" class="size-4" />
              释放选中 ({MapSet.size(@selected_robots || MapSet.new())})
            </button>
            <button phx-click="recycle_selected" class="btn btn-error btn-sm">
              <.icon name="hero-arrow-path" class="size-4" />
              回收选中 ({MapSet.size(@selected_robots || MapSet.new())})
            </button>
            <button phx-click="cleanup_all_game_robots" class="btn btn-warning btn-sm">
              <.icon name="hero-wrench-screwdriver" class="size-4" /> 全局状态清理
            </button>
            <button phx-click="batch_enable_selected" class="btn btn-info btn-sm">
              <.icon name="hero-power" class="size-4" />
              启用选中 ({MapSet.size(@selected_robots || MapSet.new())})
            </button>
            <button phx-click="batch_disable_selected" class="btn btn-neutral btn-sm">
              <.icon name="hero-pause" class="size-4" />
              禁用选中 ({MapSet.size(@selected_robots || MapSet.new())})
            </button>
            <button phx-click="enable_all_robots" class="btn btn-accent btn-sm">
              <.icon name="hero-bolt" class="size-4" /> 启用全部
            </button>
            <button phx-click="show_batch_delete_confirm" class="btn btn-error btn-outline btn-sm">
              <.icon name="hero-trash" class="size-4" />
              删除选中 ({MapSet.size(@selected_robots || MapSet.new())})
            </button>
          </div>
        </div>
        
<!-- 游戏状态清理区域 -->
        <div class="divider"></div>
        <div>
          <h3 class="text-lg font-semibold mb-2">游戏状态清理</h3>
          <p class="text-sm text-base-content/70 mb-4">清理特定游戏的机器人状态，用于解决服务器重启后机器人状态不同步的问题</p>
          <div class="flex flex-wrap gap-2">
            <%= for game_type <- ["jhandi_munda", "teen_patti", "pot_blind"] do %>
              <button
                phx-click="cleanup_game_robots"
                phx-value-game_type={game_type}
                class="btn btn-outline btn-xs"
              >
                <.icon name="hero-wrench" class="size-3" />
                清理 {String.replace(game_type, "_", " ") |> String.upcase()}
              </button>
            <% end %>
          </div>
        </div>
        
<!-- 创建机器人表单 -->
        <%= if @show_create_form do %>
          <div class="divider"></div>
          <div>
            <h3 class="text-lg font-semibold mb-4">创建新机器人</h3>
            <form phx-submit="create_robot" class="space-y-4">
              <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <div class="form-control">
                  <label class="label"><span class="label-text">昵称</span></label>
                  <input
                    type="text"
                    name="nickname"
                    class="input input-bordered input-sm"
                    placeholder="留空自动生成"
                  />
                </div>
                <div class="form-control">
                  <label class="label"><span class="label-text">游戏类型</span></label>
                  <select name="game_type" class="select select-bordered select-sm">
                    <option value="jhandi_munda">Jhandi Munda</option>
                    <option value="teen_patti">Teen Patti</option>
                    <option value="pot_blind">Pot Blind</option>
                  </select>
                </div>
                <div class="form-control">
                  <label class="label"><span class="label-text">性格类型</span></label>
                  <select name="personality_type" class="select select-bordered select-sm">
                    <option value="balanced">平衡型</option>
                    <option value="aggressive">激进型</option>
                    <option value="conservative">保守型</option>
                    <option value="trend_follower">趋势型</option>
                    <option value="contrarian">逆向型</option>
                  </select>
                </div>
                <div class="form-control">
                  <label class="label"><span class="label-text">初始积分</span></label>
                  <input
                    type="number"
                    name="initial_points"
                    value="100000"
                    min="1000"
                    max="1000000"
                    class="input input-bordered input-sm"
                  />
                </div>
              </div>
              <div class="flex items-center gap-4">
                <button type="submit" class="btn btn-primary btn-sm">
                  <.icon name="hero-plus" class="size-4" /> 创建机器人
                </button>
                <div class="divider divider-horizontal"></div>
                <div class="flex items-center gap-2">
                  <span class="text-sm font-medium">批量创建：</span>
                  <form phx-submit="batch_create_robots" class="flex items-center gap-2">
                    <input
                      type="number"
                      name="count"
                      placeholder="数量"
                      min="1"
                      max="50"
                      class="input input-bordered input-xs w-20"
                    />
                    <select name="game_type" class="select select-bordered select-xs">
                      <option value="jhandi_munda">Jhandi Munda</option>
                      <option value="teen_patti">Teen Patti</option>
                      <option value="pot_blind">Pot Blind</option>
                    </select>
                    <button type="submit" class="btn btn-success btn-xs">
                      <.icon name="hero-squares-plus" class="size-3" /> 批量创建
                    </button>
                  </form>
                </div>
              </div>
            </form>
          </div>
        <% end %>
      </div>
    </div>
    
<!-- 过滤器区域 -->
    <div class="card bg-base-100 shadow-lg mb-6">
      <div class="card-body">
        <div class="space-y-4">
          <!-- 状态过滤 -->
          <div class="flex items-center gap-4">
            <span class="text-sm font-semibold">状态过滤：</span>
            <div class="flex flex-wrap gap-2">
              <%= for {status, label} <- [
                {:all, "全部"}, {:idle, "空闲"}, {:in_game, "游戏中"}, {:assigned, "已分配"},
                {:in_round, "回合中"}, {:recycling, "回收中"}, {:releasing, "释放中"}, {:insufficient_funds, "积分不足"}
              ] do %>
                <button
                  phx-click="filter_status"
                  phx-value-status={status}
                  class={[
                    "btn btn-xs",
                    if(@filter_status == status, do: "btn-primary", else: "btn-ghost")
                  ]}
                >
                  {label}
                </button>
              <% end %>
            </div>
          </div>
          
<!-- 游戏类型过滤 -->
          <div class="flex items-center gap-4">
            <span class="text-sm font-semibold">游戏过滤：</span>
            <div class="flex flex-wrap gap-2">
              <%= for {game_type, label} <- [
                {:all, "全部"}, {"jhandi_munda", "Jhandi Munda"}, {"teen_patti", "Teen Patti"}, {"pot_blind", "Pot Blind"}
              ] do %>
                <button
                  phx-click="filter_game_type"
                  phx-value-game_type={game_type}
                  class={[
                    "btn btn-xs",
                    if(@filter_game_type == game_type, do: "btn-success", else: "btn-ghost")
                  ]}
                >
                  {label}
                </button>
              <% end %>
            </div>
          </div>
          
<!-- 启用状态过滤 -->
          <div class="flex items-center gap-4">
            <span class="text-sm font-semibold">启用状态：</span>
            <div class="flex flex-wrap gap-2">
              <%= for {enabled, label} <- [{:all, "全部"}, {:enabled, "已启用"}, {:disabled, "已禁用"}] do %>
                <button
                  phx-click="filter_enabled"
                  phx-value-enabled={enabled}
                  class={[
                    "btn btn-xs",
                    if(@filter_enabled == enabled, do: "btn-secondary", else: "btn-ghost")
                  ]}
                >
                  {label}
                </button>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
    
<!-- 机器人列表 -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <%= if @loading do %>
          <div class="p-8 text-center">
            <span class="loading loading-spinner loading-md text-primary mr-3"></span>
            <span class="text-base-content/70">加载中...</span>
          </div>
        <% else %>
          <%= if length(@robots || []) > 0 do %>
            <div class="overflow-x-auto">
              <table class="table table-zebra table-sm">
                <thead>
                  <tr>
                    <th>
                      <input type="checkbox" class="checkbox checkbox-sm" phx-click="select_all" />
                    </th>
                    <th>机器人ID</th>
                    <th>昵称</th>
                    <th>状态</th>
                    <th>积分</th>
                    <th>游戏类型</th>
                    <th>房间ID</th>
                    <th>启用状态</th>
                    <th>最后更新</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <%= for robot <- (@robots || []) do %>
                    <tr>
                      <td>
                        <input
                          type="checkbox"
                          class="checkbox checkbox-sm"
                          phx-click="select_robot"
                          phx-value-robot_id={robot.robot_id}
                          checked={
                            MapSet.member?(@selected_robots || MapSet.new(), robot.robot_id)
                          }
                        />
                      </td>
                      <td class="font-mono text-sm">#{robot.robot_id}</td>
                      <td class="font-medium">{robot.nickname}</td>
                      <td>
                        <div class={[
                          "badge badge-sm",
                          case robot.status do
                            "idle" -> "badge-success"
                            "in_game" -> "badge-primary"
                            "assigned" -> "badge-info"
                            "in_round" -> "badge-warning"
                            "recycling" -> "badge-error"
                            "insufficient_funds" -> "badge-warning"
                            _ -> "badge-neutral"
                          end
                        ]}>
                          {status_text(robot.status)}
                        </div>
                      </td>
                      <td class="font-mono text-sm">{format_number(robot.current_points)}</td>
                      <td>
                        <%= if robot.current_game_type do %>
                          <div class="badge badge-outline badge-sm">
                            {robot.current_game_type}
                          </div>
                        <% else %>
                          <span class="text-base-content/50">-</span>
                        <% end %>
                      </td>
                      <td>
                        <%= if robot.current_room_id do %>
                          <span class="font-mono text-sm">{robot.current_room_id}</span>
                        <% else %>
                          <span class="text-base-content/50">-</span>
                        <% end %>
                      </td>
                      <td>
                        <%= if robot.is_enabled do %>
                          <div class="badge badge-success badge-sm">已启用</div>
                        <% else %>
                          <div class="badge badge-neutral badge-sm">已禁用</div>
                        <% end %>
                      </td>
                      <td class="text-xs text-base-content/70">
                        <%= if robot.status_changed_at do %>
                          {Calendar.strftime(robot.status_changed_at, "%m-%d %H:%M")}
                        <% else %>
                          -
                        <% end %>
                      </td>
                      <td>
                        <div class="flex items-center gap-1">
                          <%= if robot.is_enabled do %>
                            <button
                              phx-click="disable_robot"
                              phx-value-robot_id={robot.robot_id}
                              class="btn btn-neutral btn-xs"
                              title="禁用机器人"
                            >
                              <.icon name="hero-pause" class="size-3" />
                            </button>
                          <% else %>
                            <button
                              phx-click="enable_robot"
                              phx-value-robot_id={robot.robot_id}
                              class="btn btn-success btn-xs"
                              title="启用机器人"
                            >
                              <.icon name="hero-play" class="size-3" />
                            </button>
                          <% end %>
                          <button
                            phx-click="recycle_robot"
                            phx-value-robot_id={robot.robot_id}
                            class="btn btn-warning btn-xs"
                            title="回收机器人"
                          >
                            <.icon name="hero-arrow-path" class="size-3" />
                          </button>
                          <button
                            phx-click="show_delete_confirm"
                            phx-value-robot_id={robot.robot_id}
                            phx-value-robot_name={robot.nickname}
                            class="btn btn-error btn-xs"
                            title="删除机器人"
                          >
                            <.icon name="hero-trash" class="size-3" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          <% else %>
            <div class="p-8 text-center text-base-content/50">
              <.icon name="hero-cpu-chip" class="size-12 mx-auto mb-2" />
              <p>暂无机器人数据</p>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
    
<!-- 分页控制 -->
    <%= if (@total_pages || 0) > 1 do %>
      <div class="mt-6 flex items-center justify-center">
        <div class="join">
          <button
            phx-click="change_page"
            phx-value-page={(@page || 1) - 1}
            disabled={(@page || 1) <= 1}
            class="join-item btn btn-sm"
          >
            <.icon name="hero-chevron-left" class="size-4" />上一页
          </button>
          <button class="join-item btn btn-sm btn-active">
            第 {@page || 1} 页 / 共 {@total_pages || 1} 页
          </button>
          <button
            phx-click="change_page"
            phx-value-page={(@page || 1) + 1}
            disabled={(@page || 1) >= (@total_pages || 1)}
            class="join-item btn btn-sm"
          >
            下一页<.icon name="hero-chevron-right" class="size-4" />
          </button>
        </div>
      </div>
    <% end %>
    
<!-- 删除确认模态框 -->
    <%= if @show_delete_confirm do %>
      <div class="modal modal-open">
        <div class="modal-box">
          <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-error/20 mb-4">
              <.icon name="hero-exclamation-triangle" class="size-6 text-error" />
            </div>
            <h3 class="text-lg font-semibold mb-2">确认删除</h3>
            <p class="text-sm text-base-content/70 mb-6">
              <%= if @delete_type == :batch do %>
                确定要删除选中的 {length(@delete_target || [])} 个机器人吗？此操作不可撤销。
              <% else %>
                确定要删除机器人 "{(@delete_target || %{}).name || "未知"}" 吗？此操作不可撤销。
              <% end %>
            </p>
            <div class="modal-action justify-center">
              <button phx-click="cancel_delete" class="btn btn-ghost">取消</button>
              <button phx-click="confirm_delete" class="btn btn-error">
                <.icon name="hero-trash" class="size-4" />删除
              </button>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
