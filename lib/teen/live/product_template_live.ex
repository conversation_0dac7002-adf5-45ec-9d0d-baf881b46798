defmodule Teen.Live.ProductTemplateLive do
  @moduledoc """
  商品模板管理页面

  提供商品模板的创建、查看、编辑和管理功能
  """

  use AshBackpex.LiveResource
  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ShopSystem.ProductTemplate
    layout({Teen.Layouts, :admin})

    @impl Backpex.LiveResource
    def singular_name, do: "商品模板"

    @impl Backpex.LiveResource
    def plural_name, do: "商品模板"

    @impl Backpex.LiveResource
    def mount(_params, _session, socket) do
      socket = assign(socket, :fluid?, true)
      {:ok, socket}
    end

    @impl Backpex.LiveResource
    def can?(assigns, action, item) do
      case action do
        :index -> true
        :show -> true
        :new -> true
        :edit -> true
        :delete -> true
        _ -> false
      end
    end

    fields do
      field :template_name do
        module Backpex.Fields.Text
        label("模板名称")
        searchable(true)
        orderable(true)
      end

      field :product_type do
        module Backpex.Fields.Select
        label("商品类型")

        options([
          {"月卡", :monthly_card},
          {"周卡", :weekly_card},
          {"次卡", :play_card},
          {"金币礼包", :coin_package},
          {"VIP礼包", :vip_package},
          {"特殊道具", :special_item},
          {"充值奖励包", :recharge_bonus}
        ])

        searchable(true)
        orderable(true)
      end

      field :is_default do
        module Backpex.Fields.Boolean
        label("默认模板")
        orderable(true)
      end

      field :default_config do
        module Backpex.Fields.Textarea
        label("默认配置 (JSON)")
        only([:show, :edit, :new])

        render(fn assigns ->
          config_json = Jason.encode!(assigns.value, pretty: true)
          assigns = assign(assigns, :config_json, config_json)

          ~H"""
          <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-60"><%= @config_json %></pre>
          """
        end)
      end

      field :config_schema do
        module Backpex.Fields.Textarea
        label("配置说明 (JSON)")
        only([:show, :edit, :new])

        render(fn assigns ->
          if assigns.value && assigns.value != %{} do
            schema_json = Jason.encode!(assigns.value, pretty: true)
            assigns = assign(assigns, :schema_json, schema_json)

            ~H"""
            <pre class="bg-blue-50 p-2 rounded text-sm overflow-auto max-h-40"><%= @schema_json %></pre>
            """
          else
            ~H"""
            <span class="text-gray-500">无配置说明</span>
            """
          end
        end)
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
        orderable(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:show])
        orderable(true)
      end
    end

    filters do
      filter :product_type do
        module Teen.Live.Filters.ProductTypeSelect
      end

      filter :is_default do
        module Teen.Live.Filters.BooleanSelect
      end
    end
  end

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      }
    ]
  end

  # @impl Backpex.LiveResource
  # def metrics do
  #   [
  #     total_templates: %{
  #       module: Backpex.Metrics.Simple,
  #       label: "模板总数",
  #       value: fn _socket ->
  #         case Teen.ShopSystem.ProductTemplate.read() do
  #           {:ok, templates} -> length(templates)
  #           {:error, _} -> 0
  #         end
  #       end
  #     }
  #   ]
  # end
end
