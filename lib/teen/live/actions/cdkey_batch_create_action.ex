defmodule Teen.Live.Actions.CdkeyBatchCreateAction do
  use Backpex.ResourceAction
  import Ecto.Changeset
  require Logger

  @impl Backpex.ResourceAction
  def label(), do: "批量创建"

  @impl Backpex.ResourceAction
  def title(), do: "批量创建CDKEY"

  @impl Backpex.ResourceAction
  def fields do
    [
      count: %{
        module: Backpex.Fields.Number,
        label: "生成数量",
        type: :integer,
        default: fn _assigns -> 10 end,
        required: true
      },
      prefix: %{
        module: Backpex.Fields.Text,
        label: "前缀",
        type: :string,
        default: fn _assigns -> "CDKEY" end,
        required: true
      },
      max_uses: %{
        module: Backpex.Fields.Number,
        label: "最大使用次数",
        type: :integer,
        default: fn _assigns -> 1 end,
        required: true
      },
      valid_days: %{
        module: Backpex.Fields.Number,
        label: "有效天数",
        type: :integer,
        default: fn _assigns -> 30 end,
        required: true
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金币",
        type: :integer,
        default: fn _assigns -> 100 end,
        required: true
      }
    ]
  end

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    Logger.info("BatchCreateAction changeset called with attrs: #{inspect(change)}")

    change
    |> cast(attrs, [:count, :prefix, :max_uses, :valid_days, :reward_amount])
    |> validate_required([:count, :prefix, :max_uses, :valid_days, :reward_amount])
    |> validate_number(:count, greater_than: 0, less_than_or_equal_to: 1000)
    |> validate_number(:max_uses, greater_than: 0)
    |> validate_number(:valid_days, greater_than: 0)
    |> validate_number(:reward_amount, greater_than: 0)
    |> validate_length(:prefix, min: 1, max: 10)
  end

  # 修正函数签名：handle(socket, data) 而不是 handle(socket, params)
  @impl Backpex.ResourceAction
  def handle(socket, data) do
    Logger.info("BatchCreateAction handle called with data: #{inspect(data)}")

    # data 是从 changeset 中提取的验证后的数据
    count = Map.get(data, :count, 10)
    prefix = Map.get(data, :prefix, "CDKEY")
    max_uses = Map.get(data, :max_uses, 1)
    valid_days = Map.get(data, :valid_days, 30)
    reward_amount = Map.get(data, :reward_amount, 100)

    reward_config = %{
      type: :coins,
      amount: reward_amount,
      items: %{},
      max_uses: max_uses,
      valid_from: DateTime.utc_now(),
      valid_to: DateTime.add(DateTime.utc_now(), valid_days * 24 * 60 * 60, :second),
      creator: "管理员",
      purpose: "批量创建"
    }

    Logger.info("开始批量创建CDKEY: 数量=#{count}, 前缀=#{prefix}")

    # 生成兑换码数据
    batch_name = "#{prefix}_#{DateTime.to_unix(DateTime.utc_now())}"
    codes = generate_codes(prefix, count)

    # 准备批量创建的数据
    cdkey_data =
      Enum.map(codes, fn code ->
        %{
          code: code,
          max_uses: max_uses,
          rewards: [
            Teen.Types.Reward.new(:coins, reward_amount)
          ],
          valid_from: DateTime.utc_now(),
          valid_to: DateTime.add(DateTime.utc_now(), valid_days * 24 * 60 * 60, :second),
          creator: "管理员"
        }
      end)

    # 使用 Ash.bulk_create 批量创建
    case Ash.bulk_create(cdkey_data, Teen.ActivitySystem.Cdkey, :create) do
      %Ash.BulkResult{records: records, errors: []} when is_list(records) ->
        message = "成功创建 #{length(records)} 个CDKEY，批次: #{batch_name}"
        Logger.info("批量创建成功: #{message}")
        {:ok, socket |> Phoenix.LiveView.put_flash(:info, message)}

      %Ash.BulkResult{records: records, errors: errors} ->
        success_count = if is_list(records), do: length(records), else: 0
        error_count = if is_list(errors), do: length(errors), else: 0
        message = "创建完成：成功 #{success_count} 个，失败 #{error_count} 个"
        Logger.warn("批量创建部分失败: #{message}")
        Logger.error("批量创建失败详情: #{inspect(records)}")
        Logger.error("批量创建失败详情: #{inspect(errors)}")
        {:ok, socket |> Phoenix.LiveView.put_flash(:warning, message)}

      {:error, reason} ->
        error_message = "批量创建失败: #{inspect(reason)}"
        Logger.error(error_message)
        {:ok, socket |> Phoenix.LiveView.put_flash(:error, error_message)}

      result ->
        error_message = "批量创建返回意外结果: #{inspect(result)}"
        Logger.error(error_message)
        {:ok, socket |> Phoenix.LiveView.put_flash(:error, "批量创建失败，请检查日志")}
    end
  end

  defp generate_codes(prefix, count) do
    charset = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"

    for _ <- 1..count do
      suffix =
        for _ <- 1..8, into: "" do
          <<Enum.random(String.to_charlist(charset))>>
        end

      "#{prefix}#{suffix}"
    end
    |> Enum.uniq()
    |> Enum.take(count)
  end
end
