defmodule Teen.Live.Filters.UserPermissionFilter do
  @moduledoc """
  用户角色过滤器

  用于在用户管理页面中按角色过滤用户
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "用户角色"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"全部", nil},
      {"普通用户", "user"},
      {"管理员", "admin"},
      {"超级管理员", "super_admin"}
    ]
  end

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input field={@form[:value]} type="select" options={options()} prompt="选择用户角色" />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource) do
    import Ash.Query

    # Filter by role code through the roles relationship
    query
    |> load(:roles)
    |> filter(exists(roles, code == ^value))
  end
end
