defmodule Teen.Live.Filters.ProductStatusSelect do
  @moduledoc """
  商品状态选择过滤器
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "商品状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择商品状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"上架", :active},
      {"下架", :inactive},
      {"售罄", :sold_out}
    ]
  end
end
