defmodule Teen.Live.Filters.BanStatusSelect do
  @moduledoc """
  封禁状态选择过滤器
  """

  use Backpex.Filters.Select
  import Ash.Query

  @impl Backpex.Filter
  def label, do: "封禁状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择封禁状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"已解封", 0},
      {"封禁中", 1}
    ]
  end

  @impl Backpex.Filter
  def filter(query, value, _assigns) do
    case value do
      nil ->
        query

      status_value when status_value in [0, 1] ->
        filter(query, status == ^status_value)

      _ ->
        query
    end
  end
end
