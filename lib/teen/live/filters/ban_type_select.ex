defmodule Teen.Live.Filters.BanTypeSelect do
  @moduledoc """
  封禁类型选择过滤器
  """

  use Backpex.Filters.Select
  import Ash.Query

  @impl Backpex.Filter
  def label, do: "封禁类型"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择封禁类型..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"账号封禁", 1},
      {"设备封禁", 2},
      {"IP封禁", 3},
      {"支付封禁", 4}
    ]
  end

  @impl Backpex.Filter
  def filter(query, value, _assigns) do
    case value do
      nil ->
        query

      ban_type_value when ban_type_value in [1, 2, 3, 4] ->
        filter(query, ban_type == ^ban_type_value)

      _ ->
        query
    end
  end
end
