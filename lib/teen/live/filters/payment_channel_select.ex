defmodule Teen.Live.Filters.PaymentChannelSelect do
  @moduledoc """
  支付渠道选择过滤器
  """

  use Backpex.Filters.Select
  import Ash.Query

  @impl Backpex.Filter
  def label, do: "支付渠道"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择支付渠道..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    # 获取可用的支付渠道
    case Teen.PaymentSystem.PaymentGateway.list_active() do
      {:ok, gateways} ->
        options = [{"全部", nil}]

        gateway_options =
          gateways
          |> Enum.map(fn gateway -> {gateway.gateway_name, gateway.id} end)

        options ++ gateway_options

      {:error, _} ->
        [{"全部", nil}]
    end
  end

  @impl Backpex.Filter
  def filter(query, value, _assigns) do
    case value do
      nil ->
        query

      channel_id when is_binary(channel_id) ->
        filter(query, gateway_config_id == ^channel_id)

      _ ->
        query
    end
  end
end
