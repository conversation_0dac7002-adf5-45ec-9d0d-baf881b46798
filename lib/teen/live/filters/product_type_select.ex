defmodule Teen.Live.Filters.ProductTypeSelect do
  @moduledoc """
  商品类型选择过滤器
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "商品类型"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择商品类型..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"月卡", :monthly_card},
      {"周卡", :weekly_card},
      {"次卡", :play_card},
      {"金币礼包", :coin_package},
      {"VIP礼包", :vip_package},
      {"特殊道具", :special_item},
      {"充值奖励包", :recharge_bonus}
    ]
  end
end
