defmodule Teen.Live.Filters.ProgressStatusSelect do
  @moduledoc """
  处理状态选择过滤器
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "处理状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择处理状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"排队中", "0"},
      {"处理中", "1"},
      {"支付成功", "2"},
      {"支付失败", "3"},
      {"人工处理", "4"},
      {"已取消", "5"}
    ]
  end
end
