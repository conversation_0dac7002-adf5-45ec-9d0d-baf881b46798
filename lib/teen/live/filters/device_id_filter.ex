defmodule Teen.Live.Filters.DeviceIdFilter do
  @moduledoc """
  设备ID过滤器

  用于在设备管理页面中按设备ID过滤设备
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "设备ID"

  @impl Backpex.Filter
  def type, do: :text

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def render(assigns) do
    ~H"""
    <div>
      <.input field={@form[@field]} type="text" placeholder="输入设备ID" />
    </div>
    """
  end

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input field={@form[:value]} type="text" placeholder="输入设备ID" />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource, _socket) do
    import Ash.Query

    query
    |> filter(contains(device_id, ^value))
  end
end
