defmodule Teen.Live.Filters.PurchasePaymentStatusSelect do
  @moduledoc """
  购买记录支付状态选择过滤器
  """

  use Backpex.Filters.Select
  import Ash.Query

  @impl Backpex.Filter
  def label, do: "支付状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择支付状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"待支付", :pending},
      {"已完成", :completed},
      {"支付失败", :failed},
      {"已退款", :refunded}
    ]
  end

  @impl Backpex.Filter
  def filter(query, value, _assigns) do
    case value do
      nil ->
        query

      status when is_atom(status) ->
        filter(query, payment_status == ^status)

      _ ->
        query
    end
  end
end
