defmodule Teen.Live.Filters.BooleanSelect do
  @moduledoc """
  布尔值选择过滤器
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "布尔值"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"是", true},
      {"否", false}
    ]
  end
end
