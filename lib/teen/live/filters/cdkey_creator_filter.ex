defmodule Teen.Live.Filters.CdkeyCreatorFilter do
  use Backpex.Filter
  import Ash.Expr
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "创建人"

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <.input field={@form[:value]} type="text" placeholder="输入创建人" />
    """
  end

  @impl Backpex.Filter
  def filter(query, %{"value" => value}) when value != "" do
    Ash.Query.filter(query, expr(contains(creator, ^value)))
  end

  def filter(query, _params), do: query
end
