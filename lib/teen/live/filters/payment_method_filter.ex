defmodule Teen.Live.Filters.PaymentMethodSelect do
  @moduledoc """
  支付方式选择过滤器
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "支付方式"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择支付方式..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"银行卡", "bank_card"},
      {"支付宝", "alipay"},
      {"UPI", "upi"}
    ]
  end
end
