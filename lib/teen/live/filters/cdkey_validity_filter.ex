defmodule Teen.Live.Filters.CdkeyValidityFilter do
  use Backpex.Filter
  import Ash.Expr
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "有效性"

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <.input
      field={@form[:value]}
      type="select"
      options={[
        {"全部", ""},
        {"有效", "valid"},
        {"已过期", "expired"},
        {"未生效", "not_started"}
      ]}
      prompt="选择有效性"
    />
    """
  end

  @impl Backpex.Filter
  def filter(query, %{"value" => "valid"}) do
    now = DateTime.utc_now()
    Ash.Query.filter(query, expr(valid_from <= ^now and valid_to >= ^now))
  end

  def filter(query, %{"value" => "expired"}) do
    now = DateTime.utc_now()
    Ash.Query.filter(query, expr(valid_to < ^now))
  end

  def filter(query, %{"value" => "not_started"}) do
    now = DateTime.utc_now()
    Ash.Query.filter(query, expr(valid_from > ^now))
  end

  def filter(query, _params), do: query
end
