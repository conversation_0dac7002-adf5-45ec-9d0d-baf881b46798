defmodule Teen.Live.Filters.DeliveryStatusSelect do
  @moduledoc """
  商品发放状态选择过滤器
  """

  use Backpex.Filters.Select
  import Ash.Query

  @impl Backpex.Filter
  def label, do: "发放状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择发放状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"待发放", :pending},
      {"已发放", :delivered},
      {"发放失败", :failed},
      {"已取消", :cancelled}
    ]
  end

  @impl Backpex.Filter
  def filter(query, value, _assigns) do
    case value do
      nil ->
        query

      status when is_atom(status) ->
        filter(query, delivery_status == ^status)

      _ ->
        query
    end
  end
end
