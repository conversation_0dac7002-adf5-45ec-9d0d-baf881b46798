defmodule Teen.Live.Filters.CurrencySelect do
  @moduledoc """
  货币类型选择过滤器
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "货币类型"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择货币类型..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"印度卢比", :inr},
      {"美元", :usd},
      {"人民币", :cny}
    ]
  end
end
