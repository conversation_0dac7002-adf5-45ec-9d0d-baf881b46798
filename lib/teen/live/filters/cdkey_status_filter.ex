defmodule Teen.Live.Filters.CdkeyStatusFilter do
  use Backpex.Filter
  import Ash.Expr
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <.input
      field={@form[:value]}
      type="select"
      options={[
        {"全部", ""},
        {"激活", "active"},
        {"停用", "inactive"},
        {"过期", "expired"}
      ]}
      prompt="选择状态"
    />
    """
  end

  @impl Backpex.Filter
  def filter(query, %{"value" => value}) when value != "" do
    status = String.to_atom(value)
    Ash.Query.filter(query, expr(status == ^status))
  end

  def filter(query, _params), do: query
end
