defmodule Teen.Live.Filters.StatusSelect do
  @moduledoc """
  状态选择过滤器
  用于过滤支付配置的启用/禁用状态
  """

  use Backpex.Filters.Select
  import Ash.Query

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"启用", 1},
      {"禁用", 0}
    ]
  end

  @impl Backpex.Filter
  def filter(query, value, _assigns) do
    case value do
      nil ->
        query

      status_value when status_value in [0, 1] ->
        filter(query, status == ^status_value)

      _ ->
        query
    end
  end
end
