defmodule Teen.Live.Filters.CdkeyClaimRecordFilters do
  @moduledoc """
  CDKEY领取记录相关过滤器
  """

  defmodule CdkeyCodeFilter do
    use Backpex.Filter
    import Ash.Expr
    import Phoenix.Component
    import CypridinaWeb.CoreComponents

    @impl Backpex.Filter
    def label, do: "CDKEY代码"

    @impl Backpex.Filter
    def render_form(assigns) do
      ~H"""
      <.input field={@form[:value]} type="text" placeholder="输入CDKEY代码" />
      """
    end

    @impl Backpex.Filter
    def filter(query, %{"value" => value}) when value != "" do
      # 通过关联查询过滤
      Ash.Query.filter(query, expr(cdkey.code == ^value))
    end

    def filter(query, _params), do: query
  end

  defmodule UsernameFilter do
    use Backpex.Filter
    import Ash.Expr
    import Phoenix.Component
    import CypridinaWeb.CoreComponents

    @impl Backpex.Filter
    def label, do: "用户名"

    @impl Backpex.Filter
    def render_form(assigns) do
      ~H"""
      <.input field={@form[:value]} type="text" placeholder="输入用户名" />
      """
    end

    @impl Backpex.Filter
    def filter(query, %{"value" => value}) when value != "" do
      Ash.Query.filter(query, expr(contains(username, ^value)))
    end

    def filter(query, _params), do: query
  end

  defmodule IpAddressFilter do
    use Backpex.Filter
    import Ash.Expr
    import Phoenix.Component
    import CypridinaWeb.CoreComponents

    @impl Backpex.Filter
    def label, do: "IP地址"

    @impl Backpex.Filter
    def render_form(assigns) do
      ~H"""
      <.input field={@form[:value]} type="text" placeholder="输入IP地址" />
      """
    end

    @impl Backpex.Filter
    def filter(query, %{"value" => value}) when value != "" do
      Ash.Query.filter(query, expr(contains(ip_address, ^value)))
    end

    def filter(query, _params), do: query
  end
end