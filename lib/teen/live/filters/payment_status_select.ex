defmodule Teen.Live.Filters.PaymentStatusSelect do
  @moduledoc """
  支付订单状态选择过滤器
  """

  use Backpex.Filters.Select
  import Ash.Query

  @impl Backpex.Filter
  def label, do: "订单状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择订单状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"待支付", "pending"},
      {"支付中", "processing"},
      {"支付成功", "completed"},
      {"支付失败", "failed"},
      {"已取消", "cancelled"},
      {"已退款", "refunded"}
    ]
  end

  @impl Backpex.Filter
  def filter(query, value, _assigns) do
    case value do
      nil ->
        query

      status when is_binary(status) ->
        filter(query, status == ^status)

      _ ->
        query
    end
  end
end
