defmodule Teen.Live.Filters.TaskTypeFilter do
  @moduledoc """
  任务类型过滤器

  用于过滤不同类型的游戏任务
  """

  use Backpex.Filter

  @impl Backpex.Filter
  def label, do: "任务类型"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"全部", ""},
      {"游戏局数", "game_rounds"},
      {"游戏赢的局数", "win_rounds"}
    ]
  end

  @impl Backpex.Filter
  def render_form(_assigns) do
    # 简化实现，返回空字符串
    ""
  end

  @impl Backpex.Filter
  def render(_assigns) do
    # 简化实现，返回空字符串
    ""
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) when is_nil(value) or value == "" do
    query
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource, _socket) do
    task_type_atom = String.to_existing_atom(value)
    import Ecto.Query
    where(query, [item], item.task_type == ^task_type_atom)
  end

  @impl Backpex.Filter
  def can?(assigns) do
    # 所有管理员都可以使用任务类型过滤器
    assigns.current_user.permission_level >= 1
  end

  defp get_task_type_label("game_rounds"), do: "游戏局数"
  defp get_task_type_label("win_rounds"), do: "游戏赢的局数"
  defp get_task_type_label(_), do: "全部"
end
