defmodule Teen.Live.RobotManagementLive do
  @moduledoc """
  机器人管理后台界面
  """

  use <PERSON><PERSON><PERSON>ina<PERSON><PERSON>, :live_view
  require Logger

  alias Teen.RobotManagement.{RobotEntity, SimpleRobotProvider, RobotStateManager}

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 定期更新数据
      :timer.send_interval(5000, self(), :update_stats)
    end

    socket =
      socket
      |> assign(:page_title, "机器人管理")
      |> assign(:current_url, "/admin/robots")
      |> assign(:fluid?, true)
      |> assign(:robots, [])
      |> assign(:stats, %{})
      |> assign(:loading, true)
      |> assign(:show_create_form, false)
      |> assign(:selected_robots, MapSet.new())
      |> assign(:filter_status, :all)
      |> assign(:filter_game_type, :all)
      |> assign(:filter_enabled, :all)
      |> assign(:page, 1)
      |> assign(:per_page, 20)
      |> assign(:show_delete_confirm, false)
      |> assign(:delete_target, nil)
      |> assign(:delete_type, nil)
      |> load_data()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_create_form", _params, socket) do
    {:noreply, assign(socket, :show_create_form, not socket.assigns.show_create_form)}
  end

  @impl true
  def handle_event("create_robot", params, socket) do
    Logger.info("🤖 [ADMIN] 创建机器人请求: #{inspect(params)}")

    case SimpleRobotProvider.create_robot_manually(params) do
      {:ok, robot} ->
        Logger.info("🤖 [ADMIN] 机器人创建成功: #{robot.robot_id}")

        socket =
          socket
          |> put_flash(:info, "机器人创建成功！ID: #{robot.robot_id}")
          |> assign(:show_create_form, false)
          |> load_data()

        {:noreply, socket}

      {:error, error} ->
        Logger.error("🤖 [ADMIN] 机器人创建失败: #{inspect(error)}")

        {:noreply, put_flash(socket, :error, "机器人创建失败: #{inspect(error)}")}
    end
  end

  @impl true
  def handle_event(
        "batch_create_robots",
        %{"count" => count_str, "game_type" => game_type},
        socket
      ) do
    case Integer.parse(count_str) do
      {count, ""} when count > 0 and count <= 50 ->
        Logger.info("🤖 [ADMIN] 批量创建 #{count} 个机器人")

        case SimpleRobotProvider.batch_create_robots(game_type, count) do
          {:ok, robots} ->
            Logger.info("🤖 [ADMIN] 批量创建成功: #{length(robots)} 个机器人")

            socket =
              socket
              |> put_flash(:info, "批量创建成功！创建了 #{length(robots)} 个机器人")
              |> load_data()

            {:noreply, socket}

          {:error, error} ->
            Logger.error("🤖 [ADMIN] 批量创建失败: #{inspect(error)}")
            {:noreply, put_flash(socket, :error, "批量创建失败: #{inspect(error)}")}
        end

      _ ->
        {:noreply, put_flash(socket, :error, "请输入有效的数量 (1-50)")}
    end
  end

  @impl true
  def handle_event("select_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    selected = socket.assigns.selected_robots

    new_selected =
      if MapSet.member?(selected, robot_id) do
        MapSet.delete(selected, robot_id)
      else
        MapSet.put(selected, robot_id)
      end

    {:noreply, assign(socket, :selected_robots, new_selected)}
  end

  @impl true
  def handle_event("select_all", _params, socket) do
    all_robot_ids =
      socket.assigns.robots
      |> Enum.map(& &1.robot_id)
      |> MapSet.new()

    {:noreply, assign(socket, :selected_robots, all_robot_ids)}
  end

  @impl true
  def handle_event("clear_selection", _params, socket) do
    {:noreply, assign(socket, :selected_robots, MapSet.new())}
  end

  @impl true
  def handle_event("recycle_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 回收选中的机器人: #{inspect(selected)}")

      Enum.each(selected, fn robot_id ->
        RobotStateManager.recycle_robot_by_admin(robot_id, "admin", true)
      end)

      socket =
        socket
        |> put_flash(:info, "成功回收 #{length(selected)} 个机器人")
        |> assign(:selected_robots, MapSet.new())
        |> load_data()

      {:noreply, socket}
    else
      {:noreply, put_flash(socket, :warning, "请先选择要回收的机器人")}
    end
  end

  @impl true
  def handle_event("release_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 释放选中的机器人: #{inspect(selected)}")

      SimpleRobotProvider.release_robots(selected)

      socket =
        socket
        |> put_flash(:info, "成功释放 #{length(selected)} 个机器人")
        |> assign(:selected_robots, MapSet.new())
        |> load_data()

      {:noreply, socket}
    else
      {:noreply, put_flash(socket, :warning, "请先选择要释放的机器人")}
    end
  end

  @impl true
  def handle_event("filter_status", %{"status" => status}, socket) do
    filter_status = String.to_existing_atom(status)

    socket =
      socket
      |> assign(:filter_status, filter_status)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("filter_game_type", %{"game_type" => game_type}, socket) do
    filter_game_type = if game_type == "all", do: :all, else: game_type

    socket =
      socket
      |> assign(:filter_game_type, filter_game_type)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("filter_enabled", %{"enabled" => enabled}, socket) do
    filter_enabled = String.to_existing_atom(enabled)

    socket =
      socket
      |> assign(:filter_enabled, filter_enabled)
      |> assign(:page, 1)
      |> load_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("cleanup_game_robots", %{"game_type" => game_type}, socket) do
    Logger.info("🤖 [ADMIN] 清理游戏 #{game_type} 的机器人状态")

    case RobotStateManager.cleanup_robots_by_game(game_type) do
      {:ok, result} ->
        cleared_count = Map.get(result, :cleared, 0)
        total_count = Map.get(result, :total, 0)

        socket =
          socket
          |> put_flash(
            :info,
            "游戏 #{game_type} 清理完成：共检查 #{total_count} 个机器人，重置 #{cleared_count} 个"
          )
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 清理游戏机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "清理失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("cleanup_all_game_robots", _params, socket) do
    Logger.info("🤖 [ADMIN] 全局清理所有游戏的机器人状态")

    case RobotStateManager.cleanup_all_game_robots() do
      {:ok, result} ->
        total_cleared = Map.get(result, :total_cleared, 0)

        socket =
          socket
          |> put_flash(:info, "全局清理完成：总共重置了 #{total_cleared} 个机器人状态")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 全局清理失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "全局清理失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("recycle_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    Logger.info("🤖 [ADMIN] 回收单个机器人: #{robot_id}")

    case RobotStateManager.recycle_robot_by_admin(robot_id, "admin", true) do
      {:ok, _robot} ->
        socket =
          socket
          |> put_flash(:info, "机器人 #{robot_id} 回收成功")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 回收机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "回收失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("enable_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    Logger.info("🤖 [ADMIN] 启用机器人: #{robot_id}")

    case RobotStateManager.enable_robot(robot_id) do
      {:ok, _robot} ->
        socket =
          socket
          |> put_flash(:info, "机器人 #{robot_id} 启用成功")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 启用机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "启用失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("disable_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    Logger.info("🤖 [ADMIN] 禁用机器人: #{robot_id}")

    case RobotStateManager.disable_robot(robot_id) do
      {:ok, updated_robot} ->
        Logger.info("🤖 [ADMIN] 机器人 #{robot_id} 禁用成功，is_enabled: #{updated_robot.is_enabled}")

        socket =
          socket
          |> put_flash(:info, "机器人 #{robot_id} 禁用成功")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 禁用机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "禁用失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("batch_enable_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 批量启用选中的机器人: #{inspect(selected)}")

      case RobotStateManager.batch_enable_robots(selected) do
        {:ok, result} ->
          success_count = Map.get(result, :success, 0)

          socket =
            socket
            |> put_flash(:info, "成功启用 #{success_count} 个机器人")
            |> assign(:selected_robots, MapSet.new())
            |> load_data()

          {:noreply, socket}

        {:error, reason} ->
          Logger.error("🤖 [ADMIN] 批量启用失败: #{inspect(reason)}")
          {:noreply, put_flash(socket, :error, "批量启用失败: #{inspect(reason)}")}
      end
    else
      {:noreply, put_flash(socket, :warning, "请先选择要启用的机器人")}
    end
  end

  @impl true
  def handle_event("batch_disable_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 批量禁用选中的机器人: #{inspect(selected)}")

      case RobotStateManager.batch_disable_robots(selected) do
        {:ok, result} ->
          success_count = Map.get(result, :success, 0)

          socket =
            socket
            |> put_flash(:info, "成功禁用 #{success_count} 个机器人")
            |> assign(:selected_robots, MapSet.new())
            |> load_data()

          {:noreply, socket}

        {:error, reason} ->
          Logger.error("🤖 [ADMIN] 批量禁用失败: #{inspect(reason)}")
          {:noreply, put_flash(socket, :error, "批量禁用失败: #{inspect(reason)}")}
      end
    else
      {:noreply, put_flash(socket, :warning, "请先选择要禁用的机器人")}
    end
  end

  @impl true
  def handle_event("enable_all_robots", _params, socket) do
    Logger.info("🤖 [ADMIN] 启用所有机器人")

    case RobotStateManager.enable_all_robots() do
      {:ok, result} ->
        success_count = Map.get(result, :success, 0)

        socket =
          socket
          |> put_flash(:info, "成功启用 #{success_count} 个机器人")
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 启用所有机器人失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "启用所有机器人失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("delete_robot", %{"robot_id" => robot_id_str}, socket) do
    robot_id = String.to_integer(robot_id_str)
    Logger.info("🤖 [ADMIN] 删除机器人 #{robot_id}")

    case RobotStateManager.delete_robot(robot_id) do
      {:ok, _} ->
        socket =
          socket
          |> put_flash(:info, "机器人 #{robot_id} 删除成功")
          |> assign(:selected_robots, MapSet.delete(socket.assigns.selected_robots, robot_id))
          |> load_data()

        {:noreply, socket}

      {:error, :robot_not_found} ->
        Logger.warn("🤖 [ADMIN] 机器人 #{robot_id} 不存在，可能已被删除")
        # 机器人不存在，可能已经被删除了，刷新界面
        socket =
          socket
          |> put_flash(:warning, "机器人 #{robot_id} 不存在，可能已被删除")
          |> assign(:selected_robots, MapSet.delete(socket.assigns.selected_robots, robot_id))
          |> load_data()

        {:noreply, socket}

      {:error, reason} ->
        Logger.error("🤖 [ADMIN] 删除机器人失败: #{inspect(reason)}")

        error_msg =
          case reason do
            :robot_not_found -> "机器人不存在"
            _ -> "删除失败: #{inspect(reason)}"
          end

        {:noreply, put_flash(socket, :error, error_msg)}
    end
  end

  @impl true
  def handle_event("show_batch_delete_confirm", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      socket =
        socket
        |> assign(:show_delete_confirm, true)
        |> assign(:delete_target, selected)
        |> assign(:delete_type, :batch)

      {:noreply, socket}
    else
      {:noreply, put_flash(socket, :warning, "请先选择要删除的机器人")}
    end
  end

  @impl true
  def handle_event(
        "show_delete_confirm",
        %{"robot_id" => robot_id_str, "robot_name" => robot_name},
        socket
      ) do
    robot_id = String.to_integer(robot_id_str)

    socket =
      socket
      |> assign(:show_delete_confirm, true)
      |> assign(:delete_target, %{id: robot_id, name: robot_name})
      |> assign(:delete_type, :single)

    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_delete", _params, socket) do
    socket =
      socket
      |> assign(:show_delete_confirm, false)
      |> assign(:delete_target, nil)
      |> assign(:delete_type, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("confirm_delete", _params, socket) do
    case socket.assigns.delete_type do
      :batch ->
        selected = socket.assigns.delete_target
        Logger.info("🤖 [ADMIN] 批量删除选中的机器人: #{inspect(selected)}")

        case RobotStateManager.batch_delete_robots(selected) do
          {:ok, result} ->
            success_count = Map.get(result, :success, 0)
            total_count = Map.get(result, :total, 0)
            failed_count = total_count - success_count

            flash_message =
              if failed_count > 0 do
                "删除完成：成功 #{success_count} 个，失败 #{failed_count} 个"
              else
                "成功删除 #{success_count} 个机器人"
              end

            socket =
              socket
              |> put_flash(:info, flash_message)
              |> assign(:selected_robots, MapSet.new())
              |> assign(:show_delete_confirm, false)
              |> assign(:delete_target, nil)
              |> assign(:delete_type, nil)
              |> load_data()

            {:noreply, socket}

          {:error, reason} ->
            Logger.error("🤖 [ADMIN] 批量删除失败: #{inspect(reason)}")

            socket =
              socket
              |> put_flash(:error, "批量删除失败: #{inspect(reason)}")
              |> assign(:show_delete_confirm, false)
              |> assign(:delete_target, nil)
              |> assign(:delete_type, nil)

            {:noreply, socket}
        end

      :single ->
        target = socket.assigns.delete_target
        robot_id = target.id
        Logger.info("🤖 [ADMIN] 删除机器人 #{robot_id}")

        case RobotStateManager.delete_robot(robot_id) do
          {:ok, _} ->
            socket =
              socket
              |> put_flash(:info, "机器人 #{target.name} 删除成功")
              |> assign(:show_delete_confirm, false)
              |> assign(:delete_target, nil)
              |> assign(:delete_type, nil)
              |> load_data()

            {:noreply, socket}

          {:error, :robot_not_found} ->
            socket =
              socket
              |> put_flash(:error, "机器人不存在")
              |> assign(:show_delete_confirm, false)
              |> assign(:delete_target, nil)
              |> assign(:delete_type, nil)

            {:noreply, socket}

          {:error, :robot_in_use} ->
            socket =
              socket
              |> put_flash(:error, "机器人正在使用中，无法删除")
              |> assign(:show_delete_confirm, false)
              |> assign(:delete_target, nil)
              |> assign(:delete_type, nil)

            {:noreply, socket}

          {:error, reason} ->
            error_msg =
              case reason do
                :robot_not_found -> "机器人不存在"
                :robot_in_use -> "机器人正在使用中，无法删除"
                _ -> "删除失败: #{inspect(reason)}"
              end

            socket =
              socket
              |> put_flash(:error, error_msg)
              |> assign(:show_delete_confirm, false)
              |> assign(:delete_target, nil)
              |> assign(:delete_type, nil)

            {:noreply, socket}
        end

      _ ->
        socket =
          socket
          |> assign(:show_delete_confirm, false)
          |> assign(:delete_target, nil)
          |> assign(:delete_type, nil)

        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("batch_delete_selected", _params, socket) do
    selected = MapSet.to_list(socket.assigns.selected_robots)

    if length(selected) > 0 do
      Logger.info("🤖 [ADMIN] 批量删除选中的机器人: #{inspect(selected)}")

      case RobotStateManager.batch_delete_robots(selected) do
        {:ok, result} ->
          success_count = Map.get(result, :success, 0)
          total_count = Map.get(result, :total, 0)
          failed_count = total_count - success_count

          flash_message =
            if failed_count > 0 do
              "删除完成：成功 #{success_count} 个，失败 #{failed_count} 个"
            else
              "成功删除 #{success_count} 个机器人"
            end

          socket =
            socket
            |> put_flash(:info, flash_message)
            |> assign(:selected_robots, MapSet.new())
            |> load_data()

          {:noreply, socket}

        {:error, reason} ->
          Logger.error("🤖 [ADMIN] 批量删除失败: #{inspect(reason)}")
          {:noreply, put_flash(socket, :error, "批量删除失败: #{inspect(reason)}")}
      end
    else
      {:noreply, put_flash(socket, :warning, "请先选择要删除的机器人")}
    end
  end

  @impl true
  def handle_event("change_page", %{"page" => page_str}, socket) do
    case Integer.parse(page_str) do
      {page, ""} when page > 0 ->
        socket =
          socket
          |> assign(:page, page)
          |> load_data()

        {:noreply, socket}

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info(:update_stats, socket) do
    {:noreply, load_data(socket)}
  end

  # 加载数据
  defp load_data(socket) do
    try do
      # 确保默认stats
      default_stats = %{
        total: 0,
        idle: 0,
        assigned: 0,
        in_game: 0,
        in_round: 0,
        recycling: 0,
        recycled: 0,
        insufficient_funds: 0,
        enabled: 0,
        disabled: 0
      }

      # 获取统计数据
      stats =
        try do
          RobotStateManager.get_robot_status_stats()
        rescue
          error ->
            Logger.error("🤖 [ADMIN] 获取统计数据失败: #{inspect(error)}")
            %{}
        end

      # 合并默认值，确保所有键都存在
      stats = Map.merge(default_stats, stats || %{})

      # 获取机器人列表（带过滤和分页）
      filter_status = socket.assigns.filter_status
      filter_game_type = socket.assigns.filter_game_type
      filter_enabled = socket.assigns.filter_enabled
      page = socket.assigns.page
      per_page = socket.assigns.per_page

      {robots, total_count} =
        try do
          # 强制重新读取数据，避免缓存问题
          filtered_robots =
            RobotEntity
            |> Ash.Query.new()
            |> Ash.read!()
            |> filter_by_status(filter_status)
            |> filter_by_game_type(filter_game_type)
            |> filter_by_enabled(filter_enabled)
            |> Enum.sort_by(& &1.inserted_at, {:desc, DateTime})

          total_count = length(filtered_robots)
          paginated_robots = paginate(filtered_robots, page, per_page)

          Logger.debug("🤖 [ADMIN] 加载了 #{length(paginated_robots)} 个机器人，总数: #{total_count}")

          {paginated_robots, total_count}
        rescue
          error ->
            Logger.error("🤖 [ADMIN] 获取机器人列表失败: #{inspect(error)}")
            {[], 0}
        end

      # 计算总页数
      total_pages = if total_count > 0, do: ceil(total_count / per_page), else: 0

      socket
      |> assign(:robots, robots)
      |> assign(:stats, stats)
      |> assign(:total_count, total_count)
      |> assign(:total_pages, total_pages)
      |> assign(:loading, false)
    rescue
      error ->
        Logger.error("🤖 [ADMIN] 加载数据失败: #{inspect(error)}")

        socket
        |> put_flash(:error, "加载数据失败")
        |> assign(:loading, false)
    end
  end

  # 按状态过滤
  defp filter_by_status(robots, :all), do: robots
  # "游戏中"状态对应数字1
  defp filter_by_status(robots, :in_game), do: Enum.filter(robots, &(&1.status == 1))
  defp filter_by_status(robots, :idle), do: Enum.filter(robots, &(&1.status == 0))
  defp filter_by_status(robots, :recycling), do: Enum.filter(robots, &(&1.status == 2))
  defp filter_by_status(robots, :insufficient_funds), do: Enum.filter(robots, &(&1.status == 3))
  defp filter_by_status(robots, status), do: Enum.filter(robots, &(&1.status == status))

  # 按游戏类型过滤
  defp filter_by_game_type(robots, :all), do: robots

  defp filter_by_game_type(robots, game_type),
    do: Enum.filter(robots, &(&1.current_game_type == game_type))

  # 按启用状态过滤
  defp filter_by_enabled(robots, :all), do: robots
  defp filter_by_enabled(robots, :enabled), do: Enum.filter(robots, & &1.is_enabled)
  defp filter_by_enabled(robots, :disabled), do: Enum.filter(robots, &(not &1.is_enabled))

  # 分页
  defp paginate(robots, page, per_page) do
    offset = (page - 1) * per_page
    Enum.slice(robots, offset, per_page)
  end

  # 状态显示
  defp status_badge(status) do
    case status do
      # 空闲
      0 -> "bg-green-100 text-green-800"
      # 游戏中
      1 -> "bg-purple-100 text-purple-800"
      # 回收中
      2 -> "bg-red-100 text-red-800 font-semibold"
      # 积分不足
      3 -> "bg-amber-100 text-amber-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end

  defp status_text(status) do
    case status do
      0 -> "空闲"
      1 -> "游戏中"
      2 -> "回收中"
      3 -> "积分不足"
      _ -> "未知"
    end
  end

  # 格式化数字，添加千位分隔符
  defp format_number(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> String.graphemes()
    |> Enum.reverse()
    |> Enum.chunk_every(3)
    |> Enum.join(",")
    |> String.reverse()
  end

  defp format_number(_), do: "0"
end
