defmodule Teen.ItemActions.ExportPaymentOrders do
  @moduledoc """
  导出支付订单的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "导出订单"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-arrow-down-tray"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认导出"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要导出选中的订单吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("📊 [EXPORT_PAYMENT_ORDERS] handle called with #{length(items)} items")

    case export_payment_orders(items) do
      {:ok, file_path} ->
        Logger.info(
          "📊 [EXPORT_PAYMENT_ORDERS] Successfully exported #{length(items)} orders to #{file_path}"
        )

        socket =
          socket
          |> Phoenix.LiveView.put_flash(:info, "成功导出 #{length(items)} 个支付订单")
          |> Phoenix.LiveView.push_event("download", %{
            url: "/downloads/#{Path.basename(file_path)}",
            filename: Path.basename(file_path)
          })

        {:ok, socket}

      {:error, reason} ->
        Logger.error("📊 [EXPORT_PAYMENT_ORDERS] Failed to export orders: #{inspect(reason)}")

        socket =
          socket
          |> Phoenix.LiveView.put_flash(:error, "导出支付订单失败：#{inspect(reason)}")

        {:ok, socket}
    end
  end

  defp export_payment_orders(orders) do
    try do
      # 生成 CSV 内容
      csv_content = generate_csv_content(orders)

      # 生成文件名
      timestamp = DateTime.utc_now() |> DateTime.to_unix()
      filename = "payment_orders_#{timestamp}.csv"
      file_path = Path.join([System.tmp_dir(), filename])

      # 写入文件
      case File.write(file_path, csv_content) do
        :ok -> {:ok, file_path}
        {:error, reason} -> {:error, reason}
      end
    rescue
      error -> {:error, error}
    end
  end

  defp generate_csv_content(orders) do
    # CSV 头部
    headers = [
      "ID",
      "订单号",
      "用户ID",
      "支付金额",
      "实际金额",
      "支付方式",
      "支付状态",
      "渠道ID",
      "第三方订单号",
      "创建时间",
      "更新时间"
    ]

    # 转换记录为 CSV 行
    rows = Enum.map(orders, &order_to_csv_row/1)

    # 组合头部和数据行
    all_rows = [headers | rows]

    # 生成 CSV 字符串
    all_rows
    |> Enum.map(&Enum.join(&1, ","))
    |> Enum.join("\n")
  end

  defp order_to_csv_row(order) do
    [
      to_string(order.id),
      to_string(order.order_id || ""),
      to_string(order.user_id),
      to_string(order.amount),
      to_string(order.actual_amount || order.amount),
      to_string(order.payment_method || ""),
      payment_status_to_string(order.status),
      to_string(order.channel_id || ""),
      to_string(order.third_party_order_id || ""),
      to_string(order.created_at),
      to_string(order.updated_at)
    ]
  end

  defp payment_status_to_string(0), do: "待支付"
  defp payment_status_to_string(1), do: "支付成功"
  defp payment_status_to_string(2), do: "支付失败"
  defp payment_status_to_string(3), do: "已取消"
  defp payment_status_to_string(_), do: "未知"
end
