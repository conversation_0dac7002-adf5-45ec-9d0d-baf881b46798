defmodule Teen.Live.ItemActions.SetDeviceOfflineAction do
  @moduledoc """
  设置设备离线操作

  用于在设备管理页面中强制设置设备为离线状态
  """

  use Backpex.ItemAction
  import Phoenix.LiveView

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "设为离线"

  @impl Backpex.ItemAction
  def icon(_assigns, _item), do: "hero-power"

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认设为离线"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要将这个设备设为离线状态吗？"

  @impl Backpex.ItemAction
  def fields(), do: []

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    socket =
      try do
        case items do
          [item] ->
            case Cypridina.Accounts.UserDevice.set_offline(item) do
              {:ok, _updated_item} ->
                socket
                |> put_flash(:info, "设备已成功设为离线")

              {:error, _changeset} ->
                socket
                |> put_flash(:error, "设置设备离线失败")
            end

          _ ->
            socket
            |> put_flash(:error, "只能对单个设备执行离线操作")
        end
      rescue
        error ->
          socket
          |> put_flash(:error, "操作失败: #{inspect(error)}")
      end

    {:noreply, socket}
  end
end
