defmodule Teen.ItemActions.EnableConfig do
  @moduledoc """
  启用支付配置的操作
  """

  use Backpex.ItemAction

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "启用"

  @impl Backpex.ItemAction
  def icon, do: "hero-check-circle"

  @impl Backpex.ItemAction
  def handle(socket, item) do
    case Teen.PaymentSystem.PaymentConfig.enable(item) do
      {:ok, _updated_item} ->
        socket
        |> Phoenix.LiveView.put_flash(:info, "配置已启用")
        |> Phoenix.LiveView.push_navigate(to: socket.assigns.return_to)

      {:error, _changeset} ->
        socket
        |> Phoenix.LiveView.put_flash(:error, "启用失败")
    end
  end

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认启用"

  @impl Backpex.ItemAction
  def confirm_text(_assigns), do: "确定要启用此支付配置吗？"
end
