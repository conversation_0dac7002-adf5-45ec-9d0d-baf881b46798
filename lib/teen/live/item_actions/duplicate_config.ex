defmodule Teen.ItemActions.DuplicateConfig do
  @moduledoc """
  复制支付配置的操作
  """

  use Backpex.ItemAction

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "复制"

  @impl Backpex.ItemAction
  def icon, do: "hero-document-duplicate"

  @impl Backpex.ItemAction
  def handle(socket, item) do
    # 创建复制的配置数据
    duplicate_attrs = %{
      gateway_id: item.gateway_id,
      gateway_name: "#{item.gateway_name} (复制)",
      payment_type: item.payment_type,
      payment_type_name: "#{item.payment_type_name} (复制)",
      min_amount: item.min_amount,
      max_amount: item.max_amount,
      fee_rate: item.fee_rate,
      deduction_rate: item.deduction_rate,
      # 默认禁用
      status: 0,
      sort_order: item.sort_order + 1,
      config_data: item.config_data
    }

    case Teen.PaymentSystem.PaymentConfig.create(duplicate_attrs) do
      {:ok, _new_item} ->
        socket
        |> Phoenix.LiveView.put_flash(:info, "配置已复制")
        |> Phoenix.LiveView.push_navigate(to: socket.assigns.return_to)

      {:error, _changeset} ->
        socket
        |> Phoenix.LiveView.put_flash(:error, "复制失败")
    end
  end

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认复制"

  @impl Backpex.ItemAction
  def confirm_text(_assigns), do: "确定要复制此支付配置吗？"
end
