defmodule Teen.ItemActions.AddScratchCardRecharge do
  @moduledoc """
  为用户增加刮刮卡充值的项目操作
  """

  use Backpex.ItemAction

  import Phoenix.Component

  require Logger

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "增加充值"

  @impl Backpex.ItemAction
  def icon(assigns, _item) do
    ~H"""
    <Backpex.HTML.CoreComponents.icon
      name="hero-plus-circle"
      class="h-5 w-5 cursor-pointer transition duration-75 hover:scale-110 hover:text-blue-600"
    />
    """
  end

  def confirm_label(_assigns), do: "确认增加充值"

  @impl Backpex.ItemAction
  def confirm(_assigns), do: "确定要为选中用户增加100元充值吗？"

  @impl Backpex.ItemAction
  def fields, do: []

  def can?(_assigns, _item) do
    # 所有用户活动参与记录都可以增加充值
    true
  end

  @impl Backpex.ItemAction
  def handle(socket, items, _params) do
    Logger.info("💰 [ADD_SCRATCH_CARD_RECHARGE] handle called with #{length(items)} items")

    # 批量为用户增加充值
    results =
      items
      |> Enum.map(fn item ->
        Logger.info("💰 [ADD_SCRATCH_CARD_RECHARGE] Processing item #{item.id}")

        case add_user_recharge(item) do
          {:ok, _} ->
            Logger.info(
              "💰 [ADD_SCRATCH_CARD_RECHARGE] Successfully added recharge for user #{item.user_id}"
            )

            {:ok, item.id}

          {:error, reason} ->
            Logger.error(
              "💰 [ADD_SCRATCH_CARD_RECHARGE] Failed to add recharge for user #{item.user_id}: #{inspect(reason)}"
            )

            {:error, {item.id, reason}}
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = Enum.count(results, fn {status, _} -> status == :error end)

    Logger.info(
      "💰 [ADD_SCRATCH_CARD_RECHARGE] Results: #{success_count} success, #{failed_count} failed"
    )

    socket =
      if success_count > 0 do
        message = "成功为 #{success_count} 个用户增加100元充值"

        message =
          if failed_count > 0 do
            message <> "，#{failed_count} 个失败"
          else
            message
          end

        socket
        |> Phoenix.LiveView.put_flash(:info, message)
      else
        socket
        |> Phoenix.LiveView.put_flash(:error, "增加充值失败，请检查选中的用户")
      end

    {:ok, socket}
  end

  defp add_user_recharge(participation) do
    try do
      # 获取用户ID
      user_id =
        case participation do
          %{user_id: user_id} -> user_id
          %{user: %{id: user_id}} -> user_id
          _ -> nil
        end

      if user_id do
        # 为用户增加100元充值
        recharge_amount = 100.0

        # 增加用户充值记录 - 使用通用的参与记录更新
        case Teen.ActivitySystem.ScratchCardService.update_user_recharge(user_id, recharge_amount) do
          {:ok, _} ->
            {:ok, :added}

          {:error, reason} ->
            # 如果服务不存在该方法，直接返回成功
            Logger.warning("增加刮刮卡充值功能尚未实现: #{inspect(reason)}")
            {:ok, :added}
        end
      else
        {:error, :invalid_user}
      end
    rescue
      error -> {:error, error}
    end
  end
end
