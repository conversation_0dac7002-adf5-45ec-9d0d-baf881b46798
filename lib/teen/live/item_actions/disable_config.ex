defmodule Teen.ItemActions.DisableConfig do
  @moduledoc """
  禁用支付配置的操作
  """

  use Backpex.ItemAction

  @impl Backpex.ItemAction
  def label(_assigns, _item), do: "禁用"

  @impl Backpex.ItemAction
  def icon, do: "hero-x-circle"

  @impl Backpex.ItemAction
  def handle(socket, item) do
    case Teen.PaymentSystem.PaymentConfig.disable(item) do
      {:ok, _updated_item} ->
        socket
        |> Phoenix.LiveView.put_flash(:info, "配置已禁用")
        |> Phoenix.LiveView.push_navigate(to: socket.assigns.return_to)

      {:error, _changeset} ->
        socket
        |> Phoenix.LiveView.put_flash(:error, "禁用失败")
    end
  end

  @impl Backpex.ItemAction
  def confirm_label(_assigns), do: "确认禁用"

  @impl Backpex.ItemAction
  def confirm_text(_assigns), do: "确定要禁用此支付配置吗？"
end
