defmodule Teen.Live.ActivitySystem.RechargeTaskLive do
  @moduledoc """
  充值任务管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.RechargeTask
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :task_name do
        module Backpex.Fields.Text
        label("任务名称")
      end

      field :required_amount do
        module Backpex.Fields.Number
        label("所需充值金额")
      end

      field :reward_amount do
        module Backpex.Fields.Number
        label("奖励金额")
      end

      field :reward_type do
        module Backpex.Fields.Select
        label("奖励类型")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :task_type do
        module Backpex.Fields.Select
        label("任务类型")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :time_limit_hours do
        module Backpex.Fields.Number
        label("时间限制(小时)")
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "充值任务"

  @impl Backpex.LiveResource
  def plural_name, do: "充值任务"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
