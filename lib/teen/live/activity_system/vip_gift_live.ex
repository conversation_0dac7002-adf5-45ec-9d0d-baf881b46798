defmodule Teen.Live.ActivitySystem.VipGiftLive do
  @moduledoc """
  VIP礼包管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.VipGift
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :vip_level do
        module Backpex.Fields.Number
        label("VIP等级")
      end

      field :gift_name do
        module Backpex.Fields.Text
        label("礼包名称")
      end

      field :gift_type do
        module Backpex.Fields.Select
        label("礼包类型")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :reward_amount do
        module Backpex.Fields.Number
        label("奖励金额")
      end

      field :reward_type do
        module Backpex.Fields.Select
        label("奖励类型")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :cooldown_hours do
        module Backpex.Fields.Number
        label("冷却时间(小时)")
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "VIP礼包"

  @impl Backpex.LiveResource
  def plural_name, do: "VIP礼包"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
