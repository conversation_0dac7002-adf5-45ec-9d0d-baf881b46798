defmodule Teen.Live.ActivitySystem.CdkeyClaimRecordLive do
  @moduledoc """
  CDKEY领取记录管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  alias Teen.Live.Filters.CdkeyClaimRecordFilters
  alias Teen.Live.Actions.CdkeyClaimRecordActions

  backpex do
    resource Teen.ActivitySystem.CdkeyClaimRecord
    layout({Teen.Layouts, :admin})

    fields do
      field :cdkey do
        module Backpex.Fields.BelongsTo
        label("CDKEY")
        display_field(:code)
        orderable(true)
        searchable(true)
      end

      field :user_id do
        module Backpex.Fields.Text
        label("用户ID")
        searchable(true)

        render(fn assigns ->
          ~H"""
          <span class="font-mono text-sm">{String.slice(assigns.value || "", 0, 8)}...</span>
          """
        end)
      end

      field :username do
        module Backpex.Fields.Text
        label("用户名")
        searchable(true)
        orderable(true)
      end

      field :claimed_rewards do
        module Backpex.Fields.Textarea
        label("领取奖励")

        render(fn assigns ->
          rewards = assigns.value || %{}
          formatted = Jason.encode!(rewards, pretty: true)

          ~H"""
          <pre class="text-xs bg-gray-100 p-2 rounded max-w-xs overflow-auto"><%= formatted %></pre>
          """
        end)
      end

      field :claimed_at do
        module Backpex.Fields.DateTime
        label("领取时间")
        orderable(true)
      end

      field :ip_address do
        module Backpex.Fields.Text
        label("IP地址")
        searchable(true)
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("记录时间")
        orderable(true)
        readonly(true)
      end
    end

    filters do
      filter :cdkey_code do
        module CdkeyClaimRecordFilters.CdkeyCodeFilter
        label("CDKEY代码")
      end

      filter :username do
        module CdkeyClaimRecordFilters.UsernameFilter
        label("用户名")
      end

      filter :ip_address do
        module CdkeyClaimRecordFilters.IpAddressFilter
        label("IP地址")
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "CDKEY领取记录"

  @impl Backpex.LiveResource
  def plural_name, do: "CDKEY领取记录"

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :show]
      },
      export: %{
        module: CdkeyClaimRecordActions.ExportAction,
        only: [:index]
      },
      statistics: %{
        module: CdkeyClaimRecordActions.StatisticsAction,
        only: [:index]
      }
    ]
  end

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end
end
