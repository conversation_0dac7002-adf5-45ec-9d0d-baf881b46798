defmodule Teen.Live.ActivitySystem.DailyGameTaskLive do
  @moduledoc """
  每日游戏任务管理页面

  简化版本，移除复杂的自定义渲染以避免表单重置问题
  """

  use AshBackpex.LiveResource
  import Phoenix.Component
  import Phoenix.LiveView

  # 如果 Number.Delimit 不可用，我们将使用简单的格式化
  @number_formatter Application.compile_env(:cypridina, :number_formatter, :simple)

  backpex do
    resource Teen.ActivitySystem.GameTask
    load []
    layout({Teen.Layouts, :admin})
  end

  @impl Backpex.LiveResource
  def fields do
    [
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      task_name: %{
        module: Backpex.Fields.Text,
        label: "📝 任务名称",
        help_text: "请输入任务名称，最多100个字符",
        placeholder: "请输入任务名称",
        searchable: true,
        orderable: true
      },
      game_id: %{
        module: Backpex.Fields.Select,
        label: "🎮 选择游戏",
        help_text: "选择关联的游戏，选择后会自动填充游戏名称",
        options: fn _assigns -> get_game_options() end,
        searchable: true,
        prompt: "🎮 请选择游戏...",
        only: [:new, :edit]
      },
      game_name: %{
        module: Backpex.Fields.Text,
        label: "🎮 游戏名称",
        help_text: "游戏名称会根据选择的游戏自动填充",
        readonly: true
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "🎯 任务类型",
        help_text: "选择任务的完成条件类型",
        options: [
          {"🎮 游戏局数", :game_rounds},
          {"💰 充值金额", :recharge_amount},
          {"🏆 游戏胜利", :win_rounds},
          {"🎡 转盘次数", :wheel_spins},
          {"✅ 完成任务", :task_completion}
        ]
      },
      required_count: %{
        module: Backpex.Fields.Number,
        label: "🎲 所需局数",
        help_text: "完成任务需要的局数，最少1局",
        placeholder: "请输入所需局数"
      },
      max_claims: %{
        module: Backpex.Fields.Number,
        label: "🔄 每日最大领取次数",
        help_text: "每天最多可以领取的次数，最少1次",
        placeholder: "请输入最大领取次数"
      },
      target_value: %{
        module: Backpex.Fields.Number,
        label: "🎯 目标值",
        help_text: "任务的目标数值，如赢取金币数量等",
        placeholder: "请输入目标值"
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "💰 奖励金额",
        help_text: "奖励金额，以分为单位",
        placeholder: "请输入奖励金额",
        render: fn assigns ->
          formatted_value =
            case assigns.value do
              %Decimal{} = decimal -> Decimal.to_string(decimal)
              value when is_number(value) -> :erlang.float_to_binary(value / 1, decimals: 0)
              value -> to_string(value || 0)
            end

          ~H"""
          <span class="font-mono text-green-600">
            💰 <%= formatted_value %>
          </span>
          """
        end
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "🎁 奖励类型",
        help_text: "选择奖励的类型",
        options: [
          {"🪙 金币", :coins},
          {"⭐ 积分", :points},
          {"🎁 道具", :items}
        ]
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "⚡ 状态",
        help_text: "控制任务是否启用",
        options: [
          {"✅ 启用", :enabled},
          {"❌ 禁用", :disabled}
        ]
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "🚀 是否激活",
        help_text: "控制任务是否对用户可见"
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "📅 开始日期",
        help_text: "任务开始的日期",
        format: "%Y-%m-%d"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "📅 结束日期",
        help_text: "任务结束的日期",
        format: "%Y-%m-%d"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "⏰ 创建时间",
        readonly: true,
        only: [:index, :show],
        format: "%Y-%m-%d %H:%M:%S"
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "⏰ 更新时间",
        readonly: true,
        only: [:index, :show],
        format: "%Y-%m-%d %H:%M:%S"
      }
    ]
  end

  @impl Backpex.LiveResource
  def singular_name, do: "🎮 每日游戏任务"

  @impl Backpex.LiveResource
  def plural_name, do: "🎮 每日游戏任务管理"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end



  # 获取游戏选项列表
  defp get_game_options do
    try do
      # 从游戏配置中获取启用的游戏列表
      case Teen.GameManagement.ManageGameConfig.list_enabled() do
        {:ok, games} ->
          games
          |> Enum.map(fn game ->
            {"#{game.display_name} (ID: #{game.game_id})", game.game_id}
          end)
          |> Enum.sort_by(fn {label, _value} -> label end)

        {:error, _} ->
          # 如果获取失败，使用已知的游戏数据作为备选
          get_fallback_game_options()
      end
    rescue
      _error ->
        # 如果出现异常，使用已知的游戏数据作为备选
        get_fallback_game_options()
    end
  end

  # 备选游戏选项列表
  defp get_fallback_game_options do
    [
      {"Teen Patti (ID: 1)", 1},
      {"Dragon Tiger (ID: 22)", 22},
      {"Andar Bahar (ID: 30)", 30},
      {"Jhandi Munda (ID: 31)", 31},
      {"AK47 Teen Patti (ID: 32)", 32},
      {"Pot Blind (ID: 33)", 33},
      {"Safari of Wealth (ID: 34)", 34},
      {"Slot 777 (ID: 40)", 40},
      {"Slot Niu (ID: 41)", 41},
      {"Slot Cat (ID: 42)", 42}
    ]
  end
end
