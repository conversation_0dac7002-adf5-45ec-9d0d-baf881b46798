defmodule Teen.Live.ActivitySystem.RewardClaimRecordLive do
  @moduledoc """
  奖励领取记录管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.RewardClaimRecord
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :user_id do
        module Backpex.Fields.Text
        label("用户ID")
      end

      field :activity_type do
        module Backpex.Fields.Select
        label("活动类型")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :activity_id do
        module Backpex.Fields.Text
        label("活动ID")
      end

      field :reward_type do
        module Backpex.Fields.Select
        label("奖励类型")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :reward_amount do
        module Backpex.Fields.Number
        label("奖励数量")
      end

      field :claim_status do
        module Backpex.Fields.Select
        label("领取状态")

        options([
          {"选项1", "option1"},
          {"选项2", "option2"}
        ])
      end

      field :claimed_at do
        module Backpex.Fields.DateTime
        label("领取时间")
      end

      field :ip_address do
        module Backpex.Fields.Text
        label("IP地址")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "奖励领取记录"

  @impl Backpex.LiveResource
  def plural_name, do: "奖励领取记录"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
