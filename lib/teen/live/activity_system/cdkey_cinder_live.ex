defmodule Teen.Live.ActivitySystem.CdkeyCinderLive do
  @moduledoc """
  使用 Cinder 框架的 CDKEY 管理页面
  """

  use CypridinaWeb, :live_view
  use Cinder.Table.UrlSync
  import Cinder.Table.Refresh
  import CypridinaWeb.Components.Icon
  require Logger

  @impl Phoenix.LiveView
  def mount(_params, _session, socket) do
    # on_mount 钩子已经设置了 current_user，不要覆盖它
    socket =
      socket
      |> assign(:page_title, "CDKEY管理")
      |> assign(:selected_cdkey, nil)
      |> assign(:show_form, false)
      |> assign(:form_mode, :new)
      |> assign(:fluid?, true)
      |> assign(:form, nil)

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl Phoenix.LiveView
  def handle_params(params, uri, socket) do
    socket = Cinder.Table.UrlSync.handle_params(params, uri, socket)
    {:noreply, socket}
  end

  @impl Phoenix.LiveView
  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">CDKEY管理</h1>
        <p class="mt-2 text-gray-600">管理系统中的所有兑换码</p>
      </div>
      
    <!-- 操作按钮 -->
      <div class="mb-6 flex justify-between items-center">
        <div class="flex space-x-4">
          <button
            phx-click="new_cdkey"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <.icon name="hero-plus" class="w-4 h-4 mr-2" /> 新建CDKEY
          </button>

          <button
            phx-click="batch_create"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <.icon name="hero-document-duplicate" class="w-4 h-4 mr-2" /> 批量创建
          </button>

          <button
            phx-click="export_cdkeys"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <.icon name="hero-arrow-down-tray" class="w-4 h-4 mr-2" /> 导出
          </button>
        </div>
      </div>
      
    <!-- 使用 Cinder.Table 组件 -->
      <Cinder.Table.table
        id="cdkey_table"
        resource={Teen.ActivitySystem.Cdkey}
        actor={@current_user}
        url_state={@url_state}
        page_size={20}
        theme="modern"
        show_filters={true}
        show_pagination={true}
      >
        <:col :let={cdkey} field="code" filter sort label="兑换码">
          <span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
            {cdkey.code}
          </span>
        </:col>

        <:col
          :let={cdkey}
          field="status"
          filter={:select}
          sort
          label="状态"
          filter_options={[
            options: [
              {"激活", :active},
              {"停用", :inactive},
              {"过期", :expired}
            ],
            prompt: "所有状态"
          ]}
        >
          <span class={[
            "px-2 inline-flex text-xs leading-5 font-semibold rounded-full",
            case cdkey.status do
              :active -> "bg-green-100 text-green-800"
              :inactive -> "bg-gray-100 text-gray-800"
              :expired -> "bg-red-100 text-red-800"
            end
          ]}>
            {case cdkey.status do
              :active -> "激活"
              :inactive -> "停用"
              :expired -> "过期"
            end}
          </span>
        </:col>

        <:col :let={cdkey} field="max_uses" filter={:number_range} sort label="最大使用次数">
          <span class="text-sm">{cdkey.max_uses}</span>
        </:col>

        <:col :let={cdkey} field="used_count" sort label="已使用次数">
          <div class="flex items-center">
            <span class="text-sm mr-2">{cdkey.used_count}</span>
            <div class="w-16 bg-gray-200 rounded-full h-2">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={"width: #{min(100, div(cdkey.used_count * 100, max(cdkey.max_uses, 1)))}%"}
              >
              </div>
            </div>
          </div>
        </:col>

        <%!-- <:col :let={cdkey} field="creator" filter sort label="创建人">
          <span class="text-sm">{cdkey.creator}</span>
        </:col> --%>

        <:col :let={cdkey} field="valid_from" filter={:date_range} sort label="生效时间">
          <span class="text-sm">
            {Calendar.strftime(cdkey.valid_from, "%Y-%m-%d %H:%M")}
          </span>
        </:col>

        <:col :let={cdkey} field="valid_to" filter={:date_range} sort label="失效时间">
          <span class="text-sm">
            {Calendar.strftime(cdkey.valid_to, "%Y-%m-%d %H:%M")}
          </span>
        </:col>

        <%!-- <:col :let={cdkey} field="inserted_at" sort label="创建时间">
          <span class="text-sm text-gray-500">
            {Calendar.strftime(cdkey.inserted_at, "%Y-%m-%d %H:%M")}
          </span>
        </:col> --%>
        
    <!-- 操作列 -->
        <:col :let={cdkey} label="操作" class="text-right w-32">
          <div class="flex gap-1 justify-end">
            <button
              phx-click="view_cdkey"
              phx-value-id={cdkey.id}
              class="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
              title="查看详情"
            >
              <.icon name="hero-eye" class="w-4 h-4" />
            </button>
            <button
              phx-click="edit_cdkey"
              phx-value-id={cdkey.id}
              class="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded"
              title="编辑"
            >
              <.icon name="hero-pencil" class="w-4 h-4" />
            </button>
            <button
              phx-click="delete_cdkey"
              phx-value-id={cdkey.id}
              data-confirm="确定要删除这个CDKEY吗？"
              class="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
              title="删除"
            >
              <.icon name="hero-trash" class="w-4 h-4" />
            </button>
          </div>
        </:col>
      </Cinder.Table.table>
      
    <!-- 表单模态框 -->
      <div
        :if={@show_form}
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
        phx-click="close_form"
      >
        <div
          class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"
          phx-click-away="close_form"
        >
          <.cdkey_form form={@form} mode={@form_mode} />
        </div>
      </div>
    </div>
    """
  end

  # CDKEY 表单组件
  defp cdkey_form(assigns) do
    ~H"""
    <div>
      <h2 class="text-2xl font-bold mb-6">
        {if @mode == :new, do: "新建CDKEY", else: "编辑CDKEY"}
      </h2>
      <.simple_form :let={f} for={@form} phx-submit="save_cdkey" phx-change="validate_cdkey">
        <.input
          field={f[:code]}
          type="text"
          label="兑换码"
          placeholder="6-20位大写字母、数字和连字符"
        />

        <.input
          field={f[:status]}
          type="select"
          label="状态"
          options={[
            {"激活", :active},
            {"停用", :inactive},
            {"过期", :expired}
          ]}
        />

        <.input field={f[:max_uses]} type="number" label="最大使用次数" min="1" />

        <div class="grid grid-cols-2 gap-4">
          <.input field={f[:valid_from]} type="datetime-local" label="生效时间" />
          <.input field={f[:valid_to]} type="datetime-local" label="失效时间" />
        </div>

        <.input field={f[:creator]} type="text" label="创建人" />
        
    <!-- 奖励配置 -->
        <div class="space-y-4">
          <label class="block text-sm font-medium text-gray-700">奖励配置</label>
          <.inputs_for :let={reward_form} field={f[:rewards]}>
            <div class="border border-gray-200 rounded-lg p-4 space-y-4">
              <div class="flex justify-between items-center">
                <h4 class="text-sm font-medium text-gray-900">奖励 #{reward_form.index + 1}</h4>
                <button
                  type="button"
                  phx-click="remove_reward"
                  phx-value-index={reward_form.index}
                  class="text-red-600 hover:text-red-800"
                >
                  <.icon name="hero-trash" class="w-4 h-4" />
                </button>
              </div>

              <div class="grid grid-cols-3 gap-4">
                <.input
                  field={reward_form[:type]}
                  type="select"
                  label="奖励类型"
                  options={[
                    {"金币", :coins},
                    {"道具", :items},
                    {"经验", :experience}
                  ]}
                />

                <.input field={reward_form[:amount]} type="number" label="奖励数量" min="0" />

                <.input field={reward_form[:description]} type="text" label="描述" />
              </div>
            </div>
          </.inputs_for>

          <button
            type="button"
            phx-click="add_reward"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <.icon name="hero-plus" class="w-4 h-4 mr-2" /> 添加奖励
          </button>
        </div>

        <div class="flex justify-end space-x-3 pt-6">
          <button
            type="button"
            phx-click="close_form"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            取消
          </button>
          <button
            type="submit"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {if @mode == :new, do: "创建", else: "保存"}
          </button>
        </div>
      </.simple_form>
    </div>
    """
  end

  # 事件处理
  @impl Phoenix.LiveView
  def handle_event("new_cdkey", _params, socket) do
    form =
      Teen.ActivitySystem.Cdkey
      |> AshPhoenix.Form.for_create(:create,
        actor: socket.assigns.current_user,
        forms: [
          rewards: [
            type: :list,
            as: "rewards",
            default: [%Teen.Reward{type: :coins, amount: 100, description: ""}]
          ]
        ]
      )

    {:noreply,
     socket
     |> assign(:selected_cdkey, nil)
     |> assign(:form_mode, :new)
     |> assign(:form, form)
     |> assign(:show_form, true)}
  end

  def handle_event("edit_cdkey", %{"id" => id}, socket) do
    case Ash.get(Teen.ActivitySystem.Cdkey, id, actor: socket.assigns.current_user) do
      {:ok, cdkey} ->
        form =
          cdkey
          |> AshPhoenix.Form.for_update(:update,
            actor: socket.assigns.current_user,
            forms: [
              rewards: [
                type: :list,
                as: "rewards"
              ]
            ]
          )
          |> AshPhoenix.Form.to_form()

        {:noreply,
         socket
         |> assign(:selected_cdkey, cdkey)
         |> assign(:form_mode, :edit)
         |> assign(:form, form)
         |> assign(:show_form, true)}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "CDKEY不存在")}
    end
  end

  def handle_event("view_cdkey", %{"id" => id}, socket) do
    case Ash.get(Teen.ActivitySystem.Cdkey, id, actor: socket.assigns.current_user) do
      {:ok, cdkey} ->
        form =
          cdkey
          |> AshPhoenix.Form.for_update(:update,
            actor: socket.assigns.current_user,
            forms: [
              rewards: [
                type: :list,
                as: "rewards"
              ]
            ]
          )
          |> AshPhoenix.Form.to_form()

        {:noreply,
         socket
         |> assign(:selected_cdkey, cdkey)
         |> assign(:form_mode, :view)
         |> assign(:form, form)
         |> assign(:show_form, true)}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "CDKEY不存在")}
    end
  end

  def handle_event("delete_cdkey", %{"id" => id}, socket) do
    case Ash.get(Teen.ActivitySystem.Cdkey, id, actor: socket.assigns.current_user) do
      {:ok, cdkey} ->
        case Ash.destroy(cdkey, actor: socket.assigns.current_user) do
          {:ok, _} ->
            {:noreply,
             socket
             |> put_flash(:info, "CDKEY删除成功")
             |> refresh_table("cdkey_table")}

          {:error, _reason} ->
            {:noreply, put_flash(socket, :error, "删除失败")}
        end

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "CDKEY不存在")}
    end
  end

  def handle_event("save_cdkey", %{"cdkey" => cdkey_params}, socket) do
    form = AshPhoenix.Form.validate(socket.assigns.form, cdkey_params)

    case AshPhoenix.Form.submit(form) do
      {:ok, _cdkey} ->
        {:noreply,
         socket
         |> put_flash(:info, "CDKEY保存成功")
         |> assign(:show_form, false)
         |> assign(:form, nil)
         |> refresh_table("cdkey_table")}

      {:error, form} ->
        {:noreply, assign(socket, :form, form)}
    end
  end

  def handle_event("validate_cdkey", %{"cdkey" => cdkey_params}, socket) do
    form = AshPhoenix.Form.validate(socket.assigns.form, cdkey_params)
    {:noreply, assign(socket, :form, form)}
  end

  def handle_event("close_form", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_form, false)
     |> assign(:form, nil)}
  end

  def handle_event("add_reward", _params, socket) do
    form = socket.assigns.form
    current_rewards = AshPhoenix.Form.value(form, :rewards) || []

    new_reward = %Teen.Reward{
      type: :coins,
      amount: 0,
      description: "",
      metadata: %{}
    }

    updated_rewards = current_rewards ++ [new_reward]

    updated_form =
      AshPhoenix.Form.add_form(form, :rewards,
        params: %{
          "type" => :coins,
          "amount" => 0,
          "description" => "",
          "metadata" => %{}
        }
      )

    {:noreply, assign(socket, :form, updated_form)}
  end

  def handle_event("remove_reward", %{"index" => index_str}, socket) do
    index = String.to_integer(index_str)
    form = socket.assigns.form

    updated_form = AshPhoenix.Form.remove_form(form, :rewards, index)

    {:noreply, assign(socket, :form, updated_form)}
  end

  def handle_event("batch_create", _params, socket) do
    # TODO: 实现批量创建功能
    {:noreply, put_flash(socket, :info, "批量创建功能开发中")}
  end

  def handle_event("export_cdkeys", _params, socket) do
    # TODO: 实现导出功能
    {:noreply, put_flash(socket, :info, "导出功能开发中")}
  end
end
