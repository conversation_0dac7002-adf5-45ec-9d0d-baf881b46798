defmodule Teen.Live.ActivitySystem.BindingRewardLive do
  @moduledoc """
  绑定奖励管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.BindingReward
    load []
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :binding_type do
        module Backpex.Fields.Select
        label("绑定类型")

        options([
          {"手机号", "phone"},
          {"邮箱", "email"},
          {"银行卡", "bank_card"},
          {"身份证", "id_card"}
        ])
      end

      field :reward_amount do
        module Backpex.Fields.Number
        label("奖励金额")
      end

      field :reward_type do
        module Backpex.Fields.Select
        label("奖励类型")

        options([
          {"金币", "coins"},
          {"积分", "points"},
          {"现金", "cash"}
        ])
      end

      field :one_time_only do
        module Backpex.Fields.Boolean
        label("仅限一次")
      end

      field :verification_required do
        module Backpex.Fields.Boolean
        label("需要验证")
      end

      field :description do
        module Backpex.Fields.Textarea
        label("描述")
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "绑定奖励"

  @impl Backpex.LiveResource
  def plural_name, do: "绑定奖励"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
