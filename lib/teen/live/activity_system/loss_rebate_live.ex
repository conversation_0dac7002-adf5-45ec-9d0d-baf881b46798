defmodule Teen.Live.ActivitySystem.LossRebateLive do
  @moduledoc """
  亏损返利管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.LossRebateJar
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :rebate_name do
        module Backpex.Fields.Text
        label("标题")
      end

      field :max_claims do
        module Backpex.Fields.Number
        label("领取次数")
      end

      field :loss_threshold do
        module Backpex.Fields.Number
        label("用户输钱金币值（分）")
      end

      field :rebate_percentage do
        module Backpex.Fields.Number
        label("输钱奖励（百分比）")
      end

      field :max_rebate do
        module Backpex.Fields.Number
        label("奖励上限（分）")
      end

      field :calculation_period do
        module Backpex.Fields.Select
        label("计算周期")

        options([
          {"每日", "daily"},
          {"每周", "weekly"},
          {"每月", "monthly"}
        ])
      end

      field :rebate_type do
        module Backpex.Fields.Select
        label("返利类型")

        options([
          {"金币", "coins"},
          {"积分", "points"},
          {"现金", "cash"}
        ])
      end

      field :auto_distribute do
        module Backpex.Fields.Boolean
        label("自动发放")
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "亏损返利"

  @impl Backpex.LiveResource
  def plural_name, do: "亏损返利"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
