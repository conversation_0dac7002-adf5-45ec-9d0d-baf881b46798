defmodule Teen.Live.ActivitySystem.CdkeyLiveOld do
  @moduledoc """
  CDKEY管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ecto,
    adapter_config: [
      schema: Teen.Schemas.Cdkey,
      repo: Cypridina.Repo
      # create_changeset: &Teen.Schemas.Cdkey.changeset/3,
      # update_changeset: &Teen.Schemas.Cdkey.update_changeset/3
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "CDKEY"

  @impl Backpex.LiveResource
  def plural_name, do: "CDKEY管理"

  @impl Backpex.LiveResource
  def fields do
    [
      code: %{
        module: Backpex.Fields.Text,
        label: "兑换码",
        searchable: true,
        orderable: true
      },
      status: %{
        module: Backpex.Fields.Select,
        label: "状态",
        options: [
          {"激活", :active},
          {"停用", :inactive},
          {"过期", :expired}
        ],
        orderable: true
      },
      max_uses: %{
        module: Backpex.Fields.Number,
        label: "最大使用次数",
        orderable: true
      },
      used_count: %{
        module: Backpex.Fields.Number,
        label: "已使用次数",
        orderable: true,
        readonly: true
      },
      rewards: %{
        module: Backpex.Fields.InlineCRUD,
        label: "奖励配置",
        type: :embed,
        child_fields: [
          type: %{
            module: Backpex.Fields.Select,
            label: "奖励类型",
            options: [
              {"金币", :coins},
              {"积分", :points},
              {"道具", :items}
            ],
            required: true
          },
          amount: %{
            module: Backpex.Fields.Number,
            label: "奖励数量",
            required: true
          },
          description: %{
            module: Backpex.Fields.Text,
            label: "描述",
            placeholder: "奖励描述（可选）"
          }
        ],
        only: [:new, :edit, :show]
      },
      creator: %{
        module: Backpex.Fields.Text,
        label: "创建人",
        searchable: true,
        orderable: true
      },
      valid_from: %{
        module: Backpex.Fields.DateTime,
        label: "生效时间",
        orderable: true
      },
      valid_to: %{
        module: Backpex.Fields.DateTime,
        label: "失效时间",
        orderable: true
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        orderable: true,
        readonly: true
      }
    ]
  end

  @impl Backpex.LiveResource
  def filters do
    [
      status: %{
        module: Teen.Live.Filters.CdkeyStatusFilter
      },
      creator: %{
        module: Teen.Live.Filters.CdkeyCreatorFilter
      },
      validity: %{
        module: Teen.Live.Filters.CdkeyValidityFilter
      }
    ]
  end

  @impl Backpex.LiveResource
  def resource_actions do
    [
      batch_create: %{
        module: Teen.Live.Actions.CdkeyBatchCreateAction
      },
      export: %{
        module: Teen.Live.Actions.CdkeyExportAction
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :show, :index]
      }
    ]
  end
end

defmodule Teen.Live.ActivitySystem.CdkeyLive do
  use AshBackpex.LiveResource

  backpex do
    resource Teen.ActivitySystem.Cdkey
    load []
    layout({Teen.Layouts, :admin})

    fields do
      field :code

      field :status do
        module Backpex.Fields.Select

        options([
          {"激活", :active},
          {"停用", :inactive},
          {"过期", :expired}
        ])
      end

      field :max_uses
      field :used_count

      field :rewards do
        module Teen.Live.Fields.RewardField
        type :embed

        child_fields(
          type: %{
            module: Backpex.Fields.Select,
            label: "奖励类型",
            options: [
              {"金币", :coins},
              {"积分", :points},
              {"道具", :items}
            ],
            required: true
          },
          amount: %{
            module: Backpex.Fields.Number,
            label: "奖励数量",
            required: true
          },
          description: %{
            module: Backpex.Fields.Text,
            label: "描述",
            placeholder: "奖励描述（可选）"
          }
        )
      end

      field :creator
      field :valid_from
      field :valid_to
    end

    filters do
      filter :status do
        module Teen.Live.Filters.CdkeyStatusFilter
      end

      filter :creator do
        module Teen.Live.Filters.CdkeyCreatorFilter
      end

      filter :validity do
        module Teen.Live.Filters.CdkeyValidityFilter
      end
    end

    resource_actions do
      resource_action(:batch_create, Teen.Live.Actions.CdkeyBatchCreateAction)
      resource_action(:export, Teen.Live.Actions.CdkeyExportAction)
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "CDKEY"

  @impl Backpex.LiveResource
  def plural_name, do: "CDKEY管理"

  # @impl Backpex.LiveResource
end
