defmodule Teen.Live.ActivitySystem.WeeklyCardLive do
  @moduledoc """
  周卡活动管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.WeeklyCard
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :title do
        module Backpex.Fields.Text
        label("周卡标题")
      end

      field :recharge_amount do
        module Backpex.Fields.Number
        label("充值金额（分）")
      end

      field :initial_reward do
        module Backpex.Fields.Number
        label("初始奖励（分）")
      end

      field :daily_reward do
        module Backpex.Fields.Number
        label("每日领取奖励（分）")
      end

      field :claim_days do
        module Backpex.Fields.Number
        label("可领取天数")
      end

      field :status do
        module Backpex.Fields.Select
        label("状态")

        options([
          {"启用", :enabled},
          {"禁用", :disabled}
        ])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "周卡活动"

  @impl Backpex.LiveResource
  def plural_name, do: "周卡活动"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end
end
