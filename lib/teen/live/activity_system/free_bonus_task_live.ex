defmodule Teen.Live.ActivitySystem.FreeBonusTaskLive do
  @moduledoc """
  免费任务管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.FreeBonusTask
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :task_name do
        module Backpex.Fields.Text
        label("任务名称")
      end

      field :task_type do
        module Backpex.Fields.Select
        label("任务类型")

        options([
          {"观看广告", "watch_ad"},
          {"分享游戏", "share_game"},
          {"评价应用", "rate_app"},
          {"关注社交", "follow_social"},
          {"完成调查", "complete_survey"}
        ])
      end

      field :reward_amount do
        module Backpex.Fields.Number
        label("奖励金额")
      end

      field :reward_type do
        module Backpex.Fields.Select
        label("奖励类型")

        options([
          {"金币", "coins"},
          {"积分", "points"},
          {"道具", "items"}
        ])
      end

      field :daily_limit do
        module Backpex.Fields.Number
        label("每日限制次数")
      end

      field :completion_time_seconds do
        module Backpex.Fields.Number
        label("完成时间(秒)")
      end

      field :difficulty_level do
        module Backpex.Fields.Select
        label("难度等级")

        options([
          {"简单", "easy"},
          {"中等", "medium"},
          {"困难", "hard"}
        ])
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "免费任务"

  @impl Backpex.LiveResource
  def plural_name, do: "免费任务"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
