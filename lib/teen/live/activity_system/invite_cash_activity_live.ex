defmodule Teen.Live.ActivitySystem.InviteCashActivityLive do
  @moduledoc """
  邀请奖励活动管理页面
  """

  use AshBackpex.LiveResource

  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.ActivitySystem.InviteCashActivity
    layout({Teen.Layouts, :admin})

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :activity_name do
        module Backpex.Fields.Text
        label("活动名称")
      end

      field :invite_reward do
        module Backpex.Fields.Number
        label("邀请奖励")
      end

      field :invitee_reward do
        module Backpex.Fields.Number
        label("被邀请人奖励")
      end

      field :min_invitee_recharge do
        module Backpex.Fields.Number
        label("被邀请人最小充值")
      end

      field :reward_type do
        module Backpex.Fields.Select
        label("奖励类型")

        options([
          {"金币", "coins"},
          {"积分", "points"},
          {"现金", "cash"}
        ])
      end

      field :max_invites_per_day do
        module Backpex.Fields.Number
        label("每日最大邀请数")
      end

      field :activity_duration_days do
        module Backpex.Fields.Number
        label("活动持续天数")
      end

      field :start_date do
        module Backpex.Fields.Date
        label("开始日期")
      end

      field :end_date do
        module Backpex.Fields.Date
        label("结束日期")
      end

      field :is_active do
        module Backpex.Fields.Boolean
        label("是否激活")
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
      end
    end
  end

  @impl Backpex.LiveResource
  def singular_name, do: "邀请奖励活动"

  @impl Backpex.LiveResource
  def plural_name, do: "邀请奖励活动"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
