<!-- 百人场控制仪表盘 -->
<div class="min-h-screen bg-gray-50">
  <!-- 顶部导航 -->
  <div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-4">
        <div class="flex items-center space-x-4">
          <%= if @selected_game do %>
            <button
              phx-click="back_to_overview"
              class="inline-flex items-center text-gray-500 hover:text-gray-700"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                >
                </path>
              </svg>
              返回总览
            </button>
            <span class="text-gray-300">|</span>
          <% end %>

          <h1 class="text-2xl font-bold text-gray-900">
            <%= if @selected_game do %>
              {(Enum.find(@games, fn g -> g.id == @selected_game end) || %{name: "未知游戏"}).name} - 控制面板
            <% else %>
              百人场控制仪表盘
            <% end %>
          </h1>

          <button
            phx-click="refresh"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              >
              </path>
            </svg>
            刷新
          </button>
        </div>
        
<!-- 时间范围切换 -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">时间范围:</span>
          <div class="flex rounded-md shadow-sm">
            <button
              phx-click="toggle_time_range"
              phx-value-range="minute"
              class={"px-3 py-2 text-sm font-medium border #{if @time_range == :minute, do: "bg-indigo-600 text-white border-indigo-600", else: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"} rounded-l-md"}
            >
              分钟
            </button>
            <button
              phx-click="toggle_time_range"
              phx-value-range="hour"
              class={"px-3 py-2 text-sm font-medium border-t border-b #{if @time_range == :hour, do: "bg-indigo-600 text-white border-indigo-600", else: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"}"}
            >
              小时
            </button>
            <button
              phx-click="toggle_time_range"
              phx-value-range="day"
              class={"px-3 py-2 text-sm font-medium border #{if @time_range == :day, do: "bg-indigo-600 text-white border-indigo-600", else: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"} rounded-r-md"}
            >
              天
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
<!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <%= if @selected_game && @control_status do %>
      <!-- 选中游戏的详细控制面板 -->
      <div class="space-y-6">
        <!-- 当前状态概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 当前库存 -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg
                    class="w-5 h-5 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                    >
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">当前库存</p>
                <p class="text-2xl font-bold text-gray-900">
                  ¥{format_currency(@control_status.current_inventory)}
                </p>
              </div>
            </div>
          </div>
          
<!-- 中心线 -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg
                    class="w-5 h-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                    >
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">中心线</p>
                <p class="text-2xl font-bold text-gray-900">
                  ¥{format_currency(@control_status.center_line)}
                </p>
              </div>
            </div>
          </div>
          
<!-- 库存百分比 -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <svg
                    class="w-5 h-5 text-yellow-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    >
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">库存百分比</p>
                <p class={"text-2xl font-bold #{get_inventory_status_color(@control_status.inventory_percentage)}"}>
                  {format_percentage(@control_status.inventory_percentage)}%
                </p>
              </div>
            </div>
          </div>
          
<!-- 控制模式 -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg
                    class="w-5 h-5 text-purple-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    >
                    </path>
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    >
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">控制模式</p>
                <p class={"text-lg font-bold #{get_control_mode_color(@control_status.control_status.control_mode)}"}>
                  {get_control_mode_text(@control_status.control_status.control_mode)}
                </p>
              </div>
            </div>
          </div>
        </div>
        
<!-- 四级控制线波动趋势图 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <%= if map_size(@control_line_data) > 0 do %>
            <Charts.control_line_chart
              id="control-line-trend-chart"
              chart_data={@control_line_data}
              time_range={@time_range}
              title="四级控制线波动趋势图"
              class="w-full"
            />
          <% else %>
            <div class="text-center py-8">
              <div class="text-gray-500 mb-2">
                <svg
                  class="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  >
                  </path>
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900">正在加载图表数据...</h3>
              <p class="text-sm text-gray-500 mt-1">
                当前数据大小: {map_size(@control_line_data)} |
                选中游戏: {@selected_game} |
                游戏类型: {@selected_game_type}
              </p>
              <div class="mt-4">
                <button phx-click="refresh" class="btn btn-primary btn-sm">重新加载数据</button>
              </div>
            </div>
          <% end %>
        </div>
        
<!-- 四级控制线状态 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">四级控制线状态</h3>
          
<!-- 控制线进度条 -->
          <div class="space-y-4">
            <div class="relative">
              <div class="flex justify-between text-xs text-gray-500 mb-2">
                <span>绝对收分线 (40%)</span>
                <span>预收分线 (70%)</span>
                <span>中心线 (100%)</span>
                <span>预放分线 (130%)</span>
                <span>绝对放分线 (160%)</span>
              </div>

              <div class="w-full bg-gray-200 rounded-full h-6 relative">
                <!-- 控制线区域标记 -->
                <div class="absolute inset-0 flex">
                  <div class="w-1/5 bg-red-500 rounded-l-full opacity-30"></div>
                  <div class="w-1/5 bg-orange-500 opacity-30"></div>
                  <div class="w-1/5 bg-green-500 opacity-30"></div>
                  <div class="w-1/5 bg-blue-500 opacity-30"></div>
                  <div class="w-1/5 bg-purple-500 rounded-r-full opacity-30"></div>
                </div>
                
<!-- 当前位置指示器 -->
                <div
                  class="absolute top-0 h-6 w-3 bg-black rounded transform -translate-x-1.5 flex items-center justify-center"
                  style={"left: #{calculate_control_line_position(@control_status.current_inventory, @control_status.center_line)}%"}
                >
                  <div class="w-1 h-4 bg-white rounded"></div>
                </div>
              </div>

              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>强制收分</span>
                <span>预备收分</span>
                <span>随机</span>
                <span>预备放分</span>
                <span>强制放分</span>
              </div>
            </div>
            
<!-- 控制线数值显示 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              <div class="text-center p-3 bg-red-50 rounded-lg">
                <p class="text-xs text-gray-500">绝对收分线</p>
                <p class="text-sm font-medium text-red-600">
                  ¥{format_currency(@control_status.control_lines.absolute_collect)}
                </p>
              </div>
              <div class="text-center p-3 bg-orange-50 rounded-lg">
                <p class="text-xs text-gray-500">预收分线</p>
                <p class="text-sm font-medium text-orange-600">
                  ¥{format_currency(@control_status.control_lines.pre_collect)}
                </p>
              </div>
              <div class="text-center p-3 bg-blue-50 rounded-lg">
                <p class="text-xs text-gray-500">预放分线</p>
                <p class="text-sm font-medium text-blue-600">
                  ¥{format_currency(@control_status.control_lines.pre_release)}
                </p>
              </div>
              <div class="text-center p-3 bg-purple-50 rounded-lg">
                <p class="text-xs text-gray-500">绝对放分线</p>
                <p class="text-sm font-medium text-purple-600">
                  ¥{format_currency(@control_status.control_lines.absolute_release)}
                </p>
              </div>
            </div>
          </div>
        </div>
        
<!-- 控制操作面板 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">控制操作</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 权重调整 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                控制权重 (1-1000)
              </label>
              <div class="flex space-x-2">
                <input
                  type="number"
                  min="1"
                  max="1000"
                  value={@control_status.control_status.weight || 500}
                  class="flex-1 min-w-0 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  phx-blur="update_control_weight"
                  phx-value-weight
                />
                <button
                  phx-click="update_control_weight"
                  phx-value-weight="500"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  重置
                </button>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                权重越高，触发控制的概率越大
              </p>
            </div>
            
<!-- 手动控制 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                手动控制
              </label>
              <div class="flex space-x-2">
                <button
                  phx-click="manual_control"
                  phx-value-action="force_collect"
                  class="flex-1 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  强制收分
                </button>
                <button
                  phx-click="manual_control"
                  phx-value-action="force_release"
                  class="flex-1 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  强制放分
                </button>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                手动触发控制模式，仅对下一局生效
              </p>
            </div>
          </div>
        </div>
        
<!-- 游戏状态信息 -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">游戏状态信息</h3>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700">最近结果</label>
              <p class="mt-1 text-sm text-gray-900">{@control_status.last_result || "无"}</p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">暗税累计</label>
              <p class="mt-1 text-sm text-gray-900">
                ¥{format_currency(@control_status.dark_tax_accumulated || 0)}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">最后更新</label>
              <p class="mt-1 text-sm text-gray-900">
                <%= if @control_status.last_update do %>
                  {Calendar.strftime(@control_status.last_update, "%Y-%m-%d %H:%M:%S")}
                <% else %>
                  未知
                <% end %>
              </p>
            </div>
          </div>
        </div>
      </div>
    <% else %>
      <!-- 游戏总览 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <%= for game <- @games do %>
          <div
            class="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer"
            phx-click="navigate_to_game"
            phx-value-game_id={game.id}
          >
            <div class="p-6">
              <!-- 游戏标题 -->
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">{game.name}</h3>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  百人场
                </span>
              </div>
              
<!-- 当前状态 -->
              <div class="mb-4">
                <div class="text-xl font-bold text-gray-900">
                  ¥{format_currency(game.current_inventory)}
                </div>
                <div class="text-sm text-gray-500">
                  中心线: ¥{format_currency(game.center_line)}
                </div>
              </div>
              
<!-- 控制状态 -->
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-500">状态:</span>
                  <span class={"text-sm font-medium #{get_control_mode_color(game.control_status.control_mode)}"}>
                    {get_control_mode_text(game.control_status.control_mode)}
                  </span>
                </div>
                <div class="text-sm text-gray-500">
                  权重: {game.control_status.weight || 500}
                </div>
              </div>
              
<!-- 库存百分比条 -->
              <div class="mt-4">
                <div class="flex justify-between text-xs text-gray-500 mb-1">
                  <span>收分区</span>
                  <span>中心线</span>
                  <span>放分区</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <% position =
                    calculate_control_line_position(game.current_inventory, game.center_line) %>
                  <div
                    class={"h-2 rounded-full #{get_inventory_status_color(game.inventory_percentage)}"}
                    style={"width: #{min(100, position)}%"}
                  >
                  </div>
                </div>
                <div class="flex justify-between text-xs text-gray-400 mt-1">
                  <span>40%</span>
                  <span>100%</span>
                  <span>160%</span>
                </div>
              </div>
              
<!-- 最近结果 -->
              <%= if game.last_result && game.last_result != "unknown" do %>
                <div class="mt-3 text-xs text-gray-500">
                  最近结果: {game.last_result}
                </div>
              <% end %>
              
<!-- 错误状态 -->
              <%= if Map.get(game, :error) do %>
                <div class="mt-3 text-xs text-red-500">
                  状态异常: {inspect(game.error)}
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
