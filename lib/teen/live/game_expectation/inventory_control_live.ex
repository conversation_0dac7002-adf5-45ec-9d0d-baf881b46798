defmodule Teen.Live.GameExpectation.InventoryControlLive do
  @moduledoc """
  游戏控制仪表盘

  统一游戏控制界面，支持不同类型游戏的模板切换：
  - 百人场游戏：龙虎、jhandi_munda、crash（使用四级控制线）
  - 单人游戏：老虎机等（使用钱包控制）
  - 多人游戏：其他多人游戏（使用钱包控制）
  """

  use CypridinaWeb, :live_view
  require Logger
  require Ash.Query
  import Ash.Query

  alias Teen.Resources.Inventory.WalletControl
  alias Teen.Inventory.HundredPlayerControlCalculator
  alias CypridinaWeb.Components.Charts

  # 3秒刷新一次
  @refresh_interval 3000

  # 百人场游戏类型映射
  @hundred_player_games %{
    22 => %{slug: "longhu", name: "龙虎斗", type: :longhu},
    21 => %{slug: "jhandi_munda", name: "骰子游戏", type: :jhandi_munda},
    23 => %{slug: "crash", name: "崩溃游戏", type: :crash}
  }

  # ==================== LiveView 回调 ====================

  @impl true
  def mount(_params, _session, socket) do
    Logger.info("🎮 [GAME_CONTROL_DASHBOARD] 游戏控制仪表盘启动")

    if connected?(socket) do
      # 设置定时刷新
      Process.send_after(self(), :refresh_data, @refresh_interval)
    end

    socket =
      socket
      |> assign(:page_title, "游戏控制仪表盘")
      |> assign(:current_url, "/admin/inventory_control/")
      |> assign(:loading, true)
      |> assign(:games, [])
      |> assign(:selected_game, nil)
      |> assign(:selected_game_type, nil)
      # :overview, :hundred_player, :wallet
      |> assign(:game_template, :overview)
      |> assign(:time_range, :hour)
      |> assign(:total_balance, 0)
      |> assign(:total_history, [])
      |> assign(:game_history, [])
      |> assign(:hundred_player_status, nil)
      |> assign(:fluid?, true)
      |> load_initial_data()
      |> load_total_history(:hour)

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(params, _url, socket) do
    selected_game = Map.get(params, "game_id")

    socket =
      if selected_game do
        game_id = String.to_integer(selected_game)
        {game_template, game_type} = determine_game_template(game_id)

        socket
        |> assign(:selected_game, game_id)
        |> assign(:selected_game_type, game_type)
        |> assign(:game_template, game_template)
        |> load_game_data(game_id, game_template)
      else
        socket
        |> assign(:selected_game, nil)
        |> assign(:selected_game_type, nil)
        |> assign(:game_template, :overview)
        |> assign(:hundred_player_status, nil)
      end

    {:noreply, socket}
  end

  @impl true
  def handle_event("navigate_to_game", %{"game_id" => game_id}, socket) do
    # 根据游戏类型决定导航目标
    game_id_int = String.to_integer(game_id)

    if Map.has_key?(@hundred_player_games, game_id_int) do
      # 百人场游戏：使用URL参数在同一个LiveView中切换模板
      {:noreply, push_navigate(socket, to: ~p"/admin/inventory_control/?game_id=#{game_id}")}
    else
      # 其他游戏：导航到专门的钱包控制界面
      {:noreply, push_navigate(socket, to: ~p"/admin/inventory_control/game/#{game_id}")}
    end
  end

  @impl true
  def handle_event("back_to_overview", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/admin/inventory_control/")}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    socket =
      socket
      |> load_initial_data()
      |> maybe_refresh_game_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_time_range", %{"range" => range}, socket) do
    time_range = String.to_atom(range)

    socket =
      socket
      |> assign(:time_range, time_range)
      |> load_total_history(time_range)
      |> maybe_refresh_game_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("update_control_weight", %{"weight" => weight}, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        case update_hundred_player_control_weight(game_id, weight) do
          {:ok, _config} ->
            socket =
              socket
              |> put_flash(:info, "控制权重更新成功")
              |> load_game_data(game_id, :hundred_player)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "控制权重更新失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("manual_control", %{"action" => action}, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        case trigger_hundred_player_manual_control(game_id, game_type, action) do
          {:ok, _result} ->
            socket =
              socket
              |> put_flash(:info, "手动控制执行成功")
              |> load_game_data(game_id, :hundred_player)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "手动控制执行失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("update_control_config", %{"field" => field, "value" => value}, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        case update_hundred_player_control_config(game_id, field, value) do
          {:ok, _config} ->
            socket =
              socket
              |> put_flash(:info, "配置更新成功")
              |> load_game_data(game_id, :hundred_player)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "配置更新失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("save_winner_tax_rate", %{"winner_tax_rate" => value}, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        case update_hundred_player_control_config(game_id, "winner_tax_rate", value) do
          {:ok, _config} ->
            socket =
              socket
              |> put_flash(:info, "明税抽水比例保存成功")
              |> load_game_data(game_id, :hundred_player)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "明税抽水比例保存失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("save_dark_tax_rate", %{"dark_tax_rate" => value}, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        case update_hundred_player_control_config(game_id, "dark_tax_rate", value) do
          {:ok, _config} ->
            socket =
              socket
              |> put_flash(:info, "暗税千分比保存成功")
              |> load_game_data(game_id, :hundred_player)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "暗税千分比保存失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("save_collect_line_config", params, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        # 批量更新收分线配置
        results = [
          update_hundred_player_control_config(
            game_id,
            "collect_line_max",
            params["collect_line_max"]
          ),
          update_hundred_player_control_config(
            game_id,
            "collect_line_min",
            params["collect_line_min"]
          ),
          update_hundred_player_control_config(
            game_id,
            "collect_line_ratio",
            params["collect_line_ratio"]
          ),
          update_hundred_player_control_config(
            game_id,
            "pre_collect_line_ratio",
            params["pre_collect_line_ratio"]
          )
        ]

        # 检查是否所有更新都成功
        if Enum.all?(results, &match?({:ok, _}, &1)) do
          socket =
            socket
            |> put_flash(:info, "收分线配置保存成功")
            |> load_game_data(game_id, :hundred_player)

          {:noreply, socket}
        else
          failed_results = Enum.filter(results, &match?({:error, _}, &1))
          socket = put_flash(socket, :error, "收分线配置保存失败: #{inspect(failed_results)}")
          {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("save_release_line_config", params, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        # 批量更新放分线配置
        results = [
          update_hundred_player_control_config(
            game_id,
            "release_line_max",
            params["release_line_max"]
          ),
          update_hundred_player_control_config(
            game_id,
            "release_line_min",
            params["release_line_min"]
          ),
          update_hundred_player_control_config(
            game_id,
            "release_line_ratio",
            params["release_line_ratio"]
          ),
          update_hundred_player_control_config(
            game_id,
            "pre_release_line_ratio",
            params["pre_release_line_ratio"]
          )
        ]

        # 检查是否所有更新都成功
        if Enum.all?(results, &match?({:ok, _}, &1)) do
          socket =
            socket
            |> put_flash(:info, "放分线配置保存成功")
            |> load_game_data(game_id, :hundred_player)

          {:noreply, socket}
        else
          failed_results = Enum.filter(results, &match?({:error, _}, &1))
          socket = put_flash(socket, :error, "放分线配置保存失败: #{inspect(failed_results)}")
          {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("save_control_weight", %{"control_weight" => weight}, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        case update_hundred_player_control_weight(game_id, weight) do
          {:ok, _config} ->
            socket =
              socket
              |> put_flash(:info, "控制权重保存成功")
              |> load_game_data(game_id, :hundred_player)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "控制权重保存失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("reset_control_weight", _params, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        case update_hundred_player_control_weight(game_id, "500") do
          {:ok, _config} ->
            socket =
              socket
              |> put_flash(:info, "控制权重重置成功")
              |> load_game_data(game_id, :hundred_player)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "控制权重重置失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("reset_control_config", _params, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type,
          socket.assigns.game_template} do
      {game_id, game_type, :hundred_player} when not is_nil(game_id) and not is_nil(game_type) ->
        case reset_hundred_player_control_config(game_id) do
          {:ok, _config} ->
            socket =
              socket
              |> put_flash(:info, "配置重置成功")
              |> load_game_data(game_id, :hundred_player)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "配置重置失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择百人场游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info(:refresh_data, socket) do
    # 定时刷新数据
    socket =
      socket
      |> load_initial_data()
      |> load_total_history(socket.assigns.time_range)
      |> maybe_refresh_game_data()

    # 设置下次刷新
    Process.send_after(self(), :refresh_data, @refresh_interval)

    {:noreply, socket}
  end

  @impl true
  def handle_info(_msg, socket) do
    {:noreply, socket}
  end

  # ==================== 私有函数 ====================

  # 格式化数字显示
  defp format_number(number) when is_number(number) do
    Number.Delimit.number_to_delimited(number, precision: 0)
  end

  defp format_number(_), do: "0"

  # 获取动作文本
  defp get_action_text(:force_collect), do: "强制收分"
  defp get_action_text(:collect), do: "收分"
  defp get_action_text(:force_release), do: "强制放分"
  defp get_action_text(:release), do: "放分"
  defp get_action_text(:random), do: "随机"
  defp get_action_text(_), do: "未知"

  # 获取动作颜色
  defp get_action_color(:force_collect), do: "text-red-600"
  defp get_action_color(:collect), do: "text-orange-600"
  defp get_action_color(:force_release), do: "text-green-600"
  defp get_action_color(:release), do: "text-blue-600"
  defp get_action_color(:random), do: "text-gray-600"
  defp get_action_color(_), do: "text-gray-400"

  # 计算库存百分比
  defp calculate_inventory_percentage(current_balance, base_balance) when base_balance > 0 do
    current_balance / base_balance * 100
  end

  defp calculate_inventory_percentage(_, _), do: 0

  # 获取库存条颜色
  defp get_inventory_bar_color(percentage) when percentage <= 60, do: "bg-red-500"
  defp get_inventory_bar_color(percentage) when percentage <= 80, do: "bg-orange-500"
  defp get_inventory_bar_color(percentage) when percentage <= 120, do: "bg-green-500"
  defp get_inventory_bar_color(percentage) when percentage <= 150, do: "bg-blue-500"
  defp get_inventory_bar_color(_), do: "bg-purple-500"

  defp load_initial_data(socket) do
    # 获取所有游戏及其余额，并标记游戏类型
    games =
      WalletControl.get_all_wallet_status()
      |> Enum.map(fn game ->
        game_template = if Map.has_key?(@hundred_player_games, game.id), do: "百人场", else: "钱包控制"
        Map.put(game, :template_type, game_template)
      end)

    total_balance = WalletControl.get_total_balance()

    socket
    |> assign(:loading, false)
    |> assign(:games, games)
    |> assign(:total_balance, total_balance)
  end

  defp load_total_history(socket, time_range) do
    total_history = WalletControl.get_total_wallet_history(time_range)

    socket
    |> assign(:total_history, total_history)
  end

  defp load_game_data(socket, _game_id, :overview), do: socket

  defp load_game_data(socket, game_id, :hundred_player) when is_integer(game_id) do
    # 获取百人场游戏控制状态
    game_info = Map.get(@hundred_player_games, game_id)

    if game_info do
      case HundredPlayerControlCalculator.get_control_status(game_id, game_info.type) do
        {:ok, status} ->
          # 生成控制线图表数据
          chart_data =
            generate_control_line_chart_data(game_id, game_info.type, socket.assigns.time_range)

          enhanced_status = Map.put(status, :control_line_chart_data, chart_data)
          socket |> assign(:hundred_player_status, enhanced_status)

        {:error, error} ->
          Logger.error("🎮 [GAME_CONTROL] 获取百人场状态失败: #{inspect(error)}")
          socket |> assign(:hundred_player_status, nil)
      end
    else
      socket |> assign(:hundred_player_status, nil)
    end
  end

  defp load_game_data(socket, game_id, :wallet) when is_integer(game_id) do
    # 获取钱包控制游戏历史数据
    game_history = WalletControl.get_wallet_history(game_id, socket.assigns.time_range)
    socket |> assign(:game_history, game_history)
  end

  defp load_game_data(socket, _game_id, _template), do: socket

  defp determine_game_template(game_id) do
    if Map.has_key?(@hundred_player_games, game_id) do
      game_info = @hundred_player_games[game_id]
      {:hundred_player, game_info.type}
    else
      {:wallet, :wallet_control}
    end
  end

  defp maybe_refresh_game_data(socket) do
    case {socket.assigns.selected_game, socket.assigns.game_template} do
      {game_id, template} when not is_nil(game_id) and template != :overview ->
        load_game_data(socket, game_id, template)

      _ ->
        socket
    end
  end

  defp format_number(nil), do: "N/A"
  defp format_number(num) when is_integer(num), do: to_string(num)

  defp format_number(num) when is_float(num) do
    num
    |> :erlang.float_to_binary(decimals: 2)
    |> String.replace(~r/\.?0+$/, "")
  end

  defp format_number(num), do: to_string(num)

  defp format_currency(nil), do: "¥0"

  defp format_currency(amount) when is_integer(amount) do
    "¥#{:erlang.float_to_binary(amount / 100, decimals: 2)}"
  end

  defp format_currency(amount) when is_float(amount) do
    "¥#{:erlang.float_to_binary(amount / 100, decimals: 2)}"
  end

  defp format_currency(amount), do: "¥#{amount}"

  defp get_status_color(:active), do: "text-green-600"
  defp get_status_color(:inactive), do: "text-gray-500"
  defp get_status_color(:error), do: "text-red-600"
  defp get_status_color(_), do: "text-gray-500"

  defp get_action_color(:force_release), do: "text-blue-600 font-bold"
  defp get_action_color(:release), do: "text-blue-500"
  defp get_action_color(:force_collect), do: "text-red-600 font-bold"
  defp get_action_color(:collect), do: "text-red-500"
  defp get_action_color(:random), do: "text-gray-600"
  defp get_action_color(_), do: "text-gray-500"

  defp get_action_text(:force_release), do: "强制放分"
  defp get_action_text(:release), do: "放分"
  defp get_action_text(:force_collect), do: "强制收分"
  defp get_action_text(:collect), do: "收分"
  defp get_action_text(:random), do: "随机"
  defp get_action_text(action), do: to_string(action)

  defp calculate_inventory_percentage(current, center_line) do
    # 确保使用 Decimal 计算
    current_decimal = if is_struct(current, Decimal), do: current, else: Decimal.new("#{current}")

    center_decimal =
      if is_struct(center_line, Decimal), do: center_line, else: Decimal.new("#{center_line}")

    # 避免除零
    if Decimal.equal?(center_decimal, Decimal.new("0")) do
      50.0
    else
      percentage =
        current_decimal
        |> Decimal.div(center_decimal)
        |> Decimal.mult(100)
        |> safe_to_float()

      # 不限制范围，允许负值显示
      percentage
    end
  end

  defp get_inventory_bar_color(percentage) when percentage < 50, do: "bg-red-500"
  defp get_inventory_bar_color(percentage) when percentage < 80, do: "bg-yellow-500"
  defp get_inventory_bar_color(percentage) when percentage < 120, do: "bg-green-500"
  defp get_inventory_bar_color(percentage) when percentage < 150, do: "bg-yellow-500"
  defp get_inventory_bar_color(_), do: "bg-red-500"

  # 转换总历史数据为图表格式
  defp convert_total_history_to_chart_data(history_data)
       when is_list(history_data) and length(history_data) > 0 do
    # 提取钱包余额数据
    wallet_values = Enum.map(history_data, & &1.balance)

    # 生成时间标签
    time_labels =
      Enum.map(history_data, fn data_point ->
        DateTime.to_iso8601(data_point.timestamp)
      end)

    %{
      labels: time_labels,
      values: wallet_values
    }
  end

  defp convert_total_history_to_chart_data(_) do
    # 如果没有历史数据，返回空数据
    %{
      labels: [],
      values: []
    }
  end

  # 转换游戏历史数据为图表格式
  defp convert_game_history_to_chart_data(history_data)
       when is_list(history_data) and length(history_data) > 0 do
    # 提取钱包余额数据
    wallet_values = Enum.map(history_data, & &1.balance)

    # 生成时间标签
    time_labels =
      Enum.map(history_data, fn data_point ->
        DateTime.to_iso8601(data_point.timestamp)
      end)

    %{
      labels: time_labels,
      values: wallet_values
    }
  end

  defp convert_game_history_to_chart_data(_) do
    # 如果没有历史数据，返回空数据
    %{
      labels: [],
      values: []
    }
  end

  # 百人场相关格式化函数
  defp format_currency(amount) when is_number(amount) do
    :erlang.float_to_binary(amount / 100, decimals: 2)
  end

  defp format_currency(_), do: "0.00"

  defp format_percentage(percentage) when is_number(percentage) do
    :erlang.float_to_binary(percentage, decimals: 1)
  end

  defp format_percentage(_), do: "0.0"

  defp get_control_mode_text(:absolute_collect), do: "强制收分"
  defp get_control_mode_text(:pre_collect), do: "预备收分"
  defp get_control_mode_text(:random), do: "随机"
  defp get_control_mode_text(:pre_release), do: "预备放分"
  defp get_control_mode_text(:absolute_release), do: "强制放分"
  defp get_control_mode_text(_), do: "未知"

  defp get_control_mode_color(:absolute_collect), do: "text-red-600 font-bold"
  defp get_control_mode_color(:pre_collect), do: "text-orange-600"
  defp get_control_mode_color(:random), do: "text-gray-600"
  defp get_control_mode_color(:pre_release), do: "text-blue-600"
  defp get_control_mode_color(:absolute_release), do: "text-green-600 font-bold"
  defp get_control_mode_color(_), do: "text-gray-400"

  defp get_inventory_status_color(percentage) when percentage <= 40, do: "text-red-600"
  defp get_inventory_status_color(percentage) when percentage <= 70, do: "text-orange-600"
  defp get_inventory_status_color(percentage) when percentage >= 160, do: "text-green-600"
  defp get_inventory_status_color(percentage) when percentage >= 130, do: "text-blue-600"
  defp get_inventory_status_color(_), do: "text-gray-600"

  # 计算控制线位置（百分比）
  # 根据文档，当前库存和中心线都可以为负值
  defp calculate_control_line_position(current_inventory, center_line) when center_line != 0 do
    # 确保使用 Decimal 计算
    current_decimal =
      if is_struct(current_inventory, Decimal),
        do: current_inventory,
        else: Decimal.new("#{current_inventory}")

    center_decimal =
      if is_struct(center_line, Decimal), do: center_line, else: Decimal.new("#{center_line}")

    # 避免除零检查
    if Decimal.equal?(center_decimal, Decimal.new("0")) do
      50.0
    else
      percentage =
        current_decimal
        |> Decimal.div(center_decimal)
        |> Decimal.mult(100)
        |> safe_to_float()

      cond do
        percentage <= 40 -> min(20, max(0, percentage / 40 * 20))
        percentage <= 70 -> 20 + min(20, max(0, (percentage - 40) / 30 * 20))
        percentage <= 130 -> 40 + min(20, max(0, (percentage - 70) / 60 * 20))
        percentage <= 160 -> 60 + min(20, max(0, (percentage - 130) / 30 * 20))
        true -> 80 + min(20, max(0, (percentage - 160) / 40 * 20))
      end
    end
  end

  # 中心线为0时，默认位置为50%（中心位置）
  defp calculate_control_line_position(_, 0), do: 50

  defp calculate_position_percentage(percentage) do
    cond do
      percentage <= 60 -> min(20, max(0, percentage / 60 * 20))
      percentage <= 80 -> 20 + min(20, max(0, (percentage - 60) / 20 * 20))
      percentage <= 120 -> 40 + min(20, max(0, (percentage - 80) / 40 * 20))
      percentage <= 150 -> 60 + min(20, max(0, (percentage - 120) / 30 * 20))
      true -> 80 + min(20, max(0, (percentage - 150) / 50 * 20))
    end
  end

  defp update_hundred_player_control_weight(game_id, weight_str) do
    with {weight, ""} <- Integer.parse(weight_str),
         {:ok, config} <-
           Teen.Resources.Inventory.GameControlConfig.get_by_game_id_and_type(game_id, 3) do
      Teen.Resources.Inventory.GameControlConfig.update(config, %{control_weight: weight})
    else
      :error -> {:error, "权重必须是数字"}
      {:error, reason} -> {:error, reason}
    end
  end

  defp trigger_hundred_player_manual_control(game_id, game_type, action) do
    # 这里可以实现手动控制逻辑
    Logger.info(
      "🎮 [GAME_CONTROL] 百人场手动控制: game_id=#{game_id}, type=#{game_type}, action=#{action}"
    )

    {:ok, :triggered}
  end

  defp update_hundred_player_control_config(game_id, field, value_str) do
    with {:ok, config} <-
           Teen.Resources.Inventory.GameControlConfig.get_by_game_id_and_type(game_id, 3),
         {:ok, parsed_value} <- parse_config_value(field, value_str) do
      # 将字符串字段名转换为原子
      field_atom = String.to_existing_atom(field)
      update_attrs = %{field_atom => parsed_value}
      Teen.Resources.Inventory.GameControlConfig.update(config, update_attrs)
    else
      {:error, reason} -> {:error, reason}
      :error -> {:error, "配置值格式错误"}
    end
  end

  defp reset_hundred_player_control_config(game_id) do
    with {:ok, config} <-
           Teen.Resources.Inventory.GameControlConfig.get_by_game_id_and_type(game_id, 3) do
      update_attrs = %{
        dark_tax_rate: -50,
        # 重置为默认浮动配置
        collect_line_max: 30000,
        collect_line_min: 5000,
        collect_line_ratio: Decimal.new("0.2"),
        pre_collect_line_ratio: Decimal.new("0.7"),
        release_line_max: 30000,
        release_line_min: 5000,
        release_line_ratio: Decimal.new("0.2"),
        pre_release_line_ratio: Decimal.new("0.7")
      }

      Teen.Resources.Inventory.GameControlConfig.update(config, update_attrs)
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp parse_config_value("dark_tax_rate", value_str) do
    case Integer.parse(value_str) do
      {value, ""} when value >= -1000 and value <= 1000 -> {:ok, value}
      _ -> :error
    end
  end

  defp parse_config_value(field, value_str)
       when field in [
              "collect_line_ratio",
              "pre_collect_line_ratio",
              "release_line_ratio",
              "pre_release_line_ratio"
            ] do
    case Float.parse(value_str) do
      {value, ""} when value > 0 and value <= 1.0 ->
        # 比例值：0.2 表示 20%
        decimal_value = Decimal.new("#{value}")
        {:ok, decimal_value}

      _ ->
        :error
    end
  end

  defp parse_config_value(field, value_str)
       when field in [
              "collect_line_max",
              "collect_line_min",
              "release_line_max",
              "release_line_min"
            ] do
    case Integer.parse(value_str) do
      {value, ""} when value > 0 and value <= 10_000_000 -> {:ok, value}
      _ -> :error
    end
  end

  defp parse_config_value("winner_tax_rate", value_str) do
    case Float.parse(value_str) do
      {value, ""} when value >= 0 and value <= 100 ->
        # 将百分比转换为小数：5% -> 0.05
        decimal_value = Decimal.new("#{value / 100}")
        {:ok, decimal_value}

      _ ->
        :error
    end
  end

  defp parse_config_value(_, _), do: :error

  # 安全转换为浮点数
  defp safe_to_float(value) when is_float(value), do: value
  defp safe_to_float(value) when is_integer(value), do: value * 1.0
  defp safe_to_float(value) when is_struct(value, Decimal), do: Decimal.to_float(value)

  defp safe_to_float(value) when is_binary(value) do
    case Float.parse(value) do
      {float_val, _} -> float_val
      :error -> 0.0
    end
  end

  defp safe_to_float(_), do: 0.0

  # 生成控制线图表数据
  defp generate_control_line_chart_data(game_id, game_type, time_range) do
    try do
      # 获取当前控制状态
      case HundredPlayerControlCalculator.get_control_status(game_id, game_type) do
        {:ok, current_status} ->
          # 获取真实历史数据
          data_points = get_real_historical_data(game_id, game_type, time_range, current_status)

          # 构建图表数据
          chart_data = %{
            timestamps: Enum.map(data_points, & &1.timestamp),
            current_inventory: Enum.map(data_points, &safe_to_float(&1.current_inventory)),
            center_line: Enum.map(data_points, &safe_to_float(&1.center_line)),
            absolute_collect: Enum.map(data_points, &safe_to_float(&1.absolute_collect)),
            pre_collect: Enum.map(data_points, &safe_to_float(&1.pre_collect)),
            pre_release: Enum.map(data_points, &safe_to_float(&1.pre_release)),
            absolute_release: Enum.map(data_points, &safe_to_float(&1.absolute_release)),
            control_modes: Enum.map(data_points, & &1.control_mode)
          }

          Logger.info("🎮 [INVENTORY_CONTROL] 生成真实图表数据成功: #{map_size(chart_data)}个数据集")
          chart_data

        {:error, error} ->
          Logger.error("🎮 [INVENTORY_CONTROL] 获取控制状态失败: #{inspect(error)}")
          %{}
      end
    rescue
      error ->
        Logger.error("🎮 [INVENTORY_CONTROL] 生成图表数据失败: #{inspect(error)}")
        %{}
    end
  end

  # 获取真实历史数据
  defp get_real_historical_data(game_id, game_type, time_range, current_status) do
    # 获取当前数据库状态
    case Teen.Resources.Inventory.HundredPlayerGameState.get_by_game_id_and_type(
           game_id,
           game_type
         ) do
      {:ok, game_state} ->
        # 获取游戏记录来计算历史变化
        game_records = get_recent_game_records(game_id, game_type, time_range)

        # 基于真实游戏记录生成数据点
        generate_data_points_from_records(time_range, current_status, game_state, game_records)

      {:error, _} ->
        # 如果数据库中没有记录，使用当前状态生成数据点
        generate_realistic_data_points(time_range, current_status, nil)
    end
  end

  # 获取最近的游戏记录
  defp get_recent_game_records(game_id, game_type, time_range) do
    # 根据时间范围计算查询的时间段
    {duration_seconds, _} =
      case time_range do
        # 查询20分钟内的记录
        :minute -> {1200, 60}
        # 查询24小时内的记录
        :hour -> {86400, 3600}
        # 查询7天内的记录
        :day -> {604_800, 86400}
        # 默认24小时
        _ -> {86400, 3600}
      end

    # 计算查询开始时间
    start_time = DateTime.utc_now() |> DateTime.add(-duration_seconds, :second)

    try do
      # 使用已有的 list_by_game_type 动作来获取游戏记录
      case Teen.GameManagement.GameRecord.list_by_game_type(game_type) do
        {:ok, records} ->
          # 在代码中过滤游戏ID和时间范围
          filtered_records =
            records
            |> Enum.filter(fn record ->
              record.game_id == to_string(game_id) and
                DateTime.compare(record.inserted_at, start_time) != :lt
            end)
            |> Enum.sort_by(& &1.inserted_at, {:desc, DateTime})
            # 限制最多500条记录
            |> Enum.take(500)

          Logger.info("📊 获取到 #{length(filtered_records)} 条游戏记录用于生成图表数据")
          filtered_records

        {:error, error} ->
          Logger.error("📊 获取游戏记录失败: #{inspect(error)}")
          []
      end
    rescue
      error ->
        Logger.error("📊 查询游戏记录时出错: #{inspect(error)}")
        []
    end
  end

  # 基于真实游戏记录生成数据点
  defp generate_data_points_from_records(time_range, current_status, game_state, game_records) do
    # 根据时间范围生成不同数量的数据点
    {points_count, time_step} =
      case time_range do
        # 20个点，每分钟一个
        :minute -> {20, 60}
        # 24个点，每小时一个
        :hour -> {24, 3600}
        # 7个点，每天一个
        :day -> {7, 86400}
        # 默认小时
        _ -> {24, 3600}
      end

    # 获取当前时间
    now = DateTime.utc_now()

    # 生成时间序列
    timestamps =
      0..(points_count - 1)
      |> Enum.map(fn i -> DateTime.add(now, -(points_count - 1 - i) * time_step, :second) end)

    # 按时间段分组游戏记录
    records_by_time = group_records_by_time_slots(game_records, timestamps, time_step)

    # 获取基础数据
    base_inventory = game_state.current_inventory
    base_center_line = game_state.center_line

    # 生成数据点
    timestamps
    |> Enum.with_index()
    |> Enum.map(fn {timestamp, index} ->
      # 获取该时间段的游戏记录
      time_records = Map.get(records_by_time, index, [])

      # 基于游戏记录计算库存变化
      {inventory, center_line} =
        calculate_inventory_from_records(
          time_records,
          base_inventory,
          base_center_line,
          index,
          points_count
        )

      # 重新计算控制线
      control_lines =
        calculate_control_lines_for_point(center_line, current_status.control_status.config)

      # 确定控制模式
      control_mode = determine_control_mode_for_point(inventory, control_lines)

      %{
        timestamp: timestamp,
        current_inventory: safe_to_float(inventory),
        center_line: safe_to_float(center_line),
        absolute_collect: safe_to_float(control_lines.absolute_collect),
        pre_collect: safe_to_float(control_lines.pre_collect),
        pre_release: safe_to_float(control_lines.pre_release),
        absolute_release: safe_to_float(control_lines.absolute_release),
        control_mode: control_mode
      }
    end)
  end

  # 按时间段分组游戏记录
  defp group_records_by_time_slots(records, timestamps, time_step) do
    timestamps
    |> Enum.with_index()
    |> Enum.reduce(%{}, fn {timestamp, index}, acc ->
      # 计算时间段的开始和结束
      slot_start = timestamp
      slot_end = DateTime.add(timestamp, time_step, :second)

      # 筛选该时间段的记录
      slot_records =
        Enum.filter(records, fn record ->
          DateTime.compare(record.inserted_at, slot_start) != :lt and
            DateTime.compare(record.inserted_at, slot_end) == :lt
        end)

      Map.put(acc, index, slot_records)
    end)
  end

  # 基于游戏记录计算库存变化
  defp calculate_inventory_from_records(
         time_records,
         base_inventory,
         base_center_line,
         index,
         total_points
       ) do
    # 计算该时间段的净输赢
    net_change =
      Enum.reduce(time_records, Decimal.new("0"), fn record, acc ->
        # 计算玩家的净输赢 (负值表示玩家输了钱，正值表示玩家赢了钱)
        player_net = Decimal.sub(record.win_amount, record.bet_amount)

        # 对于平台来说，玩家的输赢是相反的
        platform_net = Decimal.negate(player_net)

        Decimal.add(acc, platform_net)
      end)

    # 计算进度因子 (随时间线性变化)
    progress = index / total_points

    # 应用游戏记录的变化
    inventory_change = Decimal.mult(net_change, Decimal.new("#{1 - progress}"))
    new_inventory = Decimal.add(base_inventory, inventory_change)

    # 中心线通常变化较小，基于库存变化的一个小比例
    center_line_change = Decimal.mult(inventory_change, Decimal.new("0.1"))
    new_center_line = Decimal.add(base_center_line, center_line_change)

    {new_inventory, new_center_line}
  end

  # 生成基于真实数据的历史数据点
  defp generate_realistic_data_points(time_range, current_status, game_state) do
    # 根据时间范围生成不同数量的数据点
    {points_count, time_step} =
      case time_range do
        # 20个点，每分钟一个
        :minute -> {20, 60}
        # 24个点，每小时一个
        :hour -> {24, 3600}
        # 7个点，每天一个
        :day -> {7, 86400}
        # 默认小时
        _ -> {24, 3600}
      end

    # 获取当前时间
    now = DateTime.utc_now()

    # 生成时间序列
    timestamps =
      0..(points_count - 1)
      |> Enum.map(fn i -> DateTime.add(now, -(points_count - 1 - i) * time_step, :second) end)

    # 使用真实数据作为基础
    base_inventory =
      if game_state do
        game_state.current_inventory
      else
        current_status.current_inventory
      end

    base_center_line =
      if game_state do
        game_state.center_line
      else
        current_status.center_line
      end

    last_update =
      if game_state do
        game_state.last_update
      else
        DateTime.utc_now()
      end

    # 计算数据变化的时间差
    time_diff = DateTime.diff(now, last_update)

    # 生成数据点
    timestamps
    |> Enum.with_index()
    |> Enum.map(fn {timestamp, index} ->
      # 使用更真实的数据变化模式
      {inventory, center_line} =
        calculate_real_data_point(
          index,
          points_count,
          base_inventory,
          base_center_line,
          time_diff,
          timestamp
        )

      # 重新计算控制线
      control_lines =
        calculate_control_lines_for_point(center_line, current_status.control_status.config)

      # 确定控制模式
      control_mode = determine_control_mode_for_point(inventory, control_lines)

      %{
        timestamp: timestamp,
        current_inventory: safe_to_float(inventory),
        center_line: safe_to_float(center_line),
        absolute_collect: safe_to_float(control_lines.absolute_collect),
        pre_collect: safe_to_float(control_lines.pre_collect),
        pre_release: safe_to_float(control_lines.pre_release),
        absolute_release: safe_to_float(control_lines.absolute_release),
        control_mode: control_mode
      }
    end)
  end

  # 计算真实数据点 - 基于实际数据趋势
  defp calculate_real_data_point(
         index,
         total_points,
         base_inventory,
         base_center_line,
         time_diff,
         timestamp
       ) do
    progress = index / total_points

    # 如果数据更新时间很近，使用较小的变化
    time_factor =
      if time_diff > 3600 do
        # 数据超过1小时，使用较大变化
        1.0
      else
        # 数据较新，使用较小变化
        0.3
      end

    # 库存变化 - 基于游戏的自然波动
    inventory_change = (1 - progress) * 0.15 * time_factor * safe_to_float(base_inventory)
    inventory_noise = (:rand.uniform() - 0.5) * 0.05 * safe_to_float(base_inventory)

    # 中心线变化 - 通常变化较小
    center_line_change = (1 - progress) * 0.08 * time_factor * safe_to_float(base_center_line)
    center_line_noise = (:rand.uniform() - 0.5) * 0.02 * safe_to_float(base_center_line)

    # 计算最终值
    final_inventory =
      Decimal.add(base_inventory, Decimal.new("#{inventory_change + inventory_noise}"))

    final_center_line =
      Decimal.add(base_center_line, Decimal.new("#{center_line_change + center_line_noise}"))

    {final_inventory, final_center_line}
  end

  # 为数据点计算控制线
  defp calculate_control_lines_for_point(center_line, config) do
    collect_floating_value =
      calculate_simulated_floating_value(
        center_line,
        config.collect_line_ratio || Decimal.new("0.2"),
        config.collect_line_min || 5000,
        config.collect_line_max || 30000
      )

    release_floating_value =
      calculate_simulated_floating_value(
        center_line,
        config.release_line_ratio || Decimal.new("0.2"),
        config.release_line_min || 5000,
        config.release_line_max || 30000
      )

    pre_collect_ratio = config.pre_collect_line_ratio || Decimal.new("0.7")
    pre_release_ratio = config.pre_release_line_ratio || Decimal.new("0.7")

    %{
      center_line: center_line,
      absolute_collect: Decimal.sub(center_line, collect_floating_value),
      pre_collect:
        Decimal.sub(center_line, Decimal.mult(collect_floating_value, pre_collect_ratio)),
      pre_release:
        Decimal.add(center_line, Decimal.mult(release_floating_value, pre_release_ratio)),
      absolute_release: Decimal.add(center_line, release_floating_value)
    }
  end

  # 为数据点确定控制模式
  defp determine_control_mode_for_point(current_inventory, control_lines) do
    current_inventory_decimal =
      if is_struct(current_inventory, Decimal),
        do: current_inventory,
        else: Decimal.new("#{current_inventory}")

    cond do
      Decimal.compare(current_inventory_decimal, control_lines.absolute_collect) != :gt ->
        :absolute_collect

      Decimal.compare(current_inventory_decimal, control_lines.pre_collect) != :gt ->
        :pre_collect

      Decimal.compare(current_inventory_decimal, control_lines.absolute_release) != :lt ->
        :absolute_release

      Decimal.compare(current_inventory_decimal, control_lines.pre_release) != :lt ->
        :pre_release

      true ->
        :random
    end
  end

  # 生成历史数据点（保留原有的模拟数据函数，作为备用）
  defp generate_historical_data_points(time_range, current_status) do
    # 根据时间范围生成不同数量的数据点
    {points_count, time_step} =
      case time_range do
        # 20个点，每分钟一个
        :minute -> {20, 60}
        # 24个点，每小时一个
        :hour -> {24, 3600}
        # 7个点，每天一个
        :day -> {7, 86400}
        # 默认小时
        _ -> {24, 3600}
      end

    # 获取当前时间
    now = DateTime.utc_now()

    # 生成时间序列
    timestamps =
      0..(points_count - 1)
      |> Enum.map(fn i -> DateTime.add(now, -(points_count - 1 - i) * time_step, :second) end)

    # 基于当前状态生成模拟的历史数据
    base_inventory = current_status.current_inventory
    base_center_line = current_status.center_line
    control_lines = current_status.control_lines

    # 生成数据点
    timestamps
    |> Enum.with_index()
    |> Enum.map(fn {timestamp, index} ->
      # 模拟历史波动
      inventory_variation = generate_inventory_variation(index, points_count, base_inventory)

      center_line_variation =
        generate_center_line_variation(index, points_count, base_center_line)

      # 计算各条控制线（基于波动的中心线）
      simulated_center_line = Decimal.add(base_center_line, center_line_variation)

      # 重新计算控制线
      simulated_control_lines =
        calculate_simulated_control_lines(
          simulated_center_line,
          current_status.control_status.config
        )

      # 当前库存
      simulated_inventory = Decimal.add(base_inventory, inventory_variation)

      # 确定控制模式
      control_mode =
        determine_simulated_control_mode(simulated_inventory, simulated_control_lines)

      %{
        timestamp: timestamp,
        current_inventory: safe_to_float(simulated_inventory),
        center_line: safe_to_float(simulated_center_line),
        absolute_collect: safe_to_float(simulated_control_lines.absolute_collect),
        pre_collect: safe_to_float(simulated_control_lines.pre_collect),
        pre_release: safe_to_float(simulated_control_lines.pre_release),
        absolute_release: safe_to_float(simulated_control_lines.absolute_release),
        control_mode: control_mode
      }
    end)
  end

  # 生成库存变化模拟
  defp generate_inventory_variation(index, total_points, base_inventory) do
    # 使用正弦波和随机波动来模拟库存变化
    progress = index / total_points

    # 正弦波变化（模拟周期性波动）
    sine_wave = :math.sin(progress * 2 * :math.pi()) * 0.1

    # 随机波动
    random_factor = (:rand.uniform() - 0.5) * 0.2

    # 趋势变化（让最新数据接近当前值）
    trend_factor = (1 - progress) * 0.3

    # 计算总变化
    total_variation = (sine_wave + random_factor + trend_factor) * safe_to_float(base_inventory)

    Decimal.new("#{total_variation}")
  end

  # 生成中心线变化模拟
  defp generate_center_line_variation(index, total_points, base_center_line) do
    # 中心线变化相对较小和平滑
    progress = index / total_points

    # 缓慢的趋势变化
    trend_change = (progress - 0.5) * 0.1

    # 小幅随机变化
    random_change = (:rand.uniform() - 0.5) * 0.05

    # 计算总变化
    total_variation = (trend_change + random_change) * safe_to_float(base_center_line)

    Decimal.new("#{total_variation}")
  end

  # 计算模拟的控制线
  defp calculate_simulated_control_lines(center_line, config) do
    # 使用与主计算器相同的逻辑
    collect_floating_value =
      calculate_simulated_floating_value(
        center_line,
        config.collect_line_ratio || Decimal.new("0.2"),
        config.collect_line_min || 5000,
        config.collect_line_max || 30000
      )

    release_floating_value =
      calculate_simulated_floating_value(
        center_line,
        config.release_line_ratio || Decimal.new("0.2"),
        config.release_line_min || 5000,
        config.release_line_max || 30000
      )

    pre_collect_ratio = config.pre_collect_line_ratio || Decimal.new("0.7")
    pre_release_ratio = config.pre_release_line_ratio || Decimal.new("0.7")

    %{
      center_line: center_line,
      absolute_collect: Decimal.sub(center_line, collect_floating_value),
      pre_collect:
        Decimal.sub(center_line, Decimal.mult(collect_floating_value, pre_collect_ratio)),
      pre_release:
        Decimal.add(center_line, Decimal.mult(release_floating_value, pre_release_ratio)),
      absolute_release: Decimal.add(center_line, release_floating_value)
    }
  end

  # 计算模拟浮动值
  defp calculate_simulated_floating_value(center_line, ratio, min_value, max_value) do
    center_decimal =
      if is_struct(center_line, Decimal), do: center_line, else: Decimal.new("#{center_line}")

    ratio_decimal = if is_struct(ratio, Decimal), do: ratio, else: Decimal.new("#{ratio}")
    min_decimal = Decimal.new("#{min_value}")
    max_decimal = Decimal.new("#{max_value}")

    # floating_value = center_line × ratio
    floating_value = Decimal.mult(center_decimal, ratio_decimal)

    # 应用clamp函数
    floating_value
    |> Decimal.max(min_decimal)
    |> Decimal.min(max_decimal)
  end

  # 确定模拟的控制模式
  defp determine_simulated_control_mode(current_inventory, control_lines) do
    current_inventory_decimal =
      if is_struct(current_inventory, Decimal),
        do: current_inventory,
        else: Decimal.new("#{current_inventory}")

    cond do
      Decimal.compare(current_inventory_decimal, control_lines.absolute_collect) != :gt ->
        :absolute_collect

      Decimal.compare(current_inventory_decimal, control_lines.pre_collect) != :gt ->
        :pre_collect

      Decimal.compare(current_inventory_decimal, control_lines.absolute_release) != :lt ->
        :absolute_release

      Decimal.compare(current_inventory_decimal, control_lines.pre_release) != :lt ->
        :pre_release

      true ->
        :random
    end
  end
end
