defmodule Teen.Live.GameExpectation.HundredPlayerControlLive do
  @moduledoc """
  百人场控制仪表盘

  专门用于管理百人场游戏（龙虎、jhandi_munda、crash）的控制系统：
  - 实时控制线监控
  - 四级控制线可视化
  - 控制决策调整
  - 游戏结果历史
  - 运维配置管理
  """

  use CypridinaWeb, :live_view
  require Logger

  alias Teen.Inventory.HundredPlayerControlCalculator
  alias Teen.Resources.Inventory.{GameControlConfig, HundredPlayerGameState}
  alias CypridinaWeb.Components.Charts

  # 3秒刷新一次
  @refresh_interval 3000

  # 百人场游戏类型
  @hundred_player_games %{
    22 => %{slug: "longhu", name: "龙虎斗", type: :longhu},
    21 => %{slug: "jhandi_munda", name: "骰子游戏", type: :jhandi_munda},
    23 => %{slug: "crash", name: "崩溃游戏", type: :crash}
  }

  # ==================== LiveView 回调 ====================

  @impl true
  def mount(_params, _session, socket) do
    Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] 百人场控制仪表盘启动")

    if connected?(socket) do
      # 设置定时刷新
      Process.send_after(self(), :refresh_data, @refresh_interval)
    end

    socket =
      socket
      |> assign(:page_title, "百人场控制仪表盘")
      |> assign(:current_url, "/admin/hundred_player_control/")
      |> assign(:loading, true)
      |> assign(:games, [])
      |> assign(:selected_game, nil)
      |> assign(:selected_game_type, nil)
      |> assign(:control_status, nil)
      |> assign(:time_range, :hour)
      |> assign(:fluid?, true)
      |> assign(:control_line_data, %{})
      |> load_initial_data()
      |> load_demo_control_line_data()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(params, _url, socket) do
    selected_game = Map.get(params, "game_id")

    socket =
      if selected_game do
        game_id = String.to_integer(selected_game)
        game_info = Map.get(@hundred_player_games, game_id)

        socket
        |> assign(:selected_game, game_id)
        |> assign(:selected_game_type, game_info && game_info.type)
        |> load_game_control_status(game_id, game_info && game_info.type)
        |> load_control_line_data(game_id, game_info && game_info.type)
      else
        socket
        |> assign(:selected_game, nil)
        |> assign(:selected_game_type, nil)
        |> assign(:control_status, nil)
        |> assign(:control_line_data, %{})
      end

    {:noreply, socket}
  end

  @impl true
  def handle_event("navigate_to_game", %{"game_id" => game_id}, socket) do
    {:noreply, push_navigate(socket, to: ~p"/admin/hundred_player_control/game/#{game_id}")}
  end

  @impl true
  def handle_event("back_to_overview", _params, socket) do
    {:noreply, push_navigate(socket, to: ~p"/admin/hundred_player_control/")}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    socket =
      socket
      |> load_initial_data()
      |> maybe_refresh_game_status()

    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_time_range", %{"range" => range}, socket) do
    time_range = String.to_atom(range)

    socket =
      socket
      |> assign(:time_range, time_range)
      |> maybe_refresh_game_status()
      |> maybe_refresh_control_line_data()

    {:noreply, socket}
  end

  @impl true
  def handle_event("update_control_weight", %{"weight" => weight}, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type} do
      {game_id, game_type} when not is_nil(game_id) and not is_nil(game_type) ->
        case update_game_control_weight(game_id, weight) do
          {:ok, _config} ->
            socket =
              socket
              |> put_flash(:info, "控制权重更新成功")
              |> load_game_control_status(game_id, game_type)
              |> load_control_line_data(game_id, game_type)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "控制权重更新失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("manual_control", %{"action" => action}, socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type} do
      {game_id, game_type} when not is_nil(game_id) and not is_nil(game_type) ->
        case trigger_manual_control(game_id, game_type, action) do
          {:ok, _result} ->
            socket =
              socket
              |> put_flash(:info, "手动控制执行成功")
              |> load_game_control_status(game_id, game_type)
              |> load_control_line_data(game_id, game_type)

            {:noreply, socket}

          {:error, error} ->
            socket = put_flash(socket, :error, "手动控制执行失败: #{inspect(error)}")
            {:noreply, socket}
        end

      _ ->
        socket = put_flash(socket, :error, "请先选择游戏")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info(:refresh_data, socket) do
    # 定时刷新数据
    socket =
      socket
      |> load_initial_data()
      |> maybe_refresh_game_status()
      |> maybe_refresh_control_line_data()

    # 设置下次刷新
    Process.send_after(self(), :refresh_data, @refresh_interval)

    {:noreply, socket}
  end

  @impl true
  def handle_info(_msg, socket) do
    {:noreply, socket}
  end

  # ==================== 私有函数 ====================

  defp load_initial_data(socket) do
    # 获取所有百人场游戏状态
    games = get_hundred_player_games_status()

    socket
    |> assign(:loading, false)
    |> assign(:games, games)
  end

  defp load_game_control_status(socket, game_id, game_type) when not is_nil(game_type) do
    case HundredPlayerControlCalculator.get_control_status(game_id, game_type) do
      {:ok, status} ->
        socket |> assign(:control_status, status)

      {:error, error} ->
        Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 获取游戏控制状态失败: #{inspect(error)}")
        socket |> assign(:control_status, nil)
    end
  end

  defp load_game_control_status(socket, _game_id, _game_type) do
    socket |> assign(:control_status, nil)
  end

  defp maybe_refresh_game_status(socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type} do
      {game_id, game_type} when not is_nil(game_id) and not is_nil(game_type) ->
        load_game_control_status(socket, game_id, game_type)

      _ ->
        socket
    end
  end

  defp maybe_refresh_control_line_data(socket) do
    case {socket.assigns.selected_game, socket.assigns.selected_game_type} do
      {game_id, game_type} when not is_nil(game_id) and not is_nil(game_type) ->
        load_control_line_data(socket, game_id, game_type)

      _ ->
        socket
    end
  end

  defp load_control_line_data(socket, game_id, game_type) when not is_nil(game_type) do
    time_range = socket.assigns.time_range

    case generate_control_line_chart_data(game_id, game_type, time_range) do
      {:ok, chart_data} ->
        socket |> assign(:control_line_data, chart_data)

      {:error, error} ->
        Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 获取控制线图表数据失败: #{inspect(error)}")
        socket |> assign(:control_line_data, %{})
    end
  end

  defp load_control_line_data(socket, _game_id, _game_type) do
    socket |> assign(:control_line_data, %{})
  end

  defp get_hundred_player_games_status do
    @hundred_player_games
    |> Enum.map(fn {game_id, game_info} ->
      case HundredPlayerControlCalculator.get_control_status(game_id, game_info.type) do
        {:ok, status} ->
          %{
            id: game_id,
            name: game_info.name,
            slug: game_info.slug,
            type: game_info.type,
            current_inventory: status.current_inventory,
            center_line: status.center_line,
            inventory_percentage: status.inventory_percentage,
            control_status: status.control_status,
            last_result: status.last_result,
            last_update: status.last_update
          }

        {:error, error} ->
          Logger.warning("🎮 [HUNDRED_PLAYER_CONTROL] 获取游戏#{game_id}状态失败: #{inspect(error)}")

          %{
            id: game_id,
            name: game_info.name,
            slug: game_info.slug,
            type: game_info.type,
            current_inventory: 0,
            center_line: 0,
            inventory_percentage: 100,
            control_status: %{control_mode: :random, weight: 500},
            last_result: "unknown",
            last_update: DateTime.utc_now(),
            error: error
          }
      end
    end)
  end

  defp update_game_control_weight(game_id, weight_str) do
    with {weight, ""} <- Integer.parse(weight_str),
         {:ok, config} <- GameControlConfig.get_by_game_id_and_type(game_id, 3) do
      GameControlConfig.update(config, %{control_weight: weight})
    else
      :error -> {:error, "权重必须是数字"}
      {:error, reason} -> {:error, reason}
    end
  end

  defp trigger_manual_control(game_id, game_type, action) do
    # 这里可以实现手动控制逻辑
    Logger.info(
      "🎮 [HUNDRED_PLAYER_CONTROL] 手动控制: game_id=#{game_id}, type=#{game_type}, action=#{action}"
    )

    {:ok, :triggered}
  end

  # 格式化函数
  defp format_currency(amount) when is_number(amount) do
    # 百人场控制系统传来的数据已经是元单位，不需要再除以100
    :erlang.float_to_binary(amount, decimals: 2)
  end

  defp format_currency(amount) when is_struct(amount, Decimal) do
    # 处理Decimal类型，已经是元单位
    Decimal.to_string(amount, decimals: 2)
  end

  defp format_currency(_), do: "0.00"

  defp format_percentage(percentage) when is_number(percentage) do
    :erlang.float_to_binary(percentage, decimals: 1)
  end

  defp format_percentage(_), do: "0.0"

  defp get_control_mode_text(:absolute_collect), do: "强制收分"
  defp get_control_mode_text(:pre_collect), do: "预备收分"
  defp get_control_mode_text(:random), do: "随机"
  defp get_control_mode_text(:pre_release), do: "预备放分"
  defp get_control_mode_text(:absolute_release), do: "强制放分"
  defp get_control_mode_text(_), do: "未知"

  defp get_control_mode_color(:absolute_collect), do: "text-red-600 font-bold"
  defp get_control_mode_color(:pre_collect), do: "text-orange-600"
  defp get_control_mode_color(:random), do: "text-gray-600"
  defp get_control_mode_color(:pre_release), do: "text-blue-600"
  defp get_control_mode_color(:absolute_release), do: "text-green-600 font-bold"
  defp get_control_mode_color(_), do: "text-gray-400"

  defp get_inventory_status_color(percentage) when percentage <= 40, do: "text-red-600"
  defp get_inventory_status_color(percentage) when percentage <= 70, do: "text-orange-600"
  defp get_inventory_status_color(percentage) when percentage >= 160, do: "text-green-600"
  defp get_inventory_status_color(percentage) when percentage >= 130, do: "text-blue-600"
  defp get_inventory_status_color(_), do: "text-gray-600"

  defp calculate_control_line_position(current_inventory, center_line) when center_line > 0 do
    percentage = current_inventory / center_line * 100

    cond do
      percentage <= 40 -> min(20, max(0, percentage / 40 * 20))
      percentage <= 70 -> 20 + min(20, max(0, (percentage - 40) / 30 * 20))
      percentage <= 130 -> 40 + min(20, max(0, (percentage - 70) / 60 * 20))
      percentage <= 160 -> 60 + min(20, max(0, (percentage - 130) / 30 * 20))
      true -> 80 + min(20, max(0, (percentage - 160) / 40 * 20))
    end
  end

  defp calculate_control_line_position(_, _), do: 50

  # 生成控制线图表数据
  defp generate_control_line_chart_data(game_id, game_type, time_range) do
    try do
      # 获取当前控制状态
      case HundredPlayerControlCalculator.get_control_status(game_id, game_type) do
        {:ok, current_status} ->
          # 生成历史数据点
          data_points = generate_historical_data_points(time_range, current_status)

          # 构建图表数据
          chart_data = %{
            timestamps: Enum.map(data_points, & &1.timestamp),
            current_inventory: Enum.map(data_points, & &1.current_inventory),
            center_line: Enum.map(data_points, & &1.center_line),
            absolute_collect: Enum.map(data_points, & &1.absolute_collect),
            pre_collect: Enum.map(data_points, & &1.pre_collect),
            pre_release: Enum.map(data_points, & &1.pre_release),
            absolute_release: Enum.map(data_points, & &1.absolute_release),
            control_modes: Enum.map(data_points, & &1.control_mode)
          }

          Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] 生成图表数据成功: #{map_size(chart_data)}个数据集")
          {:ok, chart_data}

        {:error, error} ->
          Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 获取控制状态失败: #{inspect(error)}")
          {:error, error}
      end
    rescue
      error ->
        Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 生成图表数据失败: #{inspect(error)}")
        {:error, error}
    end
  end

  # 生成历史数据点
  defp generate_historical_data_points(time_range, current_status) do
    # 根据时间范围生成不同数量的数据点
    {points_count, time_step} =
      case time_range do
        # 20个点，每分钟一个
        :minute -> {20, 60}
        # 24个点，每小时一个
        :hour -> {24, 3600}
        # 7个点，每天一个
        :day -> {7, 86400}
        # 默认小时
        _ -> {24, 3600}
      end

    # 获取当前时间
    now = DateTime.utc_now()

    # 生成时间序列
    timestamps =
      0..(points_count - 1)
      |> Enum.map(fn i -> DateTime.add(now, -(points_count - 1 - i) * time_step, :second) end)

    # 基于当前状态生成模拟的历史数据
    base_inventory = current_status.current_inventory
    base_center_line = current_status.center_line
    control_lines = current_status.control_lines

    # 生成数据点
    timestamps
    |> Enum.with_index()
    |> Enum.map(fn {timestamp, index} ->
      # 模拟历史波动
      inventory_variation = generate_inventory_variation(index, points_count, base_inventory)

      center_line_variation =
        generate_center_line_variation(index, points_count, base_center_line)

      # 计算各条控制线（基于波动的中心线）
      simulated_center_line = Decimal.add(base_center_line, center_line_variation)

      # 重新计算控制线
      simulated_control_lines =
        calculate_simulated_control_lines(
          simulated_center_line,
          current_status.control_status.config
        )

      # 当前库存
      simulated_inventory = Decimal.add(base_inventory, inventory_variation)

      # 确定控制模式
      control_mode =
        determine_simulated_control_mode(simulated_inventory, simulated_control_lines)

      %{
        timestamp: timestamp,
        current_inventory: Decimal.to_float(simulated_inventory),
        center_line: Decimal.to_float(simulated_center_line),
        absolute_collect: Decimal.to_float(simulated_control_lines.absolute_collect),
        pre_collect: Decimal.to_float(simulated_control_lines.pre_collect),
        pre_release: Decimal.to_float(simulated_control_lines.pre_release),
        absolute_release: Decimal.to_float(simulated_control_lines.absolute_release),
        control_mode: control_mode
      }
    end)
  end

  # 生成库存变化模拟
  defp generate_inventory_variation(index, total_points, base_inventory) do
    # 使用正弦波和随机波动来模拟库存变化
    progress = index / total_points

    # 正弦波变化（模拟周期性波动）
    sine_wave = :math.sin(progress * 2 * :math.pi()) * 0.1

    # 随机波动
    random_factor = (:rand.uniform() - 0.5) * 0.2

    # 趋势变化（让最新数据接近当前值）
    trend_factor = (1 - progress) * 0.3

    # 计算总变化
    total_variation =
      (sine_wave + random_factor + trend_factor) * Decimal.to_float(base_inventory)

    Decimal.new("#{total_variation}")
  end

  # 生成中心线变化模拟
  defp generate_center_line_variation(index, total_points, base_center_line) do
    # 中心线变化相对较小和平滑
    progress = index / total_points

    # 缓慢的趋势变化
    trend_change = (progress - 0.5) * 0.1

    # 小幅随机变化
    random_change = (:rand.uniform() - 0.5) * 0.05

    # 计算总变化
    total_variation = (trend_change + random_change) * Decimal.to_float(base_center_line)

    Decimal.new("#{total_variation}")
  end

  # 计算模拟的控制线
  defp calculate_simulated_control_lines(center_line, config) do
    # 使用与主计算器相同的逻辑
    collect_floating_value =
      calculate_simulated_floating_value(
        center_line,
        config.collect_line_ratio || Decimal.new("0.2"),
        config.collect_line_min || 5000,
        config.collect_line_max || 30000
      )

    release_floating_value =
      calculate_simulated_floating_value(
        center_line,
        config.release_line_ratio || Decimal.new("0.2"),
        config.release_line_min || 5000,
        config.release_line_max || 30000
      )

    pre_collect_ratio = config.pre_collect_line_ratio || Decimal.new("0.7")
    pre_release_ratio = config.pre_release_line_ratio || Decimal.new("0.7")

    %{
      center_line: center_line,
      absolute_collect: Decimal.sub(center_line, collect_floating_value),
      pre_collect:
        Decimal.sub(center_line, Decimal.mult(collect_floating_value, pre_collect_ratio)),
      pre_release:
        Decimal.add(center_line, Decimal.mult(release_floating_value, pre_release_ratio)),
      absolute_release: Decimal.add(center_line, release_floating_value)
    }
  end

  # 计算模拟浮动值
  defp calculate_simulated_floating_value(center_line, ratio, min_value, max_value) do
    center_decimal =
      if is_struct(center_line, Decimal), do: center_line, else: Decimal.new("#{center_line}")

    ratio_decimal = if is_struct(ratio, Decimal), do: ratio, else: Decimal.new("#{ratio}")
    min_decimal = Decimal.new("#{min_value}")
    max_decimal = Decimal.new("#{max_value}")

    # floating_value = center_line × ratio
    floating_value = Decimal.mult(center_decimal, ratio_decimal)

    # 应用clamp函数
    floating_value
    |> Decimal.max(min_decimal)
    |> Decimal.min(max_decimal)
  end

  # 确定模拟的控制模式
  defp determine_simulated_control_mode(current_inventory, control_lines) do
    current_inventory_decimal =
      if is_struct(current_inventory, Decimal),
        do: current_inventory,
        else: Decimal.new("#{current_inventory}")

    cond do
      Decimal.compare(current_inventory_decimal, control_lines.absolute_collect) != :gt ->
        :absolute_collect

      Decimal.compare(current_inventory_decimal, control_lines.pre_collect) != :gt ->
        :pre_collect

      Decimal.compare(current_inventory_decimal, control_lines.absolute_release) != :lt ->
        :absolute_release

      Decimal.compare(current_inventory_decimal, control_lines.pre_release) != :lt ->
        :pre_release

      true ->
        :random
    end
  end

  # 加载演示控制线数据（用于总览页面）
  defp load_demo_control_line_data(socket) do
    # 如果没有选择游戏，显示演示数据
    if is_nil(socket.assigns.selected_game) do
      demo_data = generate_demo_control_line_data()
      socket |> assign(:control_line_data, demo_data)
    else
      socket
    end
  end

  # 生成演示控制线数据
  defp generate_demo_control_line_data do
    # 生成24小时的演示数据
    timestamps =
      0..23
      |> Enum.map(fn hour ->
        DateTime.new!(Date.utc_today(), Time.new!(hour, 0, 0), "Etc/UTC")
      end)

    # 生成基础数据
    base_inventory = 100_000
    base_center_line = 50000

    data_points =
      timestamps
      |> Enum.with_index()
      |> Enum.map(fn {timestamp, index} ->
        # 生成模拟的波动数据
        progress = index / 23

        # 库存波动
        inventory_wave = :math.sin(progress * 2 * :math.pi()) * 30000
        current_inventory = base_inventory + inventory_wave + (:rand.uniform() - 0.5) * 20000

        # 中心线波动
        center_line_wave = :math.sin(progress * :math.pi()) * 10000
        center_line = base_center_line + center_line_wave + (:rand.uniform() - 0.5) * 5000

        # 计算控制线
        absolute_collect = center_line - 15000
        pre_collect = center_line - 10000
        pre_release = center_line + 10000
        absolute_release = center_line + 15000

        # 确定控制模式
        control_mode =
          cond do
            current_inventory <= absolute_collect -> :absolute_collect
            current_inventory <= pre_collect -> :pre_collect
            current_inventory >= absolute_release -> :absolute_release
            current_inventory >= pre_release -> :pre_release
            true -> :random
          end

        %{
          timestamp: timestamp,
          current_inventory: current_inventory,
          center_line: center_line,
          absolute_collect: absolute_collect,
          pre_collect: pre_collect,
          pre_release: pre_release,
          absolute_release: absolute_release,
          control_mode: control_mode
        }
      end)

    # 构建图表数据
    %{
      timestamps: Enum.map(data_points, & &1.timestamp),
      current_inventory: Enum.map(data_points, & &1.current_inventory),
      center_line: Enum.map(data_points, & &1.center_line),
      absolute_collect: Enum.map(data_points, & &1.absolute_collect),
      pre_collect: Enum.map(data_points, & &1.pre_collect),
      pre_release: Enum.map(data_points, & &1.pre_release),
      absolute_release: Enum.map(data_points, & &1.absolute_release),
      control_modes: Enum.map(data_points, & &1.control_mode)
    }
  end
end
