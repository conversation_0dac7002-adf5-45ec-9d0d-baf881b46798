defmodule Teen.Live.GameExpectation.GameManagementLive do
  @moduledoc """
  游戏管理后台界面
  提供游戏配置的增删改查、状态管理等功能
  """
  use CypridinaWeb, :live_view
  require Ash.Query
  alias Teen.GameManagement
  alias Teen.GameManagement.{ManageGameConfig, LeveRoomConfig}

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page_title, "游戏管理")
     |> assign(:current_url, "/admin/games")
     |> assign(:search_term, "")
     |> assign(:selected_status, "all")
     |> assign(:show_modal, false)
     |> assign(:modal_action, nil)
     |> assign(:current_game, nil)
     |> assign(:fluid?, true)
     |> load_games(),
     layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "游戏管理")
    |> assign(:current_game, nil)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "新建游戏")
    |> assign(:current_game, %ManageGameConfig{})
    |> assign(:show_modal, true)
    |> assign(:modal_action, :new)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    game = ManageGameConfig.get_by_id!(id)

    socket
    |> assign(:page_title, "编辑游戏")
    |> assign(:current_game, game)
    |> assign(:show_modal, true)
    |> assign(:modal_action, :edit)
  end

  defp apply_action(socket, :new_room, %{"id" => game_id}) do
    game = ManageGameConfig.get_by_id!(game_id)

    socket
    |> assign(:page_title, "#{game.game_name} - 新建房间")
    |> assign(:current_game, game)
    |> assign(:current_room, %LeveRoomConfig{game_id: game.game_id})
    |> assign(:show_room_modal, true)
    |> assign(:room_modal_action, :new)
    |> assign(:view_mode, :rooms)
    |> load_rooms()
  end

  defp apply_action(socket, :edit_room, %{"id" => game_id, "room_id" => room_id}) do
    game = ManageGameConfig.get_by_id!(game_id)
    room = LeveRoomConfig.get_by_id!(room_id) |> Ash.load!(:game_config)

    socket
    |> assign(:page_title, "#{game.game_name} - 编辑房间")
    |> assign(:current_game, game)
    |> assign(:current_room, room)
    |> assign(:show_room_modal, true)
    |> assign(:room_modal_action, :edit)
    |> assign(:view_mode, :rooms)
    |> load_rooms()
  end

  defp apply_action(socket, :rooms, %{"id" => id}) do
    game = ManageGameConfig.get_by_id!(id)

    socket
    |> assign(:page_title, "房间配置 - #{game.display_name}")
    |> assign(:current_game, game)
    |> assign(:view_mode, :rooms)
    |> assign(:show_room_modal, false)
    |> assign(:room_modal_action, nil)
    |> assign(:current_room, nil)
    |> load_rooms()
  end

  @impl true
  def handle_event("search", %{"search" => %{"term" => term}}, socket) do
    {:noreply,
     socket
     |> assign(:search_term, term)
     |> load_games()}
  end

  def handle_event("filter_status", %{"status" => status}, socket) do
    {:noreply,
     socket
     |> assign(:selected_status, status)
     |> load_games()}
  end

  def handle_event("toggle_status", %{"id" => id}, socket) do
    game = ManageGameConfig.get_by_id!(id)

    result =
      if game.is_enabled do
        ManageGameConfig.disable!(game, %{}, actor: get_current_user_id(socket))
      else
        ManageGameConfig.enable!(game, %{}, actor: get_current_user_id(socket))
      end

    case result do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "游戏状态已更新")
         |> load_games()}

      {:error, error} ->
        require Logger
        Logger.error("游戏状态更新失败: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "更新失败: #{inspect(error)}")}
    end
  end

  def handle_event("delete_game", %{"id" => id}, socket) do
    game = ManageGameConfig.get_by_id!(id: id)

    case ManageGameConfig.destroy!(game) do
      :ok ->
        {:noreply,
         socket
         |> put_flash(:info, "游戏已删除")
         |> load_games()}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "删除失败")}
    end
  end

  def handle_event("close_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_modal, false)
     |> assign(:current_game, nil)
     |> assign(:modal_action, nil)}
  end

  def handle_event("save_game", %{"game_config" => game_params}, socket) do
    case socket.assigns.modal_action do
      :new ->
        game_params = Map.put(game_params, "created_by", get_current_user_id(socket))

        case ManageGameConfig.create(game_params) do
          {:ok, _game} ->
            {:noreply,
             socket
             |> put_flash(:info, "游戏创建成功")
             |> assign(:show_modal, false)
             |> load_games()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "创建失败: #{inspect(changeset.errors)}")}
        end

      :edit ->
        game_params = Map.put(game_params, "updated_by", get_current_user_id(socket))

        case ManageGameConfig.update(socket.assigns.current_game, game_params) do
          {:ok, updated_game} ->
            {:noreply,
             socket
             |> put_flash(:info, "游戏更新成功")
             |> assign(:show_modal, false)
             |> load_games()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "更新失败: #{inspect(changeset.errors)}")}
        end
    end
  end

  # 房间管理相关事件处理
  def handle_event("close_room_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_room_modal, false)
     |> assign(:current_room, nil)
     |> assign(:room_modal_action, nil)}
  end

  def handle_event("save_room", %{"room_config" => room_params}, socket) do
    # 处理JSON配置和表单参数标准化
    room_params =
      room_params
      |> process_json_configs()
      |> normalize_form_params()

    case socket.assigns.room_modal_action do
      :new ->
        current_user_id = get_current_user_id(socket)
        IO.inspect(current_user_id, label: "🔍 [DEBUG] GameManagementLive current_user_id for room creation")

        room_params =
          room_params
          |> Map.put(:game_config_id, socket.assigns.current_game.id)
          |> Map.put(:game_id, socket.assigns.current_game.game_id)
          |> Map.put(:created_by, current_user_id)

        IO.inspect(room_params, label: "🔍 [DEBUG] GameManagementLive final room_params")

        case LeveRoomConfig.create(room_params) do
          {:ok, _room} ->
            {:noreply,
             socket
             |> put_flash(:info, "房间创建成功")
             |> assign(:show_room_modal, false)
             |> load_rooms()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "创建失败: #{format_changeset_errors(changeset)}")}
        end

      :edit ->
        # 编辑时只保留允许修改的字段
        room_params =
          room_params
          |> Map.take([:min_bet, :entry_fee, :max_bet, :max_players, :is_enabled, :basic_config, :gameplay_config, :unified_config])

        case LeveRoomConfig.update(socket.assigns.current_room, room_params) do
          {:ok, updated_room} ->
            {:noreply,
             socket
             |> put_flash(:info, "房间更新成功")
             |> assign(:show_room_modal, false)
             |> load_rooms()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "更新失败: #{format_changeset_errors(changeset)}")}
        end
    end
  end

  def handle_event("toggle_room_status", %{"id" => id}, socket) do
    room = LeveRoomConfig.get_by_id!(id)

    result =
      if room.is_enabled do
        LeveRoomConfig.disable!(room, %{}, actor: get_current_user_id(socket))
      else
        LeveRoomConfig.enable!(room, %{}, actor: get_current_user_id(socket))
      end

    case result do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "房间状态已更新")
         |> load_rooms()}

      {:error, error} ->
        require Logger
        Logger.error("房间状态更新失败: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "更新失败: #{inspect(error)}")}
    end
  end

  def handle_event("delete_room", %{"id" => id}, socket) do
    room = LeveRoomConfig.get_by_id!(id)

    case LeveRoomConfig.destroy!(room) do
      :ok ->
        {:noreply,
         socket
         |> put_flash(:info, "房间已删除")
         |> load_rooms()}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "删除失败")}
    end
  end

  defp load_games(socket) do
    games =
      try do
        # 在后台管理界面中读取所有游戏配置（包括禁用的）
        ManageGameConfig
        |> Ash.read!()
        |> filter_by_search(socket.assigns.search_term)
        |> filter_by_status(socket.assigns.selected_status)
        |> Enum.sort_by(& &1.display_order)
      rescue
        error ->
          # 如果读取失败，返回空列表并记录错误
          require Logger
          Logger.error("加载游戏配置失败: #{inspect(error)}")
          []
      end

    assign(socket, :games, games)
  end

  defp load_rooms(socket) do
    rooms =
      try do
        case socket.assigns.current_game do
          nil ->
            []

          current_game ->
            LeveRoomConfig
            |> Ash.Query.filter(game_id: current_game.game_id)
            |> Ash.read!()
            |> Enum.sort_by(& &1.server_id)
        end
      rescue
        error ->
          require Logger
          Logger.error("加载房间配置失败: #{inspect(error)}")
          []
      end

    assign(socket, :rooms, rooms)
  end

  defp filter_by_search(games, ""), do: games

  defp filter_by_search(games, term) do
    term = String.downcase(term)

    Enum.filter(games, fn game ->
      String.contains?(String.downcase(game.game_name), term) or
        String.contains?(String.downcase(game.display_name), term)
    end)
  end

  defp filter_by_status(games, "all"), do: games
  defp filter_by_status(games, "enabled"), do: Enum.filter(games, & &1.is_enabled)
  defp filter_by_status(games, "disabled"), do: Enum.filter(games, &(not &1.is_enabled))

  defp filter_by_status(games, status) when status in ["0", "1", "2", "4"] do
    status_int = String.to_integer(status)
    Enum.filter(games, &(&1.status == status_int))
  end

  defp get_current_user_id(socket) do
    # 从session或assigns中获取当前用户ID
    # 这里需要根据实际的认证系统来实现
    # 如果没有当前用户，使用系统默认UUID

    case socket.assigns[:current_user] do
      %{id: user_id} when is_binary(user_id) ->
        IO.inspect(user_id, label: "🔍 [DEBUG] GameManagementLive extracted user_id from current_user")
        user_id
      _ ->
        IO.inspect("No valid user_id found in current_user", label: "🔍 [DEBUG] GameManagementLive")
        "00000000-0000-0000-0000-000000000000"
    end
  end

  defp process_json_configs(room_params) do
    # 处理统一配置 JSON
    room_params =
      case Map.get(room_params, "unified_config") do
        nil ->
          room_params

        "" ->
          room_params

        json_string when is_binary(json_string) ->
          try do
            case Jason.decode(json_string) do
              {:ok, config} when is_map(config) ->
                room_params
                |> Map.put("unified_config", config)
                |> Map.put("basic_config", Map.get(config, "game_basic", %{}))
                |> Map.put("gameplay_config", Map.get(config, "gameplay", %{}))

              {:error, _} ->
                # JSON 解析失败，保留原始字符串，让验证阶段处理错误
                room_params
            end
          rescue
            _ ->
              # 解析出错，保留原始数据
              room_params
          end

        config when is_map(config) ->
          # 已经是 map 格式，直接使用
          room_params
          |> Map.put("unified_config", config)
          |> Map.put("basic_config", Map.get(config, "game_basic", %{}))
          |> Map.put("gameplay_config", Map.get(config, "gameplay", %{}))

        _ ->
          room_params
      end

    room_params
  end

  # 标准化表单参数，处理HTML表单的特殊值并转换为原子键
  defp normalize_form_params(params) do
    params
    |> Map.update("is_enabled", true, fn
      "on" -> true
      "true" -> true
      true -> true
      _ -> false
    end)
    |> Map.update("server_id", nil, &parse_integer/1)
    |> Map.update("min_bet", nil, &parse_integer/1)
    |> Map.update("entry_fee", nil, &parse_integer/1)
    |> Map.update("max_bet", nil, &parse_integer/1)
    |> Map.update("max_players", nil, &parse_integer/1)
    |> Map.update("order_id", nil, &parse_integer/1)
    |> Map.update("port", nil, &parse_integer/1)
    |> convert_string_keys_to_atoms()
  end

  # 将字符串键转换为原子键
  defp convert_string_keys_to_atoms(params) do
    Enum.reduce(params, %{}, fn {key, value}, acc ->
      try do
        atom_key = if is_binary(key), do: String.to_existing_atom(key), else: key
        Map.put(acc, atom_key, value)
      rescue
        ArgumentError -> Map.put(acc, key, value)
      end
    end)
  end

  defp parse_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {int, ""} -> int
      _ -> value
    end
  end
  defp parse_integer(value), do: value

  defp format_changeset_errors(changeset) do
    cond do
      # 处理 Ash 错误格式
      Map.has_key?(changeset, :errors) and is_list(changeset.errors) ->
        changeset.errors
        |> Enum.map(fn
          %{field: field, message: message} -> "#{field}: #{message}"
          {field, {message, _}} -> "#{field}: #{message}"
          {field, message} when is_binary(message) -> "#{field}: #{message}"
          error -> "#{inspect(error)}"
        end)
        |> Enum.join(", ")

      # 处理 Ecto changeset 格式
      Map.has_key?(changeset, :errors) ->
        changeset.errors
        |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
        |> Enum.join(", ")

      # 其他情况
      true ->
        "#{inspect(changeset)}"
    end
  end

  defp status_badge(status) do
    case status do
      0 -> {"停用", "bg-red-100 text-red-800"}
      1 -> {"维护", "bg-yellow-100 text-yellow-800"}
      2 -> {"正常", "bg-green-100 text-green-800"}
      4 -> {"即将开放", "bg-blue-100 text-blue-800"}
      _ -> {"未知", "bg-gray-100 text-gray-800"}
    end
  end

  # 获取默认基础配置
  defp get_default_basic_config() do
    %{
      "max_players" => 1,
      "min_players" => 1,
      "auto_start_delay" => 1000,
      "difen" => 100,
      "bet_rate_num" => 9,
      "score_rate" => 1,
      "odds_config" => %{
        "1" => 0.2,
        "2" => 1,
        "3" => 2,
        "4" => 10,
        "5" => 20,
        "6" => 100,
        "7" => 200
      }
    }
  end

  # 获取默认玩法配置
  defp get_default_gameplay_config() do
    %{
      "rtp" => 85.0,
      "icon_weights" => %{
        # WILD字
        "0" => 1,
        # 香蕉
        "1" => 30,
        # 西瓜
        "2" => 25,
        # 草莓
        "3" => 20,
        # 葡萄
        "4" => 15,
        # 芒果
        "5" => 12,
        # 榴莲
        "6" => 10,
        # 山竹
        "7" => 8,
        # BAR
        "8" => 3,
        # 苹果
        "9" => 4,
        # 7
        "10" => 2
      },
      "big_win_control" => %{
        "jackpot_probability" => 0.01,
        "free_game_probability" => 0.05,
        "big_win_cooldown" => 100
      },
      "payout_table" => %{
        "0" => %{},
        "1" => %{"2" => 1, "3" => 3, "4" => 10, "5" => 75},
        "2" => %{"3" => 3, "4" => 10, "5" => 85},
        "3" => %{"3" => 15, "4" => 40, "5" => 250},
        "4" => %{"3" => 25, "4" => 50, "5" => 400},
        "5" => %{"3" => 30, "4" => 70, "5" => 550},
        "6" => %{"3" => 35, "4" => 80, "5" => 650},
        "7" => %{"3" => 45, "4" => 100, "5" => 800},
        "8" => %{"3" => 75, "4" => 175, "5" => 1250},
        "9" => %{"3" => 25, "4" => 40, "5" => 400},
        "10" => %{"3" => 100, "4" => 200, "5" => 1750}
      },
      "jackpot_seed_amount" => 10000,
      "jackpot_contribution_rate" => 0.005,
      "jackpot_min_trigger_amount" => 50000,
      "jackpot_percentage_table" => %{
        "200" => %{"5" => 30, "4" => 25, "3" => 20},
        "100" => %{"5" => 20, "4" => 15, "3" => 12},
        "20" => %{"5" => 12, "4" => 6, "3" => 4},
        "10" => %{"5" => 8, "4" => 4, "3" => 2}
      },
      "free_game_trigger_table" => %{
        "5" => 12,
        "4" => 8,
        "3" => 4
      },
      "free_game_multiplier" => 1.5,
      "enable_free_game_test" => false,
      "free_game_test_spins" => 2,
      "enable_jackpot_test" => false
    }
  end

  # 从房间配置获取游戏类型
  defp get_game_type_from_room(current_room) do
    cond do
      current_room && current_room.game_id == 40 -> "slot777"
      current_room && current_room.game_id == 41 -> "slotniu"
      current_room && current_room.game_id == 42 -> "slotcat"
      current_room && current_room.game_id == 1 -> "teenpatti"
      current_room && current_room.game_id == 22 -> "longhu"
      true -> "unknown"
    end
  end

  # 从房间获取游戏名称
  defp get_game_name_from_room(current_room) do
    cond do
      current_room && current_room.game_config && current_room.game_config.name ->
        current_room.game_config.name

      current_room && Map.has_key?(current_room, :game_id) ->
        # 通过 game_id 查找游戏名称，使用 get_game_type_from_room 作为后备
        case get_game_by_id(current_room.game_id) do
          {:ok, game} -> game.name
          _ -> get_game_type_from_room(current_room)
        end

      true ->
        "unknown"
    end
  end

  # 通过 ID 获取游戏信息
  defp get_game_by_id(game_id) do
    try do
      case Teen.GameManagement.ManageGameConfig.get_by_id(game_id) do
        {:ok, game} -> {:ok, game}
        {:error, _} -> {:error, :not_found}
      end
    rescue
      _ -> {:error, :query_failed}
    end
  end

  # 获取 Slot777 默认基础配置
  defp get_slot777_default_basic_config do
    # 直接返回真正的默认基础配置，不从数据库获取
    %{
      "room_id" => "4001",
      "room_name" => "Slot777 房间",
      "room_type" => "slot777",
      "max_players" => 100,
      "min_bet" => 1,
      "max_bet" => 1000,
      "default_bet" => 10,
      "bet_levels" => [1, 5, 10, 25, 50, 100, 250, 500, 1000],
      "auto_spin_enabled" => true,
      "turbo_mode_enabled" => true,
      "sound_enabled" => true,
      "animation_speed" => "normal",
      "game_settings" => %{
        "rows" => 3,
        "cols" => 5,
        "symbols" => [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        "wild_symbol" => 11,
        "scatter_symbol" => 12,
        "paylines" => [
          [1, 1, 1, 1, 1],
          [0, 0, 0, 0, 0],
          [2, 2, 2, 2, 2],
          [0, 1, 2, 1, 0],
          [2, 1, 0, 1, 2],
          [1, 0, 0, 0, 1],
          [1, 2, 2, 2, 1],
          [0, 0, 1, 2, 2],
          [2, 2, 1, 0, 0],
          [1, 0, 1, 2, 1],
          [1, 2, 1, 0, 1],
          [0, 1, 0, 1, 0],
          [2, 1, 2, 1, 2],
          [0, 1, 1, 1, 0],
          [2, 1, 1, 1, 2],
          [1, 1, 0, 1, 1],
          [1, 1, 2, 1, 1],
          [0, 0, 2, 0, 0],
          [2, 2, 0, 2, 2],
          [0, 2, 0, 2, 0]
        ]
      }
    }
  end

  # 获取 Slot777 默认玩法配置
  defp get_slot777_default_gameplay_config do
    # 直接返回真正的默认玩法配置，不从数据库获取
    %{
      "rtp" => 95.0,
      "icon_weights" => %{
        "1" => 100,
        "2" => 90,
        "3" => 80,
        "4" => 70,
        "5" => 60,
        "6" => 50,
        "7" => 40,
        "8" => 30,
        "9" => 20,
        "10" => 15,
        "11" => 10,
        "12" => 5
      },
      "payout_table" => %{
        "1" => %{"3" => 5, "4" => 10, "5" => 25},
        "2" => %{"3" => 5, "4" => 10, "5" => 25},
        "3" => %{"3" => 10, "4" => 20, "5" => 50},
        "4" => %{"3" => 10, "4" => 20, "5" => 50},
        "5" => %{"3" => 15, "4" => 30, "5" => 75},
        "6" => %{"3" => 15, "4" => 30, "5" => 75},
        "7" => %{"3" => 20, "4" => 40, "5" => 100},
        "8" => %{"3" => 25, "4" => 50, "5" => 125},
        "9" => %{"3" => 30, "4" => 60, "5" => 150},
        "10" => %{"3" => 50, "4" => 100, "5" => 250},
        "11" => %{"2" => 2, "3" => 10, "4" => 50, "5" => 500},
        "12" => %{"2" => 1, "3" => 5, "4" => 25, "5" => 100}
      },
      "jackpot_config" => %{
        "enabled" => true,
        "seed_amount" => 1000,
        "contribution_rate" => 0.01,
        "trigger_probability" => 0.001,
        "max_amount" => 100_000
      },
      "free_game_config" => %{
        "enabled" => true,
        "trigger_symbols" => [12],
        "min_trigger_count" => 3,
        "free_spins_count" => 10,
        "multiplier" => 2
      },
      "testing_config" => %{
        "force_win" => false,
        "force_symbols" => [],
        "debug_mode" => false
      },
      "big_win_control" => %{
        "enabled" => true,
        "threshold_multiplier" => 10,
        "max_frequency" => 0.05,
        "cooldown_spins" => 100
      }
    }
  end

  # 获取默认基础配置 JSON 字符串
  defp get_default_basic_config_json do
    Jason.encode!(get_default_basic_config(), pretty: true)
  end

  # 获取默认玩法配置 JSON 字符串
  defp get_default_gameplay_config_json do
    Jason.encode!(get_default_gameplay_config(), pretty: true)
  end

  # 获取当前房间的统一配置 JSON 字符串
  defp get_current_unified_config_json(current_room, current_game) do
    cond do
      # 如果房间有统一配置，使用房间的配置
      current_room && current_room.unified_config && current_room.unified_config != %{} ->
        Jason.encode!(current_room.unified_config, pretty: true)

      # 如果房间有基础配置和玩法配置，合并它们
      current_room && current_room.basic_config && current_room.gameplay_config ->
        unified_config = %{
          "game_basic" => current_room.basic_config,
          "gameplay" => current_room.gameplay_config
        }
        Jason.encode!(unified_config, pretty: true)

      # 如果房间只有基础配置
      current_room && current_room.basic_config ->
        unified_config = %{
          "game_basic" => current_room.basic_config,
          "gameplay" => get_default_gameplay_config_for_game(current_game)
        }
        Jason.encode!(unified_config, pretty: true)

      # 如果房间只有玩法配置
      current_room && current_room.gameplay_config ->
        unified_config = %{
          "game_basic" => get_default_basic_config_for_game(current_game),
          "gameplay" => current_room.gameplay_config
        }
        Jason.encode!(unified_config, pretty: true)

      # 默认情况：使用游戏的默认配置
      true ->
        unified_config = %{
          "game_basic" => get_default_basic_config_for_game(current_game),
          "gameplay" => get_default_gameplay_config_for_game(current_game)
        }
        Jason.encode!(unified_config, pretty: true)
    end
  rescue
    _ ->
      # 如果出现任何错误，返回默认配置
      unified_config = %{
        "game_basic" => get_default_basic_config(),
        "gameplay" => get_default_gameplay_config()
      }
      Jason.encode!(unified_config, pretty: true)
  end

  # 根据游戏获取默认基础配置
  defp get_default_basic_config_for_game(current_game) do
    cond do
      current_game && current_game.game_id == 40 -> get_slot777_default_basic_config()
      true -> get_default_basic_config()
    end
  end

  # 根据游戏获取默认玩法配置
  defp get_default_gameplay_config_for_game(current_game) do
    cond do
      current_game && current_game.game_id == 40 -> get_slot777_default_gameplay_config()
      true -> get_default_gameplay_config()
    end
  end
end
