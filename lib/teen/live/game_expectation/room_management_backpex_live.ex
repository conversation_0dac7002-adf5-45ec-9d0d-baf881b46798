defmodule Teen.Live.GameExpectation.RoomManagementBackpexLive do
  @moduledoc """
  房间管理后台界面
  """

  use AshBackpex.LiveResource
  import Phoenix.Component
  import Phoenix.LiveView

  backpex do
    resource Teen.GameManagement.LeveRoomConfig
    layout({Teen.Layouts, :admin})

    @impl Backpex.LiveResource
    def singular_name, do: "房间配置"

    @impl Backpex.LiveResource
    def plural_name, do: "房间配置"

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :game_config do
        module Backpex.Fields.BelongsTo
        label("所属游戏")
        only([:index, :show])

        display_field(:game_name)
        live_resource(Teen.Live.GameExpectation.GameManagementBackpexLive)
      end

      field :game_config_id do
        module Backpex.Fields.Select
        label("游戏配置")
        options(fn _assigns -> get_game_config_options() end)
        only([:new, :edit])
      end

      field :game_id do
        module Backpex.Fields.Number
        label("游戏ID")
        searchable(true)
      end

      field :server_id do
        module Backpex.Fields.Number
        label("服务器ID")
        searchable(true)
      end

      field :server_ip do
        module Backpex.Fields.Text
        label("服务器IP")
        searchable(true)
      end

      field :port do
        module Backpex.Fields.Number
        label("端口")
      end

      field :order_id do
        module Backpex.Fields.Number
        label("排序ID")
      end

      field :min_bet do
        module Backpex.Fields.Number
        label("最小下注")
      end

      field :entry_fee do
        module Backpex.Fields.Number
        label("入场费")
      end

      field :max_bet do
        module Backpex.Fields.Number
        label("最大下注")
      end

      field :max_players do
        module Backpex.Fields.Number
        label("最大玩家数")
      end

      field :bundle_name do
        module Backpex.Fields.Text
        label("包名")
      end

      field :is_enabled do
        module Backpex.Fields.Boolean
        label("是否启用")
      end

      field :unified_config do
        module Teen.Live.Fields.JsonField
        label("统一配置")

        # render(fn assigns ->
        #   config_text = format_config_for_display(assigns.value)
        #   assigns = assign(assigns, :config_text, config_text)

        #   Logger.info("渲染表单: #{inspect(config_text)}")

        #   ~H"""
        #   <pre class="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32"><%= @config_text %></pre>
        #   """
        # end)

        # render_form(fn assigns ->
        #   config_text = format_config_for_form(assigns.form, assigns.name)
        #   assigns = assign(assigns, :config_text, config_text)

        #   ~H"""
        #   <div class="form-control">
        #     <label class="label">
        #       <span class="label-text">统一配置 (JSON格式)</span>
        #     </label>
        #     <textarea
        #       id={assigns.id}
        #       name={Phoenix.HTML.Form.input_name(assigns.form, assigns.name)}
        #       class="textarea textarea-bordered w-full h-32"
        #       placeholder="请输入JSON格式的配置，例如: {}"
        #     ><%= @config_text %></textarea>
        #   </div>
        #   """
        # end)
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  @impl Backpex.LiveResource
  def can?(_assigns, action, _item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  # 辅助函数
  defp get_game_config_options do
    try do
      Teen.GameManagement.ManageGameConfig
      |> Ash.read!()
      |> Enum.map(fn config -> {config.display_name, config.id} end)
    rescue
      _ -> []
    end
  end

  # 为 Backpex 提供新实例创建函数
  @impl Backpex.LiveResource
  def new(_assigns) do
    struct(Teen.GameManagement.LeveRoomConfig, %{
      unified_config: %{}
    })
  end

  # 表单提交前处理
  def before_create(socket, params) do
    params = process_unified_config(params)
    {:ok, socket, params}
  end

  # 表单提交前处理 - 确保正确转换
  def before_update(socket, params, _item) do
    params = process_unified_config(params)
    {:ok, socket, params}
  end

  # 处理 unified_config 字段 - 增强版本
  defp process_unified_config(params) do
    case params["unified_config"] do
      nil -> Map.put(params, "unified_config", %{})
      "" -> Map.put(params, "unified_config", %{})
      config_string when is_binary(config_string) ->
        try do
          config = Jason.decode!(config_string)
          Map.put(params, "unified_config", config)
        rescue
          _ -> Map.put(params, "unified_config", %{})
        end
      config when is_map(config) -> params
      _ -> Map.put(params, "unified_config", %{})
    end
  end

  # 添加辅助函数
  defp format_config_for_display(nil), do: "{}"
  defp format_config_for_display(value) when is_binary(value), do: value
  defp format_config_for_display(value) when is_map(value) do
    try do
      Jason.encode!(value, pretty: true)
    rescue
      _ -> inspect(value, pretty: true)
    end
  end
  defp format_config_for_display(value), do: inspect(value, pretty: true)

  defp format_config_for_form(form, field_name) do
    case Phoenix.HTML.Form.input_value(form, field_name) do
      nil -> "{}"
      value when is_binary(value) -> value
      value when is_map(value) ->
        try do
          Jason.encode!(value, pretty: true)
        rescue
          _ -> inspect(value, pretty: true)
        end
      value -> inspect(value, pretty: true)
    end
  end
end
