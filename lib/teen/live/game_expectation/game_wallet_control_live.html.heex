<!-- 动态背景 -->
<div class="min-h-screen relative overflow-hidden">
  <!-- 动态背景层 -->
  <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
  </div>
  <div
    class="absolute inset-0 opacity-20"
    style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%239C92AC&quot; fill-opacity=&quot;0.05&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;4&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"
  >
  </div>
  
<!-- 浮动装饰元素 -->
  <div class="absolute top-20 left-10 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse">
  </div>
  <div class="absolute bottom-20 right-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000">
  </div>
  <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-indigo-500/5 rounded-full blur-3xl animate-pulse delay-500">
  </div>
  
<!-- 顶部导航 -->
  <div class="relative z-10 bg-white/10 backdrop-blur-xl border-b border-white/20 shadow-2xl">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div class="flex items-center">
          <button
            phx-click="go_back"
            class="group mr-6 p-3 text-gray-700 hover:text-gray-900 hover:bg-white/20 rounded-xl transition-all duration-300 hover:scale-110"
          >
            <svg
              class="w-6 h-6 transform group-hover:-translate-x-1 transition-transform duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              >
              </path>
            </svg>
          </button>
          <div>
            <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-800 via-purple-800 to-blue-800 bg-clip-text text-transparent drop-shadow-lg">
              {@game_info.name} 钱包控制中心
            </h1>
            <p class="text-gray-700 mt-2 flex items-center space-x-3">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-gray-800 border border-gray-300 backdrop-blur-sm">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                ID: {@game_info.id}
              </span>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-gray-800 border border-gray-300 backdrop-blur-sm">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z">
                  </path>
                </svg>
                {@game_info.type}
              </span>
            </p>
          </div>
        </div>
        
<!-- 实时状态指示器 -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center bg-gradient-to-r from-emerald-500/20 to-green-500/20 backdrop-blur-sm px-4 py-2 rounded-full border border-emerald-400/30">
            <div class="relative">
              <div class="w-3 h-3 bg-emerald-400 rounded-full animate-ping absolute"></div>
              <div class="w-3 h-3 bg-emerald-400 rounded-full relative shadow-lg shadow-emerald-400/50">
              </div>
            </div>
            <span class="text-sm font-medium text-emerald-700 ml-3">实时监控中</span>
          </div>
          <div class="text-gray-600 text-xs">
            {DateTime.now!("Asia/Shanghai") |> DateTime.to_string() |> String.slice(0, 19)}
          </div>
        </div>
      </div>
    </div>
  </div>
  
<!-- 主要内容区域 -->
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 钱包状态卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <!-- 当前余额 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-3xl border border-white/30 p-6 hover:scale-105 transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20">
        <!-- 装饰性背景 -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
        </div>
        <div class="absolute -top-10 -right-10 w-20 h-20 bg-blue-500/20 rounded-full blur-xl group-hover:bg-blue-500/30 transition-colors duration-500">
        </div>

        <div class="relative flex items-center">
          <div class="flex-shrink-0">
            <div class="w-14 h-14 bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl shadow-blue-500/30 group-hover:shadow-blue-500/50 transition-all duration-500 group-hover:rotate-6">
              <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z">
                </path>
              </svg>
            </div>
          </div>
          <div class="ml-5">
            <p class="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
              当前余额
            </p>
            <p class="text-2xl font-bold text-gray-800 group-hover:text-blue-700 transition-colors duration-300">
              ¥{Number.Delimit.number_to_delimited(@wallet_status.current_balance)}
            </p>
            <div class="w-full bg-white/20 rounded-full h-1 mt-2 overflow-hidden">
              <div class="h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full animate-pulse">
              </div>
            </div>
          </div>
        </div>
      </div>
      
<!-- 波动金额 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-3xl border border-white/30 p-6 hover:scale-105 transition-all duration-500 hover:shadow-2xl hover:shadow-emerald-500/20">
        <!-- 装饰性背景 -->
        <div class={"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 #{
          if @wallet_status.fluctuation_amount >= 0,
            do: "bg-gradient-to-br from-emerald-500/10 to-green-500/10",
            else: "bg-gradient-to-br from-red-500/10 to-rose-500/10"
        }"}>
        </div>
        <div class={"absolute -top-10 -right-10 w-20 h-20 rounded-full blur-xl transition-colors duration-500 #{
          if @wallet_status.fluctuation_amount >= 0,
            do: "bg-emerald-500/20 group-hover:bg-emerald-500/30",
            else: "bg-red-500/20 group-hover:bg-red-500/30"
        }"}>
        </div>

        <div class="relative flex items-center">
          <div class="flex-shrink-0">
            <div class={"w-14 h-14 rounded-2xl flex items-center justify-center shadow-xl transition-all duration-500 group-hover:rotate-6 #{
              if @wallet_status.fluctuation_amount >= 0,
                do: "bg-gradient-to-br from-emerald-400 via-emerald-500 to-green-600 shadow-emerald-500/30 group-hover:shadow-emerald-500/50",
                else: "bg-gradient-to-br from-red-400 via-red-500 to-rose-600 shadow-red-500/30 group-hover:shadow-red-500/50"
            }"}>
              <%= if @wallet_status.fluctuation_amount >= 0 do %>
                <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  >
                  </path>
                </svg>
              <% else %>
                <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  >
                  </path>
                </svg>
              <% end %>
            </div>
          </div>
          <div class="ml-5">
            <p class="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
              波动金额
            </p>
            <p class={"text-2xl font-bold transition-colors duration-300 #{
              if @wallet_status.fluctuation_amount >= 0,
                do: "text-emerald-700 group-hover:text-emerald-800",
                else: "text-red-700 group-hover:text-red-800"
            }"}>
              {if @wallet_status.fluctuation_amount >= 0, do: "+", else: ""}¥{Number.Delimit.number_to_delimited(
                @wallet_status.fluctuation_amount
              )}
            </p>
            <div class="w-full bg-white/20 rounded-full h-1 mt-2 overflow-hidden">
              <div class={"h-full rounded-full animate-pulse #{
                if @wallet_status.fluctuation_amount >= 0,
                  do: "bg-gradient-to-r from-emerald-400 to-green-600",
                  else: "bg-gradient-to-r from-red-400 to-rose-600"
              }"}>
              </div>
            </div>
          </div>
        </div>
      </div>
      
<!-- 波动百分比 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-3xl border border-white/30 p-6 hover:scale-105 transition-all duration-500 hover:shadow-2xl hover:shadow-purple-500/20">
        <!-- 装饰性背景 -->
        <div class={"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 #{
          if @wallet_status.fluctuation_percentage >= 0,
            do: "bg-gradient-to-br from-cyan-500/10 to-blue-500/10",
            else: "bg-gradient-to-br from-orange-500/10 to-amber-500/10"
        }"}>
        </div>
        <div class={"absolute -top-10 -right-10 w-20 h-20 rounded-full blur-xl transition-colors duration-500 #{
          if @wallet_status.fluctuation_percentage >= 0,
            do: "bg-cyan-500/20 group-hover:bg-cyan-500/30",
            else: "bg-orange-500/20 group-hover:bg-orange-500/30"
        }"}>
        </div>

        <div class="relative flex items-center">
          <div class="flex-shrink-0">
            <div class={"w-14 h-14 rounded-2xl flex items-center justify-center shadow-xl transition-all duration-500 group-hover:rotate-6 #{
              if @wallet_status.fluctuation_percentage >= 0,
                do: "bg-gradient-to-br from-cyan-400 via-cyan-500 to-blue-600 shadow-cyan-500/30 group-hover:shadow-cyan-500/50",
                else: "bg-gradient-to-br from-orange-400 via-orange-500 to-amber-600 shadow-orange-500/30 group-hover:shadow-orange-500/50"
            }"}>
              <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z">
                </path>
              </svg>
            </div>
          </div>
          <div class="ml-5">
            <p class="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
              波动百分比
            </p>
            <p class={"text-2xl font-bold transition-colors duration-300 #{
              if @wallet_status.fluctuation_percentage >= 0,
                do: "text-cyan-700 group-hover:text-cyan-800",
                else: "text-orange-700 group-hover:text-orange-800"
            }"}>
              {if @wallet_status.fluctuation_percentage >= 0, do: "+", else: ""}{case @wallet_status.fluctuation_percentage do
                val when is_float(val) -> Float.round(val, 2)
                val when is_integer(val) -> val
                val -> val
              end}%
            </p>
            <div class="w-full bg-white/20 rounded-full h-1 mt-2 overflow-hidden">
              <div class={"h-full rounded-full animate-pulse #{
                if @wallet_status.fluctuation_percentage >= 0,
                  do: "bg-gradient-to-r from-cyan-400 to-blue-600",
                  else: "bg-gradient-to-r from-orange-400 to-amber-600"
              }"}>
              </div>
            </div>
          </div>
        </div>
      </div>
      
<!-- 控制模式 -->
      <div class="group relative overflow-hidden bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-xl rounded-3xl border border-white/30 p-6 hover:scale-105 transition-all duration-500 hover:shadow-2xl hover:shadow-indigo-500/20">
        <!-- 装饰性背景 -->
        <div class={"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 #{
          case @wallet_status.control_mode do
            :collect -> "bg-gradient-to-br from-orange-500/10 to-amber-500/10"
            :release -> "bg-gradient-to-br from-cyan-500/10 to-blue-500/10"
            _ -> "bg-gradient-to-br from-gray-500/10 to-slate-500/10"
          end
        }"}>
        </div>
        <div class={"absolute -top-10 -right-10 w-20 h-20 rounded-full blur-xl transition-colors duration-500 #{
          case @wallet_status.control_mode do
            :collect -> "bg-orange-500/20 group-hover:bg-orange-500/30"
            :release -> "bg-cyan-500/20 group-hover:bg-cyan-500/30"
            _ -> "bg-gray-500/20 group-hover:bg-gray-500/30"
          end
        }"}>
        </div>

        <div class="relative flex items-center">
          <div class="flex-shrink-0">
            <div class={"w-14 h-14 rounded-2xl flex items-center justify-center shadow-xl transition-all duration-500 group-hover:rotate-6 #{
              case @wallet_status.control_mode do
                :collect -> "bg-gradient-to-br from-orange-400 via-orange-500 to-amber-600 shadow-orange-500/30 group-hover:shadow-orange-500/50"
                :release -> "bg-gradient-to-br from-cyan-400 via-cyan-500 to-blue-600 shadow-cyan-500/30 group-hover:shadow-cyan-500/50"
                _ -> "bg-gradient-to-br from-gray-400 via-gray-500 to-slate-600 shadow-gray-500/30 group-hover:shadow-gray-500/50"
              end
            }"}>
              <%= case @wallet_status.control_mode do %>
                <% :collect -> %>
                  <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    >
                    </path>
                  </svg>
                <% :release -> %>
                  <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M17 3a1 1 0 01-1 1H4a1 1 0 110-2h12a1 1 0 011 1zm-7.707 3.293a1 1 0 010 1.414L8 9.414V17a1 1 0 11-2 0V9.414L4.707 10.707a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3z"
                      clip-rule="evenodd"
                    >
                    </path>
                  </svg>
                <% _ -> %>
                  <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                      clip-rule="evenodd"
                    >
                    </path>
                  </svg>
              <% end %>
            </div>
          </div>
          <div class="ml-5">
            <p class="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-300">
              控制模式
            </p>
            <p class={"text-2xl font-bold transition-colors duration-300 #{
              case @wallet_status.control_mode do
                :collect -> "text-orange-700 group-hover:text-orange-800"
                :release -> "text-cyan-700 group-hover:text-cyan-800"
                _ -> "text-gray-700 group-hover:text-gray-800"
              end
            }"}>
              {case @wallet_status.control_mode do
                :collect -> "收分模式"
                :release -> "放分模式"
                _ -> "正常模式"
              end}
            </p>
            <div class="w-full bg-white/20 rounded-full h-1 mt-2 overflow-hidden">
              <div class={"h-full rounded-full animate-pulse #{
                case @wallet_status.control_mode do
                  :collect -> "bg-gradient-to-r from-orange-400 to-amber-600"
                  :release -> "bg-gradient-to-r from-cyan-400 to-blue-600"
                  _ -> "bg-gradient-to-r from-gray-400 to-slate-600"
                end
              }"}>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
<!-- 主要内容区域 -->
    <div class="flex flex-row gap-8" style="gap: 5%; min-height: 600px;">
      <!-- 波动曲线图 - 左侧占大部分宽度 -->
      <div style="width: 72%;" class="min-w-0">
        <div class="relative overflow-hidden bg-white/20 backdrop-blur-xl rounded-3xl border border-gray-200 shadow-2xl h-full">
          <!-- 装饰性背景 -->
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5"></div>
          <div class="absolute -top-20 -right-20 w-40 h-40 bg-indigo-500/10 rounded-full blur-3xl">
          </div>
          <div class="absolute -bottom-20 -left-20 w-40 h-40 bg-purple-500/10 rounded-full blur-3xl">
          </div>

          <div class="relative px-6 py-5 bg-gradient-to-r from-white/80 to-white/70 border-b border-gray-200 backdrop-blur-sm">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div class="flex items-center space-x-4">
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-400 via-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-indigo-500/30">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z">
                    </path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800">实时波动曲线</h3>
                  <p class="text-sm text-gray-600">钱包余额变化趋势</p>
                </div>
              </div>
              
<!-- 时间范围切换 -->
              <div class="flex space-x-1 bg-white/10 backdrop-blur-sm rounded-2xl p-1 border border-white/20 self-start sm:self-auto">
                <%= for {range, label} <- [{"minute", "分钟"}, {"hour", "小时"}, {"day", "天"}] do %>
                  <button
                    phx-click="change_time_range"
                    phx-value-range={range}
                    class={"px-3 py-2 text-sm font-medium rounded-xl transition-all duration-300 #{
                      if @time_range == range do
                        "bg-gradient-to-r from-indigo-600 to-purple-700 text-white shadow-lg shadow-indigo-500/40 scale-105"
                      else
                        "text-gray-800 hover:text-gray-900 hover:bg-white/20 hover:scale-105"
                      end
                    }"}
                  >
                    {label}
                  </button>
                <% end %>
              </div>
            </div>
          </div>

          <div class="relative p-6 flex flex-col flex-1">
            <!-- ApexCharts 图表容器 - 自适应高度 -->
            <div class="relative flex-1 bg-gradient-to-br from-white/5 to-white/10 rounded-2xl p-4 border border-white/20 backdrop-blur-sm overflow-hidden">
              <!-- 图表装饰背景 -->
              <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5">
              </div>
              <div class="absolute top-4 right-4 w-16 h-16 bg-indigo-500/10 rounded-full blur-xl">
              </div>
              <div class="absolute bottom-4 left-4 w-20 h-20 bg-purple-500/10 rounded-full blur-xl">
              </div>

              <div
                id="wallet-chart"
                phx-hook="WalletChart"
                data-chart-data={Jason.encode!(@chart_data)}
                data-time-range={@time_range}
                phx-update="ignore"
                class="relative w-full h-full z-10"
              >
              </div>
            </div>
          </div>
        </div>
      </div>
      
<!-- 配置面板 - 右侧较小宽度 -->
      <div style="width: 23%;" class="flex-shrink-0">
        <div class="relative bg-gradient-to-br from-white/90 to-white/80 backdrop-blur-xl rounded-3xl border border-gray-200 shadow-2xl min-h-full">
          <!-- 装饰性背景 -->
          <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-500/5"></div>
          <div class="absolute -top-10 -right-10 w-20 h-20 bg-emerald-500/10 rounded-full blur-xl">
          </div>
          <div class="absolute -bottom-10 -left-10 w-20 h-20 bg-teal-500/10 rounded-full blur-xl">
          </div>

          <div class="relative px-6 py-5 bg-gradient-to-r from-white/80 to-white/70 border-b border-gray-200 backdrop-blur-sm">
            <div class="flex flex-col sm:flex-row md:flex-col items-start sm:items-center md:items-start justify-between gap-3">
              <div class="flex items-center space-x-4">
                <div class="w-10 h-10 bg-gradient-to-br from-emerald-400 via-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/30">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                      clip-rule="evenodd"
                    >
                    </path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800">钱包配置</h3>
                  <p class="text-sm text-gray-600">收分放分控制参数</p>
                </div>
              </div>
              <%= if not @editing_config do %>
                <button
                  phx-click="edit_config"
                  style="color: #b92323;"
                  class="group px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-bold rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-xl shadow-blue-500/50 hover:shadow-blue-500/70 hover:scale-110 self-start sm:self-auto md:self-start border-2 border-blue-400 ring-2 ring-blue-300/50"
                >
                  <span class="flex items-center space-x-2">
                    <svg
                      class="w-4 h-4 group-hover:rotate-12 transition-transform duration-300"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z">
                      </path>
                    </svg>
                    <span>编辑</span>
                  </span>
                </button>
              <% end %>
            </div>
          </div>

          <div class="relative p-6">
            <%= if @editing_config do %>
              <!-- 编辑模式 -->
              <.form for={@config_form} phx-submit="save_config" class="space-y-6">
                <div class="space-y-4">
                  <label class="block text-sm font-medium mb-3" style="color: #d55757;">
                    <span class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fill-rule="evenodd"
                          d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                          clip-rule="evenodd"
                        >
                        </path>
                      </svg>
                      <span>收分比例 (%)</span>
                    </span>
                    <span class="text-xs ml-6" style="color: #999;">当平台亏损时的调控强度</span>
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      name="config[collect_percentage]"
                      value={@wallet_config.collect_percentage}
                      min="5"
                      max="50"
                      class="w-full px-4 py-3 bg-white/30 border border-gray-300 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 backdrop-blur-sm transition-all duration-300"
                      required
                    />
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span class="text-gray-700 text-sm">%</span>
                    </div>
                  </div>
                  <div class="bg-white/10 rounded-xl p-4 border border-gray-200 backdrop-blur-sm">
                    <p class="text-xs text-gray-700 space-y-1">
                      <span class="block">
                        • <span class="text-green-600">5-15%</span>: 温和收分，保持玩家体验
                      </span>
                      <span class="block">
                        • <span class="text-yellow-600">16-25%</span>: 标准收分，平衡收益和体验
                      </span>
                      <span class="block">
                        • <span class="text-orange-600">26-35%</span>: 积极收分，快速止损
                      </span>
                      <span class="block">
                        • <span class="text-red-600">36-50%</span>: 激进收分，最大化短期收益
                      </span>
                    </p>
                  </div>
                </div>

                <div class="space-y-4">
                  <label class="block text-sm font-medium mb-3" style="color: #d55757;">
                    <span class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-cyan-600" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fill-rule="evenodd"
                          d="M17 3a1 1 0 01-1 1H4a1 1 0 110-2h12a1 1 0 011 1zm-7.707 3.293a1 1 0 010 1.414L8 9.414V17a1 1 0 11-2 0V9.414L4.707 10.707a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3z"
                          clip-rule="evenodd"
                        >
                        </path>
                      </svg>
                      <span>放分比例 (%)</span>
                    </span>
                    <span class="text-xs ml-6" style="color: #999;">当平台盈利时的让利强度</span>
                  </label>
                  <div class="relative">
                    <input
                      type="number"
                      name="config[release_percentage]"
                      value={@wallet_config.release_percentage}
                      min="0"
                      max="35"
                      class="w-full px-4 py-3 bg-white/30 border border-gray-300 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50 backdrop-blur-sm transition-all duration-300"
                      required
                    />
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span class="text-gray-700 text-sm">%</span>
                    </div>
                  </div>
                  <div class="bg-white/10 rounded-xl p-4 border border-gray-200 backdrop-blur-sm">
                    <p class="text-xs text-gray-700 space-y-1">
                      <span class="block">
                        • <span class="text-red-600">0-5%</span>: 保守让利，最大化平台收益
                      </span>
                      <span class="block">
                        • <span class="text-yellow-600">6-15%</span>: 标准让利，维持玩家粘性
                      </span>
                      <span class="block">
                        • <span class="text-green-600">16-25%</span>: 慷慨让利，提升玩家满意度
                      </span>
                      <span class="block">
                        • <span class="text-cyan-600">26-35%</span>: 大幅让利，拉新或活动期间
                      </span>
                    </p>
                  </div>
                </div>
                
<!-- 运营策略建议 -->
                <div class="bg-gradient-to-br from-white/80 to-white/70 rounded-xl p-5 border border-gray-200 backdrop-blur-sm">
                  <h4 class="text-sm font-medium text-gray-800 mb-3 flex items-center space-x-2">
                    <span class="text-lg">💡</span>
                    <span>运营策略建议</span>
                  </h4>
                  <div class="grid grid-cols-1 gap-3">
                    <div class="bg-white/60 rounded-lg p-3 border border-gray-200">
                      <div class="flex items-center justify-between">
                        <span class="text-xs font-medium text-emerald-400">新游戏推广</span>
                        <span class="text-xs text-gray-600">收分15% + 放分20%</span>
                      </div>
                      <p class="text-xs text-gray-700 mt-1">吸引玩家，建立用户基础</p>
                    </div>
                    <div class="bg-white/60 rounded-lg p-3 border border-gray-200">
                      <div class="flex items-center justify-between">
                        <span class="text-xs font-medium text-blue-400">稳定运营</span>
                        <span class="text-xs text-gray-600">收分20% + 放分10%</span>
                      </div>
                      <p class="text-xs text-gray-700 mt-1">平衡收益与玩家体验</p>
                    </div>
                    <div class="bg-white/60 rounded-lg p-3 border border-gray-200">
                      <div class="flex items-center justify-between">
                        <span class="text-xs font-medium text-orange-400">收益优化</span>
                        <span class="text-xs text-gray-600">收分30% + 放分5%</span>
                      </div>
                      <p class="text-xs text-gray-700 mt-1">提升平台盈利能力</p>
                    </div>
                    <div class="bg-white/60 rounded-lg p-3 border border-gray-200">
                      <div class="flex items-center justify-between">
                        <span class="text-xs font-medium text-purple-400">活动期间</span>
                        <span class="text-xs text-gray-600">收分10% + 放分30%</span>
                      </div>
                      <p class="text-xs text-gray-700 mt-1">大幅让利，提升活跃度</p>
                    </div>
                  </div>
                </div>

                <div class="flex space-x-3 pt-6">
                  <button
                    type="submit"
                    class="group flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-xl hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500/50 transition-all duration-300 shadow-lg shadow-green-500/40 hover:shadow-green-500/60 hover:scale-105 border border-green-500/50"
                  >
                    <span class="flex items-center justify-center space-x-2">
                      <svg
                        class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        >
                        </path>
                      </svg>
                      <span>保存配置</span>
                    </span>
                  </button>
                  <button
                    type="button"
                    phx-click="cancel_edit"
                    class="group flex-1 bg-gray-200 text-gray-800 px-6 py-3 rounded-xl hover:bg-gray-300 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-400/50 transition-all duration-300 border border-gray-300 hover:scale-105"
                  >
                    <span class="flex items-center justify-center space-x-2">
                      <svg
                        class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clip-rule="evenodd"
                        >
                        </path>
                      </svg>
                      <span>取消</span>
                    </span>
                  </button>
                </div>
              </.form>
            <% else %>
              <!-- 显示模式 -->
              <div class="relative space-y-6">
                <!-- 收分比例显示 -->
                <div class="bg-gradient-to-br from-white/15 to-white/10 rounded-xl p-5 border border-gray-200 backdrop-blur-sm">
                  <div class="flex items-center justify-between mb-3">
                    <label
                      class="flex items-center space-x-2 text-sm font-medium"
                      style="color: #d55757;"
                    >
                      <svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fill-rule="evenodd"
                          d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                          clip-rule="evenodd"
                        >
                        </path>
                      </svg>
                      <span>收分比例</span>
                    </label>
                    <div class="text-2xl font-bold text-orange-600">
                      {@wallet_config.collect_percentage}%
                    </div>
                  </div>
                  <div class="text-sm" style="color: #999;">
                    当平台亏损时的调控强度，数值越高平台收益越快
                  </div>
                  <!-- 进度条 -->
                  <div class="mt-3 w-full bg-white/10 rounded-full h-2 overflow-hidden">
                    <div
                      class="h-full bg-gradient-to-r from-orange-400 to-amber-500 rounded-full transition-all duration-1000 ease-out"
                      style={"width: #{@wallet_config.collect_percentage * 2}%"}
                    >
                    </div>
                  </div>
                </div>
                
<!-- 放分比例显示 -->
                <div class="bg-gradient-to-br from-white/15 to-white/10 rounded-xl p-5 border border-gray-200 backdrop-blur-sm">
                  <div class="flex items-center justify-between mb-3">
                    <label
                      class="flex items-center space-x-2 text-sm font-medium"
                      style="color: #d55757;"
                    >
                      <svg class="w-4 h-4 text-cyan-600" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fill-rule="evenodd"
                          d="M17 3a1 1 0 01-1 1H4a1 1 0 110-2h12a1 1 0 011 1zm-7.707 3.293a1 1 0 010 1.414L8 9.414V17a1 1 0 11-2 0V9.414L4.707 10.707a1 1 0 01-1.414-1.414l3-3a1 1 0 011.414 0l3 3z"
                          clip-rule="evenodd"
                        >
                        </path>
                      </svg>
                      <span>放分比例</span>
                    </label>
                    <div class="text-2xl font-bold text-cyan-600">
                      {@wallet_config.release_percentage}%
                    </div>
                  </div>
                  <div class="text-sm" style="color: #999;">
                    当平台盈利时的让利强度，数值越高玩家体验越好
                  </div>
                  <!-- 进度条 -->
                  <div class="mt-3 w-full bg-white/10 rounded-full h-2 overflow-hidden">
                    <div
                      class="h-full bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full transition-all duration-1000 ease-out"
                      style={"width: #{@wallet_config.release_percentage * 2.86}%"}
                    >
                    </div>
                  </div>
                </div>
                
<!-- 当前状态指示 -->
                <div class="bg-gradient-to-br from-white/15 to-white/10 rounded-xl p-5 border border-gray-200 backdrop-blur-sm">
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-800">当前控制状态</span>
                    <div class={"flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium #{
                      case @wallet_status.control_mode do
                        :collect -> "bg-orange-500/20 text-orange-300 border border-orange-500/30"
                        :release -> "bg-cyan-500/20 text-cyan-300 border border-cyan-500/30"
                        _ -> "bg-gray-500/20 text-gray-300 border border-gray-500/30"
                      end
                    }"}>
                      <div class={"w-2 h-2 rounded-full animate-pulse #{
                        case @wallet_status.control_mode do
                          :collect -> "bg-orange-400"
                          :release -> "bg-cyan-400"
                          _ -> "bg-gray-400"
                        end
                      }"}>
                      </div>
                      <span>
                        {case @wallet_status.control_mode do
                          :collect -> "收分模式"
                          :release -> "放分模式"
                          _ -> "正常模式"
                        end}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 自定义样式 -->
<style>
  /* 添加一些自定义动画和效果 */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
  }

  .float-animation {
    animation: float 6s ease-in-out infinite;
  }

  .pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }

  /* 新增动画效果 */
  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes bounce-in {
    0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
    50% { transform: scale(1.05) rotate(2deg); }
    70% { transform: scale(0.9) rotate(-1deg); }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
  }

  @keyframes glow-pulse {
    0%, 100% {
      box-shadow: 0 0 20px rgba(99, 102, 241, 0.3),
                  0 0 40px rgba(139, 92, 246, 0.2),
                  inset 0 0 20px rgba(255, 255, 255, 0.1);
    }
    50% {
      box-shadow: 0 0 30px rgba(99, 102, 241, 0.6),
                  0 0 60px rgba(139, 92, 246, 0.4),
                  inset 0 0 30px rgba(255, 255, 255, 0.2);
    }
  }

  .shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .gradient-animation {
    background-size: 400% 400%;
    animation: gradient-shift 8s ease infinite;
  }

  .bounce-in {
    animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .glow-pulse {
    animation: glow-pulse 4s ease-in-out infinite;
  }

  /* 渐变文字效果 */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 玻璃态效果增强 */
  .glass-effect {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* 卡片悬停效果 */
  .card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.6), rgba(139, 92, 246, 0.6));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.8), rgba(139, 92, 246, 0.8));
  }
</style>

<!-- ApexCharts 将通过 Hook 处理 -->
