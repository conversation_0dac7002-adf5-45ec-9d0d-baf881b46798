defmodule Teen.Live.GameExpectation.RoomManagementLive do
  @moduledoc """
  房间配置管理界面
  """
  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, :live_view
  alias Teen.GameManagement
  alias Teen.GameManagement.{ManageGameConfig, LeveRoomConfig}

  @impl true
  def mount(%{"game_id" => game_id}, _session, socket) do
    game = ManageGameConfig.get_by_id!(id: game_id) |> Ash.load!(:room_configs)

    {:ok,
     socket
     |> assign(:game, game)
     |> assign(:page_title, "房间配置 - #{game.display_name}")
     |> assign(:current_url, "/admin/games/#{game_id}/room_management")
     |> assign(:show_modal, false)
     |> assign(:modal_action, nil)
     |> assign(:current_room, nil)
     |> assign(:fluid?, true), layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:current_room, %LeveRoomConfig{})
    |> assign(:show_modal, true)
    |> assign(:modal_action, :new)
  end

  defp apply_action(socket, :edit, %{"room_id" => room_id}) do
    room = LeveRoomConfig.get_by_id!(id: room_id)

    socket
    |> assign(:current_room, room)
    |> assign(:show_modal, true)
    |> assign(:modal_action, :edit)
  end

  @impl true
  def handle_event("close_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_modal, false)
     |> assign(:current_room, nil)
     |> assign(:modal_action, nil)}
  end

  def handle_event("save_room", %{"room_config" => room_params}, socket) do
    case socket.assigns.modal_action do
      :new ->
        room_params =
          room_params
          |> Map.put("game_config_id", socket.assigns.game.id)
          |> Map.put("game_id", socket.assigns.game.game_id)
          |> Map.put("created_by", get_current_user_id(socket))

        case LeveRoomConfig.create(room_params) do
          {:ok, _room} ->
            {:noreply,
             socket
             |> put_flash(:info, "房间创建成功")
             |> assign(:show_modal, false)
             |> reload_game()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "创建失败: #{inspect(changeset.errors)}")}
        end

      :edit ->
        room_params = Map.put(room_params, "updated_by", get_current_user_id(socket))

        case LeveRoomConfig.update(socket.assigns.current_room, room_params) do
          {:ok, _room} ->
            {:noreply,
             socket
             |> put_flash(:info, "房间更新成功")
             |> assign(:show_modal, false)
             |> reload_game()}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "更新失败: #{inspect(changeset.errors)}")}
        end
    end
  end

  def handle_event("toggle_room", %{"id" => id}, socket) do
    room = LeveRoomConfig.get_by_id!(id: id)

    result =
      if room.is_enabled do
        LeveRoomConfig.disable!(room, actor_id: get_current_user_id(socket))
      else
        LeveRoomConfig.enable!(room, actor_id: get_current_user_id(socket))
      end

    case result do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "房间状态已更新")
         |> reload_game()}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "更新失败")}
    end
  end

  def handle_event("delete_room", %{"id" => id}, socket) do
    room = LeveRoomConfig.get_by_id!(id: id)

    case LeveRoomConfig.destroy!(room) do
      :ok ->
        {:noreply,
         socket
         |> put_flash(:info, "房间已删除")
         |> reload_game()}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "删除失败")}
    end
  end

  defp reload_game(socket) do
    game = ManageGameConfig.get_by_id!(id: socket.assigns.game.id) |> Ash.load!(:room_configs)
    assign(socket, :game, game)
  end

  defp get_current_user_id(socket) do
    Map.get(socket.assigns, :current_user_id, "system")
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- 头部 -->
      <div class="mb-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{@page_title}</h1>
            <p class="mt-1 text-sm text-gray-600">管理 {@game.display_name} 的房间配置</p>
          </div>
          <div class="flex space-x-3">
            <.link
              navigate={~p"/admin/games"}
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              返回游戏列表
            </.link>
            <.link
              patch={~p"/admin/games/#{@game.id}/rooms/new"}
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              新建房间
            </.link>
          </div>
        </div>
      </div>
      
    <!-- 房间列表 -->
      <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200">
          <%= for room <- @game.room_configs do %>
            <li class="px-6 py-4">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center">
                    <div>
                      <p class="text-sm font-medium text-gray-900">
                        服务器 {room.server_id} - {room.server_ip}:{room.port}
                      </p>
                      <p class="text-sm text-gray-500">
                        排序: {room.order_id} | 底分: {room.min_bet} | 入场: {room.entry_fee}
                        <%= if room.max_bet > 0 do %>
                          | 封顶: {room.max_bet}
                        <% end %>
                        | 最大玩家: {room.max_players}
                      </p>
                      <p class="text-sm text-gray-500">包名: {room.bundle_name}</p>
                    </div>
                    <%= if room.is_enabled do %>
                      <span class="ml-4 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        启用
                      </span>
                    <% else %>
                      <span class="ml-4 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        禁用
                      </span>
                    <% end %>
                  </div>
                </div>

                <div class="flex items-center space-x-2">
                  <.link
                    patch={~p"/admin/games/#{@game.id}/rooms/#{room.id}/edit"}
                    class="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                  >
                    编辑
                  </.link>

                  <button
                    phx-click="toggle_room"
                    phx-value-id={room.id}
                    class={"text-sm font-medium #{if room.is_enabled, do: "text-red-600 hover:text-red-900", else: "text-green-600 hover:text-green-900"}"}
                  >
                    {if room.is_enabled, do: "禁用", else: "启用"}
                  </button>

                  <button
                    phx-click="delete_room"
                    phx-value-id={room.id}
                    data-confirm="确定要删除这个房间吗？"
                    class="text-red-600 hover:text-red-900 text-sm font-medium"
                  >
                    删除
                  </button>
                </div>
              </div>
            </li>
          <% end %>
        </ul>

        <%= if Enum.empty?(@game.room_configs) do %>
          <div class="text-center py-12">
            <p class="text-sm text-gray-500">暂无房间配置</p>
            <.link
              patch={~p"/admin/games/#{@game.id}/rooms/new"}
              class="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              创建第一个房间
            </.link>
          </div>
        <% end %>
      </div>
      
    <!-- 模态框 -->
      <%= if @show_modal do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                {if @modal_action == :new, do: "新建房间", else: "编辑房间"}
              </h3>

              <.form for={%{}} phx-submit="save_room">
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">服务器ID</label>
                    <input
                      type="number"
                      name="room_config[server_id]"
                      value={@current_room && @current_room.server_id}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">服务器IP</label>
                    <input
                      type="text"
                      name="room_config[server_ip]"
                      value={@current_room && @current_room.server_ip}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">端口</label>
                    <input
                      type="number"
                      name="room_config[port]"
                      value={@current_room && @current_room.port}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">排序ID</label>
                    <input
                      type="number"
                      name="room_config[order_id]"
                      value={@current_room && @current_room.order_id}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">底分</label>
                    <input
                      type="number"
                      name="room_config[min_bet]"
                      value={@current_room && @current_room.min_bet}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">入场金币</label>
                    <input
                      type="number"
                      name="room_config[entry_fee]"
                      value={@current_room && @current_room.entry_fee}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">封顶金币(0=无限制)</label>
                    <input
                      type="number"
                      name="room_config[max_bet]"
                      value={@current_room && @current_room.max_bet}
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">最大玩家数</label>
                    <input
                      type="number"
                      name="room_config[max_players]"
                      value={(@current_room && @current_room.max_players) || 6}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">游戏包名</label>
                    <input
                      type="text"
                      name="room_config[bundle_name]"
                      value={(@current_room && @current_room.bundle_name) || @game.game_name}
                      required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      name="room_config[is_enabled]"
                      checked={@current_room && @current_room.is_enabled}
                      class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label class="ml-2 block text-sm text-gray-900">启用</label>
                  </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    phx-click="close_modal"
                    class="px-4 py-2 text-sm font-medium text-gray-800 bg-gray-300 rounded-md border border-gray-400 hover:bg-gray-400 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-700 rounded-md border border-blue-600 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                  >
                    保存
                  </button>
                </div>
              </.form>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end
end
