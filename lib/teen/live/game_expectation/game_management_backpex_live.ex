defmodule Teen.Live.GameExpectation.GameManagementBackpexLive do
  @moduledoc """
  游戏管理后台界面
  """

  use AshBackpex.LiveResource
  import Phoenix.Component
  import Phoenix.LiveView

  @impl Backpex.LiveResource
  def can?(_assigns, action, _item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  backpex do
    resource Teen.GameManagement.ManageGameConfig
    layout({Teen.Layouts, :admin})

    @impl Backpex.LiveResource
    def singular_name, do: "游戏配置"

    @impl Backpex.LiveResource
    def plural_name, do: "游戏配置"

    fields do
      field :id do
        module Backpex.Fields.Text
        label("ID")
        readonly(true)
        only([:show])
      end

      field :game_id do
        module Backpex.Fields.Number
        label("游戏ID")
        searchable(true)
      end

      field :game_name do
        module Backpex.Fields.Text
        label("游戏名称")
        searchable(true)
      end

      field :display_name do
        module Backpex.Fields.Text
        label("显示名称")
        searchable(true)
      end

      field :description do
        module Backpex.Fields.Textarea
        label("游戏描述")
      end

      field :icon_url do
        module Backpex.Fields.Text
        label("图标URL")
      end

      field :status do
        module Backpex.Fields.Select
        label("运行状态")

        options([
          {"已停用", 0},
          {"维护中", 1},
          {"正常运行", 2},
          {"即将开放", 4}
        ])
      end

      field :is_enabled do
        module Backpex.Fields.Boolean
        label("是否启用")
      end

      field :display_order do
        module Backpex.Fields.Number
        label("显示顺序")
      end

      field :mode do
        module Backpex.Fields.Text
        label("游戏模式")
      end

      field :close_notice do
        module Backpex.Fields.Textarea
        label("关闭提示")
      end

      field :game_class_type do
        module Backpex.Fields.Select
        label("游戏分类")

        options([
          {"单人游戏", 1},
          {"多人游戏", 2},
          {"百人游戏", 3}
        ])
      end

      field :inserted_at do
        module Backpex.Fields.DateTime
        label("创建时间")
        readonly(true)
        only([:index, :show])
      end

      field :updated_at do
        module Backpex.Fields.DateTime
        label("更新时间")
        readonly(true)
        only([:index, :show])
      end
    end
  end

  # 为 Backpex 提供新实例创建函数
  @impl Backpex.LiveResource
  def new(_assigns) do
    struct(Teen.GameManagement.ManageGameConfig)
  end
end
