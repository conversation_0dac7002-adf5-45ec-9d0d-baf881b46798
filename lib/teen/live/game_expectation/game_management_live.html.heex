<div class="min-h-screen bg-base-200 p-6">
  <%= if assigns[:view_mode] do %>
    <%= case @view_mode do %>
      <% :rooms -> %>
        <!-- 房间视图 -->
        <div>
          <!-- 面包屑导航 -->
          <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
              <li class="inline-flex items-center">
                <.link
                  navigate={~p"/admin/games"}
                  class="inline-flex items-center text-sm font-medium text-base-content/70 hover:text-primary"
                >
                  游戏管理
                </.link>
              </li>
              <li>
                <div class="flex items-center">
                  <svg
                    class="w-3 h-3 text-base-content/50 mx-1"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 6 10"
                  >
                    <path
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="m1 9 4-4-4-4"
                    />
                  </svg>
                  <span class="ml-1 text-sm font-medium text-base-content/70 md:ml-2">
                    {@current_game.display_name} - 房间配置
                  </span>
                </div>
              </li>
            </ol>
          </nav>

          <!-- 页面标题和操作 -->
          <div class="mb-6 flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold text-base-content">{@current_game.display_name} - 房间配置</h1>
              <p class="mt-1 text-sm text-base-content/70">管理游戏的房间分场配置</p>
            </div>
            <.link
              patch={~p"/admin/games/#{@current_game.id}/rooms/new"}
              class="btn btn-primary"
            >
              新建房间
            </.link>
          </div>

          <!-- 房间列表 -->
          <div class="bg-base-100 shadow-lg overflow-hidden rounded-lg border border-base-300">
            <%= if @rooms && length(@rooms) > 0 do %>
              <ul role="list" class="divide-y divide-base-300">
                <%= for room <- @rooms do %>
                  <li class="px-6 py-4">
                    <div class="flex items-center justify-between">
                      <div class="flex-1">
                        <div class="flex items-center">
                          <div class="flex-shrink-0">
                            <div class="h-10 w-10 rounded-lg bg-primary/20 flex items-center justify-center">
                              <span class="text-sm font-medium text-primary">{room.server_id}</span>
                            </div>
                          </div>
                          <div class="ml-4 flex-1">
                            <div class="flex items-center justify-between">
                              <div>
                                <p class="text-sm font-medium text-base-content">房间 {room.server_id}</p>
                                <p class="text-sm text-base-content/70">
                                  最小下注: {room.min_bet} | 入场费: {room.entry_fee} | 最大人数: {room.max_players}
                                </p>
                              </div>
                              <div class="flex items-center space-x-2">
                                <%= if room.is_enabled do %>
                                  <span class="badge badge-success badge-sm">
                                    启用
                                  </span>
                                <% else %>
                                  <span class="badge badge-error badge-sm">
                                    禁用
                                  </span>
                                <% end %>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="flex items-center space-x-2 ml-4">
                        <.link
                          patch={~p"/admin/games/#{@current_game.id}/rooms/#{room.id}/edit"}
                          class="btn btn-primary btn-xs"
                        >
                          编辑
                        </.link>
                        <button
                          phx-click="toggle_room_status"
                          phx-value-id={room.id}
                          class={["btn btn-xs", if(room.is_enabled, do: "btn-error", else: "btn-success")]}
                        >
                          {if room.is_enabled, do: "禁用", else: "启用"}
                        </button>
                        <button
                          phx-click="delete_room"
                          phx-value-id={room.id}
                          data-confirm="确定要删除这个房间吗？"
                          class="btn btn-error btn-xs"
                        >
                          删除
                        </button>
                      </div>
                    </div>
                  </li>
                <% end %>
              </ul>
            <% else %>
              <div class="text-center py-12">
                <svg
                  class="mx-auto h-12 w-12 text-base-content/40"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-base-content">暂无房间配置</h3>
                <p class="mt-1 text-sm text-base-content/70">开始为这个游戏创建房间分场配置</p>
                <div class="mt-6">
                  <.link
                    patch={~p"/admin/games/#{@current_game.id}/rooms/new"}
                    class="btn btn-primary"
                  >
                    新建房间
                  </.link>
                </div>
              </div>
            <% end %>
          </div>

          <!-- 房间配置模态框 -->
          <%= if assigns[:show_room_modal] do %>
            <div class="modal modal-open">
              <div class="modal-box bg-base-100 max-w-4xl">
                <div class="mt-3">
                  <h3 class="text-lg font-medium text-base-content mb-4">
                    {if @room_modal_action == :new, do: "新建房间", else: "编辑房间"}
                  </h3>

                  

                  <.form for={%{}} phx-submit="save_room" class="space-y-6">
                    <!-- 基础配置 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <%= if @room_modal_action == :new do %>
                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">服务器ID</span>
                          </label>
                          <input
                            type="number"
                            name="room_config[server_id]"
                            value={@current_room && @current_room.server_id}
                            required
                            class="input input-bordered"
                            placeholder="请输入服务器ID"
                          />
                        </div>

                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">服务器IP</span>
                          </label>
                          <input
                            type="text"
                            name="room_config[server_ip]"
                            value={@current_room && @current_room.server_ip}
                            required
                            class="input input-bordered"
                            placeholder="请输入服务器IP"
                          />
                        </div>

                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">端口</span>
                          </label>
                          <input
                            type="number"
                            name="room_config[port]"
                            value={@current_room && @current_room.port}
                            required
                            class="input input-bordered"
                            placeholder="请输入端口号"
                          />
                        </div>

                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">排序ID</span>
                          </label>
                          <input
                            type="number"
                            name="room_config[order_id]"
                            value={@current_room && @current_room.order_id}
                            required
                            class="input input-bordered"
                            placeholder="请输入排序ID"
                          />
                        </div>

                        <div class="form-control">
                          <label class="label">
                            <span class="label-text">游戏包名</span>
                          </label>
                          <input
                            type="text"
                            name="room_config[bundle_name]"
                            value={(@current_room && @current_room.bundle_name) || (@current_game && @current_game.game_name)}
                            required
                            class="input input-bordered"
                            placeholder="请输入游戏包名"
                          />
                        </div>
                      <% end %>

                      <!-- 可编辑字段 - 新建和编辑都显示 -->
                      <div class="form-control">
                        <label class="label">
                          <span class="label-text">底分</span>
                        </label>
                        <input
                          type="number"
                          name="room_config[min_bet]"
                          value={@current_room && @current_room.min_bet}
                          required
                          class="input input-bordered"
                          placeholder="请输入底分"
                        />
                      </div>

                      <div class="form-control">
                        <label class="label">
                          <span class="label-text">入场金币</span>
                        </label>
                        <input
                          type="number"
                          name="room_config[entry_fee]"
                          value={@current_room && @current_room.entry_fee}
                          required
                          class="input input-bordered"
                          placeholder="请输入入场金币"
                        />
                      </div>

                      <div class="form-control">
                        <label class="label">
                          <span class="label-text">封顶金币(0=无限制)</span>
                        </label>
                        <input
                          type="number"
                          name="room_config[max_bet]"
                          value={@current_room && @current_room.max_bet}
                          class="input input-bordered"
                          placeholder="请输入封顶金币，0表示无限制"
                        />
                      </div>

                      <div class="form-control">
                        <label class="label">
                          <span class="label-text">最大玩家数</span>
                        </label>
                        <input
                          type="number"
                          name="room_config[max_players]"
                          value={(@current_room && @current_room.max_players) || 6}
                          required
                          class="input input-bordered"
                          placeholder="请输入最大玩家数"
                        />
                      </div>

                      <div class="form-control">
                        <label class="label cursor-pointer">
                          <span class="label-text">启用状态</span>
                          <input
                            type="checkbox"
                            name="room_config[is_enabled]"
                            checked={(@current_room && @current_room.is_enabled) || true}
                            class="checkbox checkbox-primary"
                          />
                        </label>
                      </div>
                    </div>

                    <!-- JSON 配置区域 -->
                    <div class="mt-6">
                      <h4 class="text-lg font-medium text-base-content mb-4">游戏配置</h4>

                      <!-- 统一配置 JSON -->
                      <div class="form-control mb-4">
                        <label class="label">
                          <span class="label-text">统一配置 (JSON)</span>
                          <button
                            type="button"
                            class="btn btn-xs btn-outline"
                            onclick="this.nextElementSibling.nextElementSibling.value = this.nextElementSibling.textContent"
                          >
                            加载默认配置
                          </button>
                        </label>
                        <script type="application/json" style="display: none;">
                          {get_current_unified_config_json(@current_room, @current_game)}
                        </script>
                        <textarea
                          name="room_config[unified_config]"
                          rows="15"
                          class="textarea textarea-bordered font-mono text-sm"
                          placeholder="请输入 JSON 格式的统一配置"
                        >{get_current_unified_config_json(@current_room, @current_game)}</textarea>
                        <div class="label">
                          <span class="label-text-alt text-gray-500">
                            包含游戏基础配置和玩法配置的完整 JSON 数据
                          </span>
                        </div>
                      </div>

                      <!-- 配置说明 -->
                      <div class="alert alert-info">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                          <h3 class="font-bold">配置说明</h3>
                          <div class="text-xs">
                            <p>• 统一配置包含完整的游戏设置，包括基础配置和玩法配置</p>
                            <p>• 请确保 JSON 格式正确，否则保存时会出错</p>
                            <p>• 点击"加载默认配置"可以重置为系统默认值</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="modal-action">
                      <button
                        type="button"
                        phx-click="close_room_modal"
                        class="btn btn-ghost"
                      >
                        取消
                      </button>
                      <button
                        type="submit"
                        class="btn btn-primary"
                      >
                        保存
                      </button>
                    </div>
                  </.form>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% _ -> %>
        <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-base-300">
          <p class="text-base-content">未知视图模式</p>
        </div>
    <% end %>
  <% else %>
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-base-content">游戏管理</h1>
    <p class="mt-1 text-sm text-base-content/70">管理游戏配置和房间设置</p>
  </div>

  <!-- 搜索和筛选 -->
  <div class="mb-6 flex flex-col sm:flex-row gap-4">
    <.form for={%{}} phx-submit="search" class="flex-1">
      <input
        type="text"
        name="search[term]"
        value={@search_term}
        placeholder="搜索游戏名称..."
        class="input input-bordered w-full"
      />
    </.form>

    <select
      phx-change="filter_status"
      name="status"
      class="select select-bordered"
    >
      <option value="all" selected={@selected_status == "all"}>全部状态</option>
      <option value="enabled" selected={@selected_status == "enabled"}>已启用</option>
      <option value="disabled" selected={@selected_status == "disabled"}>已禁用</option>
      <option value="2" selected={@selected_status == "2"}>正常运行</option>
      <option value="1" selected={@selected_status == "1"}>维护中</option>
      <option value="0" selected={@selected_status == "0"}>已停用</option>
      <option value="4" selected={@selected_status == "4"}>即将开放</option>
    </select>

    <.link
      patch={~p"/admin/games/new"}
      class="btn btn-primary"
    >
      新建游戏
    </.link>
  </div>

  <!-- 游戏列表 -->
  <div class="bg-base-100 shadow-lg overflow-hidden rounded-lg border border-base-300">
    <ul role="list" class="divide-y divide-base-300">
      <%= for game <- @games do %>
        <li class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <%= if game.icon_url do %>
                  <img class="h-10 w-10 rounded-lg" src={game.icon_url} alt={game.display_name} />
                <% else %>
                  <div class="h-10 w-10 rounded-lg bg-base-300 flex items-center justify-center">
                    <span class="text-sm font-medium text-base-content">
                      {String.first(game.display_name)}
                    </span>
                  </div>
                <% end %>
              </div>
              <div class="ml-4">
                <div class="flex items-center">
                  <p class="text-sm font-medium text-base-content">{game.display_name}</p>
                  <span class={"ml-2 badge badge-sm #{elem(status_badge(game.status), 1)}"}>
                    {elem(status_badge(game.status), 0)}
                  </span>
                  <%= if game.is_enabled do %>
                    <span class="ml-2 badge badge-success badge-sm">
                      启用
                    </span>
                  <% else %>
                    <span class="ml-2 badge badge-error badge-sm">
                      禁用
                    </span>
                  <% end %>
                </div>
                <p class="text-sm text-base-content/70">ID: {game.game_id} | 名称: {game.game_name}</p>
                <%= if game.description do %>
                  <p class="text-sm text-base-content/70">{game.description}</p>
                <% end %>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <!-- 操作按钮 -->
              <.link
                patch={~p"/admin/games/#{game.id}/edit"}
                class="btn btn-primary btn-xs"
              >
                编辑
              </.link>

              <.link
                patch={~p"/admin/games/#{game.id}/rooms"}
                class="btn btn-info btn-xs"
              >
                房间配置
              </.link>

              <button
                phx-click="toggle_status"
                phx-value-id={game.id}
                class={["btn btn-xs", if(game.is_enabled, do: "btn-error", else: "btn-success")]}
              >
                {if game.is_enabled, do: "禁用", else: "启用"}
              </button>

              <button
                phx-click="delete_game"
                phx-value-id={game.id}
                data-confirm="确定要删除这个游戏吗？"
                class="btn btn-error btn-xs"
              >
                删除
              </button>
            </div>
          </div>
        </li>
      <% end %>
    </ul>
  </div>

  <!-- 模态框 -->
  <%= if @show_modal do %>
    <div class="modal modal-open">
      <div class="modal-box bg-base-100">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-base-content mb-4">
            {if @modal_action == :new, do: "新建游戏", else: "编辑游戏"}
          </h3>

          <.form for={%{}} phx-submit="save_game">
            <div class="space-y-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">游戏ID</span>
                </label>
                <input
                  type="number"
                  name="game_config[game_id]"
                  value={@current_game && @current_game.game_id}
                  required
                  class="input input-bordered"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">游戏名称</span>
                </label>
                <input
                  type="text"
                  name="game_config[game_name]"
                  value={@current_game && @current_game.game_name}
                  required
                  class="input input-bordered"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">显示名称</span>
                </label>
                <input
                  type="text"
                  name="game_config[display_name]"
                  value={@current_game && @current_game.display_name}
                  required
                  class="input input-bordered"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">状态</span>
                </label>
                <select
                  name="game_config[status]"
                  class="select select-bordered"
                >
                  <option value="2" selected={@current_game && @current_game.status == 2}>
                    正常运行
                  </option>
                  <option value="1" selected={@current_game && @current_game.status == 1}>
                    维护中
                  </option>
                  <option value="0" selected={@current_game && @current_game.status == 0}>
                    停用
                  </option>
                  <option value="4" selected={@current_game && @current_game.status == 4}>
                    即将开放
                  </option>
                </select>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">图标URL</span>
                </label>
                <input
                  type="text"
                  name="game_config[icon_url]"
                  value={@current_game && @current_game.icon_url}
                  class="input input-bordered"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">描述</span>
                </label>
                <textarea
                  name="game_config[description]"
                  rows="3"
                  class="textarea textarea-bordered"
                ><%= @current_game && @current_game.description %></textarea>
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">显示顺序</span>
                </label>
                <input
                  type="number"
                  name="game_config[display_order]"
                  value={@current_game && @current_game.display_order}
                  class="input input-bordered"
                />
              </div>

              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">启用</span>
                  <input
                    type="checkbox"
                    name="game_config[is_enabled]"
                    checked={@current_game && @current_game.is_enabled}
                    class="checkbox"
                  />
                </label>
              </div>
            </div>

            <div class="modal-action">
              <button
                type="button"
                phx-click="close_modal"
                class="btn btn-ghost"
              >
                取消
              </button>
              <button
                type="submit"
                class="btn btn-primary"
              >
                保存
              </button>
            </div>
          </.form>
        </div>
      </div>
    </div>
  <% end %>
  <% end %>
</div>
