<!-- 游戏控制仪表盘 -->
<div class="min-h-screen bg-base-200">
  <!-- 顶部导航 -->
  <div class="bg-base-100 shadow-lg border-b border-base-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-4">
        <div class="flex items-center space-x-4">
          <%= if @selected_game do %>
            <button phx-click="back_to_overview" class="btn btn-ghost btn-sm">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                >
                </path>
              </svg>
              返回总览
            </button>
            <span class="text-base-content/30">|</span>
          <% end %>

          <h1 class="text-2xl font-bold text-base-content">
            <%= if @selected_game do %>
              <%= case @game_template do %>
                <% :hundred_player -> %>
                  百人场控制面板
                <% :wallet -> %>
                  钱包控制面板
                <% _ -> %>
                  游戏控制面板
              <% end %>
            <% else %>
              游戏控制仪表盘
            <% end %>
          </h1>

          <button phx-click="refresh" class="btn btn-primary btn-sm">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              >
              </path>
            </svg>
            刷新
          </button>
        </div>
        
<!-- 时间范围切换 -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-base-content/70">时间范围:</span>
          <div class="join">
            <button
              phx-click="toggle_time_range"
              phx-value-range="minute"
              class={[
                "join-item btn btn-sm",
                if(@time_range == :minute, do: "btn-primary", else: "btn-ghost")
              ]}
            >
              分钟
            </button>
            <button
              phx-click="toggle_time_range"
              phx-value-range="hour"
              class={[
                "join-item btn btn-sm",
                if(@time_range == :hour, do: "btn-primary", else: "btn-ghost")
              ]}
            >
              小时
            </button>
            <button
              phx-click="toggle_time_range"
              phx-value-range="day"
              class={[
                "join-item btn btn-sm",
                if(@time_range == :day, do: "btn-primary", else: "btn-ghost")
              ]}
            >
              天
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
<!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <%= if @game_template == :hundred_player && @hundred_player_status do %>
      <!-- 百人场控制面板 -->
      <div class="space-y-6">
        <!-- 当前状态概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 当前库存 -->
          <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-primary/20">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
                  <svg
                    class="w-5 h-5 text-primary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                    >
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-base-content/70">当前库存</p>
                <p class="text-2xl font-bold text-base-content">
                  ¥{format_currency(@hundred_player_status.current_inventory)}
                </p>
              </div>
            </div>
          </div>
          
<!-- 中心线 -->
          <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-success/20">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-success/20 rounded-lg flex items-center justify-center">
                  <svg
                    class="w-5 h-5 text-success"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                    >
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-base-content/70">中心线</p>
                <p class="text-2xl font-bold text-base-content">
                  ¥{format_currency(@hundred_player_status.center_line)}
                </p>
              </div>
            </div>
          </div>
          
<!-- 库存百分比 -->
          <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-warning/20">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-warning/20 rounded-lg flex items-center justify-center">
                  <svg
                    class="w-5 h-5 text-warning"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    >
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-base-content/70">库存百分比</p>
                <p class={"text-2xl font-bold #{get_inventory_status_color(@hundred_player_status.inventory_percentage)}"}>
                  {format_percentage(@hundred_player_status.inventory_percentage)}%
                </p>
              </div>
            </div>
          </div>
          
<!-- 控制模式 -->
          <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-secondary/20">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-secondary/20 rounded-lg flex items-center justify-center">
                  <svg
                    class="w-5 h-5 text-secondary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    >
                    </path>
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    >
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-base-content/70">控制模式</p>
                <p class={"text-lg font-bold #{get_control_mode_color(@hundred_player_status.control_status.control_mode)}"}>
                  {get_control_mode_text(@hundred_player_status.control_status.control_mode)}
                </p>
              </div>
            </div>
          </div>
        </div>
        
<!-- 四级控制线波动趋势图 -->
        <div
          class="bg-base-100 rounded-lg shadow-lg p-6 border border-base-300"
          style="margin-bottom: 3rem;"
        >
          <%= if @hundred_player_status.control_line_chart_data && map_size(@hundred_player_status.control_line_chart_data) > 0 do %>
            <.control_line_chart
              id="hundred-player-control-line-chart"
              chart_data={@hundred_player_status.control_line_chart_data}
              time_range={@time_range}
              title="四级控制线波动趋势图"
              class="w-full"
            />
          <% else %>
            <div class="text-center py-8">
              <div class="text-base-content/70 mb-2">
                <svg
                  class="mx-auto h-12 w-12 text-base-content/40"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  >
                  </path>
                </svg>
              </div>
              <h3 class="text-lg font-medium text-base-content">正在加载图表数据...</h3>
              <p class="text-sm text-base-content/70 mt-1">
                系统正在收集控制线历史数据，请稍候
              </p>
              <div class="mt-4">
                <button phx-click="refresh" class="btn btn-primary">
                  重新加载数据
                </button>
              </div>
            </div>
          <% end %>
        </div>
        
<!-- 四级控制线状态面板 -->
        <div
          class="bg-base-100 rounded-lg shadow-lg p-6 border border-base-300"
          style="margin-top: 2rem; z-index: 2; position: relative;"
        >
          <h3 class="text-lg font-medium text-base-content mb-4">四级控制线状态面板</h3>
          
<!-- 控制线进度条 -->
          <div class="space-y-4">
            <div class="relative">
              <div class="flex justify-between text-xs text-base-content/70 mb-2">
                <span>绝对收分线</span>
                <span>前置收分线</span>
                <span>中心线</span>
                <span>前置放分线</span>
                <span>绝对放分线</span>
              </div>

              <div class="w-full bg-base-300 rounded-full h-6 relative">
                <!-- 控制线区域标记 -->
                <div class="absolute inset-0 flex">
                  <div class="w-1/5 bg-red-500 rounded-l-full opacity-30"></div>
                  <div class="w-1/5 bg-orange-500 opacity-30"></div>
                  <div class="w-1/5 bg-green-500 opacity-30"></div>
                  <div class="w-1/5 bg-blue-500 opacity-30"></div>
                  <div class="w-1/5 bg-purple-500 rounded-r-full opacity-30"></div>
                </div>
                
<!-- 当前位置指示器 -->
                <div
                  class="absolute top-0 h-6 w-3 bg-black rounded transform -translate-x-1.5 flex items-center justify-center"
                  style={"left: #{calculate_control_line_position(@hundred_player_status.current_inventory, @hundred_player_status.center_line)}%"}
                >
                  <div class="w-1 h-4 bg-base-100 rounded"></div>
                </div>
              </div>

              <div class="flex justify-between text-xs text-base-content/50 mt-1">
                <span>强制收分</span>
                <span>前置收分</span>
                <span>随机</span>
                <span>前置放分</span>
                <span>强制放分</span>
              </div>
            </div>
            
<!-- 控制线数值显示 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              <div class="text-center p-3 bg-error/10 rounded-lg border border-error/20">
                <p class="text-xs text-base-content/70">绝对收分线</p>
                <p class="text-sm font-medium text-error">
                  ¥{format_currency(@hundred_player_status.control_lines.absolute_collect)}
                </p>
              </div>
              <div class="text-center p-3 bg-warning/10 rounded-lg border border-warning/20">
                <p class="text-xs text-base-content/70">前置收分线</p>
                <p class="text-sm font-medium text-warning">
                  ¥{format_currency(@hundred_player_status.control_lines.pre_collect)}
                </p>
              </div>
              <div class="text-center p-3 bg-info/10 rounded-lg border border-info/20">
                <p class="text-xs text-base-content/70">前置放分线</p>
                <p class="text-sm font-medium text-info">
                  ¥{format_currency(@hundred_player_status.control_lines.pre_release)}
                </p>
              </div>
              <div class="text-center p-3 bg-secondary/10 rounded-lg border border-secondary/20">
                <p class="text-xs text-base-content/70">绝对放分线</p>
                <p class="text-sm font-medium text-secondary">
                  ¥{format_currency(@hundred_player_status.control_lines.absolute_release)}
                </p>
              </div>
            </div>
          </div>
        </div>
        
<!-- 控制操作面板 -->
        <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-base-300">
          <h3 class="text-lg font-medium text-base-content mb-4">控制操作</h3>
          
<!-- 权重调整 -->
          <form phx-submit="save_control_weight" class="max-w-md">
            <label class="block text-sm font-medium text-base-content/70 mb-2">
              控制权重 (1-1000)
            </label>
            <div class="flex space-x-2">
              <input
                name="control_weight"
                type="number"
                min="1"
                max="1000"
                value={@hundred_player_status.control_status.config.control_weight || 500}
                class="input input-bordered flex-1"
              />
              <button type="submit" class="btn btn-primary btn-sm">
                保存
              </button>
              <button type="button" phx-click="reset_control_weight" class="btn btn-ghost btn-sm">
                重置
              </button>
            </div>
            <div class="mt-2 text-xs text-base-content/70 bg-info/10 p-3 rounded-lg border border-info/20">
              <h5 class="font-medium mb-2">控制权重机制说明：</h5>
              <ul class="space-y-1">
                <li>• <strong>权重范围：</strong>1-1000，数值越高控制概率越大</li>
                <li>• <strong>触发时机：</strong>库存到达前置收分线/放分线时进行权重判断</li>
                <li>• <strong>判断逻辑：</strong>系统生成1-1000随机数，≤权重值时触发控制</li>
                <li>• <strong>概率示例：</strong>权重700 = 70%概率触发，权重300 = 30%概率触发</li>
                <li>• <strong>建议配置：</strong>保守型800+，平衡型600-700，激进型500-600</li>
                <li>• <strong>默认值：</strong>500（平衡状态，50%概率触发控制）</li>
              </ul>
            </div>
          </form>
        </div>
        
<!-- 运维配置面板 -->
        <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-base-300">
          <h3 class="text-lg font-medium text-base-content mb-4">浮动控制线配置</h3>

          <div class="space-y-6">
            <!-- 收分线浮动配置 -->
            <div>
              <h4 class="text-md font-medium text-base-content mb-3">收分线浮动配置</h4>
              <form phx-submit="save_collect_line_config">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">收分浮动最大值</span>
                    </label>
                    <input
                      name="collect_line_max"
                      type="number"
                      min="1000"
                      max="10000000"
                      step="1000"
                      value={
                        if @hundred_player_status.control_status.config,
                          do:
                            @hundred_player_status.control_status.config.collect_line_max || 30000,
                          else: 30000
                      }
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">收分浮动最小值</span>
                    </label>
                    <input
                      name="collect_line_min"
                      type="number"
                      min="1000"
                      max="50000"
                      step="1000"
                      value={
                        if @hundred_player_status.control_status.config,
                          do:
                            @hundred_player_status.control_status.config.collect_line_min || 5000,
                          else: 5000
                      }
                      class="input input-bordered"
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">收分浮动比例 (0-1.0)</span>
                    </label>
                    <input
                      name="collect_line_ratio"
                      type="number"
                      min="0.01"
                      max="1.0"
                      step="0.01"
                      value={
                        if @hundred_player_status.control_status.config,
                          do:
                            Decimal.to_float(
                              @hundred_player_status.control_status.config.collect_line_ratio ||
                                Decimal.new("0.2")
                            ),
                          else: 0.2
                      }
                      class="input input-bordered"
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">前置收分线比例 (0-1.0)</span>
                    </label>
                    <input
                      name="pre_collect_line_ratio"
                      type="number"
                      min="0.01"
                      max="1.0"
                      step="0.01"
                      value={
                        if @hundred_player_status.control_status.config,
                          do:
                            Decimal.to_float(
                              @hundred_player_status.control_status.config.pre_collect_line_ratio ||
                                Decimal.new("0.7")
                            ),
                          else: 0.7
                      }
                      class="input input-bordered"
                    />
                  </div>
                </div>
                <div class="text-xs text-base-content/70 bg-success/10 p-3 rounded-lg mb-3 border border-success/20">
                  <h5 class="font-medium mb-2">收分线浮动配置说明：</h5>
                  <ul class="space-y-1">
                    <li>• <strong>计算公式：</strong>浮动值 = clamp(中心线 × 浮动比例, 最小值, 最大值)</li>
                    <li>• <strong>绝对收分线：</strong>中心线 - 浮动值（强制收分触发点）</li>
                    <li>• <strong>前置收分线：</strong>中心线 - (浮动值 × 前置比例)（权重收分触发点）</li>
                    <li>• <strong>建议配置：</strong>浮动比例0.1-0.3，前置比例0.6-0.8</li>
                  </ul>
                </div>
                <div class="flex justify-end">
                  <button type="submit" class="btn btn-primary">
                    保存收分线配置
                  </button>
                </div>
              </form>
            </div>
            
<!-- 放分线浮动配置 -->
            <div>
              <h4 class="text-md font-medium text-base-content mb-3">放分线浮动配置</h4>
              <form phx-submit="save_release_line_config">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">放分浮动最大值</span>
                    </label>
                    <input
                      name="release_line_max"
                      type="number"
                      min="1000"
                      max="10000000"
                      step="1000"
                      value={
                        if @hundred_player_status.control_status.config,
                          do:
                            @hundred_player_status.control_status.config.release_line_max || 30000,
                          else: 30000
                      }
                      class="input input-bordered"
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">放分浮动最小值</span>
                    </label>
                    <input
                      name="release_line_min"
                      type="number"
                      min="1000"
                      max="50000"
                      step="1000"
                      value={
                        if @hundred_player_status.control_status.config,
                          do:
                            @hundred_player_status.control_status.config.release_line_min || 5000,
                          else: 5000
                      }
                      class="input input-bordered"
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">放分浮动比例 (0-1.0)</span>
                    </label>
                    <input
                      name="release_line_ratio"
                      type="number"
                      min="0.01"
                      max="1.0"
                      step="0.01"
                      value={
                        if @hundred_player_status.control_status.config,
                          do:
                            Decimal.to_float(
                              @hundred_player_status.control_status.config.release_line_ratio ||
                                Decimal.new("0.2")
                            ),
                          else: 0.2
                      }
                      class="input input-bordered"
                    />
                  </div>
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text">前置放分线比例 (0-1.0)</span>
                    </label>
                    <input
                      name="pre_release_line_ratio"
                      type="number"
                      min="0.01"
                      max="1.0"
                      step="0.01"
                      value={
                        if @hundred_player_status.control_status.config,
                          do:
                            Decimal.to_float(
                              @hundred_player_status.control_status.config.pre_release_line_ratio ||
                                Decimal.new("0.7")
                            ),
                          else: 0.7
                      }
                      class="input input-bordered"
                    />
                  </div>
                </div>
                <div class="text-xs text-base-content/70 bg-secondary/10 p-3 rounded-lg mb-3 border border-secondary/20">
                  <h5 class="font-medium mb-2">放分线浮动配置说明：</h5>
                  <ul class="space-y-1">
                    <li>• <strong>计算公式：</strong>浮动值 = clamp(中心线 × 浮动比例, 最小值, 最大值)</li>
                    <li>• <strong>绝对放分线：</strong>中心线 + 浮动值（强制放分触发点）</li>
                    <li>• <strong>前置放分线：</strong>中心线 + (浮动值 × 前置比例)（权重放分触发点）</li>
                    <li>• <strong>建议配置：</strong>浮动比例0.1-0.3，前置比例0.6-0.8</li>
                  </ul>
                </div>
                <div class="flex justify-end">
                  <button type="submit" class="btn btn-primary">
                    保存放分线配置
                  </button>
                </div>
              </form>
            </div>
            
<!-- 赢家抽水配置 -->
            <div>
              <h4 class="text-md font-medium text-base-content mb-3">赢家抽水配置</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">明税抽水比例 (0-100%)</span>
                  </label>
                  <form phx-submit="save_winner_tax_rate">
                    <div class="flex space-x-2">
                      <input
                        name="winner_tax_rate"
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        value={
                          if @hundred_player_status.control_status.config,
                            do:
                              Decimal.to_float(
                                Decimal.mult(
                                  @hundred_player_status.control_status.config.winner_tax_rate ||
                                    Decimal.new("0.05"),
                                  100
                                )
                              ),
                            else: 5.0
                        }
                        class="input input-bordered flex-1"
                      />
                      <span class="inline-flex items-center px-3 py-2 border border-base-300 bg-base-200 text-base-content/70 text-sm rounded-md">
                        %
                      </span>
                      <button type="submit" class="btn btn-primary btn-sm">
                        保存
                      </button>
                    </div>
                  </form>
                  <p class="mt-1 text-xs text-base-content/70">
                    对真人玩家赢家抽水的百分比，机器人不参与抽水。例：5% 表示赢100抽5元
                  </p>
                </div>
                <div class="text-sm text-base-content/70 bg-info/10 p-4 rounded-lg border border-info/20">
                  <h5 class="font-medium mb-2">抽水机制说明：</h5>
                  <ul class="space-y-1 text-xs">
                    <li>• 只对真人玩家的盈利进行抽水</li>
                    <li>• 机器人玩家不参与抽水计算</li>
                    <li>• 明税：从玩家盈利中抽取的税金</li>
                    <li>• 暗税：从明税中抽取的比例，用于调整中心线</li>
                    <li>• 平台收入 = 明税 - 暗税</li>
                  </ul>
                </div>
              </div>
            </div>
            
<!-- 暗税配置 -->
            <div>
              <h4 class="text-md font-medium text-base-content mb-3">暗税配置</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">暗税千分比 (-1000 到 1000)</span>
                  </label>
                  <form phx-submit="save_dark_tax_rate">
                    <div class="flex space-x-2">
                      <input
                        name="dark_tax_rate"
                        type="number"
                        min="-1000"
                        max="1000"
                        step="1"
                        value={
                          if @hundred_player_status.control_status.config,
                            do: @hundred_player_status.control_status.config.dark_tax_rate || -50,
                            else: -50
                        }
                        class="input input-bordered flex-1"
                      />
                      <span class="inline-flex items-center px-3 py-2 border border-base-300 bg-base-200 text-base-content/70 text-sm rounded-md">
                        ‰
                      </span>
                      <button type="submit" class="btn btn-primary btn-sm">
                        保存
                      </button>
                    </div>
                  </form>
                  <div class="mt-2 text-xs text-base-content/70 bg-warning/10 p-3 rounded-lg border border-warning/20">
                    <h5 class="font-medium mb-2">暗税机制详解：</h5>
                    <ul class="space-y-1">
                      <li>• <strong>定义：</strong>从明税中抽取的比例，用于调整游戏控制中心线</li>
                      <li>• <strong>计算公式：</strong>暗税 = 明税 × (配置值 ÷ 1000)</li>
                      <li>• <strong>正值效果：</strong>中心线下降，系统倾向放分（利于玩家）</li>
                      <li>• <strong>负值效果：</strong>中心线上升，系统倾向收分（利于平台）</li>
                      <li>• <strong>资金流向：</strong>平台实际收入 = 明税 - 暗税</li>
                      <li>• <strong>示例：</strong>配置-50 → 明税100元 → 暗税-5元 → 平台收入105元，中心线+5</li>
                      <li>• <strong>默认：</strong>-50（即-5%，偏向平台收分）</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
<!-- 游戏状态信息 -->
        <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-base-300">
          <h3 class="text-lg font-medium text-base-content mb-4">游戏状态信息</h3>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-base-content/70">最近结果</label>
              <p class="mt-1 text-sm text-base-content">
                {@hundred_player_status.last_result || "无"}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-base-content/70">暗税累计</label>
              <p class="mt-1 text-sm text-base-content">
                ¥{format_currency(@hundred_player_status.dark_tax_accumulated || 0)}
              </p>
            </div>
            <div>
              <label class="block text-sm font-medium text-base-content/70">最后更新</label>
              <p class="mt-1 text-sm text-base-content">
                <%= if @hundred_player_status.last_update do %>
                  {Calendar.strftime(@hundred_player_status.last_update, "%Y-%m-%d %H:%M:%S")}
                <% else %>
                  未知
                <% end %>
              </p>
            </div>
          </div>
        </div>
      </div>
    <% else %>
      <!-- 游戏总览和钱包控制 -->
      <%= if @selected_game == nil do %>
        <!-- 总览卡片 -->
        <div class="bg-base-100 rounded-lg shadow-lg p-6 mb-6 border border-base-300">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-base-content">总钱包余额</h2>
            <div class="text-3xl font-bold text-base-content">
              ¥{format_number(@total_balance)}
            </div>
          </div>
          
<!-- 总钱包波动曲线 -->
          <div class="h-64 bg-base-200 rounded-lg p-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-sm font-medium text-base-content/70">总钱包波动曲线</h3>
              <span class="text-xs text-base-content/70">
                时间范围: {case @time_range do
                  :minute -> "最近60分钟"
                  :hour -> "最近24小时"
                  :day -> "最近30天"
                end}
              </span>
            </div>
            <div
              id="total-wallet-chart"
              phx-hook="MiniWalletChart"
              data-chart-data={Jason.encode!(convert_total_history_to_chart_data(@total_history))}
              data-time-range={@time_range}
              phx-update="ignore"
              class="w-full h-full"
            >
            </div>
          </div>
        </div>
        
<!-- 游戏列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <%= for game <- @games do %>
            <div
              class="bg-base-100 rounded-lg shadow-lg hover:shadow-xl transition-shadow cursor-pointer border border-base-300"
              phx-click="navigate_to_game"
              phx-value-game_id={game.id}
            >
              <div class="p-6">
                <!-- 游戏标题 -->
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-base-content">{game.name}</h3>
                  <span class={
                    "badge badge-sm " <>
                    if game.template_type == "百人场", do: "badge-secondary", else: "badge-success"
                  }>
                    {game.template_type}
                  </span>
                </div>
                
<!-- 当前余额 -->
                <div class="mb-4">
                  <div class="text-2xl font-bold text-base-content">
                    ¥{format_number(game.current_balance)}
                  </div>
                  <div class="text-sm text-base-content/70">
                    基准: ¥{format_number(game.base_balance)}
                  </div>
                </div>
                
<!-- 控制状态 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <span class="text-sm text-base-content/70">状态:</span>
                    <span class={"text-sm font-medium #{get_action_color(game.action)}"}>
                      {get_action_text(game.action)}
                    </span>
                  </div>

                  <%= if game.action_strength > 0 do %>
                    <div class="text-sm text-base-content/70">
                      强度: {Float.round(game.action_strength, 2)}
                    </div>
                  <% end %>
                </div>
                
<!-- 余额比例条 -->
                <div class="mt-4">
                  <div class="flex justify-between text-xs text-base-content/70 mb-1">
                    <span>收分线</span>
                    <span>中心线</span>
                    <span>放分线</span>
                  </div>
                  <div class="w-full bg-base-300 rounded-full h-2">
                    <% percentage =
                      calculate_inventory_percentage(game.current_balance, game.base_balance) %>
                    <div
                      class={"h-2 rounded-full #{get_inventory_bar_color(percentage)}"}
                      style={"width: #{min(100, percentage)}%"}
                    >
                    </div>
                  </div>
                  <div class="flex justify-between text-xs text-base-content/50 mt-1">
                    <span>80%</span>
                    <span>100%</span>
                    <span>120%</span>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <!-- 钱包控制面板（非百人场游戏） -->
        <div class="space-y-6">
          <!-- 游戏信息卡片 -->
          <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-base-300">
            <% selected_game_info = Enum.find(@games, fn g -> g.id == @selected_game end) %>
            <%= if selected_game_info do %>
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-base-content">
                  {selected_game_info.name} - 钱包控制
                </h2>
                <span class="badge badge-success">
                  钱包控制
                </span>
              </div>
              
<!-- 当前状态 -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="text-center p-4 bg-base-200 rounded-lg">
                  <div class="text-2xl font-bold text-base-content">
                    ¥{format_number(selected_game_info.current_balance)}
                  </div>
                  <div class="text-sm text-base-content/70">当前余额</div>
                </div>
                <div class="text-center p-4 bg-base-200 rounded-lg">
                  <div class="text-2xl font-bold text-base-content">
                    ¥{format_number(selected_game_info.base_balance)}
                  </div>
                  <div class="text-sm text-base-content/70">基准余额</div>
                </div>
                <div class="text-center p-4 bg-base-200 rounded-lg">
                  <div class={"text-lg font-bold #{get_action_color(selected_game_info.action)}"}>
                    {get_action_text(selected_game_info.action)}
                  </div>
                  <div class="text-sm text-base-content/70">控制状态</div>
                </div>
              </div>
            <% end %>
          </div>
          
<!-- 游戏波动曲线 -->
          <div class="bg-base-100 rounded-lg shadow-lg p-6 border border-base-300">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-base-content">钱包波动曲线</h3>
              <span class="text-xs text-base-content/70">显示三线控制状态</span>
            </div>
            <div class="h-64 bg-base-200 rounded-lg p-4">
              <div
                id="game-wallet-chart"
                phx-hook="MiniWalletChart"
                data-chart-data={Jason.encode!(convert_game_history_to_chart_data(@game_history))}
                data-time-range={@time_range}
                phx-update="ignore"
                class="w-full h-full"
              >
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
