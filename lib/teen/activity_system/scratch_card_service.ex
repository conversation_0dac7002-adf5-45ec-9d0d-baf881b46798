defmodule Teen.ActivitySystem.ScratchCardService do
  @moduledoc """
  30次刮卡活动业务逻辑服务

  基于现有的ScratchCardActivity系统，提供完整的业务逻辑，包括：
  - 用户数据获取和状态管理
  - 充值记录集成
  - 奖励计算和发放
  - 活动时间和状态控制
  """

  require Logger

  alias Teen.ActivitySystem.{
    ScratchCardActivity,
    ScratchCardTaskLevel,
    ScratchCardLevelReward,
    UserActivityParticipation,
    RewardClaimRecord
  }

  alias Teen.GameManagement.UserVipRecord

  @doc """
  获取用户30次刮卡活动信息
  """
  def get_user_activity_info(user_id) do
    with {:ok, activity} <- get_active_scratch_card_activity(),
         {:ok, participation} <- get_or_create_participation(user_id, activity.id),
         {:ok, levels} <- get_activity_levels(activity.id) do
      # 确保用户有VIP记录
      _ = ensure_user_vip_record(user_id)

      # 获取用户累计充值金额
      user_recharge = get_user_cumulative_recharge_amount(user_id)

      # 计算用户当前等级
      current_level = calculate_user_current_level(user_recharge, levels)

      # 获取已领取的奖励记录
      claimed_records = get_user_claimed_records(user_id, activity.id)

      activity_data = %{
        "code" => 0,
        "msg" => "获取成功",
        "user" => build_user_data(participation, user_recharge, current_level, claimed_records),
        "leveldata" => build_level_data(levels)
      }

      {:ok, activity_data}
    else
      {:error, reason} ->
        Logger.error("获取30次刮卡活动信息失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  领取30次刮卡活动奖励（支持两步领取机制）
  """
  def claim_card_reward(user_id, fetch_type) when is_integer(fetch_type) do
    case fetch_type do
      0 -> do_generate_pending_rewards(user_id)
      1 -> do_confirm_pending_rewards(user_id)
      _ -> {:error, :invalid_fetch_type}
    end
  end

  @doc """
  领取30次刮卡活动奖励（旧版本，保留兼容）
  """
  def claim_card_reward(user_id, card_index, level) do
    with {:ok, activity} <- get_active_scratch_card_activity(),
         {:ok, participation} <- get_or_create_participation(user_id, activity.id),
         :ok <- validate_claim_params(card_index, level),
         :ok <- validate_claim_eligibility(participation, card_index, level),
         {:ok, task_level} <- get_task_level(activity.id, level),
         {:ok, reward_type, reward_amount} <- calculate_reward(task_level.id),
         {:ok, _} <-
           record_claim(user_id, activity.id, card_index, level, reward_type, reward_amount),
         {:ok, _} <- update_participation_progress(participation, card_index),
         {:ok, _} <- distribute_reward(user_id, reward_type, reward_amount) do
      response_data = %{
        "code" => 0,
        "msg" => "领取成功",
        "cardindex" => card_index,
        "level" => level,
        "awardtype" => reward_type,
        "awardnum" => reward_amount
      }

      {:ok, response_data}
    else
      {:error, :invalid_level} ->
        {:ok, build_error_response(1001, "等级参数无效", card_index, level)}

      {:error, :invalid_card_index} ->
        {:ok, build_error_response(1002, "卡片索引无效", card_index, level)}

      {:error, :already_claimed} ->
        {:ok, build_error_response(1003, "卡片已被领取", card_index, level)}

      {:error, :insufficient_recharge} ->
        {:ok, build_error_response(1004, "充值金额不足", card_index, level)}

      {:error, :no_active_activity} ->
        {:ok, build_error_response(1005, "活动未开启", card_index, level)}

      {:error, reason} ->
        Logger.error("领取30次刮卡奖励失败: #{inspect(reason)}")
        {:ok, build_error_response(1999, "系统错误", card_index, level)}
    end
  end

  @doc """
  解除用户刮刮卡限制（管理员功能）
  """
  def reset_user_scratch_card_limit(user_id) do
    with {:ok, activity} <- get_active_scratch_card_activity(),
         {:ok, participation} <- get_or_create_participation(user_id, activity.id) do
      # 重置用户的参与数据
      current_data = participation.participation_data || %{}

      # 保留基础数据，但清除限制相关的数据
      reset_data =
        current_data
        # 重置已刮卡片数
        |> Map.put("current_card", 0)
        # 清空已领取记录
        |> Map.put("claimed_cards", [])
        # 清除待领取奖励
        |> Map.delete("pending_rewards")

      case participation
           |> Ash.Changeset.for_update(:update_progress, %{
             participation_data: reset_data
           })
           |> Ash.update() do
        {:ok, updated} ->
          Logger.info("已解除用户 #{user_id} 的刮刮卡限制")
          {:ok, updated}

        {:error, reason} ->
          Logger.error("解除刮刮卡限制失败: #{inspect(reason)}")
          {:error, reason}
      end
    else
      {:error, reason} ->
        Logger.error("解除刮刮卡限制失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  更新用户充值记录（当用户充值时调用）
  """
  def update_user_recharge(user_id, recharge_amount) do
    with {:ok, activity} <- get_active_scratch_card_activity(),
         {:ok, participation} <- get_or_create_participation(user_id, activity.id) do
      # 更新参与记录中的充值金额
      current_data = participation.participation_data || %{}
      current_recharge = Decimal.new(Map.get(current_data, "total_recharge", "0"))
      new_recharge = Decimal.add(current_recharge, recharge_amount)

      # 计算新等级
      new_level = calculate_level_by_recharge(new_recharge)

      updated_data =
        Map.merge(current_data, %{
          "total_recharge" => Decimal.to_string(new_recharge),
          "current_level" => new_level
        })

      case participation
           |> Ash.Changeset.for_update(:update_progress, %{
             participation_data: updated_data,
             progress: new_level
           })
           |> Ash.update() do
        {:ok, _} ->
          Logger.info("更新用户#{user_id}刮卡活动充值记录: +#{recharge_amount}分, 新等级: #{new_level}")
          {:ok, :updated}

        {:error, reason} ->
          Logger.error("更新用户充值记录失败: #{inspect(reason)}")
          {:error, reason}
      end
    else
      {:error, reason} ->
        Logger.error("更新用户充值记录失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # 私有函数

  defp do_generate_pending_rewards(user_id) do
    with {:ok, activity} <- get_active_scratch_card_activity(),
         {:ok, participation} <- get_or_create_participation(user_id, activity.id),
         {:ok, vip_record} <- ensure_user_vip_record(user_id) do
      # 获取用户当前等级
      user_recharge = get_user_cumulative_recharge_amount(user_id)
      {:ok, levels} = get_activity_levels(activity.id)
      current_level = calculate_user_current_level(user_recharge, levels)

      # 生成3张卡片的奖励
      rewards =
        Enum.map(1..3, fn index ->
          {:ok, task_level} = get_task_level(activity.id, current_level)
          {:ok, reward_type, reward_amount} = calculate_reward(task_level.id)

          %{
            "index" => index,
            "award" => reward_amount,
            "awardtype" => reward_type,
            # 添加 children 字段以兼容客户端
            "children" => []
          }
        end)

      # 保存到participation的pending_data中
      pending_data = %{
        "fetchtype" => 0,
        "level" => current_level,
        "rewards" => rewards,
        "generated_at" => DateTime.utc_now() |> DateTime.to_iso8601()
      }

      current_data = participation.participation_data || %{}
      updated_data = Map.put(current_data, "pending_rewards", pending_data)

      case participation
           |> Ash.Changeset.for_update(:update_progress, %{
             participation_data: updated_data
           })
           |> Ash.update() do
        {:ok, _} ->
          # 构建返回格式 - 使用数组而不是Map
          response_data = %{
            "code" => 0,
            "msg" => "",
            "fetchtype" => 0,
            # 直接使用数组
            "fetchawardlist" => rewards
          }

          {:ok, response_data}

        {:error, reason} ->
          Logger.error("保存待领取奖励失败: #{inspect(reason)}")
          {:error, reason}
      end
    else
      {:error, reason} ->
        Logger.error("生成待领取奖励失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp do_confirm_pending_rewards(user_id) do
    with {:ok, activity} <- get_active_scratch_card_activity(),
         {:ok, participation} <- get_or_create_participation(user_id, activity.id),
         pending_data when not is_nil(pending_data) <- get_pending_rewards(participation),
         :ok <- validate_pending_rewards(pending_data),
         {:ok, _} <- distribute_pending_rewards(user_id, activity.id, participation, pending_data) do
      # 清空pending_rewards
      current_data = participation.participation_data || %{}
      updated_data = Map.delete(current_data, "pending_rewards")

      participation
      |> Ash.Changeset.for_update(:update_progress, %{
        participation_data: updated_data
      })
      |> Ash.update()

      # 构建返回格式 - 使用数组而不是Map
      rewards = Map.get(pending_data, "rewards", [])

      response_data = %{
        "code" => 0,
        "msg" => "领取成功",
        "fetchtype" => 1,
        # 直接使用数组
        "fetchawardlist" => rewards
      }

      {:ok, response_data}
    else
      nil ->
        {:ok,
         %{
           "code" => 1010,
           "msg" => "没有待领取的奖励",
           "fetchtype" => 1,
           # 改为空数组
           "fetchawardlist" => []
         }}

      {:error, reason} ->
        Logger.error("确认领取奖励失败: #{inspect(reason)}")

        {:ok,
         %{
           "code" => 1999,
           "msg" => "系统错误",
           "fetchtype" => 1,
           # 改为空数组
           "fetchawardlist" => []
         }}
    end
  end

  defp get_pending_rewards(participation) do
    data = participation.participation_data || %{}
    Map.get(data, "pending_rewards")
  end

  defp validate_pending_rewards(pending_data) do
    # 检查是否过期（比如超过5分钟）
    case Map.get(pending_data, "generated_at") do
      nil ->
        :ok

      generated_at ->
        {:ok, generated_time, _} = DateTime.from_iso8601(generated_at)
        diff = DateTime.diff(DateTime.utc_now(), generated_time, :second)

        # 5分钟
        if diff > 300 do
          {:error, :rewards_expired}
        else
          :ok
        end
    end
  end

  defp distribute_pending_rewards(user_id, activity_id, participation, pending_data) do
    rewards = Map.get(pending_data, "rewards", [])
    level = Map.get(pending_data, "level", 1)

    # 发放所有奖励
    Enum.each(rewards, fn reward ->
      reward_type = Map.get(reward, "awardtype", 0)
      reward_amount = Map.get(reward, "award", 0)

      # 记录领取
      record_claim(user_id, activity_id, 0, level, reward_type, reward_amount)

      # 发放奖励
      distribute_reward(user_id, reward_type, reward_amount)
    end)

    # 更新已刮卡数量
    current_card = get_current_card_count(participation)
    update_card_progress(participation, current_card + 3)

    {:ok, :distributed}
  end

  defp get_current_card_count(participation) do
    data = participation.participation_data || %{}
    Map.get(data, "current_card", 0)
  end

  defp update_card_progress(participation, new_card_count) do
    current_data = participation.participation_data || %{}
    updated_data = Map.put(current_data, "current_card", new_card_count)

    participation
    |> Ash.Changeset.for_update(:update_progress, %{
      participation_data: updated_data
    })
    |> Ash.update()
  end

  defp get_active_scratch_card_activity do
    case ScratchCardActivity.list_active_activities() do
      {:ok, [activity | _]} ->
        {:ok, activity}

      {:ok, []} ->
        {:error, :no_active_activity}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_or_create_participation(user_id, activity_id) do
    try do
      case UserActivityParticipation.get_user_progress(%{
             user_id: user_id,
             activity_type: :scratch_card,
             activity_id: activity_id
           }) do
        {:ok, participation} when not is_nil(participation) ->
          {:ok, participation}

        {:ok, nil} ->
          # 创建新的参与记录
          create_new_participation(user_id, activity_id)

        {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Query.NotFound{}]}} ->
          # 没有找到记录，创建新的参与记录
          create_new_participation(user_id, activity_id)

        {:error, reason} ->
          {:error, reason}
      end
    rescue
      Ash.Error.Query.NotFound ->
        # 没有找到记录，创建新的参与记录
        create_new_participation(user_id, activity_id)
    end
  end

  defp create_new_participation(user_id, activity_id) do
    UserActivityParticipation.create(%{
      user_id: user_id,
      activity_type: :scratch_card,
      activity_id: activity_id,
      # 默认等级1
      progress: 1,
      status: :active,
      participation_data: %{
        "total_recharge" => "0",
        "current_level" => 1,
        "current_card" => 0,
        "claimed_cards" => []
      }
    })
  end

  defp ensure_user_vip_record(user_id) do
    case UserVipRecord.get_by_user_id(user_id) do
      {:ok, record} ->
        {:ok, record}

      {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Query.NotFound{}]}} ->
        # 创建默认VIP记录
        Logger.info("用户 #{user_id} 没有VIP记录，创建默认记录")

        UserVipRecord.create(%{
          user_id: user_id,
          current_vip_level: 0,
          vip_experience: Decimal.new(0),
          total_recharge_amount: Decimal.new(0)
        })

      {:error, reason} ->
        Logger.error("获取/创建VIP记录失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp get_user_vip_record(user_id) do
    case UserVipRecord.get_by_user_id(user_id) do
      {:ok, record} ->
        {:ok, record}

      {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Query.NotFound{}]}} ->
        # 创建默认VIP记录
        Logger.info("用户 #{user_id} 没有VIP记录，创建默认记录")

        UserVipRecord.create(%{
          user_id: user_id,
          current_vip_level: 0,
          vip_experience: Decimal.new(0),
          total_recharge_amount: Decimal.new(0)
        })

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_activity_levels(activity_id) do
    ScratchCardTaskLevel.list_by_activity(%{activity_id: activity_id})
  end

  defp get_task_level(activity_id, level) do
    case ScratchCardTaskLevel.list_by_activity(%{activity_id: activity_id}) do
      {:ok, levels} ->
        case Enum.find(levels, fn l -> l.task_level == level end) do
          nil -> {:error, :level_not_found}
          task_level -> {:ok, task_level}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp build_user_data(participation, user_recharge, current_level, claimed_records) do
    # 检查是否有待领取的奖励
    pending_rewards = Map.get(participation.participation_data || %{}, "pending_rewards")
    has_pending = not is_nil(pending_rewards)

    # 构建基础数据
    user_data = %{
      "currentlevel" => current_level,
      "maxcards" => 30,
      "currentcard" => length(claimed_records),
      "limitcharge" => get_level_limit_charge(current_level),
      "currentcharge" => Decimal.to_integer(user_recharge),
      "isfetch" => if(length(claimed_records) > 0, do: 1, else: 0),
      # 剩余时间，暂时设为0
      "second" => 0,
      # 活动状态始终为1（活动激活），因为能获取到活动信息说明活动是激活的
      "status" => 1,
      "lastfetchstatus" => if(has_pending, do: 1, else: 0)
    }

    # 如果有待领取的奖励，添加奖励信息以支持断线重连
    if has_pending && is_list(pending_rewards["rewards"]) do
      rewards = pending_rewards["rewards"]

      # 添加每张卡片的奖励信息
      user_data
      |> maybe_add_card_reward(rewards, 0, "one")
      |> maybe_add_card_reward(rewards, 1, "two")
      |> maybe_add_card_reward(rewards, 2, "three")
    else
      user_data
    end
  end

  defp maybe_add_card_reward(user_data, rewards, index, prefix) do
    case Enum.at(rewards, index) do
      %{"award" => award, "awardtype" => awardtype, "index" => idx} ->
        user_data
        |> Map.put("#{prefix}award", Integer.to_string(award))
        |> Map.put("#{prefix}type", awardtype)
        |> Map.put("#{prefix}index", idx)

      _ ->
        user_data
    end
  end

  defp build_level_data(levels) do
    levels
    |> Enum.reduce(%{}, fn level, acc ->
      level_rewards = get_level_rewards(level.id)

      level_data = %{
        "levelnumber" => level.task_level,
        "limitcharge" => Decimal.to_integer(level.recharge_amount),
        "levelaward" => build_level_awards(level_rewards)
      }

      Map.put(acc, Integer.to_string(level.task_level), level_data)
    end)
  end

  defp get_level_rewards(level_id) do
    case ScratchCardLevelReward.read() do
      {:ok, rewards} ->
        Enum.filter(rewards, &(&1.task_level_id == level_id))

      {:error, _} ->
        []
    end
  end

  defp build_level_awards(rewards) do
    case rewards do
      [] ->
        # 如果没有奖励配置，返回默认奖励
        [
          %{
            "awardtype" => 0,
            "minaward" => 1000,
            "maxaward" => 5000,
            # 添加 children 字段
            "children" => []
          }
        ]

      _ ->
        Enum.map(rewards, fn reward ->
          %{
            "awardtype" => if(reward.reward_type == :coins, do: 0, else: 1),
            "minaward" => Decimal.to_integer(reward.min_reward),
            "maxaward" => Decimal.to_integer(reward.max_reward),
            # 添加 children 字段
            "children" => []
          }
        end)
    end
  end

  defp validate_claim_params(card_index, level) do
    cond do
      level < 1 or level > 3 ->
        {:error, :invalid_level}

      card_index < 0 or card_index >= 30 ->
        {:error, :invalid_card_index}

      true ->
        :ok
    end
  end

  defp validate_claim_eligibility(participation, card_index, level) do
    data = participation.participation_data || %{}
    current_level = Map.get(data, "current_level", 1)
    claimed_cards = Map.get(data, "claimed_cards", [])

    cond do
      current_level < level ->
        {:error, :insufficient_recharge}

      card_already_claimed?(claimed_cards, card_index) ->
        {:error, :already_claimed}

      true ->
        :ok
    end
  end

  defp card_already_claimed?(claimed_cards, card_index) do
    Enum.any?(claimed_cards, fn claim ->
      claim["card_index"] == card_index
    end)
  end

  defp calculate_reward(task_level_id) do
    case ScratchCardLevelReward.get_random_reward(%{task_level_id: task_level_id}) do
      {:ok, [reward | _]} ->
        # 在最小和实际最大奖励之间随机生成
        min_amount = Decimal.to_integer(reward.min_reward)
        max_amount = Decimal.to_integer(reward.actual_max_reward)
        amount = :rand.uniform(max_amount - min_amount + 1) + min_amount - 1

        reward_type = if(reward.reward_type == :coins, do: 0, else: 1)
        {:ok, reward_type, amount}

      {:ok, []} ->
        # 如果没有配置奖励，使用默认值
        {:ok, 0, 1000}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp record_claim(user_id, activity_id, card_index, level, reward_type, reward_amount) do
    # 确定奖励类型
    reward_type_atom =
      case reward_type do
        0 -> :points
        1 -> :coins
        _ -> :points
      end

    RewardClaimRecord.create(%{
      user_id: user_id,
      activity_type: :scratch_card,
      activity_id: activity_id,
      reward_type: reward_type_atom,
      reward_amount: Decimal.new(reward_amount),
      reward_data: %{
        card_index: card_index,
        level: level,
        original_reward_type: reward_type
      }
    })
  end

  defp update_participation_progress(participation, card_index) do
    data = participation.participation_data || %{}
    claimed_cards = Map.get(data, "claimed_cards", [])

    new_claim = %{
      "card_index" => card_index,
      "claimed_at" => DateTime.utc_now() |> DateTime.to_iso8601()
    }

    updated_data =
      Map.merge(data, %{
        "claimed_cards" => [new_claim | claimed_cards],
        "current_card" => card_index + 1
      })

    participation
    |> Ash.Changeset.for_update(:update_progress, %{
      participation_data: updated_data
    })
    |> Ash.update()
  end

  defp distribute_reward(user_id, reward_type, reward_amount) do
    case reward_type do
      # 积分
      0 ->
        Cypridina.Ledger.game_win(0, user_id, Decimal.new(reward_amount),
          description: "30次刮卡活动奖励",
          metadata: %{
            activity_type: "scratch_card",
            reward_type: "points"
          }
        )

      # 金币 (暂时也用积分系统)
      1 ->
        Cypridina.Ledger.game_win(0, user_id, Decimal.new(reward_amount),
          description: "30次刮卡活动金币奖励",
          metadata: %{
            activity_type: "scratch_card",
            reward_type: "coins"
          }
        )

      _ ->
        {:error, :invalid_reward_type}
    end
  end

  defp calculate_level_by_recharge(recharge_amount) do
    cond do
      # >= 500元
      Decimal.compare(recharge_amount, Decimal.new("50000")) != :lt -> 3
      # >= 200元
      Decimal.compare(recharge_amount, Decimal.new("20000")) != :lt -> 2
      true -> 1
    end
  end

  defp build_error_response(code, msg, card_index, level) do
    %{
      "code" => code,
      "msg" => msg,
      "cardindex" => card_index,
      "level" => level,
      "awardtype" => 0,
      "awardnum" => 0
    }
  end

  defp get_user_cumulative_recharge_amount(user_id) do
    # 从VIP记录中获取用户累计充值金额
    case UserVipRecord.get_by_user_id(user_id) do
      {:ok, vip_record} ->
        vip_record.total_recharge_amount || Decimal.new(0)

      {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Query.NotFound{}]}} ->
        Logger.debug("用户 #{user_id} 没有VIP记录，返回默认充值金额0")
        Decimal.new(0)

      {:error, _} ->
        Decimal.new(0)
    end
  end

  defp calculate_user_current_level(user_recharge, levels) do
    levels
    |> Enum.reverse()
    |> Enum.find(fn level ->
      Decimal.compare(user_recharge, level.recharge_amount) != :lt
    end)
    |> case do
      nil -> 1
      level -> level.task_level
    end
  end

  defp get_user_claimed_records(user_id, activity_id) do
    case RewardClaimRecord.read() do
      {:ok, records} ->
        Enum.filter(records, &(&1.user_id == user_id and &1.activity_id == activity_id))

      {:error, _} ->
        []
    end
  end

  defp get_level_limit_charge(level) do
    case level do
      # 100元
      1 -> 10000
      # 200元
      2 -> 20000
      # 500元
      3 -> 50000
      _ -> 10000
    end
  end
end
