defmodule Teen.ActivitySystem.TaskProgressService do
  @moduledoc """
  任务进度更新服务

  专门处理各种任务进度的更新逻辑，包括：
  - 游戏任务进度更新
  - 充值任务进度更新
  - 登录任务进度更新
  - VIP任务进度更新
  - 其他活动任务进度更新
  """

  require Logger

  alias Teen.ActivitySystem.{
    UserActivityParticipation,
    GameTask,
    RechargeTask,
    SevenDayTask,
    WeeklyCard,
    VipGift,
    RewardClaimRecord
  }

  alias Teen.Events.EventPublisher

  @doc """
  更新七日登录任务进度
  """
  def update_seven_day_login(user_id) do
    Logger.debug("🎯 [TASK_PROGRESS] 更新七日登录任务 - 用户: #{user_id}")

    with {:ok, active_tasks} <- SevenDayTask.list_active_tasks(),
         {:ok, participation} <- get_or_create_participation(user_id, :seven_day_task) do
      # 检查是否是连续登录
      today = Date.utc_today()
      last_login = get_last_login_date(participation)

      if is_consecutive_login?(last_login, today) do
        new_progress = rem(participation.progress + 1, 7)
        new_progress = if new_progress == 0, do: 7, else: new_progress

        {:ok, updated} = increment_progress(participation, 1, %{last_login_date: today})

        # 检查是否完成了某一天的任务
        check_seven_day_completion(user_id, new_progress, active_tasks)

        {:ok, updated}
      else
        # 重置进度
        {:ok, updated} = reset_progress(participation, 1, %{last_login_date: today})
        {:ok, updated}
      end
    end
  end

  @doc """
  更新充值任务进度
  """
  def update_recharge_tasks(user_id, amount) do
    # 处理amount参数，可能是整数或包含amount的map
    actual_amount =
      case amount do
        %{amount: amt} -> amt
        amt when is_integer(amt) -> amt
        amt -> amt
      end

    Logger.debug("🎯 [TASK_PROGRESS] 更新充值任务 - 用户: #{user_id}, 金额: #{inspect(actual_amount)}")

    with {:ok, active_tasks} <- RechargeTask.list_active_tasks() do
      Enum.each(active_tasks, fn task ->
        if actual_amount >= task.recharge_amount do
          case get_or_create_participation(user_id, :recharge_task, task.id) do
            {:ok, participation} ->
              {:ok, updated} = increment_progress(participation, 1)
              check_task_completion(updated, task)

            {:error, reason} ->
              Logger.error("🎯 [TASK_PROGRESS] 充值任务进度更新失败: #{inspect(reason)}")
          end
        end
      end)
    end
  end

  @doc """
  更新游戏局数任务进度
  """
  def update_game_count_tasks(user_id, game_id) do
    Logger.debug("🎯 [TASK_PROGRESS] 更新游戏局数任务 - 用户: #{user_id}, 游戏: #{game_id}")

    with {:ok, game_tasks} <- GameTask.list_by_game(%{game_id: game_id}) do
      Enum.each(game_tasks, fn task ->
        if task.task_type == :game_count do
          case get_or_create_participation(user_id, :game_task, task.id) do
            {:ok, participation} ->
              {:ok, updated} = increment_progress(participation, 1)
              check_task_completion(updated, task)

            {:error, reason} ->
              Logger.error("🎯 [TASK_PROGRESS] 游戏任务进度更新失败: #{inspect(reason)}")
          end
        end
      end)
    end
  end

  @doc """
  更新游戏胜局任务进度
  """
  def update_game_win_tasks(user_id, game_id) do
    Logger.debug("🎯 [TASK_PROGRESS] 更新游戏胜局任务 - 用户: #{user_id}, 游戏: #{game_id}")

    with {:ok, game_tasks} <- GameTask.list_by_game(%{game_id: game_id}) do
      Enum.each(game_tasks, fn task ->
        if task.task_type == :game_win do
          case get_or_create_participation(user_id, :game_task, task.id) do
            {:ok, participation} ->
              {:ok, updated} = increment_progress(participation, 1)
              check_task_completion(updated, task)

            {:error, reason} ->
              Logger.error("🎯 [TASK_PROGRESS] 游戏胜局任务进度更新失败: #{inspect(reason)}")
          end
        end
      end)
    end
  end

  @doc """
  更新VIP相关任务进度
  """
  def update_vip_tasks(user_id, vip_level) do
    Logger.debug("🎯 [TASK_PROGRESS] 更新VIP任务 - 用户: #{user_id}, VIP等级: #{vip_level}")

    with {:ok, vip_gifts} <- VipGift.list_by_level(vip_level: vip_level) do
      Enum.each(vip_gifts, fn gift ->
        case get_or_create_participation(user_id, :vip_gift, gift.id) do
          {:ok, participation} ->
            if participation.progress == 0 do
              {:ok, updated} = increment_progress(participation, 1)
              check_task_completion(updated, gift)
            end

          {:error, reason} ->
            Logger.error("🎯 [TASK_PROGRESS] VIP任务进度更新失败: #{inspect(reason)}")
        end
      end)
    end
  end

  @doc """
  更新绑定任务进度
  """
  def update_binding_tasks(user_id, binding_type) do
    Logger.debug("🎯 [TASK_PROGRESS] 更新绑定任务 - 用户: #{user_id}, 类型: #{binding_type}")

    # 这里可以根据绑定类型更新相应的任务进度
    # 例如手机绑定、邮箱绑定等
    case get_or_create_participation(user_id, :binding_reward) do
      {:ok, participation} ->
        {:ok, updated} = increment_progress(participation, 1)
        check_binding_completion(updated, binding_type)

      {:error, reason} ->
        Logger.error("🎯 [TASK_PROGRESS] 绑定任务进度更新失败: #{inspect(reason)}")
    end
  end

  # ==================== 私有辅助函数 ====================

  defp get_or_create_participation(user_id, activity_type, activity_id \\ nil) do
    case UserActivityParticipation.get_user_progress(
           user_id,
           activity_type,
           activity_id
         ) do
      {:ok, participation} ->
        {:ok, participation}

      {:error, %Ash.Error.Query.NotFound{}} ->
        UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: activity_type,
          activity_id: activity_id,
          progress: 0,
          status: :active,
          participation_data: %{}
        })

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp increment_progress(participation, increment, extra_data \\ %{}) do
    new_progress = participation.progress + increment
    new_data = Map.merge(participation.participation_data || %{}, extra_data)

    UserActivityParticipation.update_progress(participation, %{
      progress: new_progress,
      participation_data: new_data
    })
  end

  defp reset_progress(participation, new_progress, extra_data \\ %{}) do
    new_data = Map.merge(participation.participation_data || %{}, extra_data)

    UserActivityParticipation.update_progress(participation, %{
      progress: new_progress,
      participation_data: new_data
    })
  end

  defp check_task_completion(participation, task) do
    required_count = task.required_count || 1

    if participation.progress >= required_count do
      Logger.info("🎯 [TASK_PROGRESS] 任务完成 - 用户: #{participation.user_id}, 任务: #{task.id}")

      # 发布任务完成事件
      EventPublisher.publish_task_completed(
        participation.user_id,
        task.id,
        participation.activity_type,
        reward_amount: task.reward_amount
      )

      # 这里可以触发奖励发放
      trigger_reward_distribution(participation, task)
    end
  end

  defp check_seven_day_completion(user_id, day_number, active_tasks) do
    # 查找对应天数的任务
    matching_task = Enum.find(active_tasks, fn task -> task.day_number == day_number end)

    if matching_task do
      Logger.info("🎯 [TASK_PROGRESS] 七日登录第#{day_number}天完成 - 用户: #{user_id}")

      EventPublisher.publish_task_completed(
        user_id,
        matching_task.id,
        :seven_day_task,
        reward_amount: matching_task.reward_amount
      )
    end
  end

  defp check_binding_completion(participation, binding_type) do
    Logger.info("🎯 [TASK_PROGRESS] 绑定任务完成 - 用户: #{participation.user_id}, 类型: #{binding_type}")

    EventPublisher.publish_task_completed(
      participation.user_id,
      participation.activity_id,
      :binding_reward,
      reward_amount: get_binding_reward_amount(binding_type)
    )
  end

  defp trigger_reward_distribution(participation, task) do
    # 这里可以集成奖励发放逻辑
    # 例如调用钱包系统增加用户余额
    Logger.info(
      "🎯 [TASK_PROGRESS] 触发奖励发放 - 用户: #{participation.user_id}, 奖励: #{task.reward_amount}"
    )
  end

  defp get_last_login_date(participation) do
    case participation.participation_data do
      %{"last_login_date" => date_string} when is_binary(date_string) ->
        case Date.from_iso8601(date_string) do
          {:ok, date} -> date
          _ -> nil
        end

      %{last_login_date: %Date{} = date} ->
        date

      _ ->
        nil
    end
  end

  defp is_consecutive_login?(nil, _today), do: true

  defp is_consecutive_login?(last_login, today) do
    Date.diff(today, last_login) == 1
  end

  defp get_binding_reward_amount(:phone), do: 100
  defp get_binding_reward_amount(:email), do: 50
  defp get_binding_reward_amount(_), do: 0
end
