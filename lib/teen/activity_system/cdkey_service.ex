defmodule Teen.ActivitySystem.CdkeyService do
  @moduledoc """
  兑换码服务

  处理兑换码的生成、验证和兑换
  """

  alias Teen.ActivitySystem.{Cdkey, CdkeyClaimRecord, RewardDistributionService}
  alias Cypridina.Accounts.User
  alias Phoenix.PubSub

  require Logger

  # 兑换码字符集（去除容易混淆的字符）
  @charset "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"
  @code_length 12

  @doc """
  生成兑换码
  """
  def generate_codes(batch_name, count, reward_config) do
    codes =
      for _ <- 1..count do
        generate_single_code()
      end

    # 创建兑换码记录
    Enum.map(codes, fn code ->
      case Cdkey.create(%{
             code: code,
             batch_name: batch_name,
             reward_type: reward_config.type || :coins,
             reward_amount: reward_config.amount || 0,
             reward_items: reward_config.items || %{},
             max_uses: reward_config.max_uses || 1,
             used_count: 0,
             valid_from: reward_config.valid_from || DateTime.utc_now(),
             valid_to:
               reward_config.valid_to ||
                 DateTime.add(DateTime.utc_now(), 30 * 24 * 60 * 60, :second),
             status: :active
           }) do
        {:ok, cdkey} ->
          Logger.info("生成兑换码: #{code}")
          {:ok, cdkey}

        {:error, reason} ->
          Logger.error("生成兑换码失败: #{inspect(reason)}")
          {:error, reason}
      end
    end)
  end

  @doc """
  使用兑换码
  """
  def redeem_code(user_id, code) do
    with {:ok, cdkey} <- validate_code(code),
         :ok <- validate_user_eligibility(user_id, cdkey),
         {:ok, rewards} <- parse_cdkey_rewards(cdkey),
         {:ok, _} <- mark_code_used(cdkey, user_id),
         {:ok, distribution_result} <- distribute_cdkey_rewards(user_id, cdkey, rewards),
         {:ok, _} <- create_redeem_record(user_id, cdkey, distribution_result) do
      Logger.info("兑换码使用成功: 用户=#{user_id}, 码=#{code}")

      # 发布兑换事件
      PubSub.broadcast(Cypridina.PubSub, "cdkey_events", {
        :cdkey_redeemed,
        %{
          user_id: user_id,
          code: code,
          rewards: distribution_result.distributed_rewards,
          total_amount: distribution_result.total_amount,
          timestamp: DateTime.utc_now()
        }
      })

      {:ok,
       %{
         code: code,
         rewards: distribution_result.distributed_rewards,
         total_amount: distribution_result.total_amount,
         message: "兑换成功"
       }}
    else
      {:error, reason} ->
        Logger.error("兑换码使用失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  验证兑换码
  """
  def validate_code(code) do
    code = String.upcase(String.trim(code))

    case Cdkey.by_code(code) do
      {:ok, [cdkey | _]} ->
        validate_cdkey_status(cdkey)

      {:ok, []} ->
        {:error, :invalid_code}

      {:error, _} ->
        {:error, :system_error}
    end
  end

  @doc """
  批量生成兑换码（带前缀）
  """
  def generate_batch_with_prefix(prefix, count, reward_config) do
    batch_name = "#{prefix}_#{DateTime.to_unix(DateTime.utc_now())}"

    codes =
      for i <- 1..count do
        "#{prefix}#{generate_suffix(8)}"
      end
      |> Enum.uniq()
      |> Enum.take(count)

    # 创建兑换码记录
    results =
      Enum.map(codes, fn code ->
        case Cdkey.create(%{
               code: code,
               batch_name: batch_name,
               reward_type: reward_config.type || :coins,
               reward_amount: reward_config.amount || 0,
               reward_items: reward_config.items || %{},
               max_uses: reward_config.max_uses || 1,
               used_count: 0,
               valid_from: reward_config.valid_from || DateTime.utc_now(),
               valid_to:
                 reward_config.valid_to ||
                   DateTime.add(DateTime.utc_now(), 30 * 24 * 60 * 60, :second),
               status: :active
             }) do
          {:ok, cdkey} ->
            {:ok, cdkey}

          {:error, reason} ->
            {:error, reason}
        end
      end)

    successful = Enum.filter(results, fn {status, _} -> status == :ok end)

    Logger.info("批量生成兑换码: 批次=#{batch_name}, 成功=#{length(successful)}/#{count}")

    {:ok,
     %{
       batch_name: batch_name,
       total: count,
       successful: length(successful),
       codes: Enum.map(successful, fn {:ok, cdkey} -> cdkey.code end)
     }}
  end

  @doc """
  查询兑换码状态
  """
  def get_code_status(code) do
    case validate_code(code) do
      {:ok, cdkey} ->
        {:ok,
         %{
           code: cdkey.code,
           status: cdkey.status,
           used_count: cdkey.used_count,
           max_uses: cdkey.max_uses,
           remaining_uses: cdkey.max_uses - cdkey.used_count,
           valid_from: cdkey.valid_from,
           valid_to: cdkey.valid_to,
           rewards: format_rewards(cdkey)
         }}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取批次统计
  """
  def get_batch_statistics(batch_name) do
    case Cdkey.by_batch(batch_name) do
      {:ok, cdkeys} ->
        total = length(cdkeys)
        used = Enum.count(cdkeys, fn c -> c.used_count > 0 end)

        expired =
          Enum.count(cdkeys, fn c ->
            DateTime.compare(DateTime.utc_now(), c.valid_to) == :gt
          end)

        {:ok,
         %{
           batch_name: batch_name,
           total_codes: total,
           used_codes: used,
           unused_codes: total - used,
           expired_codes: expired,
           usage_rate: if(total > 0, do: Float.round(used / total * 100, 2), else: 0)
         }}

      _ ->
        {:error, :batch_not_found}
    end
  end

  @doc """
  导出兑换码
  """
  def export_codes(batch_name) do
    case Cdkey.by_batch(batch_name) do
      {:ok, cdkeys} ->
        csv_content =
          (["兑换码,状态,使用次数,最大使用次数,有效期开始,有效期结束,奖励类型,奖励数量\n"] ++
             Enum.map(cdkeys, fn c ->
               "#{c.code},#{c.status},#{c.used_count},#{c.max_uses},#{c.valid_from},#{c.valid_to},#{c.reward_type},#{c.reward_amount}\n"
             end))
          |> Enum.join("")

        {:ok, csv_content}

      _ ->
        {:error, :batch_not_found}
    end
  end

  # 私有函数

  defp generate_single_code do
    for _ <- 1..@code_length, into: "" do
      <<Enum.random(String.to_charlist(@charset))>>
    end
    |> format_code()
  end

  defp generate_suffix(length) do
    for _ <- 1..length, into: "" do
      <<Enum.random(String.to_charlist(@charset))>>
    end
  end

  defp format_code(code) do
    # 格式化为 XXXX-XXXX-XXXX
    code
    |> String.graphemes()
    |> Enum.chunk_every(4)
    |> Enum.map(&Enum.join/1)
    |> Enum.join("-")
  end

  defp validate_cdkey_status(cdkey) do
    now = DateTime.utc_now()

    cond do
      cdkey.status != :active ->
        {:error, :code_inactive}

      DateTime.compare(now, cdkey.valid_from) == :lt ->
        {:error, :code_not_yet_valid}

      DateTime.compare(now, cdkey.valid_to) == :gt ->
        {:error, :code_expired}

      cdkey.used_count >= cdkey.max_uses ->
        {:error, :code_used_up}

      true ->
        {:ok, cdkey}
    end
  end

  defp validate_user_eligibility(user_id, cdkey) do
    with {:ok, _user} <- get_user(user_id),
         :ok <- check_user_already_used(user_id, cdkey) do
      :ok
    else
      {:error, reason} -> {:error, reason}
    end
  end

  defp get_user(user_id) do
    case Cypridina.Accounts.User.get_by_id(user_id) do
      {:ok, user} -> {:ok, user}
      {:error, _} -> {:error, :user_not_found}
    end
  end

  defp check_user_already_used(user_id, cdkey) do
    # 检查用户是否已经使用过这个兑换码
    case CdkeyClaimRecord.by_user_and_cdkey(user_id, cdkey.id) do
      {:ok, []} -> :ok
      {:ok, [_record | _]} -> {:error, :already_redeemed}
      # 如果查询失败，允许继续（可能是首次使用）
      {:error, _} -> :ok
    end
  end

  defp mark_code_used(cdkey, user_id) do
    Cdkey.use_code(cdkey)
  end

  # 解析CDKEY奖励配置
  defp parse_cdkey_rewards(cdkey) do
    # 新的数据结构使用 rewards 字段
    rewards = cdkey.rewards || []

    # 转换为 RewardDistributionService 期望的格式
    reward_config =
      rewards
      |> Enum.reduce(%{}, fn reward, acc ->
        case reward.type do
          :coins -> Map.put(acc, "coins", reward.amount)
          :cash -> Map.put(acc, "cash", reward.amount)
          :points -> Map.put(acc, "points", reward.amount)
          # items handling removed as metadata not available
          :items -> acc
          _ -> acc
        end
      end)

    RewardDistributionService.parse_reward_config(reward_config)
  end

  # 使用奖励发放服务分发奖励
  defp distribute_cdkey_rewards(user_id, cdkey, rewards) do
    source = %{
      source_type: :cdkey,
      source_id: cdkey.id,
      activity_name: "CDKEY兑换",
      metadata: %{
        "cdkey_code" => cdkey.code,
        "creator" => cdkey.creator,
        "purpose" => "cdkey_redemption"
      }
    }

    RewardDistributionService.distribute_rewards(user_id, rewards, source)
  end

  defp create_redeem_record(user_id, cdkey, distribution_result) do
    # 获取用户名
    username =
      case Cypridina.Accounts.User.read(%{id: user_id}) do
        {:ok, user} -> user.username || "未知用户"
        _ -> "未知用户"
      end

    # 计算总金币数量
    claimed_coins =
      distribution_result.distributed_rewards
      |> Enum.reduce(Decimal.new(0), fn reward, acc ->
        case reward.type do
          :coins -> Decimal.add(acc, Decimal.new(reward.amount))
          _ -> acc
        end
      end)

    CdkeyClaimRecord.create(%{
      cdkey_id: cdkey.id,
      user_id: user_id,
      username: username,
      cdkey_code: cdkey.code,
      claimed_coins: claimed_coins
    })
  end

  defp format_rewards(cdkey) do
    # 使用新的奖励解析和格式化
    case parse_cdkey_rewards(cdkey) do
      {:ok, rewards} -> rewards
      _ -> []
    end
  end

  @doc """
  使兑换码失效
  """
  def deactivate_code(code) do
    case validate_code(code) do
      {:ok, cdkey} ->
        Cdkey.update(cdkey, %{status: :inactive})

      error ->
        error
    end
  end

  @doc """
  批量使兑换码失效
  """
  def deactivate_batch(batch_name) do
    case Cdkey.by_batch(batch_name) do
      {:ok, cdkeys} ->
        results =
          Enum.map(cdkeys, fn cdkey ->
            Cdkey.update(cdkey, %{status: :inactive})
          end)

        successful = Enum.count(results, fn {status, _} -> status == :ok end)
        {:ok, %{total: length(cdkeys), successful: successful}}

      _ ->
        {:error, :batch_not_found}
    end
  end
end
