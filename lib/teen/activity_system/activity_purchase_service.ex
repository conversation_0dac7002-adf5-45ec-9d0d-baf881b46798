defmodule Teen.ActivitySystem.ActivityPurchaseService do
  @moduledoc """
  活动商品购买服务

  处理活动相关的商品购买，包括：
  - 周卡/月卡购买
  - 礼包购买
  - 活动商品验证
  - 库存检查
  - 支付处理
  """

  alias Teen.ActivitySystem.{
    WeeklyCard,
    FirstRechargeGift,
    VipGift,
    UserActivityParticipation,
    ActivityService
  }

  alias Cypridina.Accounts.User
  alias Teen.ProductSystem.{Product, UserPurchase}

  require Logger

  @doc """
  购买活动商品
  """
  def purchase_activity_product(user_id, product_type, product_id, payment_method \\ :points) do
    with {:ok, product} <- validate_product(product_type, product_id),
         {:ok, _} <-
           check_purchase_eligibility(user_id, product_type, product_id, payment_method),
         {:ok, _} <- check_inventory(product_type, product_id),
         {:ok, _} <- process_payment(user_id, product, payment_method),
         {:ok, participation} <- activate_product(user_id, product_type, product_id, product) do
      Logger.info("活动商品购买成功: 用户=#{user_id}, 类型=#{product_type}, ID=#{product_id}")

      {:ok,
       %{
         product: product,
         participation: participation,
         message: "购买成功"
       }}
    else
      {:error, reason} ->
        Logger.error("活动商品购买失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  检查用户是否可以购买指定活动商品
  """
  def can_purchase?(user_id, product_type, product_id) do
    case check_purchase_eligibility(user_id, product_type, product_id, :points) do
      {:ok, _} -> true
      _ -> false
    end
  end

  @doc """
  获取活动商品信息
  """
  def get_product_info(product_type, product_id) do
    validate_product(product_type, product_id)
  end

  # 私有函数

  defp validate_product(:weekly_card, product_id) do
    case WeeklyCard.read(product_id) do
      {:ok, card} ->
        {:ok,
         %{
           id: card.id,
           name: card.card_type,
           price: card.price,
           duration: card.duration_days,
           daily_reward: card.daily_reward,
           immediate_reward: card.immediate_reward,
           total_value: card.total_value
         }}

      {:error, _} ->
        {:error, :product_not_found}
    end
  end

  defp validate_product(:first_recharge_gift, product_id) do
    case FirstRechargeGift.read(product_id) do
      {:ok, gift} ->
        {:ok,
         %{
           id: gift.id,
           name: gift.title,
           # 首充礼包免费
           price: Decimal.new("0"),
           reward_coins: gift.reward_coins,
           limit_days: gift.limit_days
         }}

      {:error, _} ->
        {:error, :product_not_found}
    end
  end

  defp validate_product(:vip_gift, product_id) do
    case VipGift.read(product_id) do
      {:ok, gift} ->
        {:ok,
         %{
           id: gift.id,
           name: "VIP#{gift.required_vip_level}礼包",
           price: gift.price,
           reward_coins: gift.reward_coins,
           reward_items: gift.reward_items,
           required_vip_level: gift.required_vip_level
         }}

      {:error, _} ->
        {:error, :product_not_found}
    end
  end

  defp validate_product(_, _) do
    {:error, :unsupported_product_type}
  end

  defp check_purchase_eligibility(user_id, :weekly_card, product_id, _payment_method) do
    # 检查是否已经拥有有效的周卡/月卡
    case ActivityService.get_user_participation(user_id, :weekly_card, product_id) do
      {:ok, participation} when not is_nil(participation) ->
        if participation.status == :active do
          {:error, :already_active}
        else
          {:ok, :can_purchase}
        end

      _ ->
        {:ok, :can_purchase}
    end
  end

  defp check_purchase_eligibility(user_id, :first_recharge_gift, product_id, payment_method) do
    # 检查是否已经购买过首充礼包
    case ActivityService.get_user_participation(user_id, :first_recharge_gift, product_id) do
      {:ok, participation} when not is_nil(participation) ->
        # 如果是免费发放（首充奖励），允许重复发放
        if payment_method == :free do
          {:ok, :can_purchase}
        else
          {:error, :already_purchased}
        end

      _ ->
        # 检查是否在注册后的有效期内
        case User.get_by_id(user_id) do
          {:ok, user} ->
            case FirstRechargeGift.read(product_id) do
              {:ok, gift} ->
                days_since_register = DateTime.diff(DateTime.utc_now(), user.inserted_at, :day)

                if days_since_register <= gift.limit_days do
                  {:ok, :can_purchase}
                else
                  {:error, :expired}
                end

              _ ->
                {:error, :product_not_found}
            end

          _ ->
            {:error, :user_not_found}
        end
    end
  end

  defp check_purchase_eligibility(user_id, :vip_gift, product_id, _payment_method) do
    # 检查VIP等级要求
    case VipGift.read(product_id) do
      {:ok, gift} ->
        # 获取用户VIP等级
        user_vip_level =
          case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
            {:ok, info} -> info.vip_level
            _ -> 0
          end

        if user_vip_level >= gift.required_vip_level do
          # 检查是否已经购买过
          case ActivityService.get_user_participation(user_id, :vip_gift, product_id) do
            {:ok, participation} when not is_nil(participation) ->
              {:error, :already_purchased}

            _ ->
              {:ok, :can_purchase}
          end
        else
          {:error, :vip_level_insufficient}
        end

      _ ->
        {:error, :product_not_found}
    end
  end

  defp check_purchase_eligibility(_, _, _, _) do
    {:error, :unsupported_product_type}
  end

  defp check_inventory(:weekly_card, _product_id) do
    # 周卡/月卡无库存限制
    {:ok, :unlimited}
  end

  defp check_inventory(:first_recharge_gift, product_id) do
    case FirstRechargeGift.read(product_id) do
      {:ok, gift} ->
        if gift.max_purchases > 0 do
          # TODO: 检查已购买数量
          {:ok, :available}
        else
          {:ok, :unlimited}
        end

      _ ->
        {:error, :product_not_found}
    end
  end

  defp check_inventory(:vip_gift, _product_id) do
    # VIP礼包每人限购一次
    {:ok, :available}
  end

  defp check_inventory(_, _) do
    {:error, :unsupported_product_type}
  end

  defp process_payment(user_id, product, :points) do
    # 使用积分支付
    price = Decimal.to_integer(product.price)

    case Cypridina.Accounts.get_user_points(user_id) do
      {:ok, points} when points >= price ->
        case Cypridina.Accounts.deduct_points(user_id, price, "activity_purchase") do
          {:ok, _} ->
            {:ok, :payment_success}

          {:error, reason} ->
            {:error, {:payment_failed, reason}}
        end

      {:ok, _} ->
        {:error, :insufficient_points}

      {:error, reason} ->
        {:error, {:payment_error, reason}}
    end
  end

  defp process_payment(user_id, product, :cash) do
    # 现金支付需要创建支付订单
    # TODO: 集成支付系统
    {:error, :cash_payment_not_implemented}
  end

  defp process_payment(_user_id, _product, :free) do
    # 免费发放，无需支付
    {:ok, :payment_success}
  end

  defp process_payment(_, _, _) do
    {:error, :unsupported_payment_method}
  end

  defp activate_product(user_id, :weekly_card, product_id, product) do
    # 激活周卡/月卡
    start_time = DateTime.utc_now()
    end_time = DateTime.add(start_time, product.duration * 24 * 60 * 60, :second)

    participation_data = %{
      card_type: product.name,
      start_time: start_time,
      end_time: end_time,
      last_claim_time: nil,
      claimed_days: 0,
      total_claimed: 0
    }

    case UserActivityParticipation.create(%{
           user_id: user_id,
           activity_type: :weekly_card,
           activity_id: product_id,
           progress: 0,
           status: :active,
           participation_data: participation_data
         }) do
      {:ok, participation} ->
        # 发放立即奖励
        if product.immediate_reward > 0 do
          Cypridina.Accounts.add_points(
            user_id,
            Decimal.to_integer(product.immediate_reward),
            transaction_type: :bonus,
            description: "weekly_card_immediate"
          )
        end

        {:ok, participation}

      {:error, reason} ->
        {:error, {:activation_failed, reason}}
    end
  end

  defp activate_product(user_id, :first_recharge_gift, product_id, product) do
    # 激活首充礼包
    participation_data = %{
      purchase_time: DateTime.utc_now(),
      reward_coins: product.reward_coins,
      reward_items: product.reward_items
    }

    case UserActivityParticipation.create(%{
           user_id: user_id,
           activity_type: :first_recharge_gift,
           activity_id: product_id,
           progress: 1,
           status: :completed,
           participation_data: participation_data
         }) do
      {:ok, participation} ->
        # 立即发放奖励
        if product.reward_coins > 0 do
          Cypridina.Accounts.add_points(
            user_id,
            Decimal.to_integer(product.reward_coins),
            transaction_type: :bonus,
            description: "first_recharge_reward"
          )
        end

        # TODO: 发放道具奖励

        {:ok, participation}

      {:error, reason} ->
        {:error, {:activation_failed, reason}}
    end
  end

  defp activate_product(user_id, :vip_gift, product_id, product) do
    # 激活VIP礼包
    participation_data = %{
      purchase_time: DateTime.utc_now(),
      vip_level: product.required_vip_level,
      reward_coins: product.reward_coins,
      reward_items: product.reward_items
    }

    case UserActivityParticipation.create(%{
           user_id: user_id,
           activity_type: :vip_gift,
           activity_id: product_id,
           progress: 1,
           status: :completed,
           participation_data: participation_data
         }) do
      {:ok, participation} ->
        # 立即发放奖励
        if product.reward_coins > 0 do
          Cypridina.Accounts.add_points(
            user_id,
            Decimal.to_integer(product.reward_coins),
            transaction_type: :bonus,
            description: "vip_gift_reward"
          )
        end

        # TODO: 发放道具奖励

        {:ok, participation}

      {:error, reason} ->
        {:error, {:activation_failed, reason}}
    end
  end

  defp activate_product(_, _, _, _) do
    {:error, :unsupported_product_type}
  end

  @doc """
  领取周卡/月卡每日奖励
  """
  def claim_daily_reward(user_id, card_id) do
    case ActivityService.get_user_participation(user_id, :weekly_card, card_id) do
      {:ok, participation} when not is_nil(participation) and participation.status == :active ->
        participation_data = participation.participation_data || %{}
        last_claim = participation_data["last_claim_time"]

        # 检查是否今天已经领取
        today = Date.utc_today()
        last_claim_date = if last_claim, do: DateTime.to_date(last_claim), else: nil

        if last_claim_date == today do
          {:error, :already_claimed_today}
        else
          # 检查是否在有效期内
          end_time = participation_data["end_time"]

          if DateTime.compare(DateTime.utc_now(), end_time) == :lt do
            # 更新领取记录
            new_data =
              participation_data
              |> Map.put("last_claim_time", DateTime.utc_now())
              |> Map.put("claimed_days", (participation_data["claimed_days"] || 0) + 1)

            case UserActivityParticipation.update_progress(participation, %{
                   participation_data: new_data
                 }) do
              {:ok, _} ->
                # 发放奖励
                case WeeklyCard.read(card_id) do
                  {:ok, card} ->
                    Cypridina.Accounts.add_points(
                      user_id,
                      Decimal.to_integer(card.daily_reward),
                      transaction_type: :bonus,
                      description: "weekly_card_daily"
                    )

                    {:ok,
                     %{
                       reward: card.daily_reward,
                       claimed_days: new_data["claimed_days"]
                     }}

                  _ ->
                    {:error, :card_not_found}
                end

              {:error, reason} ->
                {:error, reason}
            end
          else
            # 卡片已过期，更新状态
            UserActivityParticipation.update_progress(participation, %{
              status: :completed
            })

            {:error, :card_expired}
          end
        end

      {:ok, _} ->
        {:error, :card_not_active}

      {:error, reason} ->
        {:error, reason}
    end
  end
end
