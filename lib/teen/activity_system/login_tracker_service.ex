defmodule Teen.ActivitySystem.LoginTrackerService do
  @moduledoc """
  登录追踪服务

  负责：
  - 记录用户登录
  - 计算连续登录天数
  - 触发登录奖励
  - 统计登录数据
  """

  alias Teen.ActivitySystem.{UserLoginRecord, ActivityService}
  alias Teen.ActivitySystem.UserActivityParticipation
  require Logger

  @doc """
  记录用户登录并触发相关奖励
  """
  def track_user_login(user_id, opts \\ %{}) do
    ip_address = Map.get(opts, :ip_address)
    device_type = Map.get(opts, :device_type)
    client_info = Map.get(opts, :client_info, %{})

    with {:ok, login_record} <-
           create_login_record(user_id, ip_address, device_type, client_info),
         :ok <- update_seven_day_progress_if_needed(user_id, login_record),
         :ok <- trigger_login_rewards_if_needed(user_id, login_record) do
      Logger.info("用户登录追踪成功", %{
        user_id: user_id,
        consecutive_days: login_record.consecutive_days,
        is_first_today: login_record.is_first_today
      })

      {:ok,
       %{
         login_record: login_record,
         consecutive_days: login_record.consecutive_days,
         is_first_today: login_record.is_first_today
       }}
    else
      {:error, reason} = error ->
        Logger.error("用户登录追踪失败", %{
          user_id: user_id,
          reason: inspect(reason)
        })

        error
    end
  end

  @doc """
  获取用户的连续登录天数
  """
  def get_user_consecutive_days(user_id) do
    today = Date.utc_today()

    case UserLoginRecord.get_today_login(%{user_id: user_id, date: today}) do
      {:ok, record} when not is_nil(record) ->
        {:ok, record.consecutive_days}

      _ ->
        # 如果今天还没登录，查看昨天的记录
        yesterday = Date.add(today, -1)

        case UserLoginRecord.get_user_login_history(%{
               user_id: user_id,
               start_date: yesterday,
               end_date: yesterday,
               limit: 1
             }) do
          {:ok, [record | _]} ->
            # 如果昨天有登录，今天登录后会是 consecutive_days + 1
            {:ok, record.consecutive_days}

          _ ->
            # 没有登录记录，返回0
            {:ok, 0}
        end
    end
  end

  @doc """
  获取用户的登录历史
  """
  def get_user_login_history(user_id, days \\ 30) do
    end_date = Date.utc_today()
    start_date = Date.add(end_date, -days)

    case UserLoginRecord.get_user_login_history(%{
           user_id: user_id,
           start_date: start_date,
           end_date: end_date,
           limit: days
         }) do
      {:ok, records} ->
        {:ok, format_login_history(records)}

      error ->
        error
    end
  end

  @doc """
  检查用户今天是否已经登录
  """
  def has_logged_in_today?(user_id) do
    today = Date.utc_today()

    case UserLoginRecord.get_today_login(%{user_id: user_id, date: today}) do
      {:ok, record} when not is_nil(record) -> true
      _ -> false
    end
  end

  @doc """
  获取用户的登录统计信息
  """
  def get_user_login_stats(user_id) do
    with {:ok, consecutive_days} <- get_user_consecutive_days(user_id),
         {:ok, history} <- get_user_login_history(user_id, 30),
         total_days <- calculate_total_login_days(history),
         today_logged_in <- has_logged_in_today?(user_id) do
      {:ok,
       %{
         consecutive_days: consecutive_days,
         total_login_days: total_days,
         today_logged_in: today_logged_in,
         last_30_days: length(history),
         recent_history: Enum.take(history, 7)
       }}
    end
  end

  # 私有函数

  defp create_login_record(user_id, ip_address, device_type, client_info) do
    UserLoginRecord.create(%{
      user_id: user_id,
      ip_address: ip_address,
      device_type: device_type,
      client_info: client_info
    })
  end

  defp update_seven_day_progress_if_needed(user_id, login_record) do
    # 只有今日首次登录才更新七日签到进度
    if login_record.is_first_today do
      case get_or_create_seven_day_participation(user_id) do
        {:ok, participation} ->
          # 更新参与记录的进度
          updated_data =
            participation.participation_data ||
              %{}
              |> Map.put(:last_login, Date.utc_today())
              |> Map.put(:consecutive_days, login_record.consecutive_days)

          case UserActivityParticipation.update(participation, %{
                 # 七日签到最多7天
                 progress: min(login_record.consecutive_days, 7),
                 participation_data: updated_data
               }) do
            {:ok, _} -> :ok
            error -> error
          end

        error ->
          Logger.error("更新七日签到进度失败", %{
            user_id: user_id,
            error: inspect(error)
          })

          # 不影响登录记录
          :ok
      end
    else
      :ok
    end
  end

  defp get_or_create_seven_day_participation(user_id) do
    case UserActivityParticipation.get_user_progress(%{
           user_id: user_id,
           activity_type: :seven_day_task
         }) do
      {:ok, nil} ->
        # 创建新的参与记录
        UserActivityParticipation.create(%{
          user_id: user_id,
          activity_type: :seven_day_task,
          progress: 0,
          status: :active,
          participation_data: %{
            start_date: Date.utc_today(),
            last_login: nil,
            claimed_days: []
          }
        })

      {:ok, participation} ->
        {:ok, participation}

      error ->
        error
    end
  end

  defp trigger_login_rewards_if_needed(user_id, login_record) do
    # 只有今日首次登录才触发奖励
    if login_record.is_first_today do
      # 异步触发登录奖励，不影响登录流程
      Task.start(fn ->
        try do
          # 触发日常登录奖励
          ActivityService.trigger_daily_login_bonus(user_id, login_record.consecutive_days)

          # 检查连续登录奖励
          ActivityService.check_consecutive_login_rewards(user_id, login_record.consecutive_days)
        rescue
          error ->
            Logger.error("触发登录奖励失败", %{
              user_id: user_id,
              error: inspect(error)
            })
        end
      end)
    end

    :ok
  end

  defp format_login_history(records) do
    Enum.map(records, fn record ->
      %{
        date: record.login_date,
        time: record.login_time,
        consecutive_days: record.consecutive_days,
        is_first_today: record.is_first_today
      }
    end)
  end

  defp calculate_total_login_days(history) do
    # 计算唯一的登录日期数
    history
    |> Enum.map(& &1.date)
    |> Enum.uniq()
    |> length()
  end
end
