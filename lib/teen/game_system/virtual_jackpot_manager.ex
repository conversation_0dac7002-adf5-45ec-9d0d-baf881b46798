defmodule Teen.GameSystem.VirtualJackpotManager do
  @moduledoc """
  虚拟中奖管理器

  专门处理三个单人游戏的虚拟中奖：
  - 每个游戏最多保持20条虚拟中奖记录
  - 支持生成符合各游戏协议格式的虚拟中奖
  - 支持实时广播虚拟中奖
  - 与真实记录混合显示
  """

  use GenServer
  require Logger
  alias Teen.RobotManagement.RobotEntity
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Config
  alias Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig
  alias Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatConfig

  # 广播需要调用 RoomBase 模块的函数
  # 注意：广播功能需要完整的Phoenix应用启动

  @max_records_per_game 20

  # ==================== 根据游戏配置计算真实下注金额 ====================

  # 计算 Slot777 下注金额：odds_config * bet_rate_num * difen
  defp calculate_slot777_bet_amounts do
    config = Slot777Config.get_default_config()
    odds_config = config.betting.odds_config
    bet_rate_num = config.betting.bet_rate_num  # 9
    difen = config.betting.difen  # 100

    # 转换 odds_config 映射为下注金额列表
    Enum.map(odds_config, fn {_level, multiplier} ->
      trunc(multiplier * bet_rate_num * difen)
    end)
    |> Enum.sort()
  end

  # 计算 SlotNiu 下注金额：直接使用配置中的 bet_amounts
  defp calculate_slotniu_bet_amounts do
    config = SlotNiuConfig.get_default_config()
    # SlotNiu 配置中已经有计算好的 bet_amounts，直接使用
    bet_amounts = config.betting.bet_amounts

    # 转换为整数（去除小数点，因为系统内部使用整数）
    Enum.map(bet_amounts, fn amount ->
      trunc(amount * 100)  # 1.8 -> 180, 9 -> 900 等
    end)
    |> Enum.sort()
  end

  # 计算 SlotCat 下注金额：bet_multipliers * difen * bet_rate_num
  defp calculate_slotcat_bet_amounts do
    config = SlotCatConfig.get_default_config()
    bet_multipliers = config.game.bet_multipliers
    difen = config.game.difen  # 100
    bet_rate_num = config.game.bet_rate_num  # 9

    # 计算每个倍率对应的下注金额
    Enum.map(bet_multipliers, fn multiplier ->
      trunc(multiplier * difen * bet_rate_num)
    end)
    |> Enum.sort()
  end

  # 获取带有动态计算下注金额的完整游戏配置
  defp get_complete_game_config(game_type) do
    base_config = get_base_game_config(game_type)

    if base_config do
      bet_amounts = case game_type do
        "slot777" -> calculate_slot777_bet_amounts()
        "slotniu" -> calculate_slotniu_bet_amounts()
        "slotcat" -> calculate_slotcat_bet_amounts()
        _ -> []
      end

      Map.put(base_config, :bet_amounts, bet_amounts)
    else
      nil
    end
  end

  # 定时生成配置
  @auto_generation_config %{
    enabled: true,
    # 每个游戏的生成间隔（秒）- 1到30秒之间随机
    intervals: %{
      "slot777" => {1, 40},    # 1-30秒随机间隔
      "slotniu" => {1, 40},    # 1-30秒随机间隔
      "slotcat" => {1, 40}     # 1-30秒随机间隔
    }
  }

  # 获取游戏的静态配置（不包含动态计算的 bet_amounts）
  defp get_base_game_config(game_type) do
    case game_type do
      "slot777" ->
        %{
          game_id: 40,
          sub_id: 1006,
          win_amount_ranges: %{
            small: {10_000, 50_000},      # 小奖 70%
            medium: {50_000, 200_000},    # 中奖 25%
            big: {200_000, 500_000}       # 大奖 5%
          },
          win_type_probability: %{small: 0.7, medium: 0.25, big: 0.05}
        }

      "slotniu" ->
        %{
          game_id: 41,
          sub_id: 1008,
          win_amount_ranges: %{
            small: {15_000, 80_000},
            medium: {80_000, 300_000},
            big: {300_000, 800_000}
          },
          win_type_probability: %{small: 0.7, medium: 0.25, big: 0.05}
        }

      "slotcat" ->
        %{
          game_id: 42,
          sub_id: 1006, # sc_slotcat_jpaward_p()就是1006别改成1005了
          win_amount_ranges: %{
            small: {20_000, 100_000},
            medium: {100_000, 500_000},
            big: {500_000, 1_000_000}
          },
          win_type_probability: %{small: 0.65, medium: 0.3, big: 0.05}
        }

      _ ->
        nil
    end
  end

  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  @impl true
  def init(state) do
    # 初始化状态：存储虚拟记录、定时器引用和显示奖池状态
    initial_state = %{
      virtual_records: %{},
      timers: %{},
      # 新增：显示奖池管理
      display_jackpots: init_display_jackpots()
    }

    # 如果启用自动生成，启动定时器
    if @auto_generation_config.enabled do
      schedule_auto_generation()
    end

    # 启动显示奖池跳动定时器
    schedule_display_jackpot_tick()

    Logger.info("🎰 [VIRTUAL_JACKPOT] 虚拟中奖管理器启动成功")
    {:ok, initial_state}
  end

  @doc """
  添加虚拟中奖记录（自动维护20条限制）
  """
  def add_virtual_win(game_type, win_record) do
    GenServer.call(__MODULE__, {:add_virtual_win, game_type, win_record})
  end

  @doc """
  获取虚拟中奖记录
  """
  def get_virtual_records(game_type, limit \\ @max_records_per_game) do
    GenServer.call(__MODULE__, {:get_virtual_records, game_type, limit})
  end

  # 获取显示奖池状态
  def get_display_jackpot_status do
    GenServer.call(__MODULE__, :get_display_jackpot_status)
  end

  # 获取单个游戏的显示奖池状态
  def get_display_jackpot(game_type) do
    GenServer.call(__MODULE__, {:get_display_jackpot, game_type})
  end

  @doc """
  生成虚拟中奖并广播
  """
  def generate_and_broadcast_virtual_win(game_type) do
    case generate_virtual_win(game_type) do
      {:ok, win_record} ->
        # 添加到记录中
        add_virtual_win(game_type, win_record)

        # 广播虚拟中奖
        broadcast_virtual_win(game_type, win_record)

        {:ok, win_record}

      error -> error
    end
  end

  @doc """
  生成单个虚拟中奖记录（用于测试）
  """
  def generate_virtual_win(game_type) do
    config = get_complete_game_config(game_type)

    if config do
      # 1. 先随机选择下注金额
      bet_amount = Enum.random(config.bet_amounts)

      # 2. 根据下注金额和游戏规则确定可用的奖项类型
      available_win_types = get_available_win_types(game_type, bet_amount)

      # 如果没有可用的奖项类型（下注不足），重新选择更高的下注金额
      {final_bet_amount, final_available_types} =
        if Enum.empty?(available_win_types) do
          # 为slot777和slotcat选择9000以上的下注金额
          eligible_bets = Enum.filter(config.bet_amounts, fn bet -> bet >= 9000 end)
          if Enum.empty?(eligible_bets) do
            # 如果没有9000以上的下注选项，返回错误
            {bet_amount, []}
          else
            higher_bet = Enum.random(eligible_bets)
            {higher_bet, get_available_win_types(game_type, higher_bet)}
          end
        else
          {bet_amount, available_win_types}
        end

      # 如果仍然没有可用奖项，返回错误
      if Enum.empty?(final_available_types) do
        {:error, "游戏 #{game_type} 没有足够高的下注选项来生成中奖"}
      else
        # 3. 从可用的奖项类型中随机选择
        win_type = select_win_type_from_available(final_available_types, config.win_type_probability)

        # 4. 根据奖项类型和奖池金额计算真实的中奖金额
        jackpot_amount = calculate_realistic_jackpot_amount(game_type, win_type, final_bet_amount, config)

        # 5. 生成虚拟玩家信息
        win_record = %{
          id: "virtual_#{System.system_time(:millisecond)}_#{:rand.uniform(9999)}",
          game_type: game_type,
          player_id: "fake_#{:rand.uniform(999999)}",
          player_name: RobotEntity.random_robot_name(),
          avatar_id: RobotEntity.random_robot_avatar(),
          avatar_url: "",
          jackpot_amount: jackpot_amount,
          bet_amount: final_bet_amount,
          jackpot_level: map_win_type_to_level(win_type),
          game_specific_data: generate_game_specific_data(game_type, win_type, final_bet_amount, jackpot_amount),
          win_time: DateTime.utc_now(),
          is_virtual: true
        }

        {:ok, win_record}
      end
    else
      {:error, "不支持的游戏类型: #{game_type}"}
    end
  end

  @doc """
  手动生成指定数量的虚拟中奖记录（用于初始化）
  """
  def generate_initial_virtual_wins(game_type, count \\ 10) do
    results =
      1..count
      |> Enum.map(fn _i ->
        case generate_virtual_win(game_type) do
          {:ok, win_record} ->
            # 为初始记录设置历史时间（1-24小时前）
            hours_ago = :rand.uniform(24)
            minutes_ago = :rand.uniform(60)
            historical_time = DateTime.add(DateTime.utc_now(), -(hours_ago * 3600 + minutes_ago * 60), :second)

            historical_record = %{win_record | win_time: historical_time}
            add_virtual_win(game_type, historical_record)
            {:ok, historical_record}

          error -> error
        end
      end)

    success_count = Enum.count(results, fn {status, _} -> status == :ok end)
    {:ok, success_count}
  end

  # ==================== GenServer 回调函数 ====================

  @impl true
  def handle_call({:add_virtual_win, game_type, win_record}, _from, state) do
    virtual_records = state.virtual_records
    game_records = Map.get(virtual_records, game_type, [])

    # 添加新记录并保持最大20条限制
    updated_records =
      [win_record | game_records]
      |> Enum.take(@max_records_per_game)

    new_virtual_records = Map.put(virtual_records, game_type, updated_records)
    new_state = %{state | virtual_records: new_virtual_records}

    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call({:get_virtual_records, game_type, limit}, _from, state) do
    virtual_records = state.virtual_records
    game_records = Map.get(virtual_records, game_type, [])

    # 按时间倒序返回指定数量的记录
    limited_records =
      game_records
      |> Enum.sort_by(& &1.win_time, {:desc, DateTime})
      |> Enum.take(limit)

    {:reply, limited_records, state}
  end

  @impl true
  def handle_call(:get_display_jackpot_status, _from, state) do
    {:reply, state.display_jackpots, state}
  end

  @impl true
  def handle_call({:get_display_jackpot, game_type}, _from, state) do
    display_jackpot = Map.get(state.display_jackpots, game_type)
    {:reply, display_jackpot, state}
  end

  @impl true
  def handle_info(:auto_generate_virtual_wins, state) do
    # 添加完整的状态结构检查和恢复
    validated_state = validate_and_fix_state_structure(state)

    # 为所有游戏生成虚拟中奖，使用reduce来累积状态变化
    game_types = ["slot777", "slotniu", "slotcat"]

    updated_state = Enum.reduce(game_types, validated_state, fn game_type, acc_state ->
      interval_range = @auto_generation_config.intervals[game_type]
      current_time = System.system_time(:second)

      # 检查是否到了该游戏的生成时间
      if should_generate_for_game?(game_type, current_time, interval_range) do
        case generate_virtual_win(game_type) do
          {:ok, win_record} ->
            # 直接添加到当前状态中（避免调用自己）
            # 状态已通过validate_and_fix_state_structure验证，可以安全访问
            virtual_records = acc_state.virtual_records
            game_records = Map.get(virtual_records, game_type, [])

            # 添加新记录并保持最大20条限制
            updated_records =
              [win_record | game_records]
              |> Enum.take(@max_records_per_game)

            new_virtual_records = Map.put(virtual_records, game_type, updated_records)
            new_acc_state = %{acc_state | virtual_records: new_virtual_records}

            # 广播虚拟中奖
            broadcast_virtual_win(game_type, win_record)

            # 新增：影响显示奖池
            final_acc_state = handle_virtual_win_impact(new_acc_state, game_type, win_record.jackpot_amount)

            final_acc_state

          {:error, reason} ->
            Logger.error("❌ [VIRTUAL_JACKPOT] 自动生成虚拟中奖失败 - 游戏: #{game_type}, 原因: #{inspect(reason)}")
            acc_state
        end
      else
        acc_state
      end
    end)

    # 继续调度下一次自动生成
    schedule_auto_generation()

    {:noreply, updated_state}
  end

  # 处理显示奖池跳动
  @impl true
  def handle_info(:display_jackpot_tick, state) do
    # 添加完整的状态结构检查和恢复
    validated_state = validate_and_fix_state_structure(state)

    # 检查状态恢复是否成功
    if not Map.has_key?(validated_state, :display_jackpots) do
      Logger.error("🎰 [DISPLAY_JACKPOT] 状态恢复失败，跳过此次更新: #{inspect(state)}")
      {:noreply, validated_state}
    else
      # 更新所有游戏的显示奖池
      new_display_jackpots = update_all_display_jackpots(validated_state.display_jackpots)

      # 广播更新到游戏房间
      broadcast_display_jackpots(new_display_jackpots)

      # 安排下次更新
      schedule_display_jackpot_tick()

      new_state = %{validated_state | display_jackpots: new_display_jackpots}
      {:noreply, new_state}
    end
  end

  @impl true
  def handle_info(msg, state) do
    Logger.warning("🎰 [VIRTUAL_JACKPOT] 收到未处理的消息: #{inspect(msg)}")
    {:noreply, state}
  end

  # ==================== 私有函数 ====================

  # 验证并修复状态结构
  defp validate_and_fix_state_structure(state) do
    cond do
      # 如果状态是正确的结构
      is_map(state) and Map.has_key?(state, :virtual_records) and Map.has_key?(state, :display_jackpots) and Map.has_key?(state, :timers) ->
        state

      # 如果状态包含display_jackpots但缺少其他字段（最常见的错误情况）
      is_map(state) and Map.has_key?(state, "slot777") ->
        # 将损坏的状态作为display_jackpots，重建完整状态
        recovered_state = %{
          virtual_records: %{},  # 重置虚拟记录
          timers: %{},          # 重置定时器
          display_jackpots: state  # 保留display_jackpots数据
        }

        recovered_state

      # 其他未知损坏情况，完全重建
      true ->
        Logger.error("🎰 [STATE_RECOVERY] 状态结构严重损坏，执行完全重建")

        %{
          virtual_records: %{},
          timers: %{},
          display_jackpots: init_display_jackpots()
        }
    end
  end

  # 调度自动生成
  defp schedule_auto_generation do
    # 每1秒检查一次是否需要生成虚拟中奖（支持1-30秒随机间隔）
    Process.send_after(__MODULE__, :auto_generate_virtual_wins, 1_000)
  end

  # 检查是否应该为指定游戏生成虚拟中奖
  defp should_generate_for_game?(game_type, current_time, interval_range) do
    # 获取上次生成时间和下次生成时间的键
    last_generation_key = "last_generation_#{game_type}"
    next_generation_key = "next_generation_#{game_type}"

    case :ets.whereis(:virtual_jackpot_timers) do
      :undefined ->
        # 创建ETS表来存储时间戳
        :ets.new(:virtual_jackpot_timers, [:set, :public, :named_table])
        # 首次运行，设置随机的下次生成时间
        {min_interval, max_interval} = interval_range
        random_interval = min_interval + :rand.uniform(max_interval - min_interval)
        next_time = current_time + random_interval
        :ets.insert(:virtual_jackpot_timers, {last_generation_key, current_time})
        :ets.insert(:virtual_jackpot_timers, {next_generation_key, next_time})
        true  # 首次运行，生成虚拟中奖

      _table ->
        case :ets.lookup(:virtual_jackpot_timers, next_generation_key) do
          [] ->
            # 没有下次生成时间记录，设置一个随机时间
            {min_interval, max_interval} = interval_range
            random_interval = min_interval + :rand.uniform(max_interval - min_interval)
            next_time = current_time + random_interval
            :ets.insert(:virtual_jackpot_timers, {last_generation_key, current_time})
            :ets.insert(:virtual_jackpot_timers, {next_generation_key, next_time})
            true

          [{^next_generation_key, next_time}] ->
            # 检查是否到了生成时间
            if current_time >= next_time do
              # 更新时间戳并设置下一个随机间隔
              {min_interval, max_interval} = interval_range
              random_interval = min_interval + :rand.uniform(max_interval - min_interval)
              new_next_time = current_time + random_interval
              :ets.insert(:virtual_jackpot_timers, {last_generation_key, current_time})
              :ets.insert(:virtual_jackpot_timers, {next_generation_key, new_next_time})
              true
            else
              false
            end
        end
    end
  end

  # 根据概率选择奖项类型
  defp select_win_type_by_probability(probabilities) do
    rand_val = :rand.uniform()

    # 创建累积概率列表
    cumulative_probs =
      probabilities
      |> Enum.sort_by(fn {type, _prob} ->
        case type do
          :big -> 3    # 优先级：big > medium > small
          :medium -> 2
          :small -> 1
        end
      end, :desc)
      |> Enum.reduce({[], 0}, fn {type, prob}, {acc, cumsum} ->
        new_cumsum = cumsum + prob
        {[{type, new_cumsum} | acc], new_cumsum}
      end)
      |> elem(0)
      |> Enum.reverse()

    # 根据随机值选择奖项类型
    Enum.find_value(cumulative_probs, :small, fn {type, cumulative_prob} ->
      if rand_val <= cumulative_prob, do: type
    end)
  end

  # 根据中奖金额反推合理的下注金额
  defp calculate_reasonable_bet(jackpot_amount, bet_amounts) do
    # 计算合理的倍率范围 (10-200倍)
    reasonable_multiplier = 10 + :rand.uniform(190)
    target_bet = div(jackpot_amount, reasonable_multiplier)

    # 找到最接近的有效下注金额
    Enum.min_by(bet_amounts, fn bet -> abs(bet - target_bet) end)
  end

  # 将奖项类型映射为级别
  defp map_win_type_to_level(:small), do: 1
  defp map_win_type_to_level(:medium), do: 2
  defp map_win_type_to_level(:big), do: 3

  # 生成游戏特定数据
  defp generate_game_specific_data("slot777", win_type, bet_amount, jackpot_amount) do
    # slot777: sevennum表示矩阵中"7"符号的数量
    # 只有>=3个7才能触发Jackpot，数量越多获得的奖池比例越高
    # 根据奖金金额智能选择合理的seven_count
    seven_count = case win_type do
      :big ->
        # 大奖通常需要5个以上的7才能获得高额奖金
        Enum.random([5, 6])
      :medium ->
        # 中奖通常4个7比较合理
        Enum.random([4, 5])
      :small ->
        # 小奖3个7就足够了
        Enum.random([3, 4])
    end
    %{sevennum: seven_count}
  end

  defp generate_game_specific_data("slotniu", win_type, bet_amount, jackpot_amount) do
    # slotniu: niunum表示矩阵中"牛头"符号的数量
    # 根据bet_amount和jackpot_amount反推合理的niunum
    # 参考倍率表：奖池金额/10000 * 倍率 = 实际获得金额

    # 根据下注金额选择合适的牛头数量范围
    niunum = case win_type do
      :big ->
        # 大奖：需要较多牛头才能获得高倍率
        if bet_amount >= 18000 do
          Enum.random([7, 8, 9])  # 高额下注配高牛头数
        else
          Enum.random([6, 7, 8])  # 中等下注适中牛头数
        end
      :medium ->
        # 中奖：中等牛头数量
        Enum.random([4, 5, 6])
      :small ->
        # 小奖：最少的触发条件
        Enum.random([3, 4])
    end

    # 计算对应的倍率mult - 从SlotNiu配置的倍率表获取
    mult = get_slotniu_multiplier(bet_amount, niunum)

    %{niunum: niunum, jackpotlevel: map_win_type_to_level(win_type), mult: mult}
  end

  defp generate_game_specific_data("slotcat", win_type, bet_amount, jackpot_amount) do
    # slotcat: jackpotnum表示触发的Jackpot符号数量
    # SlotCat规范化规则：3个显示3，4个显示4，5个或更多显示5
    # SlotCat有三个奖池(left/right/center)，jackpotnum影响触发哪个奖池
    jackpot_count = case win_type do
      :big ->
        # 大奖：触发center奖池，5个7符号
        5
      :medium ->
        # 中奖：触发right奖池，4个7符号
        4
      :small ->
        # 小奖：触发left奖池，3个7符号
        3
    end
    %{jackpotnum: jackpot_count, jackpotlevel: map_win_type_to_level(win_type)}
  end

  defp generate_game_specific_data(_, _, _, _), do: %{}

  # 计算真实的Jackpot奖金金额 - 基于奖池百分比
  defp calculate_realistic_jackpot_amount(game_type, win_type, bet_amount, config) do
    # 获取当前奖池金额
    current_jackpot = get_current_jackpot_amount(game_type)

    # 智能奖金计算策略
    case game_type do
      "slot777" ->
        calculate_intelligent_jackpot_amount("slot777", win_type, bet_amount, current_jackpot, config)

      "slotniu" ->
        calculate_intelligent_jackpot_amount("slotniu", win_type, bet_amount, current_jackpot, config)

      "slotcat" ->
        calculate_intelligent_jackpot_amount("slotcat", win_type, bet_amount, current_jackpot, config)

      _ ->
        # 回退到固定范围计算
        {min_amount, max_amount} = config.win_amount_ranges[win_type]
        min_amount + :rand.uniform(max_amount - min_amount)
    end
  end

  # 智能奖金计算 - 根据奖池大小采用不同策略
  defp calculate_intelligent_jackpot_amount(game_type, win_type, bet_amount, current_jackpot, config) do
    case game_type do
      "slotniu" ->
        # SlotNiu特殊处理：使用游戏配置的倍率表计算
        game_specific_data = generate_game_specific_data("slotniu", win_type, bet_amount, 0)
        niunum = game_specific_data.niunum
        multiplier = get_slotniu_multiplier(bet_amount, niunum)

        # 获取真实奖池金额（原始单位，未除以100）
        raw_jackpot = Teen.GameSystem.JackpotManager.get_jackpot_balance(41, :jackpot)

        # 按照SlotNiu的公式计算：(奖池金额 / 10000) * 倍率
        jackpot_amount = trunc((raw_jackpot / 10000) * multiplier)

        # 🔥 修复：确保奖金合理范围，使用原始奖池金额计算上限
        min_amount = bet_amount * 2
        # 不超过原始奖池的30%（安全上限，避免奖池过度减少）
        max_amount = trunc(raw_jackpot * 0.30)
        final_amount = max(min(jackpot_amount, max_amount), min_amount)

        final_amount

      "slotcat" ->
        # SlotCat特殊处理：从对应的单个奖池计算
        game_specific_data = generate_game_specific_data("slotcat", win_type, bet_amount, 0)
        jackpotnum = game_specific_data.jackpotnum
        {single_pool_amount, pool_type} = get_slotcat_single_pool_amount(jackpotnum)

        # 获取对应单个奖池的百分比
        jackpot_percentage = calculate_slotcat_jackpot_percentage(win_type, bet_amount)
        jackpot_based_amount = trunc(single_pool_amount * jackpot_percentage / 100)

        # 确保奖金合理（至少是下注的2倍，但不超过奖池的30%）
        min_amount = bet_amount * 2
        max_amount = trunc(single_pool_amount * 0.3)
        max(min(jackpot_based_amount, max_amount), min_amount)

      _ ->
        # 其他游戏使用原有逻辑
        # 获取奖池百分比
        jackpot_percentage = case game_type do
          "slot777" -> calculate_slot777_jackpot_percentage(win_type, bet_amount)
          "slotniu" -> calculate_slotniu_jackpot_percentage(win_type, bet_amount)
          "slotcat" -> calculate_slotcat_jackpot_percentage(win_type, bet_amount)
        end

        # 计算基于奖池百分比的奖金
        jackpot_based_amount = trunc(current_jackpot * jackpot_percentage / 100)

        # 根据奖池大小和下注金额采用不同策略
        calculated_amount = cond do
          # 奖池很大（>100万），优先使用奖池百分比
          current_jackpot >= 1_000_000 ->
            # 确保至少是下注的5倍，但不超过奖池百分比
            max(jackpot_based_amount, bet_amount * 5)

          # 奖池中등（10万-100万），混合策略
          current_jackpot >= 100_000 ->
            # 使用奖池百分比，但有合理的倍率范围
            realistic_multiplier = get_realistic_multiplier(win_type, bet_amount)
            multiplier_based_amount = bet_amount * realistic_multiplier
            max(jackpot_based_amount, multiplier_based_amount)

          # 奖池较小（<10万），主要基于下注倍率
          true ->
            realistic_multiplier = get_realistic_multiplier(win_type, bet_amount)
            bet_amount * realistic_multiplier
        end

        # 🔥 重要：确保中奖金额永远不超过奖池的30%（安全上限）
        max_allowed = trunc(current_jackpot * 0.30)
        final_amount = min(calculated_amount, max_allowed)
         if final_amount < calculated_amount do
          Logger.info("🎰 [SLOT777_SAFETY] 中奖金额限制: #{trunc(calculated_amount)} -> #{trunc(final_amount)} (奖池30%上限)")
        end
        final_amount
    end
  end

  # 获取真实的倍率范围（基于win_type和下注金额）
  defp get_realistic_multiplier(win_type, bet_amount) do
    # 根据下注金额调整倍率范围
    base_multipliers = case win_type do
      :big -> {50, 200}      # 大奖：50-200倍
      :medium -> {15, 80}    # 中奖：15-80倍
      :small -> {5, 30}      # 小奖：5-30倍
    end

    {min_mult, max_mult} = base_multipliers

    # 高额下注降低倍率，低额下注提高倍率
    adjusted_multipliers = case bet_amount do
      # 高额下注（>₹500）：降低倍率
      amount when amount >= 50_000 ->
        {max(min_mult * 0.3, 3), max_mult * 0.5}

      # 中等下注（₹50-₹500）：正常倍率
      amount when amount >= 5_000 ->
        {min_mult * 0.6, max_mult * 0.7}

      # 低额下注（<₹50）：提高倍率
      _ ->
        {min_mult * 1.2, max_mult * 1.5}
    end

    {adj_min, adj_max} = adjusted_multipliers
    adj_min + :rand.uniform(trunc(adj_max - adj_min))
  end

  # 获取当前奖池金额（显示单位）
  defp get_current_jackpot_amount(game_type) do
    case game_type do
      "slot777" ->
        raw_amount = Teen.GameSystem.JackpotManager.get_jackpot_balance(40, :main)
        raw_amount / 100

      "slotniu" ->
        raw_amount = Teen.GameSystem.JackpotManager.get_jackpot_balance(41, :jackpot)
        raw_amount / 100

      "slotcat" ->
        # SlotCat返回三个奖池总和（只用于计算整体奖池大小判断）
        center = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :center)
        left = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :left)
        right = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :right)
        total_amount = center + left + right
        total_amount / 100

      _ ->
        raw_amount = Teen.GameSystem.JackpotManager.get_jackpot_balance(40, :main)
        raw_amount / 100
    end
  end

  # 获取SlotCat单个奖池金额（根据jackpotnum确定）
  defp get_slotcat_single_pool_amount(jackpotnum) do
    pool_type = case jackpotnum do
      3 -> :left     # 小奖：左边奖池
      4 -> :right    # 中奖：右边奖池
      5 -> :center   # 大奖：中间奖池
      _ -> :left     # 默认左边奖池
    end

    raw_amount = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, pool_type)
    {raw_amount / 100, pool_type}
  end

  # 计算Slot777的Jackpot百分比
  defp calculate_slot777_jackpot_percentage(win_type, bet_amount) do
    # 计算单线下注金额（模拟真实游戏逻辑）
    single_line_bet = bet_amount / 9 / 100  # 9条线，除以100转换为实际金额

    # 根据单线下注和win_type确定百分比范围
    base_percentage = cond do
      single_line_bet >= 200 ->
        case win_type do
          :big -> Enum.random([15, 20, 25])     # 15%-25%
          :medium -> Enum.random([8, 12, 15])   # 8%-15%
          :small -> Enum.random([3, 5, 8])      # 3%-8%
        end
      single_line_bet >= 100 ->
        case win_type do
          :big -> Enum.random([10, 15, 20])     # 10%-20%
          :medium -> Enum.random([5, 8, 12])    # 5%-12%
          :small -> Enum.random([2, 3, 5])      # 2%-5%
        end
      single_line_bet >= 20 ->
        case win_type do
          :big -> Enum.random([5, 8, 12])       # 5%-12%
          :medium -> Enum.random([2, 4, 6])     # 2%-6%
          :small -> Enum.random([1, 2, 3])      # 1%-3%
        end
      single_line_bet >= 10 ->
        case win_type do
          :big -> Enum.random([3, 5, 8])        # 3%-8%
          :medium -> Enum.random([1, 2, 4])     # 1%-4%
          :small -> Enum.random([0.5, 1, 2])    # 0.5%-2%
        end
      true ->
        # 下注不足，使用最小百分比
        case win_type do
          :big -> Enum.random([1, 2, 3])
          :medium -> Enum.random([0.5, 1, 1.5])
          :small -> Enum.random([0.2, 0.5, 1])
        end
    end

    base_percentage
  end

  # 计算SlotNiu的Jackpot百分比 - 不再使用，改为直接计算倍率
  defp calculate_slotniu_jackpot_percentage(win_type, bet_amount) do
    # SlotNiu使用倍率系统，但也可以转换为奖池百分比
    case win_type do
      :big -> Enum.random([8, 12, 18])       # 8%-18%
      :medium -> Enum.random([3, 6, 10])     # 3%-10%
      :small -> Enum.random([1, 2, 4])       # 1%-4%
    end
  end

  # 计算SlotCat的Jackpot百分比
  defp calculate_slotcat_jackpot_percentage(win_type, bet_amount) do
    # SlotCat有三个奖池，百分比相对较高
    case win_type do
      :big -> Enum.random([20, 30, 40])      # 20%-40% (center pool)
      :medium -> Enum.random([8, 15, 25])    # 8%-25% (right pool)
      :small -> Enum.random([3, 6, 12])      # 3%-12% (left pool)
    end
  end

  # 获取SlotNiu的倍率 - 根据bet_amount和niunum从配置表获取
  defp get_slotniu_multiplier(bet_amount, niunum) do
    config = SlotNiuConfig.get_default_config()
    multipliers = config.jackpot.multipliers

    # 转换bet_amount为倍率表中的键值
    bet_key = case bet_amount do
      amount when amount >= 180_000 -> 180_000
      amount when amount >= 90_000 -> 90_000
      amount when amount >= 18_000 -> 18000
      amount when amount >= 9_000 -> 9000
      amount when amount >= 1_800 -> 1800
      amount when amount >= 900 -> 900
      amount when amount >= 180 -> 180
      _ -> 180  # 默认最低倍率
    end

    # 从倍率表获取对应的倍率
    bet_config = Map.get(multipliers, bet_key, %{})
    Map.get(bet_config, niunum, 1)  # 默认倍率为1
  end

  # 根据游戏类型和下注金额获取可用的奖项类型
  defp get_available_win_types(game_type, bet_amount) do
    case game_type do
      game_type when game_type in ["slot777", "slotcat"] ->
        # slot777和slotcat需要下注9000（90元）才能中奖，低于9000不中奖
        if bet_amount >= 9000 do
          [:small, :medium, :big]
        else
          []  # 下注不足9000，不生成任何中奖
        end

      "slotniu" ->
        # slotniu没有下注限制，所有奖项都可用
        [:small, :medium, :big]

      _ ->
        [:small, :medium]
    end
  end

  # 从可用奖项类型中根据概率选择
  defp select_win_type_from_available(available_types, win_type_probability) do
    # 计算可用奖项的总概率
    total_prob =
      available_types
      |> Enum.map(&Map.get(win_type_probability, &1, 0))
      |> Enum.sum()

    # 重新计算每个可用奖项的概率
    normalized_probs =
      available_types
      |> Enum.map(fn type ->
        prob = Map.get(win_type_probability, type, 0)
        {type, prob / total_prob}
      end)
      |> Enum.into(%{})

    # 根据重新计算的概率选择奖项类型
    select_win_type_by_probability(normalized_probs)
  end

  # 广播虚拟中奖
  defp broadcast_virtual_win(game_type, win_record) do
    config = get_complete_game_config(game_type)

    # 根据游戏类型生成对应格式的广播数据
    broadcast_data = format_broadcast_data(game_type, win_record)

    # 直接实现广播逻辑，而不依赖RoomBase的宏
    message = %{
      # MainProto.Game
      "mainId" => 5,
      "subId" => config.sub_id,
      "data" => broadcast_data
    }

    game_type_atom = String.to_atom(game_type)

    case Cypridina.RoomSystem.RoomManager.broadcast_to_game_type(game_type_atom, {:broadcast, message}) do
      {:ok, %{success: success_count, total: total_count}} ->
        :ok

      {:error, reason} ->
        Logger.error("❌ [VIRTUAL_JACKPOT] 虚拟中奖广播失败: #{inspect(reason)}")
        :error
    end
  end

  # 根据游戏类型格式化广播数据
  defp format_broadcast_data("slot777", win_record) do
    %{
      "playerid" => win_record.player_id,
      "playername" => win_record.player_name,
      "headid" => win_record.avatar_id,
      "wxheadurl" => win_record.avatar_url,
      "sevennum" => win_record.game_specific_data.sevennum,
      "bet" => win_record.bet_amount,
      "winscore" => win_record.jackpot_amount,
      "time" => DateTime.to_unix(win_record.win_time, :millisecond)
    }
  end

  defp format_broadcast_data("slotniu", win_record) do
    %{
      "playerid" => win_record.player_id,
      "playername" => win_record.player_name,
      "name" => win_record.player_name,
      "headid" => win_record.avatar_id,
      "wxheadurl" => win_record.avatar_url,
      "winscore" => win_record.jackpot_amount,
      "jackpotlevel" => win_record.game_specific_data.jackpotlevel,
      "niunum" => win_record.game_specific_data.niunum,
      "betamount" => win_record.bet_amount,
      "mult" => win_record.game_specific_data.mult
    }
  end

  defp format_broadcast_data("slotcat", win_record) do
    %{
      "playerid" => win_record.player_id,
      "name" => win_record.player_name,
      "headid" => win_record.avatar_id,
      "wxheadurl" => win_record.avatar_url,
      "winscore" => win_record.jackpot_amount,
      "jackpotnum" => win_record.game_specific_data.jackpotnum,
      "bet" => win_record.bet_amount
    }
  end

  # ==================== 显示奖池跳动管理 ====================

  # 初始化显示奖池状态
  defp init_display_jackpots do
    games = ["slot777", "slotniu", "slotcat"]

    Enum.reduce(games, %{}, fn game_type, acc ->
      real_jackpot = get_real_jackpot_amount(game_type)

      Map.put(acc, game_type, %{
        real_jackpot: real_jackpot,
        display_jackpot: real_jackpot,  # 初始时显示值等于真实值
        last_increment: 50,
        last_update: System.system_time(:millisecond)
      })
    end)
  end

  # 获取真实奖池金额 - 参考各游戏房间的实际获取方式
  defp get_real_jackpot_amount(game_type) do
    case game_type do
      "slot777" ->
        # Slot777 使用 main 奖池
        raw_amount = Teen.GameSystem.JackpotManager.get_jackpot_balance(40, :main)
        raw_amount / 100

      "slotniu" ->
        # SlotNiu 使用 jackpot 奖池 (参考 slotniu_room.ex:59)
        raw_amount = Teen.GameSystem.JackpotManager.get_jackpot_balance(41, :jackpot)
        raw_amount / 100

      "slotcat" ->
        # SlotCat 使用三个奖池：center, left, right (参考 slotcat_room.ex:303-307)
        center = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :center)
        left = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :left)
        right = Teen.GameSystem.JackpotManager.get_jackpot_balance(42, :right)
        total_amount = center + left + right
        total_amount / 100

      _ ->
        # 默认使用 Slot777 的方式
        raw_amount = Teen.GameSystem.JackpotManager.get_jackpot_balance(40, :main)
        raw_amount / 100
    end
  end

  # 启动显示奖池跳动定时器
  defp schedule_display_jackpot_tick do
    Process.send_after(self(), :display_jackpot_tick, 1000)  # 每秒跳动
  end

  # 更新所有游戏的显示奖池
  defp update_all_display_jackpots(display_jackpots) do
    games = ["slot777", "slotniu", "slotcat"]

    Enum.reduce(games, display_jackpots, fn game_type, acc ->
      update_single_display_jackpot(acc, game_type)
    end)
  end

  # 更新单个游戏的显示奖池
  defp update_single_display_jackpot(display_jackpots, game_type) do
    current_state = Map.get(display_jackpots, game_type)
    real_jackpot = get_real_jackpot_amount(game_type)

    # 计算增量
    increment = calculate_display_increment(current_state.display_jackpot, real_jackpot, game_type)

    # 更新显示奖池
    new_display_jackpot = current_state.display_jackpot + increment

    # 🔥 修复边界控制：允许显示奖池超过真实奖池，但超过时增长速度减慢
    # 最小值：不能低于真实奖池的85%
    new_display_jackpot = max(new_display_jackpot, real_jackpot * 0.85)

    # 🔥 移除上限限制，允许显示奖池持续增长（但增长会在calculate_display_increment中控制速度）

    new_state = %{
      real_jackpot: real_jackpot,
      display_jackpot: new_display_jackpot,
      last_increment: increment,
      last_update: System.system_time(:millisecond)
    }

    Map.put(display_jackpots, game_type, new_state)
  end

  # 计算显示奖池增量
  defp calculate_display_increment(display_jackpot, real_jackpot, game_type) do
    deviation = real_jackpot - display_jackpot

    # 基础增量配置（每秒）
    base_increments = %{
      "slot777" => {20, 60},   # 20-60/秒
      "slotniu" => {30, 80},   # 30-80/秒
      "slotcat" => {40, 120}   # 40-120/秒
    }

    {min_inc, max_inc} = Map.get(base_increments, game_type, {30, 80})

    cond do
      # 🔥 新增：显示值超过真实值：缓慢继续增长
      deviation < -50000 ->
        # 显示奖池比真实奖池高太多，非常缓慢增长
        Enum.random(5..15)
      # 🔥 新增：显示值稍高于真实值：慢速增长
      deviation < 0 ->
        # 显示奖池比真实奖池稍高，慢速增长
        Enum.random(10..30)

      # 显示值远低于真实值：大幅追赶
      deviation > 50000 ->
        Enum.random(200..500)

      # 显示值略低于真实值：中等增量
      deviation > 10000 ->
        Enum.random(80..200)

      # 接近真实值：正常小增量
      true ->
        Enum.random(min_inc..max_inc)
    end
  end

  # 广播显示奖池到游戏房间 - 使用与1006协议相同的GenServer.cast广播方式
  defp broadcast_display_jackpots(display_jackpots) do
    Enum.each(display_jackpots, fn {game_type, state} ->
      # 构建协议消息
      protocol_message = build_jackpot_protocol_message(game_type, state.display_jackpot)

      # 将字符串game_type转换为原子，因为RoomManager中的匹配使用原子类型
      atom_game_type = case game_type do
        "slot777" -> :slot777
        "slotniu" -> :slotniu
        "slotcat" -> :slotcat
        _ -> String.to_atom(game_type)
      end

      # 使用与1006协议相同的方式：通过RoomManager.broadcast_to_game_type发送{:broadcast, message}
      case Cypridina.RoomSystem.RoomManager.broadcast_to_game_type(atom_game_type, {:broadcast, protocol_message}) do
        {:ok, %{success: success_count, total: total_count}} ->
          sub_id = case game_type do
            "slot777" -> 1005
            "slotniu" -> 1007
            "slotcat" -> 1005
          end

        {:error, reason} ->
          Logger.error("🎰 [DISPLAY_BROADCAST] #{game_type} 广播失败: #{inspect(reason)}")
      end
    end)
  end

  # 构建不同游戏的奖池协议消息
  defp build_jackpot_protocol_message(game_type, display_jackpot) do
    # display_jackpot是已经除以100的显示金额（如100.54），需要转换回原始格式（如10054）
    original_amount = trunc(display_jackpot * 100)

    case game_type do
      "slot777" ->
        # Slot777: SC_SLOT777_JACKPOT_P = 1005, 单个jackpot字段
        %{
          "mainId" => 5,
          "subId" => 1005,
          "data" => %{
            "jackpot" => original_amount
          }
        }

      "slotniu" ->
        # SlotNiu: SC_SLOTNIU_JACKPOT_P = 1007, 单个jackpot字段
        %{
          "mainId" => 5,
          "subId" => 1007,
          "data" => %{
            "jackpot" => original_amount
          }
        }

      "slotcat" ->
        # SlotCat: SC_SLOTCAT_JACKPOT_P = 1005, 三个奖池字段
        # 显示奖池平均分配到三个奖池
        average_amount = trunc(original_amount / 3)
        %{
          "mainId" => 5,
          "subId" => 1005,
          "data" => %{
            "jackpot" => average_amount,      # 中间奖池
            "jackpotleft" => average_amount,  # 左边奖池
            "jackpotright" => average_amount  # 右边奖池
          }
        }
    end
  end

  # 处理虚拟中奖对显示奖池的影响
  defp handle_virtual_win_impact(state, game_type, win_amount) do
    # 确保状态结构正确
    validated_state = validate_and_fix_state_structure(state)

    current_display = get_in(validated_state.display_jackpots, [game_type, :display_jackpot]) || 0

    # 🔥 修复虚拟中奖扣除比例过高导致的"直接减半"问题
    # 虚拟中奖应该只是轻微影响显示奖池，而不应该大幅扣除
    deduction_rates = %{
      "slot777" => {0.02, 0.05},  # 2%-5% (原来10%-30%)
      "slotniu" => {0.03, 0.08},  # 3%-8% (原来15%-40%)
      "slotcat" => {0.04, 0.10}   # 4%-10% (原来20%-50%)
    }

    {min_rate, max_rate} = Map.get(deduction_rates, game_type, {0.03, 0.08})
    deduction_rate = :rand.uniform() * (max_rate - min_rate) + min_rate

    # 计算扣除金额
    deduction = win_amount * deduction_rate
    # 获取真实奖池作为参考
    current_real_jackpot = get_real_jackpot_amount(game_type)
    # 🔥 修复：确保显示奖池不会低于真实奖池的85%（避免过度偏离真实值）
    min_allowed = max(current_real_jackpot * 0.85, 0)  # 不低于真实奖池的85%
    new_display_jackpot = max(current_display - deduction, min_allowed)

    # 更新状态
    put_in(validated_state, [:display_jackpots, game_type, :display_jackpot], new_display_jackpot)
  end

  # 删除未使用的函数，虚拟中奖影响已经集成到主流程中
end
