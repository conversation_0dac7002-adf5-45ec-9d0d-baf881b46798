defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiLogic do
  @moduledoc """
  Teen Patti游戏逻辑模块

  实现核心游戏算法，包括：
  - 牌型判断和比较
  - 手牌评估和排序
  - 胜负计算
  - 牌型强度评估

  基于C++服务端的IPoker.cpp实现
  """

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiGame

  # 扑克牌点数定义 (对应emPokeValue)
  @card_values %{
    2 => 2,
    3 => 3,
    4 => 4,
    5 => 5,
    6 => 6,
    7 => 7,
    8 => 8,
    # A=14
    9 => 9,
    10 => 10,
    11 => 11,
    12 => 12,
    13 => 13,
    14 => 14
  }

  # 花色定义
  @card_suits %{
    # 黑桃
    spades: 1,
    # 红心
    hearts: 2,
    # 方块
    diamonds: 3,
    # 梅花
    clubs: 4
  }

  @doc """
  计算手牌的牌型

  ## 参数
  - cards: 手牌列表，每张牌包含 %{value: 点数, suit: 花色}

  ## 返回
  牌型原子，对应TeenPattiGame.card_types()
  """
  def calculate_card_type(cards) when length(cards) == 3 do
    # 按点数排序
    sorted_cards = Enum.sort_by(cards, & &1.value)

    # 统计点数出现次数
    value_counts =
      cards
      |> Enum.group_by(& &1.value)
      |> Enum.map(fn {value, cards} -> {value, length(cards)} end)
      |> Enum.into(%{})

    # 检查是否同花色
    same_suit = cards |> Enum.map(& &1.suit) |> Enum.uniq() |> length() == 1

    # 检查是否顺子
    is_sequence = is_sequence?(sorted_cards)

    cond do
      # 豹子：三张点数相同
      map_size(value_counts) == 1 ->
        :trail

      # 对子：两张点数相同
      map_size(value_counts) == 2 ->
        :pair

      # 同花顺：既是同花又是顺子
      same_suit and is_sequence ->
        :pure_sequence

      # 顺子：连续的点数
      is_sequence ->
        :sequence

      # 同花：相同花色
      same_suit ->
        :color

      # 高牌：其他情况
      true ->
        :high_card
    end
  end

  def calculate_card_type(_), do: :none

  @doc """
  比较两手牌的大小

  ## 参数
  - cards1: 第一手牌
  - cards2: 第二手牌

  ## 返回
  - :greater - cards1 > cards2
  - :less - cards1 < cards2
  - :equal - cards1 == cards2
  """
  def compare_hands(cards1, cards2) do
    type1 = calculate_card_type(cards1)
    type2 = calculate_card_type(cards2)

    type_strength1 = get_type_strength(type1)
    type_strength2 = get_type_strength(type2)

    cond do
      type_strength1 > type_strength2 ->
        :greater

      type_strength1 < type_strength2 ->
        :less

      true ->
        # 牌型相同，比较具体大小
        compare_same_type(cards1, cards2, type1)
    end
  end

  @doc """
  检查是否为顺子
  """
  defp is_sequence?(sorted_cards) do
    values = Enum.map(sorted_cards, & &1.value)

    # 检查普通顺子
    is_normal_sequence =
      values
      |> Enum.with_index()
      |> Enum.all?(fn {value, index} ->
        index == 0 or value == Enum.at(values, index - 1) + 1
      end)

    # 检查A-2-3特殊顺子
    is_a23_sequence = values == [2, 3, 14]

    is_normal_sequence or is_a23_sequence
  end

  @doc """
  获取牌型强度值
  """
  defp get_type_strength(card_type) do
    case card_type do
      :trail -> 6
      :pure_sequence -> 5
      :sequence -> 4
      :color -> 3
      :pair -> 2
      :high_card -> 1
      _ -> 0
    end
  end

  @doc """
  比较相同牌型的手牌
  """
  defp compare_same_type(cards1, cards2, card_type) do
    case card_type do
      :trail ->
        compare_trail(cards1, cards2)

      :pair ->
        compare_pair(cards1, cards2)

      :sequence ->
        compare_sequence(cards1, cards2)

      :pure_sequence ->
        compare_sequence(cards1, cards2)

      _ ->
        compare_high_cards(cards1, cards2)
    end
  end

  @doc """
  比较豹子牌型
  """
  defp compare_trail(cards1, cards2) do
    value1 = hd(cards1).value
    value2 = hd(cards2).value

    cond do
      value1 > value2 -> :greater
      value1 < value2 -> :less
      true -> :equal
    end
  end

  @doc """
  比较对子牌型
  """
  defp compare_pair(cards1, cards2) do
    {pair_value1, single_value1} = get_pair_values(cards1)
    {pair_value2, single_value2} = get_pair_values(cards2)

    cond do
      pair_value1 > pair_value2 ->
        :greater

      pair_value1 < pair_value2 ->
        :less

      single_value1 > single_value2 ->
        :greater

      single_value1 < single_value2 ->
        :less

      true ->
        :equal
    end
  end

  @doc """
  比较顺子牌型
  """
  defp compare_sequence(cards1, cards2) do
    max_value1 = get_sequence_max_value(cards1)
    max_value2 = get_sequence_max_value(cards2)

    cond do
      max_value1 > max_value2 -> :greater
      max_value1 < max_value2 -> :less
      true -> :equal
    end
  end

  @doc """
  比较高牌
  """
  defp compare_high_cards(cards1, cards2) do
    sorted1 = Enum.sort_by(cards1, & &1.value, :desc)
    sorted2 = Enum.sort_by(cards2, & &1.value, :desc)

    compare_card_by_card(sorted1, sorted2)
  end

  @doc """
  逐张比较牌的大小
  """
  defp compare_card_by_card([], []), do: :equal

  defp compare_card_by_card([card1 | rest1], [card2 | rest2]) do
    cond do
      card1.value > card2.value -> :greater
      card1.value < card2.value -> :less
      true -> compare_card_by_card(rest1, rest2)
    end
  end

  @doc """
  获取对子的对子值和单牌值
  """
  defp get_pair_values(cards) do
    value_counts =
      cards
      |> Enum.group_by(& &1.value)
      |> Enum.map(fn {value, cards} -> {value, length(cards)} end)

    {pair_value, _} = Enum.find(value_counts, fn {_, count} -> count == 2 end)
    {single_value, _} = Enum.find(value_counts, fn {_, count} -> count == 1 end)

    {pair_value, single_value}
  end

  @doc """
  获取顺子的最大值
  """
  defp get_sequence_max_value(cards) do
    values = Enum.map(cards, & &1.value) |> Enum.sort()

    # 检查是否为A-2-3特殊顺子
    if values == [2, 3, 14] do
      # A-2-3顺子的最大值是3
      3
    else
      Enum.max(values)
    end
  end

  @doc """
  计算手牌强度分数 (用于AI决策)
  """
  def calculate_hand_strength(cards) do
    card_type = calculate_card_type(cards)
    base_strength = get_type_strength(card_type)

    # 根据具体牌型计算详细强度
    detail_strength =
      case card_type do
        :trail ->
          # 豹子：根据点数计算
          value = hd(cards).value
          base_strength * 100 + value

        :pure_sequence ->
          # 同花顺：根据最大值计算
          max_value = get_sequence_max_value(cards)
          base_strength * 100 + max_value

        :sequence ->
          # 顺子：根据最大值计算
          max_value = get_sequence_max_value(cards)
          base_strength * 100 + max_value

        :color ->
          # 同花：根据最大牌计算
          max_value = Enum.max_by(cards, & &1.value).value
          base_strength * 100 + max_value

        :pair ->
          # 对子：根据对子值和单牌值计算
          {pair_value, single_value} = get_pair_values(cards)
          base_strength * 100 + pair_value * 10 + single_value

        :high_card ->
          # 高牌：根据最大牌计算
          max_value = Enum.max_by(cards, & &1.value).value
          base_strength * 100 + max_value

        _ ->
          0
      end

    detail_strength
  end

  @doc """
  生成一副标准扑克牌 (52张)
  """
  def generate_deck do
    for suit <- [:spades, :hearts, :diamonds, :clubs],
        value <- 2..14 do
      %{suit: suit, value: value}
    end
  end

  @doc """
  洗牌
  """
  def shuffle_deck(deck) do
    Enum.shuffle(deck)
  end

  @doc """
  发牌给玩家
  """
  def deal_cards(deck, player_count) when player_count <= 5 do
    deck
    |> Enum.take(player_count * 3)
    |> Enum.chunk_every(3)
    |> Enum.with_index()
    |> Enum.map(fn {cards, index} -> {index, cards} end)
    |> Enum.into(%{})
  end

  def deal_cards(_deck, _player_count) do
    # 超过5个玩家时返回空映射
    %{}
  end

  @doc """
  获取牌型的中文名称
  """
  def get_card_type_name(card_type) do
    case card_type do
      :trail -> "豹子"
      :pure_sequence -> "同花顺"
      :sequence -> "顺子"
      :color -> "同花"
      :pair -> "对子"
      :high_card -> "高牌"
      _ -> "无效"
    end
  end

  @doc """
  获取牌型的英文名称
  """
  def get_card_type_name_en(card_type) do
    case card_type do
      :trail -> "Trail"
      :pure_sequence -> "Pure Sequence"
      :sequence -> "Sequence"
      :color -> "Color"
      :pair -> "Pair"
      :high_card -> "High Card"
      _ -> "Invalid"
    end
  end

  @doc """
  获取手牌强度 (用于结算时的牌力比较)
  这是calculate_hand_strength的别名，保持与旧项目的兼容性
  """
  def get_hand_strength(cards) do
    calculate_hand_strength(cards)
  end

  @doc """
  计算获胜概率 (基于蒙特卡洛模拟)
  """
  def calculate_win_probability(my_cards, opponent_count, simulations \\ 1000) do
    wins =
      1..simulations
      |> Enum.count(fn _ ->
        # 生成剩余牌堆
        remaining_deck =
          generate_deck()
          |> Enum.reject(fn card -> card in my_cards end)
          |> shuffle_deck()

        # 为对手发牌
        opponent_hands =
          remaining_deck
          |> Enum.take(opponent_count * 3)
          |> Enum.chunk_every(3)

        # 检查我的牌是否最大
        Enum.all?(opponent_hands, fn opponent_cards ->
          compare_hands(my_cards, opponent_cards) == :greater
        end)
      end)

    wins / simulations
  end
end
