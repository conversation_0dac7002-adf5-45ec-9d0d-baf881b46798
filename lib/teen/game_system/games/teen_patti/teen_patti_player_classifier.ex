defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiPlayerClassifier do
  @moduledoc """
  Teen Patti 玩家分类模块

  根据《TP服务器算法文档.docx》对玩家进行分类：
  - 免费玩家：新玩家、待充值玩家
  - 付费玩家：1-3充玩家、4充及以上玩家
  - 机器人：系统控制的AI玩家

  同时处理玩家状态：正常、急救、回头玩家等
  """

  require Logger
  alias Teen.Services.{LuckService, TurnoverService}
  alias Cypridina.Accounts

  # 玩家类型定义
  @player_types %{
    # 免费玩家
    # 新玩家第一局
    new_first: :new_first,
    # 新玩家金币未达到80
    new_under_80: :new_under_80,
    # 待充值玩家（金币首次超过80）
    waiting_recharge: :waiting_recharge,

    # 付费玩家
    # 1-3充玩家
    recharge_1_3: :recharge_1_3,
    # 4充及以上玩家
    recharge_4_plus: :recharge_4_plus,

    # 特殊类型
    # 机器人
    robot: :robot,
    # 回头玩家（超过3天未上线）
    returning: :returning
  }

  # 玩家状态定义
  @player_states %{
    # 正常状态
    normal: :normal,
    # 急救状态
    emergency: :emergency,
    # 被控制状态（总提充比触发）
    controlled: :controlled
  }

  # 配置常量
  # 新玩家金币阈值
  @new_player_gold_threshold 80
  # 回头玩家天数阈值
  @returning_player_days 3

  @doc """
  获取玩家类型和状态

  返回: {player_type, player_state, extra_info}
  """
  def classify_player(player_id, current_gold \\ nil) do
    # 获取玩家信息
    with {:ok, user} <- Accounts.get_user(player_id),
         {:ok, recharge_count} <- get_recharge_count(user.id),
         {:ok, luck_value} <- LuckService.get_user_luck(user.id),
         {:ok, total_recharge} <- get_total_recharge(user.id),
         {:ok, total_withdraw} <- get_total_withdraw(user.id) do
      # 检查是否是机器人
      if is_robot?(user) do
        {:robot, :normal, %{}}
      else
        # 获取玩家金币（如果没有传入则从资产中获取）
        player_gold = current_gold || get_player_gold(user)

        # 检查是否是回头玩家
        if is_returning_player?(user) do
          Logger.info("👤 [CLASSIFIER] 玩家#{player_id}是回头玩家（超过#{@returning_player_days}天未上线）")
          {:returning, :normal, %{treated_as: :new_first}}
        else
          # 根据充值次数分类
          classify_by_recharge(
            user,
            recharge_count,
            luck_value,
            total_recharge,
            total_withdraw,
            player_gold
          )
        end
      end
    else
      {:error, reason} ->
        Logger.error("👤 [CLASSIFIER] 玩家#{player_id}分类失败: #{inspect(reason)}")
        {:new_under_80, :normal, %{error: reason}}
    end
  end

  @doc """
  检查玩家是否处于急救状态
  """
  def check_emergency_state(player_id, recharge_count, total_recharge, total_withdraw) do
    if recharge_count >= 1 and recharge_count <= 3 do
      withdraw_ratio = calculate_withdraw_ratio(total_recharge, total_withdraw)

      if withdraw_ratio <= 0.2 do
        {:emergency, withdraw_ratio}
      else
        {:normal, withdraw_ratio}
      end
    else
      {:normal, 0.0}
    end
  end

  @doc """
  检查玩家是否被控制（基于总提充比）
  """
  def check_control_state(player_id, recharge_count, total_recharge, total_withdraw) do
    withdraw_ratio = calculate_withdraw_ratio(total_recharge, total_withdraw)

    case recharge_count do
      1 ->
        cond do
          # 幸运值100
          withdraw_ratio >= 1.98 -> {:controlled, :severe, withdraw_ratio}
          # 幸运值300
          withdraw_ratio >= 1.55 -> {:controlled, :moderate, withdraw_ratio}
          true -> {:normal, :none, withdraw_ratio}
        end

      2 ->
        cond do
          withdraw_ratio >= 2.22 -> {:controlled, :severe, withdraw_ratio}
          withdraw_ratio >= 1.75 -> {:controlled, :moderate, withdraw_ratio}
          true -> {:normal, :none, withdraw_ratio}
        end

      3 ->
        cond do
          withdraw_ratio >= 2.6 -> {:controlled, :severe, withdraw_ratio}
          withdraw_ratio >= 2.0 -> {:controlled, :moderate, withdraw_ratio}
          true -> {:normal, :none, withdraw_ratio}
        end

      _ ->
        {:normal, :none, withdraw_ratio}
    end
  end

  @doc """
  获取玩家的第一局标记
  """
  def is_first_game?(player_id) do
    # 检查玩家是否有游戏记录
    # TODO: 需要接入实际的游戏记录查询
    case get_player_game_count(player_id) do
      0 -> true
      _ -> false
    end
  end

  @doc """
  获取玩家类型的友好名称
  """
  def get_type_name(player_type) do
    case player_type do
      :new_first -> "新玩家首局"
      :new_under_80 -> "新玩家(金币<80)"
      :waiting_recharge -> "待充值玩家"
      :recharge_1_3 -> "1-3充玩家"
      :recharge_4_plus -> "4充+玩家"
      :robot -> "机器人"
      :returning -> "回头玩家"
      _ -> "未知类型"
    end
  end

  @doc """
  获取玩家状态的友好名称
  """
  def get_state_name(player_state) do
    case player_state do
      :normal -> "正常"
      :emergency -> "急救"
      :controlled -> "受控"
      _ -> "未知状态"
    end
  end

  # 私有函数

  defp classify_by_recharge(
         user,
         recharge_count,
         luck_value,
         total_recharge,
         total_withdraw,
         player_gold
       ) do
    cond do
      # 未充值玩家
      recharge_count == 0 or luck_value == -1 ->
        classify_free_player(user, player_gold)

      # 1-3充玩家
      recharge_count >= 1 and recharge_count <= 3 ->
        state = determine_recharge_player_state(recharge_count, total_recharge, total_withdraw)
        {:recharge_1_3, state, %{recharge_count: recharge_count, luck_value: luck_value}}

      # 4充及以上玩家
      recharge_count >= 4 ->
        {:recharge_4_plus, :normal, %{recharge_count: recharge_count, luck_value: luck_value}}

      true ->
        {:new_under_80, :normal, %{}}
    end
  end

  defp classify_free_player(user, player_gold) do
    cond do
      # 检查是否是第一局
      is_first_game?(user.id) ->
        {:new_first, :normal, %{first_game: true}}

      # 金币未达到阈值
      player_gold < @new_player_gold_threshold ->
        {:new_under_80, :normal, %{gold: player_gold}}

      # 金币首次超过阈值，进入待充值状态
      true ->
        {:waiting_recharge, :normal, %{gold: player_gold}}
    end
  end

  defp determine_recharge_player_state(recharge_count, total_recharge, total_withdraw) do
    {state, _, _} = check_emergency_state(nil, recharge_count, total_recharge, total_withdraw)

    if state == :emergency do
      :emergency
    else
      {control_state, _, _} =
        check_control_state(nil, recharge_count, total_recharge, total_withdraw)

      if control_state == :controlled do
        :controlled
      else
        :normal
      end
    end
  end

  defp is_robot?(user) do
    username = to_string(user.username)

    String.starts_with?(username, "robot_") or
      String.contains?(username, "bot") or
      Map.get(user, :is_robot, false)
  end

  defp is_returning_player?(user) do
    case user.last_login_at do
      nil ->
        false

      last_login ->
        days_since_login = DateTime.diff(DateTime.utc_now(), last_login, :day)
        days_since_login >= @returning_player_days
    end
  end

  defp get_player_gold(user) do
    # TODO: 实际获取玩家金币
    Cypridina.Accounts.get_user_points(user.id)
  end

  defp get_recharge_count(user_id) do
    # TODO: 实际查询充值次数
    case TurnoverService.get_user_recharge_count(user_id) do
      {:ok, count} -> {:ok, count}
      _ -> {:ok, 0}
    end
  end

  defp get_total_recharge(user_id) do
    # TODO: 实际查询总充值金额
    case TurnoverService.get_user_total_recharge(user_id) do
      {:ok, amount} -> {:ok, amount}
      _ -> {:ok, 0}
    end
  end

  defp get_total_withdraw(user_id) do
    # TODO: 实际查询总提现金额（包括身上余额）
    case TurnoverService.get_user_total_withdraw(user_id) do
      {:ok, amount} -> {:ok, amount}
      _ -> {:ok, 0}
    end
  end

  defp get_player_game_count(player_id) do
    # TODO: 实际查询玩家游戏局数
    0
  end

  defp calculate_withdraw_ratio(total_recharge, total_withdraw) do
    if total_recharge > 0 do
      # +0 是身上余额，需要实际获取
      Float.round((total_withdraw + 0) / total_recharge, 2)
    else
      0.0
    end
  end
end
