defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiDealerV2 do
  @moduledoc """
  Teen Patti 发牌模块 V2（全新重构版）

  使用新的模块化系统：
  - TeenPattiPlayerClassifier: 玩家分类
  - TeenPattiProbabilityConfig: 概率配置
  - TeenPattiCardExchanger: 换牌策略
  - TeenPattiLuckManager: 幸运值管理
  """

  require Logger

  alias Cypridina.Teen.GameSystem.Games.TeenPatti.{
    TeenPattiLogic,
    TeenPattiPlayerClassifier,
    TeenPattiProbabilityConfig,
    TeenPattiCardExchanger,
    TeenPattiLuckManager
  }

  @doc """
  主发牌函数
  """
  def deal_cards(players, game_context) do
    Logger.info("🎴 [DEALER_V2] 开始发牌 - 玩家数:#{length(players)}")

    # 1. 分类玩家
    {classified_players, player_classifications} = classify_all_players(players, game_context)

    # 2. 生成初始牌
    initial_cards = generate_initial_cards(classified_players, player_classifications)

    # 3. 应用换牌策略
    final_cards =
      TeenPattiCardExchanger.apply_exchange_strategy(
        initial_cards,
        classified_players,
        game_context
      )

    Logger.info("🎴 [DEALER_V2] 发牌完成")
    {final_cards, player_classifications}
  end

  @doc """
  处理玩家中途充值
  """
  def handle_player_recharge(
        current_cards,
        _players,
        recharge_player_id,
        recharge_amount,
        pot_total,
        player_bet
      ) do
    Logger.info("💰 [RECHARGE] 玩家#{recharge_player_id}充值#{recharge_amount}")

    player_cards = Map.get(current_cards, recharge_player_id)

    # 应用充值后的特殊换牌策略
    TeenPattiCardExchanger.apply_recharge_special_exchange(
      player_cards,
      current_cards,
      recharge_player_id,
      recharge_amount,
      pot_total,
      player_bet
    )
  end

  @doc """
  处理玩家弃牌后的特殊情况
  """
  def handle_fold_special_event(_player_id, player_cards, final_two_robots) do
    {robot1_id, robot1_cards} = Enum.at(final_two_robots, 0)
    {robot2_id, robot2_cards} = Enum.at(final_two_robots, 1)

    # 应用弃牌后的特殊换牌
    TeenPattiCardExchanger.handle_fold_special_exchange(
      player_cards,
      robot1_id,
      robot2_id,
      robot1_cards,
      robot2_cards
    )
  end

  # 私有函数

  defp classify_all_players(players, game_context) do
    classified_players =
      Enum.map(players, fn player ->
        if Map.get(player, :is_robot, false) do
          # 机器人保持原样
          Map.put(player, :player_type, :robot)
        else
          # 真实玩家分类
          current_gold = get_player_gold(player, game_context)

          {player_type, player_state, extra_info} =
            TeenPattiPlayerClassifier.classify_player(player.numeric_id, current_gold)

          player
          |> Map.put(:player_type, player_type)
          |> Map.put(:player_state, player_state)
          |> Map.put(:classification_info, extra_info)
          |> Map.put(:luck_value, Map.get(extra_info, :luck_value, -1))
        end
      end)

    # 构建分类映射
    classifications =
      classified_players
      |> Enum.map(fn p -> {p.numeric_id, Map.get(p, :player_type)} end)
      |> Enum.into(%{})

    {classified_players, classifications}
  end

  defp generate_initial_cards(players, _player_classifications) do
    # 生成牌堆
    deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()

    # 为每个玩家生成牌
    {cards_map, _} =
      Enum.reduce(players, {%{}, deck}, fn player, {acc, current_deck} ->
        {player_cards, remaining_deck} = generate_cards_for_player(player, current_deck)
        {Map.put(acc, player.numeric_id, player_cards), remaining_deck}
      end)

    cards_map
  end

  defp generate_cards_for_player(player, deck) do
    player_type = Map.get(player, :player_type, :unknown)
    luck_value = Map.get(player, :luck_value, -1)
    is_robot = Map.get(player, :is_robot, false)

    # 获取概率配置
    probability_config =
      TeenPattiProbabilityConfig.get_probability_config(
        player_type,
        luck_value,
        is_robot
      )

    # 随机选择牌型
    card_type = TeenPattiProbabilityConfig.random_card_type(probability_config)

    Logger.info(
      "🎴 [GENERATE] 玩家#{player.numeric_id}(#{TeenPattiPlayerClassifier.get_type_name(player_type)}) 生成牌型:#{card_type}"
    )

    # 生成指定牌型
    generate_specific_card_type(card_type, deck)
  end

  defp generate_specific_card_type(card_type, deck) do
    case card_type do
      :high_card -> generate_high_card(deck)
      :pair -> generate_pair(deck)
      :color -> generate_color(deck)
      :sequence -> generate_sequence(deck)
      :pure_sequence -> generate_pure_sequence(deck)
      :trail -> generate_trail(deck)
      _ -> {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  # 各种牌型生成函数

  defp generate_high_card(deck) do
    # 选3张不同点数的牌，确保不是其他牌型
    # 多取一些以便筛选
    cards =
      Enum.take_random(deck, 5)
      |> Enum.uniq_by(fn card ->
        rank = card.value
        rank
      end)
      |> Enum.take(3)

    if length(cards) == 3 and TeenPattiLogic.calculate_card_type(cards) == :high_card do
      {cards, deck -- cards}
    else
      # 重试
      new_deck = Enum.shuffle(deck)
      generate_high_card(new_deck)
    end
  end

  defp generate_pair(deck) do
    # 按点数分组
    grouped =
      Enum.group_by(deck, fn card ->
        rank = card.value
        rank
      end)

    # 找有至少2张的点数
    pairs = Enum.filter(grouped, fn {_, cards} -> length(cards) >= 2 end)

    if length(pairs) > 0 do
      {rank, cards} = Enum.random(pairs)
      pair_cards = Enum.take(cards, 2)

      # 找一张不同点数的牌
      other_card =
        (deck -- cards)
        |> Enum.find(fn card ->
          r = card.value
          r != rank
        end)

      if other_card do
        result = pair_cards ++ [other_card]
        {result, deck -- result}
      else
        generate_pair(Enum.shuffle(deck))
      end
    else
      generate_pair(Enum.shuffle(deck))
    end
  end

  defp generate_color(deck) do
    # 按花色分组
    grouped =
      Enum.group_by(deck, fn card ->
        suit = card.suit
        suit
      end)

    # 找有至少3张的花色
    colors = Enum.filter(grouped, fn {_, cards} -> length(cards) >= 3 end)

    if length(colors) > 0 do
      {_, cards} = Enum.random(colors)
      selected = Enum.take_random(cards, 3)

      # 确保不是顺子或同花顺
      if TeenPattiLogic.calculate_card_type(selected) == :color do
        {selected, deck -- selected}
      else
        generate_color(Enum.shuffle(deck))
      end
    else
      generate_color(Enum.shuffle(deck))
    end
  end

  defp generate_sequence(deck) do
    # 顺子序列
    sequences = [
      # A-2-3
      [14, 2, 3],
      [2, 3, 4],
      [3, 4, 5],
      [4, 5, 6],
      [5, 6, 7],
      [6, 7, 8],
      [7, 8, 9],
      [8, 9, 10],
      [9, 10, 11],
      [10, 11, 12],
      [11, 12, 13],
      # Q-K-A
      [12, 13, 14]
    ]

    # 随机尝试生成顺子
    Enum.shuffle(sequences)
    |> Enum.find_value(fn ranks ->
      cards =
        Enum.map(ranks, fn rank ->
          Enum.find(deck, fn card ->
            r = card.value
            r == rank
          end)
        end)

      if Enum.all?(cards) do
        # 确保不是同花顺
        suits =
          cards
          |> Enum.map(fn c ->
            s = c.suit
            s
          end)
          |> Enum.uniq()

        if length(suits) > 1 do
          {cards, deck -- cards}
        else
          nil
        end
      else
        nil
      end
    end) || generate_sequence(Enum.shuffle(deck))
  end

  defp generate_pure_sequence(deck) do
    # 按花色分组
    grouped =
      Enum.group_by(deck, fn card ->
        suit = card.suit
        suit
      end)

    # 在每个花色中找顺子
    Enum.find_value(grouped, fn {_suit, cards} ->
      if length(cards) >= 3 do
        # 尝试生成顺子
        sorted =
          Enum.sort_by(cards, fn c ->
            rank = c.value
            rank
          end)

        # 找连续的3张
        sorted
        |> Enum.chunk_every(3, 1, :discard)
        |> Enum.find(fn chunk ->
          ranks =
            Enum.map(chunk, fn c ->
              r = c.value
              r
            end)

          # 检查是否连续
          case ranks do
            [a, b, c] when b == a + 1 and c == b + 1 -> true
            # A-2-3特殊情况
            [2, 3, 14] -> true
            _ -> false
          end
        end)
        |> case do
          nil -> nil
          cards -> {cards, deck -- cards}
        end
      else
        nil
      end
    end) || generate_pure_sequence(Enum.shuffle(deck))
  end

  defp generate_trail(deck) do
    # 按点数分组
    grouped =
      Enum.group_by(deck, fn card ->
        rank = card.value
        rank
      end)

    # 找有至少3张的点数
    trails = Enum.filter(grouped, fn {_, cards} -> length(cards) >= 3 end)

    if length(trails) > 0 do
      {_, cards} = Enum.random(trails)
      selected = Enum.take(cards, 3)
      {selected, deck -- selected}
    else
      # 没有三条，降级生成同花顺
      generate_pure_sequence(deck)
    end
  end

  defp get_player_gold(player, game_context) do
    # 从游戏上下文获取玩家金币
    player_id = player.numeric_id
    player_bets = Map.get(game_context, :player_total_bets, %{})
    base_gold = Map.get(player, :points, 0)
    bet_amount = Map.get(player_bets, player_id, 0)
    base_gold - bet_amount
  end

  @doc """
  检查机器人充值等待事件
  """
  def check_robot_recharge_event(_robot_id, robot_cards, bet_rounds, player_type) do
    if player_type == :new_under_80 do
      TeenPattiCardExchanger.check_robot_recharge_event(robot_cards, bet_rounds)
    else
      false
    end
  end

  @doc """
  生成冤家牌
  """
  def generate_rival_cards_for_event(player_cards, robot_count) do
    TeenPattiCardExchanger.generate_rival_cards(player_cards, robot_count)
  end
end
