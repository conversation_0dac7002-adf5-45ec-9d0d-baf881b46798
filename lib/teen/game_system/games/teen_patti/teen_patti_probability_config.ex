defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiProbabilityConfig do
  @moduledoc """
  Teen Patti 牌型概率配置模块

  根据《TP服务器算法文档.docx》实现的概率配置系统：
  - 免费玩家（新玩家、未达到80金币、待充值玩家）使用特定概率
  - 充值玩家根据幸运值动态调整概率
  - 机器人使用固定概率配置
  """

  # 牌型定义（权重方式）
  @card_types [:high_card, :pair, :color, :sequence, :pure_sequence, :trail]

  # 机器人固定概率配置（幸运值恒定500）
  @robot_probability %{
    # 60%
    high_card: 60.0,
    # 20%
    pair: 20.0,
    # 12%
    color: 12.0,
    # 7%
    sequence: 7.0,
    # 0.5%
    pure_sequence: 0.5,
    # 0.5%
    trail: 0.5
  }

  # 免费玩家概率配置
  @free_player_probabilities %{
    # 新玩家第一局
    new_player_first_game: %{
      player: %{
        high_card: 3.0,
        pair: 5.0,
        color: 1.0,
        sequence: 85.0,
        pure_sequence: 1.0,
        trail: 5.0
      },
      robot: @robot_probability
    },

    # 新玩家（金币未达到80）
    new_player_under_80: %{
      player: %{
        high_card: 54.0,
        pair: 15.0,
        color: 3.0,
        sequence: 25.0,
        pure_sequence: 1.0,
        trail: 1.0
      },
      robot: @robot_probability
    },

    # 待充值玩家（金币首次超过80）
    waiting_recharge: %{
      player: %{
        high_card: 65.0,
        pair: 24.0,
        color: 8.0,
        sequence: 3.0,
        pure_sequence: 0.0,
        trail: 0.0
      },
      robot: @robot_probability
    }
  }

  @doc """
  获取玩家的牌型概率配置

  参数：
  - player_type: 玩家类型 (:new_first, :new_under_80, :waiting_recharge, :recharge_1_3, :recharge_4_plus)
  - luck_value: 幸运值（充值玩家使用）
  - is_robot: 是否是机器人
  """
  def get_probability_config(player_type, luck_value \\ nil, is_robot \\ false)

  # 机器人始终使用固定概率
  def get_probability_config(_player_type, _luck_value, true) do
    @robot_probability
  end

  # 免费玩家类型
  def get_probability_config(:new_first, _luck_value, false) do
    @free_player_probabilities.new_player_first_game.player
  end

  def get_probability_config(:new_under_80, _luck_value, false) do
    @free_player_probabilities.new_player_under_80.player
  end

  def get_probability_config(:waiting_recharge, _luck_value, false) do
    @free_player_probabilities.waiting_recharge.player
  end

  # 充值玩家根据幸运值使用动态概率
  def get_probability_config(player_type, luck_value, false)
      when player_type in [:recharge_1_3, :recharge_4_plus] do
    convert_weights_to_probability(luck_value)
  end

  @doc """
  将权重转换为百分比概率

  幸运值对应的权重已在 TeenPattiLuckManager 中定义
  """
  def convert_weights_to_probability(luck_value) do
    alias Cypridina.Teen.GameSystem.Games.TeenPatti.TeenPattiLuckManager

    weights = TeenPattiLuckManager.get_card_probability_weights(luck_value)

    if weights do
      total_weight = weights |> Map.values() |> Enum.sum()

      weights
      |> Enum.map(fn {card_type, weight} ->
        {card_type, Float.round(weight / total_weight * 100, 2)}
      end)
      |> Enum.into(%{})
    else
      # 默认配置
      %{
        high_card: 60.0,
        pair: 21.0,
        color: 12.0,
        sequence: 8.0,
        pure_sequence: 0.5,
        trail: 0.5
      }
    end
  end

  @doc """
  根据概率配置随机选择一个牌型
  """
  def random_card_type(probability_config) do
    total = probability_config |> Map.values() |> Enum.sum()
    random_value = :rand.uniform() * total

    probability_config
    |> Enum.sort_by(fn {_type, prob} -> prob end, :desc)
    |> Enum.reduce_while({random_value, nil}, fn {card_type, prob}, {remaining, _} ->
      if remaining <= prob do
        {:halt, {0, card_type}}
      else
        {:cont, {remaining - prob, nil}}
      end
    end)
    |> elem(1)
  end

  @doc """
  获取特殊事件概率配置
  """
  def get_special_event_config do
    %{
      # 机器人充值等待触发概率（新玩家金币未达到80时）
      # 1%
      robot_recharge_trigger: 1,

      # 待充值玩家冤家牌触发概率
      # 40%
      rival_cards_trigger: 40,

      # 新玩家第一局换牌概率
      # 80%
      new_player_exchange: 80,

      # 待充值玩家换牌概率
      # 70%
      waiting_recharge_exchange: 70,

      # 充值后特殊处理概率
      recharge_special_handling: %{
        # 赢的金额 < 充值金额：100%让玩家赢
        under_1x: 100,
        # 赢的金额 >= 充值金额 < 2倍：60%让玩家赢
        between_1x_2x: 60,
        # 赢的金额 >= 2倍充值金额：30%让玩家赢
        over_2x: 30
      }
    }
  end

  @doc """
  验证概率配置是否有效（总和应该接近100%）
  """
  def validate_probability_config(config) do
    total = config |> Map.values() |> Enum.sum()

    if abs(total - 100.0) < 0.1 do
      {:ok, config}
    else
      {:error, "概率总和不等于100%，当前: #{total}%"}
    end
  end

  @doc """
  获取调试信息
  """
  def debug_info(player_type, luck_value, is_robot) do
    config = get_probability_config(player_type, luck_value, is_robot)

    %{
      player_type: player_type,
      luck_value: luck_value,
      is_robot: is_robot,
      probabilities: config,
      total: config |> Map.values() |> Enum.sum()
    }
  end
end
