defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiLuckManager do
  @moduledoc """
  Teen Patti 幸运值管理模块

  根据《幸运值方案.docx》实现的幸运值调整逻辑：
  - 幸运值仅针对充值玩家有效（-1表示未充值）
  - 幸运值范围：0-1000
  - 每局自动衰减1点
  - 根据输赢倍数分段调整
  - 特殊事件额外调整（连续输赢、极端爆炸局、大牌）
  """

  require Logger
  alias Teen.Services.LuckService

  # 幸运值常量
  @min_luck 0
  @max_luck 1000
  @default_luck 500
  @natural_decay 1
  @threshold 500

  # 幸运值调整表（根据输赢倍数）
  @luck_adjustment_table %{
    # 输赢倍数区间 => {输局增加范围, 赢局减少范围}
    {1, 5} => {{1, 2}, {1, 2}},
    {6, 10} => {{4, 6}, {3, 5}},
    {11, 20} => {{8, 12}, {6, 10}},
    {21, 40} => {{15, 25}, {10, 20}},
    {41, 70} => {{25, 35}, {15, 30}},
    {71, 100} => {{40, 55}, {30, 50}},
    {101, 200} => {{55, 70}, {40, 60}},
    {201, :infinity} => {{75, 100}, {50, 80}}
  }

  # 特殊事件配置
  @consecutive_lose_count 5
  @consecutive_win_count 3
  @extreme_bet_multiplier 300
  @extreme_win_luck 150
  @extreme_lose_luck 900

  # 牌型定义（用于特殊事件）
  # 三条
  @trail_card_type :trail

  @doc """
  计算并更新玩家幸运值

  参数：
  - player_id: 玩家ID
  - is_win: 是否赢局
  - bet_multiplier: 输赢倍数
  - card_type: 玩家牌型（用于特殊事件）
  - consecutive_results: 连续输赢记录
  - is_priority_adjustment: 是否为优先幸运值调整（TP服务器算法文档2-1-d）
  """
  def update_player_luck(
        player_id,
        is_win,
        bet_multiplier,
        card_type \\ nil,
        consecutive_results \\ [],
        is_priority_adjustment \\ false
      ) do
    # 获取当前幸运值（player_id可以是UUID或numeric_id）
    {:ok, current_luck} = LuckService.get_user_luck(player_id)

    # 未充值玩家(-1)不调整幸运值
    if current_luck == -1 do
      Logger.debug("🍀 [LUCK] 玩家#{player_id}未充值，不调整幸运值")
      {:ok, -1}
    else
      # 如果是优先幸运值调整，直接返回不做处理
      if is_priority_adjustment do
        Logger.info("🍀 [LUCK] 玩家#{player_id}触发优先幸运值调整规则，本局不执行常规调整")
        {:ok, current_luck}
      else
        # 计算新幸运值
        new_luck =
          calculate_new_luck(current_luck, is_win, bet_multiplier, card_type, consecutive_results)

        # 更新数据库
        case LuckService.admin_reset_luck(player_id, new_luck) do
          {:ok, _} ->
            Logger.info(
              "🍀 [LUCK] 玩家#{player_id}幸运值更新: #{current_luck} → #{new_luck} (#{if is_win, do: "赢", else: "输"}#{bet_multiplier}倍)"
            )

            {:ok, new_luck}

          {:error, reason} ->
            Logger.error("🍀 [LUCK] 玩家#{player_id}幸运值更新失败: #{inspect(reason)}")
            {:error, reason}
        end
      end
    end
  end

  @doc """
  根据充值状态更新玩家幸运值（特殊规则）
  """
  def update_luck_on_recharge(player_id, recharge_count, total_recharge_amount, withdrawal_amount) do
    # 计算总提充比
    total_withdraw_ratio = calculate_withdraw_ratio(total_recharge_amount, withdrawal_amount)

    new_luck =
      case recharge_count do
        1 -> handle_1_recharge_player(total_withdraw_ratio)
        2 -> handle_2_recharge_player(total_withdraw_ratio)
        3 -> handle_3_recharge_player(total_withdraw_ratio)
        # 4充及以上玩家充值后设为750
        _ when recharge_count >= 4 -> 750
        _ -> @default_luck
      end

    # 更新幸运值
    case LuckService.admin_reset_luck(player_id, new_luck) do
      {:ok, _} ->
        Logger.info(
          "🍀 [LUCK] 玩家#{player_id}充值后幸运值更新为: #{new_luck} (充值次数:#{recharge_count}, 提充比:#{total_withdraw_ratio})"
        )

        {:ok, new_luck}

      {:error, reason} ->
        Logger.error("🍀 [LUCK] 玩家#{player_id}充值后幸运值更新失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  检查并处理急救状态
  """
  def check_emergency_status(player_id, recharge_count, total_recharge_amount, withdrawal_amount) do
    total_withdraw_ratio = calculate_withdraw_ratio(total_recharge_amount, withdrawal_amount)

    # 1-3充玩家提充比≤0.2时进入急救状态
    if recharge_count >= 1 and recharge_count <= 3 and total_withdraw_ratio <= 0.2 do
      case LuckService.admin_reset_luck(player_id, 750) do
        {:ok, _} ->
          Logger.info("🚨 [LUCK] 玩家#{player_id}进入急救状态，幸运值设为750")
          {:ok, :emergency}

        {:error, reason} ->
          {:error, reason}
      end
    else
      {:ok, :normal}
    end
  end

  # 私有函数

  defp calculate_new_luck(current_luck, is_win, bet_multiplier, card_type, consecutive_results) do
    # 1. 基础调整
    base_adjustment = get_base_adjustment(is_win, bet_multiplier, current_luck)

    # 2. 自然衰减
    after_decay = current_luck - @natural_decay + base_adjustment

    # 3. 特殊事件调整（优先级高于基础调整）
    after_special =
      apply_special_events(after_decay, is_win, bet_multiplier, card_type, consecutive_results)

    # 4. 确保在范围内
    after_special
    |> max(@min_luck)
    |> min(@max_luck)
  end

  defp get_base_adjustment(is_win, bet_multiplier, current_luck) do
    # 找到对应的倍数区间
    {win_range, lose_range} = find_adjustment_range(bet_multiplier)

    if is_win do
      # 赢局减少幸运值
      -random_in_range(win_range)
    else
      # 输局增加幸运值
      adjustment = random_in_range(lose_range)

      # 超过阈值时边际递减
      if current_luck >= @threshold do
        # 递减30%
        round(adjustment * 0.7)
      else
        adjustment
      end
    end
  end

  defp find_adjustment_range(bet_multiplier) do
    @luck_adjustment_table
    |> Enum.find(fn {{min, max}, _} ->
      if max == :infinity do
        bet_multiplier >= min
      else
        bet_multiplier >= min and bet_multiplier <= max
      end
    end)
    |> case do
      {_, ranges} -> ranges
      # 默认值
      nil -> {{1, 2}, {1, 2}}
    end
  end

  defp apply_special_events(luck_value, is_win, bet_multiplier, card_type, consecutive_results) do
    luck_value
    |> handle_consecutive_results(consecutive_results)
    |> handle_extreme_bet(is_win, bet_multiplier)
    |> handle_special_cards(card_type)
  end

  defp handle_consecutive_results(luck_value, consecutive_results) do
    lose_count = count_consecutive(:lose, consecutive_results)
    win_count = count_consecutive(:win, consecutive_results)

    cond do
      # 连续输5局
      lose_count >= @consecutive_lose_count ->
        adjustment = random_in_range({3, 5})
        Logger.info("🍀 [SPECIAL] 连续输#{lose_count}局，额外增加幸运值: +#{adjustment}")
        luck_value + adjustment

      # 连续赢3局
      win_count >= @consecutive_win_count ->
        adjustment = random_in_range({5, 10})
        Logger.info("🍀 [SPECIAL] 连续赢#{win_count}局，额外减少幸运值: -#{adjustment}")
        luck_value - adjustment

      true ->
        luck_value
    end
  end

  defp handle_extreme_bet(luck_value, is_win, bet_multiplier) do
    if bet_multiplier >= @extreme_bet_multiplier do
      if is_win do
        Logger.info("🍀 [SPECIAL] 极端爆炸局赢#{bet_multiplier}倍，幸运值直接设为#{@extreme_win_luck}")
        @extreme_win_luck
      else
        Logger.info("🍀 [SPECIAL] 极端爆炸局输#{bet_multiplier}倍，幸运值直接设为#{@extreme_lose_luck}")
        @extreme_lose_luck
      end
    else
      luck_value
    end
  end

  defp handle_special_cards(luck_value, card_type) do
    if card_type == @trail_card_type do
      adjustment = random_in_range({5, 10})
      Logger.info("🍀 [SPECIAL] 拿到三条，额外减少幸运值: -#{adjustment}")
      luck_value - adjustment
    else
      luck_value
    end
  end

  defp count_consecutive(result_type, results) do
    results
    |> Enum.take_while(&(&1 == result_type))
    |> length()
  end

  defp random_in_range({min, max}) do
    min + :rand.uniform(max - min + 1) - 1
  end

  defp calculate_withdraw_ratio(total_recharge, total_withdraw) do
    if total_recharge > 0 do
      Float.round(total_withdraw / total_recharge, 2)
    else
      0.0
    end
  end

  defp handle_1_recharge_player(withdraw_ratio) do
    cond do
      withdraw_ratio >= 1.98 -> 100
      withdraw_ratio >= 1.55 -> 300
      true -> 750
    end
  end

  defp handle_2_recharge_player(withdraw_ratio) do
    cond do
      withdraw_ratio >= 2.22 -> 100
      withdraw_ratio >= 1.75 -> 300
      true -> 750
    end
  end

  defp handle_3_recharge_player(withdraw_ratio) do
    cond do
      withdraw_ratio >= 2.6 -> 100
      withdraw_ratio >= 2.0 -> 300
      true -> 750
    end
  end

  @doc """
  获取玩家幸运值对应的牌型概率权重

  根据幸运值范围返回不同的牌型概率配置
  """
  def get_card_probability_weights(luck_value) when luck_value == -1 do
    # 未充值玩家使用特殊配置
    nil
  end

  def get_card_probability_weights(luck_value) do
    cond do
      luck_value >= 850 and luck_value <= 1000 ->
        # 好运爆棚
        %{
          high_card: 400,
          pair: 250,
          color: 150,
          sequence: 150,
          pure_sequence: 30,
          trail: 20
        }

      luck_value >= 650 and luck_value <= 849 ->
        # 小有手气
        %{
          high_card: 500,
          pair: 230,
          color: 130,
          sequence: 120,
          pure_sequence: 15,
          trail: 5
        }

      luck_value >= 400 and luck_value <= 649 ->
        # 手气平稳
        %{
          high_card: 600,
          pair: 210,
          color: 120,
          sequence: 80,
          pure_sequence: 5,
          trail: 5
        }

      luck_value >= 200 and luck_value <= 399 ->
        # 有点背
        %{
          high_card: 720,
          pair: 150,
          color: 80,
          sequence: 45,
          pure_sequence: 3,
          trail: 0
        }

      luck_value >= 1 and luck_value <= 199 ->
        # 霉运缠身
        %{
          high_card: 820,
          pair: 100,
          color: 50,
          sequence: 25,
          pure_sequence: 0,
          trail: 0
        }

      true ->
        # 默认配置（幸运值500）
        %{
          high_card: 600,
          pair: 210,
          color: 120,
          sequence: 80,
          pure_sequence: 5,
          trail: 5
        }
    end
  end
end
