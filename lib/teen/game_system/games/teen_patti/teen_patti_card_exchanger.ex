defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiCardExchanger do
  @moduledoc """
  Teen Patti 换牌策略模块

  根据《TP服务器算法文档.docx》实现的换牌逻辑：
  - 新玩家第一局特殊换牌
  - 新玩家金币未达到80的换牌策略
  - 待充值玩家的换牌策略
  - 充值玩家的特殊事件处理
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.TeenPatti.{TeenPattiLogic, TeenPattiProbabilityConfig}

  @doc """
  应用换牌策略

  根据玩家类型和当前牌局情况决定是否换牌
  """
  def apply_exchange_strategy(cards_map, players, game_context) do
    # 获取真实玩家和机器人
    {real_players, robots} = Enum.split_with(players, fn p -> !Map.get(p, :is_robot, false) end)

    # 对每个真实玩家应用换牌策略
    Enum.reduce(real_players, cards_map, fn player, acc_cards ->
      player_cards = Map.get(acc_cards, player.numeric_id)
      player_type = Map.get(player, :player_type, :unknown)

      case apply_player_exchange(player, player_cards, acc_cards, player_type, game_context) do
        {:exchange, new_cards_map} ->
          Logger.info("🔄 [EXCHANGER] 玩家#{player.numeric_id}触发换牌")
          new_cards_map

        {:no_exchange, _} ->
          acc_cards
      end
    end)
  end

  @doc """
  新玩家第一局换牌策略
  """
  def apply_new_player_first_game_exchange(player_cards, all_cards, player_id) do
    player_card_type = TeenPattiLogic.calculate_card_type(player_cards)

    # 找出场上所有牌型
    all_player_cards = all_cards |> Map.to_list()

    case player_card_type do
      card_type when card_type in [:high_card, :pair, :color] ->
        # 非顺子牌型的处理
        handle_non_sequence_exchange(player_id, player_cards, all_player_cards)

      :sequence ->
        # 顺子牌型的处理
        handle_sequence_exchange(player_id, player_cards, all_player_cards)

      card_type when card_type in [:pure_sequence, :trail] ->
        # 同花顺或三条不换牌
        Logger.info("🔄 [NEW_FIRST] 玩家拿到#{TeenPattiLogic.get_card_type_name(card_type)}，不换牌")
        {:no_exchange, nil}
    end
  end

  @doc """
  新玩家（金币未达到80）换牌策略
  """
  def apply_new_player_under_80_exchange(player_cards, all_cards, player_id) do
    player_card_type = TeenPattiLogic.calculate_card_type(player_cards)

    if player_card_type in [:sequence, :pure_sequence, :trail] do
      # 检查是否是最大牌
      if is_biggest_hand?(player_cards, all_cards, player_id) do
        {:no_exchange, nil}
      else
        # 70%概率换成最大牌
        if :rand.uniform(100) <= 70 do
          biggest_hand = find_biggest_hand(all_cards)

          Logger.info(
            "🔄 [UNDER_80] 玩家拿到#{TeenPattiLogic.get_card_type_name(player_card_type)}但不是最大，换成最大牌"
          )

          {:exchange, exchange_cards(all_cards, player_id, biggest_hand.player_id)}
        else
          {:no_exchange, nil}
        end
      end
    else
      {:no_exchange, nil}
    end
  end

  @doc """
  待充值玩家换牌策略
  """
  def apply_waiting_recharge_exchange(player_cards, all_cards, player_id) do
    # 检查是否是最大牌
    if is_biggest_hand?(player_cards, all_cards, player_id) do
      # 70%概率与第二大牌交换
      if :rand.uniform(100) <= 70 do
        second_biggest = find_second_biggest_hand(all_cards, player_id)

        if second_biggest do
          Logger.info("🔄 [WAITING_RECHARGE] 玩家是最大牌，与第二大牌交换")
          {:exchange, exchange_cards(all_cards, player_id, second_biggest.player_id)}
        else
          {:no_exchange, nil}
        end
      else
        {:no_exchange, nil}
      end
    else
      {:no_exchange, nil}
    end
  end

  @doc """
  充值后的特殊换牌处理

  根据玩家充值金额和可能赢得的金额决定是否让玩家赢
  """
  def apply_recharge_special_exchange(
        player_cards,
        all_cards,
        player_id,
        recharge_amount,
        pot_total,
        player_bet
      ) do
    # 计算玩家可能赢得的净金额
    win_amount = pot_total - player_bet

    # 获取特殊处理概率
    special_config = TeenPattiProbabilityConfig.get_special_event_config()

    win_probability =
      calculate_recharge_win_probability(win_amount, recharge_amount, special_config)

    # 根据概率决定是否让玩家赢
    if :rand.uniform(100) <= win_probability do
      ensure_player_wins(player_cards, all_cards, player_id)
    else
      ensure_player_loses(player_cards, all_cards, player_id)
    end
  end

  # 私有函数

  defp apply_player_exchange(player, player_cards, all_cards, player_type, game_context) do
    case player_type do
      :new_first ->
        apply_new_player_first_game_exchange(player_cards, all_cards, player.numeric_id)

      :new_under_80 ->
        apply_new_player_under_80_exchange(player_cards, all_cards, player.numeric_id)

      :waiting_recharge ->
        apply_waiting_recharge_exchange(player_cards, all_cards, player.numeric_id)

      _ ->
        {:no_exchange, nil}
    end
  end

  defp handle_non_sequence_exchange(player_id, player_cards, all_player_cards) do
    # 检查是否有其他玩家拿到顺子、同花顺或三条
    strong_hands = find_strong_hands(all_player_cards)

    if length(strong_hands) > 0 do
      # 将最大的牌换给玩家
      biggest_hand =
        Enum.max_by(strong_hands, fn {_, cards} ->
          TeenPattiLogic.calculate_hand_strength(cards)
        end)

      {biggest_id, _} = biggest_hand
      Logger.info("🔄 [NEW_FIRST] 玩家拿到非顺子，将最大牌换给玩家")
      {:exchange, exchange_cards(Enum.into(all_player_cards, %{}), player_id, biggest_id)}
    else
      {:no_exchange, nil}
    end
  end

  defp handle_sequence_exchange(player_id, player_cards, all_player_cards) do
    # 检查是否有其他玩家拿到同花顺或三条
    stronger_hands =
      Enum.filter(all_player_cards, fn {id, cards} ->
        id != player_id and TeenPattiLogic.calculate_card_type(cards) in [:pure_sequence, :trail]
      end)

    if length(stronger_hands) > 0 do
      # 将最大的牌换给玩家
      {biggest_id, _} =
        Enum.max_by(stronger_hands, fn {_, cards} ->
          TeenPattiLogic.calculate_hand_strength(cards)
        end)

      Logger.info("🔄 [NEW_FIRST] 玩家拿到顺子，但有更大牌型，换成最大牌")
      {:exchange, exchange_cards(Enum.into(all_player_cards, %{}), player_id, biggest_id)}
    else
      # 检查是否有更大的顺子
      bigger_sequences =
        Enum.filter(all_player_cards, fn {id, cards} ->
          id != player_id and
            TeenPattiLogic.calculate_card_type(cards) == :sequence and
            TeenPattiLogic.compare_hands(cards, player_cards) > 0
        end)

      if length(bigger_sequences) > 0 and :rand.uniform(100) <= 80 do
        {biggest_id, _} =
          Enum.max_by(bigger_sequences, fn {_, cards} ->
            TeenPattiLogic.calculate_hand_strength(cards)
          end)

        Logger.info("🔄 [NEW_FIRST] 玩家拿到顺子，80%概率换成最大顺子")
        {:exchange, exchange_cards(Enum.into(all_player_cards, %{}), player_id, biggest_id)}
      else
        {:no_exchange, nil}
      end
    end
  end

  defp find_strong_hands(all_player_cards) do
    Enum.filter(all_player_cards, fn {_, cards} ->
      TeenPattiLogic.calculate_card_type(cards) in [:sequence, :pure_sequence, :trail]
    end)
  end

  defp is_biggest_hand?(player_cards, all_cards, player_id) do
    Enum.all?(all_cards, fn {id, cards} ->
      id == player_id or TeenPattiLogic.compare_hands(player_cards, cards) >= 0
    end)
  end

  defp find_biggest_hand(all_cards) do
    all_cards
    |> Map.to_list()
    |> Enum.max_by(fn {_, cards} ->
      TeenPattiLogic.calculate_hand_strength(cards)
    end)
    |> then(fn {id, cards} -> %{player_id: id, cards: cards} end)
  end

  defp find_second_biggest_hand(all_cards, exclude_id) do
    all_cards
    |> Map.to_list()
    |> Enum.reject(fn {id, _} -> id == exclude_id end)
    |> case do
      [] ->
        nil

      hands ->
        {id, cards} =
          Enum.max_by(hands, fn {_, cards} ->
            TeenPattiLogic.calculate_hand_strength(cards)
          end)

        %{player_id: id, cards: cards}
    end
  end

  defp exchange_cards(all_cards, player1_id, player2_id) do
    player1_cards = Map.get(all_cards, player1_id)
    player2_cards = Map.get(all_cards, player2_id)

    all_cards
    |> Map.put(player1_id, player2_cards)
    |> Map.put(player2_id, player1_cards)
  end

  defp calculate_recharge_win_probability(win_amount, recharge_amount, special_config) do
    recharge_config = special_config.recharge_special_handling

    cond do
      win_amount < recharge_amount ->
        recharge_config.under_1x

      win_amount >= recharge_amount and win_amount < recharge_amount * 2 ->
        recharge_config.between_1x_2x

      true ->
        recharge_config.over_2x
    end
  end

  defp ensure_player_wins(player_cards, all_cards, player_id) do
    if is_biggest_hand?(player_cards, all_cards, player_id) do
      {:no_exchange, nil}
    else
      # 找到最大牌并交换
      biggest = find_biggest_hand(all_cards)

      if biggest.player_id != player_id do
        Logger.info("🔄 [RECHARGE_SPECIAL] 确保玩家赢，换成最大牌")
        {:exchange, exchange_cards(all_cards, player_id, biggest.player_id)}
      else
        {:no_exchange, nil}
      end
    end
  end

  defp ensure_player_loses(player_cards, all_cards, player_id) do
    if is_biggest_hand?(player_cards, all_cards, player_id) do
      # 玩家是最大牌，需要换成较小的牌
      second_biggest = find_second_biggest_hand(all_cards, player_id)

      if second_biggest do
        Logger.info("🔄 [RECHARGE_SPECIAL] 确保玩家输，与第二大牌交换")
        {:exchange, exchange_cards(all_cards, player_id, second_biggest.player_id)}
      else
        {:no_exchange, nil}
      end
    else
      {:no_exchange, nil}
    end
  end

  @doc """
  处理玩家弃牌后的特殊换牌

  新玩家拿到同花以上的牌选择弃牌后，如果出现最后两个机器人比牌，
  80%概率将两个机器人的牌换得比玩家小
  """
  def handle_fold_special_exchange(player_cards, robot1_id, robot2_id, robot1_cards, robot2_cards) do
    player_card_type = TeenPattiLogic.calculate_card_type(player_cards)

    if player_card_type in [:color, :sequence, :pure_sequence, :trail] do
      # 检查玩家是否不是最大牌
      player_strength = TeenPattiLogic.calculate_hand_strength(player_cards)
      robot1_strength = TeenPattiLogic.calculate_hand_strength(robot1_cards)
      robot2_strength = TeenPattiLogic.calculate_hand_strength(robot2_cards)

      if player_strength < robot1_strength or player_strength < robot2_strength do
        # 80%概率执行换牌
        if :rand.uniform(100) <= 80 do
          Logger.info("🔄 [FOLD_SPECIAL] 玩家弃牌后触发特殊换牌，确保机器人牌比玩家小")

          # 生成比玩家小的牌
          new_robot1_cards = generate_smaller_cards(player_cards)
          new_robot2_cards = generate_smaller_cards(player_cards)

          %{
            robot1_id => new_robot1_cards,
            robot2_id => new_robot2_cards
          }
        else
          nil
        end
      else
        nil
      end
    else
      nil
    end
  end

  defp generate_smaller_cards(_target_cards) do
    # 生成比目标牌小的高牌（不超过10）
    deck = TeenPattiLogic.generate_deck()

    # 过滤出点数不超过10的牌
    small_cards =
      Enum.filter(deck, fn card ->
        rank = card.value
        rank <= 10
      end)

    # 随机选3张
    Enum.take_random(small_cards, 3)
  end

  @doc """
  检查机器人是否触发充值等待事件

  新玩家（金币未达到80）时，当机器人拿到顺子及以上牌型且下注到第n轮，有1%概率触发
  """
  def check_robot_recharge_event(robot_cards, bet_rounds) do
    card_type = TeenPattiLogic.calculate_card_type(robot_cards)

    # 只有顺子及以上牌型才可能触发
    if card_type in [:sequence, :pure_sequence, :trail] && bet_rounds >= 3 do
      # 1%概率触发
      :rand.uniform(100) == 1
    else
      false
    end
  end

  @doc """
  生成冤家牌
  """
  def generate_rival_cards(player_cards, robot_count) do
    # 根据玩家牌型生成合适的冤家牌组合
    player_type = TeenPattiLogic.calculate_card_type(player_cards)

    case player_type do
      :high_card ->
        # 玩家是高牌，给机器人发对子
        generate_multiple_hands([:pair], robot_count)

      :pair ->
        # 玩家是对子，给机器人发同花或顺子
        generate_multiple_hands([:color, :sequence], robot_count)

      :color ->
        # 玩家是同花，给机器人发顺子
        generate_multiple_hands([:sequence, :pure_sequence], robot_count)

      :sequence ->
        # 玩家是顺子，给机器人发同花顺或三条
        generate_multiple_hands([:pure_sequence, :trail], robot_count)

      _ ->
        # 其他情况，给机器人发高牌
        generate_multiple_hands([:high_card], robot_count)
    end
  end

  defp generate_multiple_hands(card_types, count) do
    deck = TeenPattiLogic.generate_deck() |> TeenPattiLogic.shuffle_deck()

    Enum.reduce(1..count, {[], deck}, fn _, {hands, remaining_deck} ->
      card_type = Enum.random(card_types)
      {hand, new_deck} = generate_specific_type(card_type, remaining_deck)
      {[hand | hands], new_deck}
    end)
    |> elem(0)
    |> Enum.reverse()
  end

  defp generate_specific_type(card_type, deck) do
    # 这里可以复用 TeenPattiDealerV2 中的生成函数
    # 或者实现简单版本
    case card_type do
      :high_card -> {Enum.take(deck, 3), Enum.drop(deck, 3)}
      :pair -> generate_pair_from_deck(deck)
      :color -> generate_color_from_deck(deck)
      :sequence -> generate_sequence_from_deck(deck)
      :pure_sequence -> generate_pure_sequence_from_deck(deck)
      :trail -> generate_trail_from_deck(deck)
      _ -> {Enum.take(deck, 3), Enum.drop(deck, 3)}
    end
  end

  # 简单实现各种牌型生成
  defp generate_pair_from_deck(deck) do
    # 简化实现：取前3张牌
    {Enum.take(deck, 3), Enum.drop(deck, 3)}
  end

  defp generate_color_from_deck(deck) do
    {Enum.take(deck, 3), Enum.drop(deck, 3)}
  end

  defp generate_sequence_from_deck(deck) do
    {Enum.take(deck, 3), Enum.drop(deck, 3)}
  end

  defp generate_pure_sequence_from_deck(deck) do
    {Enum.take(deck, 3), Enum.drop(deck, 3)}
  end

  defp generate_trail_from_deck(deck) do
    {Enum.take(deck, 3), Enum.drop(deck, 3)}
  end
end
