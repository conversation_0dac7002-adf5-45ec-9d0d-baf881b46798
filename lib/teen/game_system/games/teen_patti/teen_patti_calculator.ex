defmodule <PERSON>pridina.Teen.GameSystem.Games.TeenPatti.TeenPattiCalculator do
  @moduledoc """
  Teen Patti 纯计算模块

  所有函数都是纯函数，只做计算，不修改任何数据
  Room模块调用这些函数获取计算结果，然后自己更新数据
  """

  require Logger

  # 默认配置
  @default_config %{
    default_luck_value: 500,
    luck_min: 100,
    luck_max: 900
  }

  # ========================================
  # 连续输赢计算
  # ========================================

  @doc """
  计算新的连续输赢状态

  参数:
  - current_streak: 当前连续状态
  - is_win: 是否获胜

  返回: 新的连续状态
  """
  def calculate_consecutive_streak(current_streak, is_win) do
    new_streak =
      cond do
        # 当前是赢，之前是输（负数）-> 先重置为0，再+1
        is_win and current_streak < 0 -> 1
        # 当前是赢，之前也是赢（正数或0）-> 继续+1
        is_win -> current_streak + 1
        # 当前是输，之前是赢（正数）-> 先重置为0，再-1
        not is_win and current_streak > 0 -> -1
        # 当前是输，之前也是输（负数或0）-> 继续-1
        not is_win -> current_streak - 1
      end

    # 检查是否需要重置
    final_streak =
      cond do
        # 连续赢3局 -> 重置
        new_streak >= 3 ->
          Logger.info("🎯 [STREAK_RESET] 连续赢3局，重置连续状态")
          0

        # 连续输5局 -> 重置
        new_streak <= -5 ->
          Logger.info("🎯 [STREAK_RESET] 连续输5局，重置连续状态")
          0

        # 其他情况保持不变
        true ->
          new_streak
      end

    Logger.info("🎯 [STREAK_CALC] 连续状态计算: #{current_streak} -> #{new_streak} -> #{final_streak}")
    final_streak
  end

  @doc """
  检查连续输赢是否触发幸运值调整

  参数:
  - streak: 连续状态
  - current_luck: 当前幸运值

  返回: {new_luck, reason} 或 nil
  """
  def check_consecutive_luck_adjustment(streak, current_luck) do
    cond do
      # 连续输5局：幸运值 (+3) ~ (+5)
      streak <= -5 ->
        adjustment = Enum.random(3..5)
        new_luck = min(@default_config.luck_max, current_luck + adjustment)
        {new_luck, "连续输5局(+#{adjustment})"}

      # 连续赢3局：幸运值 (-5) ~ (-10)
      streak >= 3 ->
        adjustment = Enum.random(5..10)
        new_luck = max(@default_config.luck_min, current_luck - adjustment)
        {new_luck, "连续赢3局(-#{adjustment})"}

      true ->
        nil
    end
  end

  # ========================================
  # 幸运值调整计算
  # ========================================

  @doc """
  计算普通输赢的幸运值调整

  参数:
  - win_lose_multiplier: 输赢倍数
  - is_win: 是否获胜

  返回: 调整值
  """
  def calculate_normal_luck_adjustment(win_lose_multiplier, _is_win) do
    # 根据倍数计算调整值
    cond do
      win_lose_multiplier >= 100 -> 5
      win_lose_multiplier >= 50 -> 4
      win_lose_multiplier >= 20 -> 3
      win_lose_multiplier >= 10 -> 2
      true -> 1
    end
  end

  @doc """
  检查是否为极端爆炸局

  参数:
  - actual_multiplier: 实际倍数
  - is_win: 是否获胜

  返回: {new_luck, reason} 或 nil
  """
  def check_extreme_explosion(actual_multiplier, is_win) do
    if actual_multiplier > 300 do
      if is_win do
        {150, "极端爆炸局获胜(>300倍)"}
      else
        {900, "极端爆炸局失败(>300倍)"}
      end
    else
      nil
    end
  end

  @doc """
  应用幸运值衰减（向500靠拢）

  参数:
  - current_luck: 当前幸运值

  返回: 衰减后的幸运值
  """
  def apply_luck_decay(current_luck) do
    cond do
      current_luck > 500 -> max(500, current_luck - 1)
      current_luck < 500 -> min(500, current_luck + 1)
      true -> current_luck
    end
  end

  # ========================================
  # 综合计算函数
  # ========================================

  @doc """
  计算完整的幸运值和连续状态调整

  参数:
  - current_luck: 当前幸运值
  - consecutive_streak: 当前连续状态
  - win_lose_multiplier: 输赢倍数
  - is_win: 是否获胜
  - base_bet: 底分
  - is_charged_player: 是否充值玩家
  - withdraw_ratio_adjusted: 是否已被提充比调整

  返回: %{
    new_luck_value: 新幸运值,
    new_consecutive_streak: 新连续状态,
    adjustment_reason: 调整原因,
    should_reset_flags: 是否重置标记
  }
  """
  def calculate_settlement_adjustment(
        current_luck,
        consecutive_streak,
        win_lose_multiplier,
        is_win,
        base_bet,
        is_charged_player,
        withdraw_ratio_adjusted \\ false
      ) do
    Logger.info(
      "🎯 [SETTLEMENT_CALC] 开始计算结算调整 - 幸运值:#{current_luck}, 连续:#{consecutive_streak}, 倍数:#{win_lose_multiplier}, 胜利:#{is_win}"
    )

    # 🎯 优先级1：如果当局已由提充比调整幸运值，不再进行其他调整
    if withdraw_ratio_adjusted do
      Logger.info("🎯 [PRIORITY] 当局已由提充比调整幸运值，跳过其他调整")

      # 只更新连续输赢状态
      new_consecutive_streak = calculate_consecutive_streak(consecutive_streak, is_win)

      %{
        new_luck_value: current_luck,
        new_consecutive_streak: new_consecutive_streak,
        adjustment_reason: "提充比已调整，跳过",
        should_reset_flags: true
      }
    else
      # 只对充值玩家调整幸运值
      if not is_charged_player do
        Logger.info("🆓 [FREE_PLAYER] 免费玩家，只更新连续输赢记录")

        # 免费玩家也要更新连续输赢记录，但不调整幸运值
        new_consecutive_streak = calculate_consecutive_streak(consecutive_streak, is_win)

        %{
          new_luck_value: current_luck,
          new_consecutive_streak: new_consecutive_streak,
          adjustment_reason: "免费玩家，不调整幸运值",
          should_reset_flags: false
        }
      else
        Logger.info("💰 [CHARGED_PLAYER] 充值玩家，进行完整幸运值调整")

        # 充值玩家进行完整的幸运值调整计算
        calculate_charged_player_adjustment(
          current_luck,
          consecutive_streak,
          win_lose_multiplier,
          is_win,
          base_bet
        )
      end
    end
  end

  # 计算充值玩家的完整调整
  defp calculate_charged_player_adjustment(
         current_luck,
         consecutive_streak,
         win_lose_multiplier,
         is_win,
         base_bet
       ) do
    # 计算实际倍数（输赢金额 / 底分）
    actual_multiplier = win_lose_multiplier / base_bet

    # 🎯 优先级2：极端爆炸局判断（>300倍）
    extreme_adjustment = check_extreme_explosion(actual_multiplier, is_win)

    if extreme_adjustment do
      {new_luck, reason} = extreme_adjustment
      Logger.info("💥 [EXTREME_EXPLOSION] #{reason}，幸运值直接设为 #{new_luck}")

      # 极端爆炸局后不重置连续输赢记录
      %{
        new_luck_value: new_luck,
        # 保持不变
        new_consecutive_streak: consecutive_streak,
        adjustment_reason: reason,
        should_reset_flags: true
      }
    else
      # 🎯 优先级3：更新连续输赢记录并检查连续条件
      new_consecutive_streak = calculate_consecutive_streak(consecutive_streak, is_win)

      consecutive_adjustment =
        check_consecutive_luck_adjustment(new_consecutive_streak, current_luck)

      if consecutive_adjustment do
        {new_luck, reason} = consecutive_adjustment
        Logger.info("🔄 [CONSECUTIVE] #{reason}，幸运值调整为 #{new_luck}")

        # 连续条件触发后重置记录
        %{
          new_luck_value: new_luck,
          # 重置
          new_consecutive_streak: 0,
          adjustment_reason: reason,
          should_reset_flags: true
        }
      else
        # 🎯 优先级4：普通输赢倍数调整
        adjustment = calculate_normal_luck_adjustment(win_lose_multiplier, is_win)

        new_luck =
          if is_win do
            max(@default_config.luck_min, current_luck - adjustment)
          else
            min(@default_config.luck_max, current_luck + adjustment)
          end

        # 每局自动衰减1点（向500靠拢）
        new_luck = apply_luck_decay(new_luck)

        Logger.info(
          "🎯 [NORMAL_ADJUSTMENT] 普通调整: #{current_luck} #{if is_win, do: "-", else: "+"}#{adjustment} = #{current_luck + if is_win, do: -adjustment, else: adjustment}, 衰减后: #{new_luck}"
        )

        %{
          new_luck_value: new_luck,
          new_consecutive_streak: new_consecutive_streak,
          adjustment_reason: "普通倍数调整(#{if is_win, do: "-", else: "+"}#{adjustment})",
          should_reset_flags: true
        }
      end
    end
  end

  # ========================================
  # 充值相关计算
  # ========================================

  @doc """
  根据充值次数计算初始幸运值（简单规则）

  参数:
  - recharge_count: 充值次数

  返回: 幸运值
  """
  def calculate_recharge_luck_value(recharge_count) do
    case recharge_count do
      # 首充
      1 -> 750
      # 二充
      2 -> 750
      # 三充
      3 -> 750
      # 其他
      _ -> 500
    end
  end

  @doc """
  根据提充比计算幸运值（复杂规则）

  参数:
  - recharge_count: 充值次数
  - withdraw_ratio: 提充比

  返回: 幸运值
  """
  def calculate_luck_by_withdraw_ratio(recharge_count, withdraw_ratio) do
    case recharge_count do
      1 ->
        cond do
          withdraw_ratio >= 1.98 -> 100
          withdraw_ratio >= 1.55 -> 300
          true -> 500
        end

      2 ->
        cond do
          withdraw_ratio >= 2.22 -> 100
          withdraw_ratio >= 1.75 -> 300
          true -> 500
        end

      3 ->
        cond do
          withdraw_ratio >= 2.6 -> 100
          withdraw_ratio >= 2.0 -> 300
          true -> 500
        end

      _ ->
        500
    end
  end

  # ========================================
  # 紧急救援状态计算
  # ========================================

  @doc """
  检查紧急救援状态

  参数:
  - withdraw_ratio: 提充比
  - current_emergency_status: 当前紧急救援状态 (0=没有, 1=拥有一次, 2=正在使用)

  返回: {new_status, new_luck, reason} 或 nil
  """
  def check_emergency_rescue_status(withdraw_ratio, current_emergency_status) do
    cond do
      # 提充比 < 0.2 且未在使用紧急救援 -> 触发紧急救援
      withdraw_ratio < 0.2 and current_emergency_status != 2 ->
        {2, 750, "紧急救援触发(提充比<0.2)"}

      # 提充比 >= 0.3 且正在使用紧急救援 -> 退出紧急救援
      withdraw_ratio >= 0.3 and current_emergency_status == 2 ->
        {0, nil, "紧急救援退出(提充比>=0.3)"}

      # 其他情况不变
      true ->
        nil
    end
  end

  @doc """
  检查是否可以使用紧急救援

  参数:
  - emergency_status: 紧急救援状态

  返回: boolean
  """
  def can_use_emergency_rescue?(emergency_status) do
    emergency_status == 1 or emergency_status == 2
  end

  # ========================================
  # 玩家类型计算
  # ========================================

  @doc """
  计算玩家类型

  参数:
  - recharge_count: 充值次数
  - current_coins: 当前积分
  - total_games: 总游戏次数
  - last_online_time: 最后在线时间（可选，用于判断回头玩家）

  返回: 玩家类型原子
  """
  def calculate_player_type(recharge_count, current_coins, total_games, last_online_time \\ nil) do
    cond do
      # 🎯 优先级1：根据充值次数判断
      recharge_count >= 4 -> :charged_4_plus
      recharge_count == 3 -> :charged_3
      recharge_count == 2 -> :charged_2
      recharge_count == 1 -> :charged_1
      # 🎯 优先级2：回头玩家判断（超过3天未上线）
      is_returning_player?(last_online_time) -> :returning
      # 🎯 优先级3：新玩家判断
      # 新玩家
      total_games == 0 -> :new
      # 🎯 优先级4：根据金币判断
      # 待充值玩家（金币少于80）*100
      current_coins >= 8000 -> :waiting_recharge
      # 玩家金币没有达80*100
      current_coins < 8000 -> :new_never_80
      # 🎯 默认：免费玩家
      true -> :free
    end
  end

  # 检查是否为回头玩家（超过3天未上线）
  defp is_returning_player?(last_online_time) do
    case last_online_time do
      nil ->
        false

      time ->
        # 计算距离现在的天数
        days_offline = DateTime.diff(DateTime.utc_now(), time, :day)
        days_offline > 3
    end
  end

  @doc """
  检查是否为充值玩家

  参数:
  - recharge_count: 充值次数

  返回: boolean
  """
  def is_charged_player?(recharge_count) do
    recharge_count > 0
  end
end
