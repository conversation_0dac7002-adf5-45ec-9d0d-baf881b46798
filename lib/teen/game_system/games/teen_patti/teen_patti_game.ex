defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.TeenPatti.TeenPattiGame do
  @moduledoc """
  Teen Patti游戏定义模块

  实现游戏工厂行为，定义Teen Patti游戏的基本信息和配置
  包含所有游戏相关的配置参数，提供统一的配置管理

  基于C++服务端实现和前端协议定义
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  require Logger

  # ==================== 游戏基础配置 ====================

  # 游戏状态定义 (对应前端GameState)
  @game_states %{
    # EM_TEENPATTI_GAMESTATE_START - 游戏开始
    start: 0,
    # EM_TEENPATTI_GAMESTATE_SENDCARD - 发牌状态
    send_card: 1,
    # EM_TEENPATTI_GAMESTATE_BET - 下注状态
    bet: 2,
    # EM_TEENPATTI_GAMESTATE_COMPETITION - 比牌状态
    competition: 3,
    # EM_TEENPATTI_GAMESTATE_END - 结束状态
    end: 4,
    # EM_TEENPATTI_GAMESTATE_WAITRECHARGE - 等待充值
    wait_recharge: 5
  }

  # 玩家状态定义 (对应EM_TEENPATTI_PLAYERSTATE)
  @player_states %{
    # EM_TEENPATTI_PLAYER_NONE - 无效状态
    none: 0,
    # EM_TEENPATTI_PLAYER_PLAY - 游戏状态
    play: 1,
    # EM_TEENPATTI_PLAYER_FOLD - 弃牌状态
    fold: 2,
    # EM_TEENPATTI_PLAYER_LOSE - 输牌状态
    lose: 3
  }

  # 下注类型定义 (对应BetType)
  @bet_types %{
    # BOTTOM - 底注
    bottom: 0,
    # FILL - 下注
    fill: 1,
    # COMPETITION - 比牌
    competition: 2,
    # FOLD - 弃牌
    fold: 3
  }

  # 牌型定义 (对应CardType)
  @card_types %{
    # EM_TEENPATTI_CARDTYPE_NONE - 无
    none: 0,
    # EM_TEENPATTI_CARDTYPE_DAN - 单牌/高牌
    high_card: 1,
    # EM_TEENPATTI_CARDTYPE_DUIZI - 对子
    pair: 2,
    # EM_TEENPATTI_CARDTYPE_TONGHUA - 同花
    color: 3,
    # EM_TEENPATTI_CARDTYPE_SHUNZI - 顺子
    sequence: 4,
    # EM_TEENPATTI_CARDTYPE_TONGHUASHUN - 同花顺
    pure_sequence: 5,
    # EM_TEENPATTI_CARDTYPE_BAOZI - 豹子
    trail: 6
  }

  @impl true
  def game_type, do: :teen_patti

  @impl true
  def game_name, do: "Teen Patti"

  @impl true
  def room_module, do: Cypridina.Teen.GameSystem.Games.TeenPatti.TeenPattiRoom

  @impl true
  def default_config do
    %{
      # ==================== 房间基础配置 ====================
      # 💬 单聊费用 - 玩家间私聊需要支付的金币
      single_chat_fee: 10,
      # 💰 打赏荷官费用 - 打赏荷官需要支付的金币
      tip_dealer_fee: 10,
      # 🎯 暂离踢出局数 - 暂离玩家多少局没回来才踢出房间（默认1局）
      zanli_kick_rounds: 2,
      # 最大玩家数
      max_players: 5,
      # 最小玩家数
      min_players: 2,
      # 自动开始延迟 (毫秒)
      auto_start_delay: 3000,

      # ==================== 机器人配置 ====================
      # 启用机器人
      enable_robots: true,
      # 机器人数量
      robot_count: 2,

      # ==================== 游戏时间配置 ====================
      # 发牌时间(秒)
      send_card_time: 3,
      # 操作等待时间(秒)
      operation_wait_time: 10,
      # 比牌时间(秒)
      competition_time: 15,
      # 延迟结算时间(秒)
      delay_settlement_time: 3,
      # 充值等待时间(秒)
      recharge_wait_time: 250,

      # ==================== 下注配置 ====================
      # 底注
      base_bet: 10,
      # 总注限制
      pot_limit: 1000,
      # 单注封顶
      chaal_limit: 100,
      # 暗注最大回合
      blind_round_limit: 5,
      # 最小比牌轮数
      comp_min_turn_num: 3,
      # 每局可拒绝比牌次数
      reject_comp_num: 3,

      # ==================== 牌型赔率配置 ====================
      # 基于牌型强度的赔率系统
      card_type_multipliers: %{
        # 豹子 5倍
        trail: 5.0,
        # 同花顺 4倍
        pure_sequence: 4.0,
        # 顺子 3倍
        sequence: 3.0,
        # 同花 2.5倍
        color: 2.5,
        # 对子 2倍
        pair: 2.0,
        # 高牌 1.5倍
        high_card: 1.5
      },

      # ==================== 庄家配置 ====================
      # 庄家模式: "rotation" (轮庄) 或 "winner" (赢庄)
      banker_mode: "winner"
    }
  end

  # ==================== 协议配置 ====================
  # SC_TEENPATTI_START_P = 1000,		//开始
  # SC_TEENPATTI_SENDCARD_P,			//发牌1
  # CS_TEENPATTI_BET_P,					//下注2
  # SC_TEENPATTI_BET_P,					//下注3
  # CS_TEENPATTI_LOOK_P,				//看牌4
  # SC_TEENPATTI_LOOK_P,				//看牌5
  # CS_TEENPATTI_COMPETITION_P,			//比牌6
  # SC_TEENPATTI_COMPETITION_P,			//比牌7
  # CS_TEENPATTI_COMPCONFIRM_P,			//比牌确认8
  # SC_TEENPATTI_COMPCONFIRM_P,			//比牌确认9
  # CS_TEENPATTI_FOLD_P,				//弃牌10
  # SC_TEENPATTI_FOLD_P,				//弃牌11
  # CS_TEENPATTI_WAITRECHARGE_P,		//等待充值12
  # SC_TEENPATTI_WAITRECHARGE_P,		//等待充值13
  # SC_TEENPATTI_WAITOPT_P,				//等待操作14
  # SC_TEENPATTI_JIESHUAN_P,			//结算15
  # 协议号定义 (严格匹配前端TPDefine.ts中的自动递增枚举)
  @protocols %{
    # 服务端发送协议 (SC) - 匹配前端枚举顺序
    # SC_TEENPATTI_START_P = 1000
    sc_teenpatti_start: 1000,
    # SC_TEENPATTI_SENDCARD_P (自动递增)
    sc_teenpatti_sendcard: 1001,
    # SC_TEENPATTI_BET_P (自动递增)
    sc_teenpatti_bet: 1003,
    # SC_TEENPATTI_LOOK_P (自动递增)
    sc_teenpatti_look: 1005,
    # SC_TEENPATTI_COMPETITION_P (自动递增)
    sc_teenpatti_competition: 1007,
    # SC_TEENPATTI_COMPCONFIRM_P (自动递增)
    sc_teenpatti_compconfirm: 1009,
    # SC_TEENPATTI_FOLD_P (自动递增)
    sc_teenpatti_fold: 1011,
    # SC_TEENPATTI_WAITRECHARGE_P (自动递增)
    sc_teenpatti_waitrecharge: 1013,
    # SC_TEENPATTI_WAITOPT_P (自动递增)
    sc_teenpatti_waitopt: 1014,
    # SC_TEENPATTI_JIESHUAN_P (自动递增)
    sc_teenpatti_jieshuan: 1015,

    # 客户端发送协议 (CS) - 匹配前端枚举顺序
    # CS_TEENPATTI_BET_P (自动递增)
    cs_teenpatti_bet: 1002,
    # CS_TEENPATTI_LOOK_P (自动递增)
    cs_teenpatti_look: 1004,
    # CS_TEENPATTI_COMPETITION_P (自动递增)
    cs_teenpatti_competition: 1006,
    # CS_TEENPATTI_COMPCONFIRM_P (自动递增)
    cs_teenpatti_compconfirm: 1008,
    # CS_TEENPATTI_FOLD_P (自动递增)
    cs_teenpatti_fold: 1010,
    # CS_TEENPATTI_WAITRECHARGE_P (自动递增)
    cs_teenpatti_waitrecharge: 1012
  }

  @impl true
  def is_lobby_game?, do: false

  @impl true
  def supported_game_ids do
    [
      # Teen Patti游戏ID
      1,
      # 协议中使用的ID
      1001
    ]
  end

  # ==================== 配置访问器函数 ====================

  @doc """
  获取游戏状态定义
  """
  def game_states, do: @game_states

  @doc """
  获取玩家状态定义
  """
  def player_states, do: @player_states

  @doc """
  获取下注类型定义
  """
  def bet_types, do: @bet_types

  @doc """
  获取牌型定义
  """
  def card_types, do: @card_types

  @doc """
  获取协议配置
  """
  def protocols, do: @protocols

  @doc """
  获取指定协议号
  """
  def protocol(name), do: Map.get(@protocols, name)

  @doc """
  获取完整的游戏配置
  """
  def full_config(overrides \\ %{}) do
    base_config = default_config()

    full_config =
      Map.merge(base_config, %{
        game_states: @game_states,
        player_states: @player_states,
        bet_types: @bet_types,
        card_types: @card_types,
        protocols: @protocols
      })

    Map.merge(full_config, overrides)
  end

  @doc """
  获取合并后的配置
  接收 RoomManager 传入的合并后配置，进行最终处理
  """
  def get_merged_config(room_config) when is_map(room_config) do
    Logger.info("🃏 [TEEN_PATTI_CONFIG] 使用智能合并配置")

    # 获取默认配置作为基础
    default_config = default_config()

    # 使用智能合并：传入的键优先，缺失的用默认值补充
    final_config = smart_merge_configs(default_config, room_config)

    Logger.info("🃏 [TEEN_PATTI_CONFIG] 智能合并完成，配置项数: #{map_size(final_config)}")
    final_config
  end

  def get_merged_config(_room_config) do
    Logger.warning("🃏 [TEEN_PATTI_CONFIG] 配置格式错误，使用默认配置")
    default_config()
  end

  # 智能合并配置：传入的键优先，缺失的用默认值补充，解决键冲突
  defp smart_merge_configs(default_config, room_config)
       when is_map(default_config) and is_map(room_config) do
    if map_size(room_config) == 0 do
      Logger.info("🃏 [SMART_MERGE] 使用默认配置 - 房间配置为空")
      default_config
    else
      Logger.info("🃏 [SMART_MERGE] 开始智能合并 - 房间配置项数: #{map_size(room_config)}")

      # 先进行后台配置字段映射
      mapped_config = map_backend_config_fields(room_config)

      # 智能合并：传入配置优先，默认配置补充缺失项
      merged_config = smart_deep_merge(default_config, mapped_config)

      Logger.info("🃏 [SMART_MERGE] 合并完成")
      merged_config
    end
  end

  defp smart_merge_configs(default_config, _room_config) do
    Logger.warning("🃏 [SMART_MERGE] 配置格式错误，使用默认配置")
    default_config || %{}
  end

  # 映射后台配置字段到游戏配置字段
  defp map_backend_config_fields(backend_config) do
    mapped_config = %{}

    # 映射后台管理系统的字段到游戏配置字段
    mapped_config =
      backend_config
      |> Enum.reduce(mapped_config, fn {key, value}, acc ->
        case normalize_key(key) do
          # 后台字段 -> 游戏字段映射
          :min_bet ->
            Map.put(acc, :base_bet, value)

          :entry_fee ->
            Map.put(acc, :entry_fee, value)

          :max_bet ->
            if value > 0 do
              Map.put(acc, :chaal_limit, value)
            else
              acc
            end

          :pot_limit ->
            Map.put(acc, :pot_limit, value)

          :max_players ->
            Map.put(acc, :max_players, value)

          # 保持其他字段不变
          other_key ->
            Map.put(acc, other_key, value)
        end
      end)

    # 合并原始配置（保留未映射的字段）
    Map.merge(backend_config, mapped_config)
  end

  # 标准化键（字符串转原子，处理数字字符串）
  defp normalize_key(key) when is_binary(key) do
    try do
      String.to_existing_atom(key)
    rescue
      ArgumentError ->
        String.to_atom(key)
    end
  end

  defp normalize_key(key) when is_atom(key), do: key
  defp normalize_key(key), do: key

  # 智能深度合并：解决键冲突，传入配置优先
  defp smart_deep_merge(default_config, room_config)
       when is_map(default_config) and is_map(room_config) do
    # 第一步：从默认配置开始
    result = default_config

    # 第二步：遍历房间配置，智能覆盖
    Enum.reduce(room_config, result, fn {room_key, room_value}, acc ->
      # 查找是否有冲突的键（字符串 vs 原子）
      conflicting_key = find_conflicting_key(room_key, Map.keys(acc))

      cond do
        # 情况1：完全匹配的键，直接覆盖
        Map.has_key?(acc, room_key) ->
          if is_map(room_value) and is_map(Map.get(acc, room_key)) do
            # 递归合并嵌套Map
            Map.put(acc, room_key, smart_deep_merge(Map.get(acc, room_key), room_value))
          else
            # 直接覆盖
            Map.put(acc, room_key, room_value)
          end

        # 情况2：有冲突键（字符串 vs 原子），删除冲突键，使用传入的键
        conflicting_key != nil ->
          acc
          |> Map.delete(conflicting_key)
          |> Map.put(room_key, room_value)

        # 情况3：新键，直接添加
        true ->
          Map.put(acc, room_key, room_value)
      end
    end)
  end

  # 查找冲突的键（字符串 vs 原子）
  defp find_conflicting_key(target_key, existing_keys) do
    target_str = to_string(target_key)

    Enum.find(existing_keys, fn existing_key ->
      existing_str = to_string(existing_key)
      target_str == existing_str and target_key != existing_key
    end)
  end

  @doc """
  获取机器人行为配置

  提供机器人运行时需要的关键配置参数
  """
  def robot_behavior do
    %{
      # 机器人最低金币要求
      min_money: 1000,

      # 机器人看牌概率
      look_probability: 0.6,

      # 机器人操作延迟范围（毫秒）
      operation_delay: %{
        min: 1000,
        max: 5000
      }
    }
  end
end
