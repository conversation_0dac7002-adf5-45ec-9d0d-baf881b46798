defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.PotBlind.PotBlindAI do
  @moduledoc """
  PotBlind游戏机器人AI决策引擎 - 🔧 增强比牌频率版本
  """

  require Lo<PERSON>
  alias <PERSON><PERSON>rid<PERSON>.Teen.GameSystem.Games.PotBlind.PotBlindGameLogic

  @doc """
  为机器人生成行动决策
  """
  def make_robot_decision(_state, robot_player) do
    Logger.info("🤖 [POT_BLIND_AI] 机器人 #{robot_player.numeric_id} 开始决策")

    # 获取机器人AI数据
    ai_data = get_robot_ai_data(robot_player)

    # 评估牌力
    hand_strength = evaluate_hand_strength(robot_player.cards)

    # 根据性格生成决策
    decision = generate_decision(hand_strength, ai_data)

    # 计算思考时间
    think_time = calculate_think_time(ai_data, decision)

    Logger.info(
      "🤖 [POT_BLIND_AI] 机器人 #{robot_player.numeric_id} 决策: #{inspect(decision)}, 思考时间: #{think_time}ms"
    )

    {decision, think_time}
  end

  # ==================== 私有函数 ====================

  # 获取机器人AI数据
  defp get_robot_ai_data(robot_player) do
    case robot_player do
      %{user: %{robot_ai: ai_data}} when is_map(ai_data) -> ai_data
      %{robot_ai_data: ai_data} when is_map(ai_data) -> ai_data
      _ -> %{personality: :balanced, aggression: 0.5, risk_tolerance: 0.5}
    end
  end

  # 评估牌力
  defp evaluate_hand_strength(cards) when is_list(cards) and length(cards) == 3 do
    case PotBlindGameLogic.get_card_type(cards) do
      # 豹子
      :trail -> 0.95 + :rand.uniform() * 0.05
      # 同花顺
      :pure_sequence -> 0.85 + :rand.uniform() * 0.1
      # 顺子
      :sequence -> 0.75 + :rand.uniform() * 0.1
      # 同花
      :color -> 0.65 + :rand.uniform() * 0.1
      # 对子
      :pair -> 0.45 + :rand.uniform() * 0.2
      # 高牌
      :high_card -> 0.1 + :rand.uniform() * 0.3
      # 默认
      _ -> 0.2 + :rand.uniform() * 0.3
    end
  end

  defp evaluate_hand_strength(_), do: 0.2

  # 生成决策 - 🔧 大幅增强比牌频率
  defp generate_decision(hand_strength, ai_data) do
    personality = Map.get(ai_data, :personality, :balanced)

    # 计算比牌概率
    competition_prob = calculate_competition_probability(hand_strength, personality)

    # 决策逻辑
    cond do
      :rand.uniform() < competition_prob ->
        # nil表示自动选择上家
        {:competition, nil}

      should_bet(hand_strength, personality) ->
        fill = choose_bet_size(hand_strength, personality)
        {:bet, fill}

      true ->
        {:fold, nil}
    end
  end

  # 计算比牌概率 - 🔧 各种性格都大幅提高比牌频率
  defp calculate_competition_probability(hand_strength, personality) do
    case personality do
      :aggressive ->
        cond do
          # 强牌70%比牌
          hand_strength >= 0.8 -> 0.7
          # 中等偏上50%比牌
          hand_strength >= 0.6 -> 0.5
          # 中等30%比牌
          hand_strength >= 0.4 -> 0.3
          # 弱牌15%诈唬比牌
          hand_strength >= 0.2 -> 0.15
          true -> 0.0
        end

      :loose ->
        cond do
          # 强牌60%比牌
          hand_strength >= 0.7 -> 0.6
          # 中等偏上40%比牌
          hand_strength >= 0.5 -> 0.4
          # 中等25%比牌
          hand_strength >= 0.3 -> 0.25
          # 弱牌10%诈唬比牌
          hand_strength >= 0.15 -> 0.1
          true -> 0.0
        end

      :balanced ->
        cond do
          # 强牌50%比牌
          hand_strength >= 0.8 -> 0.5
          # 中等偏上30%比牌
          hand_strength >= 0.6 -> 0.3
          # 中等15%比牌
          hand_strength >= 0.4 -> 0.15
          true -> 0.0
        end

      :conservative ->
        cond do
          # 非常强牌40%比牌
          hand_strength >= 0.9 -> 0.4
          # 强牌25%比牌
          hand_strength >= 0.8 -> 0.25
          true -> 0.0
        end

      :tight ->
        cond do
          # 非常强牌30%比牌
          hand_strength >= 0.9 -> 0.3
          # 强牌15%比牌
          hand_strength >= 0.8 -> 0.15
          true -> 0.0
        end

      :tricky ->
        # 狡猾型根据牌力动态调整
        cond do
          # 强牌40%比牌
          hand_strength >= 0.8 -> 0.4
          # 中等偏上25%比牌
          hand_strength >= 0.6 -> 0.25
          # 中等10%比牌
          hand_strength >= 0.4 -> 0.1
          true -> 0.0
        end

      _ ->
        0.0
    end
  end

  # 判断是否应该下注
  defp should_bet(hand_strength, personality) do
    threshold =
      case personality do
        :aggressive -> 0.3
        :loose -> 0.25
        :balanced -> 0.4
        :conservative -> 0.6
        :tight -> 0.7
        :tricky -> 0.35
        _ -> 0.4
      end

    hand_strength >= threshold
  end

  # 选择下注大小
  defp choose_bet_size(hand_strength, personality) do
    base_size = if hand_strength > 0.7, do: 2, else: 1

    case personality do
      :aggressive when hand_strength > 0.8 -> base_size + 1
      :loose when hand_strength > 0.6 -> base_size + 1
      _ -> base_size
    end
  end

  # 计算思考时间
  defp calculate_think_time(ai_data, decision) do
    base_time =
      case decision do
        # 比牌需要更多思考时间
        {:competition, _} -> 3000
        # 大额下注需要思考
        {:bet, fill} when fill >= 3 -> 2500
        # 普通下注
        {:bet, _} -> 1500
        # 弃牌较快
        {:fold, _} -> 1000
        _ -> 1500
      end

    # 根据性格调整思考时间
    personality_modifier =
      case Map.get(ai_data, :personality, :balanced) do
        # 激进型决策较快
        :aggressive -> 0.8
        # 保守型思考较久
        :conservative -> 1.3
        # 紧型谨慎思考
        :tight -> 1.2
        # 松型决策较快
        :loose -> 0.9
        # 狡猾型适中
        :tricky -> 1.1
        # 平衡型
        _ -> 1.0
      end

    # 添加随机变化
    # 0.7 到 1.3
    random_factor = 0.7 + :rand.uniform() * 0.6

    final_time = base_time * personality_modifier * random_factor
    # 限制在1-8秒之间
    round(max(1000, min(8000, final_time)))
  end
end
