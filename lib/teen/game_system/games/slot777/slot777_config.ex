defmodule Cyprid<PERSON>.Teen.GameSystem.Games.Slot777.Slot777Config do
  @moduledoc """
  Slot777游戏配置管理模块 - 简化版本
  现在只提供默认配置，实际的配置合并由 RoomManager 统一处理
  """

  require Logger

  def get_default_config do
    %{
      # ===== 基础游戏配置 =====
      game_basic: %{
        rows: 3,
        cols: 5,
        max_lines: 9,
        # 游戏名称
        name: "Slot777老虎机",
        # 游戏版本
        version: "2.0.0"
      },
      ## 设置抽水比例
      rake_percentage: 0,

      # ===== 下注配置 =====
      betting: %{
        # 底分 (基础下注单位)
        difen: 100,
        # 固定倍率 (对应前端的 BET_RATE_NUM)
        bet_rate_num: 9,
        # 金币比例 (对应前端的 SCORE_RATE)
        score_rate: 1,
        # 下注倍率选项
        odds_config: %{
          1 => 0.2,
          2 => 1,
          3 => 2,
          4 => 10,
          5 => 20,
          6 => 100,
          7 => 200
        },
        # 最小下注
        min_bet: 1,
        # 最大下注
        max_bet: 1000
      },

      # ===== 中奖率控制配置 =====
      win_rate: %{
        # 整体返还率 (RTP - Return to Player)
        rtp: 85.0,
        # 图标权重配置 (数字越大出现概率越高)
        icon_weights: %{
          # WILD字 - 极低频 (降低)
          0 => 1,
          # 香蕉 - 高频 (最低赔率) (增加)
          1 => 30,
          # 西瓜 - 高频 (增加)
          2 => 25,
          # 草莓 - 中频 (增加)
          3 => 20,
          # 葡萄 - 中频
          4 => 15,
          # 芒果 - 中频
          5 => 12,
          # 榴莲 - 中频
          6 => 10,
          # 山竹 - 低频
          7 => 8,
          # BAR - 低频 (高赔率) (降低)
          8 => 3,
          # 苹果 - 低频 (免费游戏触发) (降低)
          9 => 4,
          # 7 - 极低频 (Jackpot级别) (降低)
          10 => 2
        },
        # 大奖触发概率控制
        big_win_control: %{
          # Jackpot触发概率 (万分之一)
          jackpot_probability: 0.01,
          # 免费游戏触发概率控制
          free_game_probability: 0.05,
          # 连续大奖间隔控制 (防止连续大奖)
          big_win_cooldown: 100
        }
      },

      # ===== 赔率表配置 =====
      payout: %{
        # 赔率表 (图标类型 => {连击数 => 赔率}) - 按照游戏规则图片更新
        payout_table: %{
          # WILD字 (替代其他图标，无独立赔率)
          0 => %{},
          # 香蕉 (按图片: 2连1倍, 3连3倍, 4连10倍, 5连75倍)
          1 => %{2 => 1, 3 => 3, 4 => 10, 5 => 75},
          # 西瓜 (按图片: 3连3倍, 4连10倍, 5连85倍)
          2 => %{3 => 3, 4 => 10, 5 => 85},
          # 草莓 (按图片: 3连15倍, 4连40倍, 5连250倍)
          3 => %{3 => 15, 4 => 40, 5 => 250},
          # 葡萄 (按图片: 3连25倍, 4连50倍, 5连400倍)
          4 => %{3 => 25, 4 => 50, 5 => 400},
          # 芒果 (按图片: 3连30倍, 4连70倍, 5连550倍)
          5 => %{3 => 30, 4 => 70, 5 => 550},
          # 榴莲 (按图片: 3连35倍, 4连80倍, 5连650倍)
          6 => %{3 => 35, 4 => 80, 5 => 650},
          # 山竹 (按图片: 3连45倍, 4连100倍, 5连800倍)
          7 => %{3 => 45, 4 => 100, 5 => 800},
          # BAR (按图片: 3连75倍, 4连175倍, 5连1250倍)
          8 => %{3 => 75, 4 => 175, 5 => 1250},
          # 苹果 (FREE，按图片: 3连25倍, 4连40倍, 5连400倍)
          9 => %{3 => 25, 4 => 40, 5 => 400},
          # 7 (JACKPOT，按图片: 3连100倍, 4连200倍, 5连1750倍)
          10 => %{3 => 100, 4 => 200, 5 => 1750}
        }
      },

      # ===== Jackpot配置 =====
      jackpot: %{
        # 总奖池贡献率
        # 2% 总奖池贡献率
        contribution_rate: 0.02,

        # 奖池配置
        pools: [
          %{
            identifier: :main,
            # 100万底金
            base_amount: 1_000_000,
            # 最小金额
            min_amount: 50000,
            # 最大金额
            max_amount: 20_000_000,
            # 100%贡献率（单一奖池）
            contribution_rate: 1.0,
            trigger_conditions: %{
              # 最少3个7触发
              min_seven_count: 3,
              # 最小单线下注
              min_single_line_bet: 10
            }
          }
        ],

        # Jackpot比例配置 (根据7的数量和单线下注金额)
        percentage_table: %{
          # 单线下注₹200
          # 降低比例
          200 => %{5 => 30, 4 => 25, 3 => 20},
          # 单线下注₹100
          # 降低比例
          100 => %{5 => 20, 4 => 15, 3 => 12},
          # 单线下注₹20
          # 降低比例
          20 => %{5 => 12, 4 => 6, 3 => 4},
          # 单线下注₹10
          # 降低比例
          10 => %{5 => 8, 4 => 4, 3 => 2}
        }
      },

      # ===== 免费游戏配置 =====
      free_game: %{
        # 触发免费游戏需要的苹果数量和对应次数
        trigger_table: %{
          # 5个苹果 = 12次免费 (降低)
          5 => 15,
          # 4个苹果 = 8次免费 (降低)
          4 => 10,
          # 3个苹果 = 4次免费 (降低)
          3 => 5
        },
        # 免费游戏倍率
        # 降低倍率
        multiplier: 1.5,
        # 免费游戏中的特殊权重 (可选)
        special_weights: nil
      },

      # ===== 测试功能配置 =====
      testing: %{
        # 是否启用免费游戏测试
        enable_free_game_test: false,
        # 免费游戏测试触发条件 (连续旋转次数)
        # 增加到50次
        free_game_test_spins: 2,
        # 是否启用Jackpot测试
        enable_jackpot_test: false,
        # 测试模式下的特殊配置
        test_mode_config: %{
          # 测试时使用的特殊权重
          test_icon_weights: %{
            # WILD字 - 测试时增加
            0 => 5,
            # 香蕉
            1 => 20,
            # 西瓜
            2 => 20,
            # 草莓
            3 => 15,
            # 葡萄
            4 => 15,
            # 芒果
            5 => 10,
            # 榴莲
            6 => 10,
            # 山竹
            7 => 8,
            # BAR
            8 => 5,
            # 苹果 - 测试时增加
            9 => 10,
            # 7
            10 => 2
          }
        }
      },

      # ===== 房间配置 =====
      room: %{
        # 最大玩家数
        max_players: 1,
        # 最小玩家数
        min_players: 1,
        # 自动开始延迟
        auto_start_delay: 1000,
        # 是否启用机器人
        enable_robots: false,
        # 机器人数量
        robot_count: 0
      }
    }
  end

  def get_current_config() do
    Logger.info("🎰 [SLOT777_CONFIG] 返回默认配置")
    get_default_config()
  end

  @doc """
  获取合并后的配置
  接收 RoomManager 传入的合并后配置，进行最终处理
  """
  def get_merged_config(room_config) when is_map(room_config) do
    Logger.info("🎰 [SLOT777_CONFIG] 使用智能合并配置")

    # 获取默认配置作为基础
    default_config = get_default_config()

    # 使用智能合并：传入的键优先，缺失的用默认值补充
    final_config = smart_merge_configs(default_config, room_config)

    Logger.info("🎰 [SLOT777_CONFIG] 智能合并完成，配置项数: #{map_size(final_config)}")
    final_config
  end

  # 智能合并配置：传入的键优先，缺失的用默认值补充，解决键冲突
  defp smart_merge_configs(default_config, room_config)
       when is_map(default_config) and is_map(room_config) do
    if map_size(room_config) == 0 do
      Logger.info("🎰 [SMART_MERGE] 使用默认配置 - 房间配置为空")
      default_config
    else
      Logger.info("🎰 [SMART_MERGE] 开始智能合并 - 房间配置项数: #{map_size(room_config)}")

      # 智能合并：传入配置优先，默认配置补充缺失项
      merged_config = smart_deep_merge(default_config, room_config)

      Logger.info("🎰 [SMART_MERGE] 合并完成")
      merged_config
    end
  end

  defp smart_merge_configs(default_config, _room_config) do
    Logger.warning("🎰 [SMART_MERGE] 配置格式错误，使用默认配置")
    default_config || %{}
  end

  # 智能深度合并：解决键冲突，传入配置优先
  defp smart_deep_merge(default_config, room_config)
       when is_map(default_config) and is_map(room_config) do
    # 第一步：从默认配置开始
    result = default_config

    # 第二步：遍历房间配置，智能覆盖
    Enum.reduce(room_config, result, fn {room_key, room_value}, acc ->
      # 查找是否有冲突的键（字符串 vs 原子）
      conflicting_key = find_conflicting_key(room_key, Map.keys(acc))

      cond do
        # 情况1：完全匹配的键，直接覆盖
        Map.has_key?(acc, room_key) ->
          if is_map(room_value) and is_map(Map.get(acc, room_key)) do
            # 递归合并嵌套Map
            Map.put(acc, room_key, smart_deep_merge(Map.get(acc, room_key), room_value))
          else
            # 直接覆盖
            Map.put(acc, room_key, room_value)
          end

        # 情况2：有冲突键（字符串 vs 原子），删除冲突键，使用传入的键
        conflicting_key != nil ->
          acc
          # 删除冲突的默认键
          |> Map.delete(conflicting_key)
          # 使用传入的键
          |> Map.put(room_key, room_value)

        # 情况3：新键，直接添加
        true ->
          Map.put(acc, room_key, room_value)
      end
    end)
  end

  defp smart_deep_merge(_default, room_config), do: room_config

  # 查找冲突的键（字符串 vs 原子）
  defp find_conflicting_key(target_key, existing_keys) do
    Enum.find(existing_keys, fn existing_key ->
      # 排除完全相同的键
      if target_key == existing_key do
        false
      else
        # 检查是否是字符串 vs 原子的冲突
        cond do
          # 字符串键 vs 原子键
          is_binary(target_key) and is_atom(existing_key) ->
            to_string(existing_key) == target_key

          # 原子键 vs 字符串键
          is_atom(target_key) and is_binary(existing_key) ->
            to_string(target_key) == existing_key

          # 数字键的特殊处理
          is_integer(target_key) and is_binary(existing_key) ->
            Integer.to_string(target_key) == existing_key

          is_binary(target_key) and is_integer(existing_key) ->
            target_key == Integer.to_string(existing_key)

          true ->
            false
        end
      end
    end)
  end

  def validate_config(config) when is_map(config) do
    required_keys = [
      :game_basic,
      :betting,
      :win_rate,
      :payout,
      :symbol_weights,
      :jackpot,
      :free_game,
      :testing
    ]

    missing_keys = Enum.filter(required_keys, fn key -> not Map.has_key?(config, key) end)

    case missing_keys do
      [] -> {:ok, config}
      keys -> {:error, "缺少必需的配置项: #{inspect(keys)}"}
    end
  end

  def validate_config(_), do: {:error, "配置必须是Map类型"}

  def reload_config, do: :ok
  def update_config(_new_config), do: :ok
end
