defmodule Teen.GameSystem.Games.Slot777.ProbabilityIntegration do
  @moduledoc """
  Slot777游戏概率调整集成示例

  展示如何在Slot777游戏中集成智能概率调控系统
  """

  require Logger
  alias Teen.Resources.Inventory.ProbabilityAdjustment

  # 基础图标权重配置
  @base_icon_weights %{
    # WILD (万能牌)
    0 => 1,
    # 香蕉 (低价值)
    1 => 30,
    # 西瓜
    2 => 25,
    # 草莓
    3 => 20,
    # 葡萄
    4 => 15,
    # 芒果
    5 => 12,
    # 榴莲
    6 => 10,
    # 山竹
    7 => 8,
    # BAR (高价值)
    8 => 3,
    # FREE (免费游戏)
    9 => 4,
    # JACKPOT (最高价值)
    10 => 2
  }

  @doc """
  生成游戏结果 - 集成概率调整

  ## 参数
  - bet_odds: 下注倍率
  - player_money: 玩家金币
  - game_config: 游戏配置

  ## 返回
  调整后的游戏结果
  """
  def generate_game_result_with_adjustment(bet_odds, player_money, game_config \\ %{}) do
    try do
      # 1. 获取概率调整建议（快速调用 < 10ms）
      # slot777 game_id = 40
      adjustment_result = ProbabilityAdjustment.get_quick_adjustment(40, 1)

      # 2. 根据建议调整图标权重
      adjusted_weights =
        if adjustment_result.success do
          adjust_icon_weights(@base_icon_weights, adjustment_result.suggestion)
        else
          Logger.warn("概率调整获取失败，使用原始权重")
          @base_icon_weights
        end

      # 3. 使用调整后的权重生成游戏结果
      game_result = generate_icons_with_weights(adjusted_weights)

      # 4. 记录调整信息（异步，不影响游戏性能）
      Task.start(fn ->
        # 使用数字game_id
        log_adjustment_usage(40, adjustment_result.suggestion, game_result)
      end)

      # 5. 返回结果，包含调整信息
      Map.put(game_result, :probability_adjustment, %{
        suggestion: adjustment_result.suggestion,
        description: adjustment_result.description,
        applied: adjustment_result.success
      })
    rescue
      error ->
        Logger.error("概率调整集成失败: #{inspect(error)}")

        # 失败时使用原始权重，确保游戏正常运行
        game_result = generate_icons_with_weights(@base_icon_weights)

        Map.put(game_result, :probability_adjustment, %{
          suggestion: 0,
          description: "调整失败，使用原始概率",
          applied: false
        })
    end
  end

  @doc """
  根据调整建议修改图标权重

  ## 参数
  - base_weights: 基础权重配置
  - adjustment: 调整值 (-10 到 +10)

  ## 返回
  调整后的权重配置
  """
  def adjust_icon_weights(base_weights, adjustment) do
    # 定义高价值图标（影响玩家中奖率）
    # WILD, BAR, FREE, JACKPOT
    high_value_icons = [0, 8, 9, 10]
    # 普通水果
    low_value_icons = [1, 2, 3, 4, 5, 6, 7]

    # 计算调整系数
    # 转换为小数
    adjustment_factor = adjustment / 100

    Enum.map(base_weights, fn {icon, weight} ->
      cond do
        # 高价值图标：根据调整值修改权重
        icon in high_value_icons ->
          # 负值增加高价值图标权重（对玩家有利）
          # 正值减少高价值图标权重（对平台有利）
          multiplier = 1 - adjustment_factor
          new_weight = weight * multiplier
          # 确保权重至少为1
          {icon, max(1, round(new_weight))}

        # 低价值图标：反向微调，保持总体平衡
        icon in low_value_icons ->
          # 与高价值图标相反的调整
          # 调整幅度较小
          multiplier = 1 + adjustment_factor * 0.3
          new_weight = weight * multiplier
          {icon, max(1, round(new_weight))}

        # 其他图标：保持不变
        true ->
          {icon, weight}
      end
    end)
    |> Enum.into(%{})
  end

  @doc """
  使用权重生成图标矩阵

  ## 参数
  - weights: 图标权重配置

  ## 返回
  3x5的图标矩阵和中奖信息
  """
  def generate_icons_with_weights(weights) do
    # 创建权重池
    weight_pool = create_weight_pool(weights)

    # 生成3x5矩阵
    matrix =
      for _row <- 1..3 do
        for _col <- 1..5 do
          Enum.random(weight_pool)
        end
      end

    # 计算中奖线
    win_lines = calculate_win_lines(matrix)

    # 计算总倍率
    total_multiplier = calculate_total_multiplier(win_lines)

    %{
      matrix: matrix,
      win_lines: win_lines,
      total_multiplier: total_multiplier,
      weights_used: weights
    }
  end

  # 创建权重池
  defp create_weight_pool(weights) do
    Enum.flat_map(weights, fn {icon, weight} ->
      List.duplicate(icon, weight)
    end)
  end

  # 计算中奖线（简化版）
  defp calculate_win_lines(matrix) do
    # 这里是简化的中奖线计算
    # 实际实现需要根据Slot777的具体规则

    # 示例：检查第一行是否有连续相同图标
    first_row = Enum.at(matrix, 0)

    case check_consecutive_icons(first_row) do
      {icon, count} when count >= 3 ->
        [%{line: 1, icon: icon, count: count, multiplier: get_icon_multiplier(icon, count)}]

      _ ->
        []
    end
  end

  # 检查连续相同图标
  defp check_consecutive_icons(row) do
    first_icon = List.first(row)

    consecutive_count =
      row
      # 0是万能牌
      |> Enum.take_while(fn icon -> icon == first_icon or icon == 0 end)
      |> length()

    {first_icon, consecutive_count}
  end

  # 获取图标倍率（简化版）
  defp get_icon_multiplier(icon, count) do
    base_multipliers = %{
      # 香蕉
      1 => %{3 => 3, 4 => 10, 5 => 75},
      # 西瓜
      2 => %{3 => 3, 4 => 10, 5 => 85},
      # BAR
      8 => %{3 => 75, 4 => 175, 5 => 1250},
      # JACKPOT
      10 => %{3 => 100, 4 => 200, 5 => 1750}
    }

    multipliers = Map.get(base_multipliers, icon, %{3 => 1, 4 => 2, 5 => 5})
    Map.get(multipliers, count, 0)
  end

  # 计算总倍率
  defp calculate_total_multiplier(win_lines) do
    Enum.reduce(win_lines, 0, fn line, acc ->
      acc + line.multiplier
    end)
  end

  # 记录调整使用情况
  defp log_adjustment_usage(game_id, adjustment, game_result) do
    try do
      Logger.info("游戏 #{game_id} 使用概率调整: #{adjustment}, 结果倍率: #{game_result.total_multiplier}")

      # 可以在这里添加更详细的统计记录
      :telemetry.execute(
        [:slot777, :probability_adjustment],
        %{adjustment: adjustment, multiplier: game_result.total_multiplier},
        %{game_id: game_id}
      )
    rescue
      error ->
        Logger.warn("记录调整使用失败: #{inspect(error)}")
    end
  end

  @doc """
  获取当前权重配置信息 - 用于调试
  """
  def get_current_weights_info(adjustment \\ 0) do
    adjusted_weights = adjust_icon_weights(@base_icon_weights, adjustment)

    %{
      base_weights: @base_icon_weights,
      adjusted_weights: adjusted_weights,
      adjustment_applied: adjustment,
      high_value_total: calculate_high_value_weight(adjusted_weights),
      low_value_total: calculate_low_value_weight(adjusted_weights)
    }
  end

  defp calculate_high_value_weight(weights) do
    [0, 8, 9, 10]
    |> Enum.map(fn icon -> Map.get(weights, icon, 0) end)
    |> Enum.sum()
  end

  defp calculate_low_value_weight(weights) do
    [1, 2, 3, 4, 5, 6, 7]
    |> Enum.map(fn icon -> Map.get(weights, icon, 0) end)
    |> Enum.sum()
  end
end
