defmodule Cypridina.Teen.GameSystem.Games.Slot777.Slot777FreeGameTest do
  @moduledoc """
  Slot777 免费游戏测试模块

  提供免费游戏的测试功能，可以设置在指定次数旋转后触发免费游戏
  """

  require Logger

  # 免费游戏测试状态结构
  defstruct [
    # 是否启用免费游戏测试模式
    :enabled,
    # 触发免费游戏需要的旋转次数（默认2次）
    :trigger_count,
    # 玩家旋转次数计数器 %{player_id => count}
    :player_spin_counts,
    # 免费游戏类型：:normal, :mega, :super
    :free_game_type
  ]

  @doc """
  初始化免费游戏测试状态
  """
  def init_state(opts \\ []) do
    %__MODULE__{
      enabled: Keyword.get(opts, :enabled, false),
      # 默认2次旋转触发
      trigger_count: Keyword.get(opts, :trigger_count, 2),
      player_spin_counts: %{},
      free_game_type: Keyword.get(opts, :free_game_type, :normal)
    }
  end

  @doc """
  启用免费游戏测试模式
  """
  def enable_test_mode(test_state, trigger_count \\ 2, free_game_type \\ :normal) do
    new_state = %{
      test_state
      | enabled: true,
        trigger_count: trigger_count,
        free_game_type: free_game_type,
        player_spin_counts: %{}
    }

    Logger.info("🎰 [FREE_GAME_TEST] 免费游戏测试模式已启用：#{trigger_count}次旋转后触发#{free_game_type}免费游戏")
    new_state
  end

  @doc """
  禁用免费游戏测试模式
  """
  def disable_test_mode(test_state) do
    new_state = %{test_state | enabled: false, player_spin_counts: %{}}
    Logger.info("🎰 [FREE_GAME_TEST] 免费游戏测试模式已禁用")
    new_state
  end

  @doc """
  记录玩家旋转次数，返回新状态
  """
  def record_player_spin(test_state, player_id) do
    if test_state.enabled do
      current_count = Map.get(test_state.player_spin_counts, player_id, 0)
      new_count = current_count + 1
      new_counts = Map.put(test_state.player_spin_counts, player_id, new_count)
      new_state = %{test_state | player_spin_counts: new_counts}

      Logger.info(
        "🎰 [FREE_GAME_TEST] 玩家 #{player_id} 旋转计数: #{new_count}/#{test_state.trigger_count}"
      )

      new_state
    else
      test_state
    end
  end

  @doc """
  检查玩家是否应该触发免费游戏（测试模式）
  """
  def should_trigger_free_game(test_state, player_id) do
    if test_state.enabled do
      current_count = Map.get(test_state.player_spin_counts, player_id, 0)
      should_trigger = current_count >= test_state.trigger_count

      Logger.info(
        "🎰 [FREE_GAME_TEST] 玩家 #{player_id} 旋转计数: #{current_count}/#{test_state.trigger_count}, 应该触发免费游戏: #{should_trigger}"
      )

      should_trigger
    else
      false
    end
  end

  @doc """
  重置玩家的旋转计数器（免费游戏触发后）
  """
  def reset_player_count(test_state, player_id) do
    if test_state.enabled do
      Logger.info("🎰 [FREE_GAME_TEST] 玩家 #{player_id} 触发免费游戏后重置旋转计数器")
      new_counts = Map.put(test_state.player_spin_counts, player_id, 0)
      %{test_state | player_spin_counts: new_counts}
    else
      test_state
    end
  end

  @doc """
  生成测试免费游戏结果
  根据免费游戏类型生成不同的星星数量
  """
  def generate_test_free_game_matrix(test_state) do
    case test_state.free_game_type do
      :normal ->
        # 3个星星 = 10次免费游戏
        generate_matrix_with_stars(3)

      :mega ->
        # 4个星星 = 15次免费游戏
        generate_matrix_with_stars(4)

      :super ->
        # 5个星星 = 20次免费游戏
        generate_matrix_with_stars(5)

      _ ->
        generate_matrix_with_stars(3)
    end
  end

  @doc """
  获取免费游戏次数
  """
  def get_free_times(test_state) do
    case test_state.free_game_type do
      # 3个星星 = 10次免费
      :normal -> 10
      # 4个星星 = 15次免费
      :mega -> 15
      # 5个星星 = 20次免费
      :super -> 20
      _ -> 10
    end
  end

  ## 私有辅助函数

  @doc """
  生成包含指定数量星星的矩阵
  """
  defp generate_matrix_with_stars(star_count) do
    # 创建基础矩阵 (3行5列)
    base_matrix = [
      [1, 2, 3, 4, 5],
      [6, 7, 1, 2, 3],
      [4, 5, 6, 7, 1]
    ]

    # 在矩阵中随机放置指定数量的星星（值为8）
    place_stars_in_matrix(base_matrix, star_count)
  end

  @doc """
  在矩阵中放置星星
  """
  defp place_stars_in_matrix(matrix, star_count) do
    # 获取所有位置
    positions = for row <- 0..2, col <- 0..4, do: {row, col}

    # 随机选择位置放置星星
    star_positions = Enum.take_random(positions, star_count)

    # 更新矩阵
    Enum.reduce(star_positions, matrix, fn {row, col}, acc_matrix ->
      List.update_at(acc_matrix, row, fn row_list ->
        # 8 = 星星
        List.replace_at(row_list, col, 8)
      end)
    end)
  end

  @doc """
  设置免费游戏触发次数
  """
  def set_trigger_count(test_state, count) when count > 0 do
    new_state = %{test_state | trigger_count: count, player_spin_counts: %{}}
    Logger.info("🎰 [FREE_GAME_TEST] 免费游戏触发次数设置为: #{count}")
    new_state
  end

  @doc """
  设置免费游戏类型
  """
  def set_free_game_type(test_state, type) when type in [:normal, :mega, :super] do
    new_state = %{test_state | free_game_type: type}
    Logger.info("🎰 [FREE_GAME_TEST] 免费游戏类型设置为: #{type}")
    new_state
  end

  @doc """
  获取测试状态信息
  """
  def get_test_info(test_state) do
    %{
      enabled: test_state.enabled,
      trigger_count: test_state.trigger_count,
      free_game_type: test_state.free_game_type,
      player_counts: test_state.player_spin_counts
    }
  end

  @doc """
  清理玩家数据（当玩家退出房间时调用）
  """
  def cleanup_player_data(test_state, player_id) do
    Logger.info("🧹 [FREE_GAME_TEST_CLEANUP] 清理玩家数据 - 玩家: #{player_id}")

    # 从旋转计数器中移除玩家
    new_player_spin_counts = Map.delete(test_state.player_spin_counts, player_id)

    new_state = %{test_state | player_spin_counts: new_player_spin_counts}

    Logger.info("✅ [FREE_GAME_TEST_CLEANUP] 玩家数据清理完成 - 玩家: #{player_id}")
    new_state
  end
end
