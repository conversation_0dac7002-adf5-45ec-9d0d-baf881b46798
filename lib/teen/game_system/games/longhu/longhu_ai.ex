defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.LongHu.LongHuAI do
  @moduledoc """
  龙虎斗游戏AI机器人模块

  功能：
  - 管理机器人的创建和生命周期
  - 实现智能下注策略
  - 处理机器人行为和个性系统
  - 机器人金币管理和状态维护
  """

  require Logger

  alias Cypridina.Teen.GameSystem.Games.LongHu.LongHuGame
  alias Cypridina.Teen.GameSystem.PlayerDataBuilder

  @doc """
  为房间添加所需的机器人

  ## 参数
  - state: 房间状态
  """
  def add_robots_if_needed(state) do
    current_robot_count = count_robots(state)
    target_robot_count = state.game_data.config.robot_count

    if current_robot_count < target_robot_count do
      needed_robots = target_robot_count - current_robot_count

      Logger.info(
        "🤖 [ADD_ROBOTS] 当前机器人: #{current_robot_count}, 目标: #{target_robot_count}, 需要添加: #{needed_robots}"
      )

      Enum.reduce(1..needed_robots, state, fn i, acc_state ->
        add_single_robot(acc_state, i)
      end)
    else
      state
    end
  end

  @doc """
  动态管理机器人 - 清退低积分机器人并补充新机器人

  ## 参数
  - state: 房间状态
  """
  def manage_robots_dynamically(state) do
    # 1. 清退积分不足的机器人
    state_after_cleanup = cleanup_broke_robots(state)

    # 2. 补充新机器人到目标数量
    state_after_refill = add_robots_if_needed(state_after_cleanup)

    # 3. 随机轮换部分老机器人（保持新鲜感）
    rotate_old_robots(state_after_refill)
  end

  @doc """
  添加单个机器人

  ## 参数
  - state: 房间状态
  - index: 机器人序号
  """
  def add_single_robot(state, index \\ 1) do
    # 使用负数作为机器人ID，基于房间ID、时间戳和序号生成唯一负数
    room_hash = :erlang.phash2(state.id, 1000)
    time_hash = :erlang.phash2(DateTime.utc_now(), 1000)
    robot_id = -(1_000_000 + room_hash * 1000 + time_hash + index)

    # 使用统一配置的机器人名称
    robot_name = Teen.RobotManagement.RobotEntity.random_robot_name()

    # 使用统一的机器人数据构造方法
    robot_base_data = %{
      numeric_id: robot_id,
      id: robot_id,
      nickname: robot_name,
      # 使用统一配置的头像
      avatar_id: Teen.RobotManagement.RobotEntity.random_robot_avatar(),
      # 使用统一配置的积分生成
      points: LongHuGame.generate_robot_initial_money(),
      # 随机等级 1-10
      level: :rand.uniform(10),
      # 机器人个性参数 - 使用统一配置
      # 激进程度 0-1
      aggression: :rand.uniform(),
      # 下注频率 80%-100%
      bet_frequency: 0.8 + :rand.uniform() * 0.2,
      # 偏好区域
      favorite_area: LongHuGame.random_robot_favorite_area(),
      # 下注风格
      bet_style: LongHuGame.random_robot_bet_style(),
      # 风险承受能力
      risk_tolerance: :rand.uniform(),
      # 是否跟随趋势
      follow_trend: :rand.uniform() > 0.5,
      # 学习率 0.05-0.2
      learning_rate: 0.05 + :rand.uniform() * 0.15,
      # 记忆深度 5-15
      memory_depth: 5 + :rand.uniform(10),
      # 创建时间
      created_at: DateTime.utc_now()
    }

    # 调用统一的数据构造方法
    robot_data =
      Cypridina.Teen.GameSystem.PlayerDataBuilder.create_robot_player_data(robot_base_data,
        is_ready: true
      )

    Logger.info(
      "🤖 [ADD_ROBOT] 添加新机器人: #{robot_id} (#{robot_name}) - 积分: #{robot_data.user.points}"
    )

    %{state | players: Map.put(state.players, robot_id, robot_data)}
  end

  @doc """
  为机器人安排下注时间

  ## 参数
  - state: 房间状态
  """
  def schedule_robot_bets(state) do
    # 让机器人在整个下注阶段都保持活跃
    robot_players = Enum.filter(state.players, fn {_id, player} -> player.is_robot end)
    # 转换为毫秒
    bet_time_ms = state.game_data.config.bet_time * 1000

    # 使用统一配置的机器人行为参数
    robot_behavior = LongHuGame.robot_behavior()

    Logger.info(
      "🤖 [SCHEDULE_ROBOT_BETS] 调度 #{length(robot_players)} 个机器人在 #{state.game_data.config.bet_time}秒内智能下注"
    )

    # 为每个机器人安排个性化的下注时机
    Enum.each(robot_players, fn {robot_id, player} ->
      # 获取机器人AI属性
      aggression =
        Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_attribute(player, :aggression) || 0.5

      bet_style =
        Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_attribute(player, :bet_style) ||
          :moderate

      bet_frequency =
        Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_attribute(player, :bet_frequency) || 0.9

      risk_tolerance =
        Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_attribute(player, :risk_tolerance) ||
          0.5

      follow_trend =
        Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_attribute(player, :follow_trend) ||
          false

      # 计算机器人的下注次数（1-4次，基于个性）
      max_bets = calculate_robot_bet_count(aggression, bet_style, risk_tolerance)

      # 为每次下注安排时间，增加更多随机性
      schedule_robot_bet_times(
        robot_id,
        max_bets,
        bet_time_ms,
        aggression,
        follow_trend,
        robot_behavior
      )
    end)
  end

  # 计算机器人的下注次数
  defp calculate_robot_bet_count(aggression, bet_style, risk_tolerance) do
    base_count =
      case bet_style do
        :conservative -> 1
        :moderate -> 2
        :aggressive -> 3
        _ -> 2
      end

    # 根据激进程度和风险承受能力调整
    adjustment =
      cond do
        # 最激进的机器人最多4次
        aggression > 0.8 and risk_tolerance > 0.7 -> 2
        # 中等激进+1次
        aggression > 0.6 -> 1
        # 保守的机器人-1次
        aggression < 0.3 -> -1
        true -> 0
      end

    max(1, min(4, base_count + adjustment))
  end

  # 为机器人安排具体的下注时间
  defp schedule_robot_bet_times(
         robot_id,
         bet_count,
         bet_time_ms,
         aggression,
         follow_trend,
         robot_behavior
       ) do
    # 生成下注时间点，增加更多随机性和个性化
    bet_times = generate_personalized_bet_times(bet_count, bet_time_ms, aggression, follow_trend)

    # 为每个时间点安排下注
    Enum.with_index(bet_times, 1)
    |> Enum.each(fn {delay, index} ->
      # 添加"犹豫"时间 - 有些机器人会在下注前犹豫
      hesitation_delay =
        if :rand.uniform() < 0.3 do
          # 0-2秒的犹豫时间
          :rand.uniform(2000)
        else
          0
        end

      final_delay = delay + hesitation_delay
      Process.send_after(self(), {:robot_bet, robot_id}, final_delay)

      Logger.info(
        "🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第#{index}次下注: #{final_delay}ms (犹豫: #{hesitation_delay}ms)"
      )
    end)

    # 跟风行为 - 有些机器人会在其他玩家下注后跟风
    if follow_trend and :rand.uniform() < 0.4 do
      # 在中后期安排一次跟风下注
      follow_delay = trunc(bet_time_ms * 0.6) + :rand.uniform(trunc(bet_time_ms * 0.3))
      Process.send_after(self(), {:robot_follow_bet, robot_id}, follow_delay)
      Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 跟风下注: #{follow_delay}ms")
    end
  end

  # 生成个性化的下注时间点
  defp generate_personalized_bet_times(bet_count, bet_time_ms, aggression, follow_trend) do
    # 根据个性特征调整时间分布
    time_segments = divide_time_segments(bet_time_ms, aggression)

    # 在每个时间段内随机选择时间点
    1..bet_count
    |> Enum.map(fn bet_index ->
      segment_index = min(bet_index - 1, length(time_segments) - 1)
      {start_time, end_time} = Enum.at(time_segments, segment_index)

      # 在时间段内随机选择，增加变化性
      base_time = start_time + :rand.uniform(max(1, end_time - start_time))

      # 添加个性化的时间偏移
      personality_offset = calculate_personality_offset(aggression, follow_trend, bet_index)

      # 确保至少1秒后下注
      max(1000, base_time + personality_offset)
    end)
    # 确保时间顺序
    |> Enum.sort()
  end

  # 根据激进程度划分时间段
  defp divide_time_segments(bet_time_ms, aggression) do
    cond do
      aggression > 0.7 ->
        # 激进型：更多集中在前期和后期
        [
          {1000, trunc(bet_time_ms * 0.3)},
          {trunc(bet_time_ms * 0.3), trunc(bet_time_ms * 0.6)},
          {trunc(bet_time_ms * 0.6), trunc(bet_time_ms * 0.9)},
          {trunc(bet_time_ms * 0.9), bet_time_ms - 1000}
        ]

      aggression < 0.3 ->
        # 保守型：更多集中在中期
        [
          {trunc(bet_time_ms * 0.2), trunc(bet_time_ms * 0.5)},
          {trunc(bet_time_ms * 0.4), trunc(bet_time_ms * 0.7)},
          {trunc(bet_time_ms * 0.6), trunc(bet_time_ms * 0.8)},
          {trunc(bet_time_ms * 0.7), trunc(bet_time_ms * 0.9)}
        ]

      true ->
        # 中等型：均匀分布
        segment_size = div(bet_time_ms, 4)

        [
          {1000, segment_size},
          {segment_size, segment_size * 2},
          {segment_size * 2, segment_size * 3},
          {segment_size * 3, bet_time_ms - 1000}
        ]
    end
  end

  # 计算个性化时间偏移
  defp calculate_personality_offset(aggression, follow_trend, bet_index) do
    # -1000 到 +1000 的随机偏移
    base_offset = :rand.uniform(2000) - 1000

    # 激进程度影响
    aggression_offset =
      if aggression > 0.7 do
        # 激进的机器人倾向于更早下注
        -500
      else
        # 保守的机器人倾向于更晚下注
        500
      end

    # 跟风特性影响
    follow_offset =
      if follow_trend and bet_index > 1 do
        # 跟风的机器人在后续下注时稍微延迟
        300
      else
        0
      end

    base_offset + aggression_offset + follow_offset
  end

  @doc """
  生成智能机器人下注决策 - 避免单边下注

  ## 参数
  - robot_player: 机器人玩家数据
  - state: 游戏状态

  ## 返回
  {area, amount} - 下注区域和金额
  """
  def generate_smart_robot_bet(robot_player, state) do
    history = state.game_data.history
    total_bets = state.game_data.total_bets
    config = state.game_data.config

    # 获取机器人AI数据
    ai_data = Cypridina.Teen.GameSystem.PlayerData.get_robot_ai_data(robot_player)
    aggression = Map.get(ai_data, :aggression, 0.5)
    bet_style = Map.get(ai_data, :bet_style, :moderate)
    risk_tolerance = Map.get(ai_data, :risk_tolerance, 0.5)

    # 检查机器人当前的下注情况
    robot_id = robot_player.numeric_id
    current_bets = Map.get(state.game_data.bets, robot_id, %{})

    # 智能选择下注区域，避免单边下注
    area = choose_balanced_bet_area(ai_data, history, total_bets, current_bets, risk_tolerance)

    # 计算下注金额
    chip_values = LongHuGame.chip_values()
    amount = calculate_robot_bet_amount(bet_style, aggression, chip_values, config)

    {area, amount}
  end

  # 选择平衡的下注区域，避免单边下注
  defp choose_balanced_bet_area(ai_data, history, total_bets, current_bets, risk_tolerance) do
    current_areas = Map.keys(current_bets)

    cond do
      # 如果机器人还没有下注，使用智能策略选择
      Enum.empty?(current_areas) ->
        choose_initial_bet_area(ai_data, history, total_bets, risk_tolerance)

      # 如果机器人已经在一个区域下注，倾向于选择其他区域
      length(current_areas) == 1 ->
        choose_diversified_bet_area(current_areas, ai_data, history, total_bets, risk_tolerance)

      # 如果机器人已经在两个区域下注，可能选择第三个区域或加注现有区域
      length(current_areas) == 2 ->
        choose_final_bet_area(current_areas, ai_data, history, total_bets, risk_tolerance)

      # 如果已经在三个区域都下注，随机选择一个区域加注
      true ->
        Enum.random([:long, :hu, :he])
    end
  end

  # 选择初始下注区域
  defp choose_initial_bet_area(ai_data, history, total_bets, risk_tolerance) do
    # 60%概率使用传统策略，40%概率随机选择（增加多样性）
    if :rand.uniform() < 0.6 do
      choose_robot_bet_area(ai_data, history, total_bets)
    else
      # 随机选择，但偏向龙虎（和的概率较低）
      case :rand.uniform(10) do
        n when n <= 4 -> :long
        n when n <= 8 -> :hu
        _ -> :he
      end
    end
  end

  # 选择多样化的下注区域（避免单边下注）
  defp choose_diversified_bet_area(current_areas, ai_data, history, total_bets, risk_tolerance) do
    [current_area] = current_areas

    # 根据风险承受能力决定多样化策略
    # 40%-80%
    diversification_probability = 0.4 + risk_tolerance * 0.4

    if :rand.uniform() < diversification_probability do
      # 选择不同的区域
      available_areas = [:long, :hu, :he] -- current_areas

      case current_area do
        :long ->
          # 如果已经下注龙，70%概率选择虎，30%概率选择和
          if :rand.uniform() < 0.7, do: :hu, else: :he

        :hu ->
          # 如果已经下注虎，70%概率选择龙，30%概率选择和
          if :rand.uniform() < 0.7, do: :long, else: :he

        :he ->
          # 如果已经下注和，随机选择龙或虎
          Enum.random([:long, :hu])
      end
    else
      # 继续在同一区域加注
      current_area
    end
  end

  # 选择最终下注区域（已经在两个区域下注）
  defp choose_final_bet_area(current_areas, ai_data, history, total_bets, risk_tolerance) do
    remaining_areas = [:long, :hu, :he] -- current_areas

    # 50%概率选择剩余区域，50%概率在现有区域加注
    if :rand.uniform() < 0.5 and not Enum.empty?(remaining_areas) do
      hd(remaining_areas)
    else
      # 在现有区域中选择一个加注
      Enum.random(current_areas)
    end
  end

  @doc """
  选择机器人下注区域

  ## 参数
  - ai_data: 机器人AI数据
  - history: 历史记录
  - total_bets: 当前总下注

  ## 返回
  下注区域原子
  """
  def choose_robot_bet_area(ai_data, history, total_bets) do
    favorite_area = Map.get(ai_data, :favorite_area, :long)
    follow_trend = Map.get(ai_data, :follow_trend, false)
    risk_tolerance = Map.get(ai_data, :risk_tolerance, 0.5)

    # 策略权重 - 基于AI属性调整
    strategy_roll = :rand.uniform(100)

    # 根据风险承受能力调整策略分布
    {favorite_weight, trend_weight, popular_weight, random_weight} =
      if risk_tolerance > 0.7 do
        # 高风险：更多跟随热门和趋势
        {20, 30, 40, 10}
      else
        # 低风险：更多偏好区域
        {40, 20, 30, 10}
      end

    cond do
      # 偏好区域
      strategy_roll <= favorite_weight ->
        favorite_area

      # 跟随趋势（如果机器人有这个特性）
      strategy_roll <= favorite_weight + trend_weight and follow_trend ->
        get_trending_area(history)

      # 跟随热门区域
      strategy_roll <= favorite_weight + trend_weight + popular_weight ->
        get_popular_area(total_bets)

      # 完全随机
      true ->
        Enum.random([:long, :hu, :he])
    end
  end

  @doc """
  计算机器人下注金额 - 使用单一筹码类型

  ## 参数
  - bet_style: 下注风格 (:conservative, :moderate, :aggressive)
  - aggression: 激进程度 (0-1)
  - chip_values: 筹码面值列表
  - config: 游戏配置

  ## 返回
  下注金额
  """
  def calculate_robot_bet_amount(bet_style, aggression, chip_values, config) do
    # 使用统一配置的筹码权重
    chip_weights = LongHuGame.robot_chip_weights()

    # 使用统一配置的下注风格参数
    bet_style_config = LongHuGame.robot_bet_style_config()
    {min_quantity, max_quantity} = Map.get(bet_style_config, bet_style, {2, 5})

    # 根据激进程度调整数量上限
    adjusted_max_quantity = trunc(max_quantity * (0.6 + aggression * 0.4))
    chip_quantity = min_quantity + :rand.uniform(max(1, adjusted_max_quantity - min_quantity))

    # 按权重随机选择一种筹码面值
    selected_chip_value = select_chip_by_weight(chip_values, chip_weights)

    # 计算总下注金额（只使用一种筹码）
    total_amount = selected_chip_value * chip_quantity

    # 确保在游戏限制范围内
    final_amount = max(config.min_bet, min(total_amount, config.max_bet))

    # 如果超出限制，调整为最大允许的该筹码倍数
    if final_amount < total_amount do
      max_quantity_allowed = div(config.max_bet, selected_chip_value)
      selected_chip_value * max(1, max_quantity_allowed)
    else
      final_amount
    end
  end

  # 计算目标下注金额范围 - 使用更合理的金额范围
  defp calculate_target_amount_range(bet_style, aggression, config) do
    # 基础金额范围（使用筹码面值作为基准）
    base_ranges = %{
      # 1-10个最小筹码
      conservative: {1000, 10000},
      # 中等金额范围
      moderate: {5000, 50000},
      # 较大金额范围
      aggressive: {10000, 200_000}
    }

    {min_base, max_base} = Map.get(base_ranges, bet_style, {5000, 50000})

    # 根据激进程度调整范围
    # 0.5 到 2.0
    aggression_multiplier = 0.5 + aggression * 1.5

    min_amount = trunc(min_base * aggression_multiplier)
    max_amount = trunc(max_base * aggression_multiplier)

    # 确保不超过游戏限制，但至少要能使用多种筹码
    final_min = max(1000, min(min_amount, config.max_bet))
    final_max = min(config.max_bet, max_amount)

    # 确保最小值不会太大导致无法下注
    if final_min > final_max do
      {1000, max(5000, config.max_bet)}
    else
      {final_min, final_max}
    end
  end

  # 生成筹码组合 - 更像真实玩家的下注方式
  defp generate_chip_combination({min_amount, max_amount}, chip_values, chip_weights, aggression) do
    # 目标金额
    target_amount = min_amount + :rand.uniform(max(1, max_amount - min_amount))

    # 使用贪心算法生成筹码组合，从大到小选择筹码
    generate_realistic_chip_combination(target_amount, chip_values, aggression)
  end

  # 生成更真实的筹码组合
  defp generate_realistic_chip_combination(target_amount, chip_values, aggression) do
    # 按面值从大到小排序
    sorted_chips = Enum.sort(chip_values, :desc)

    # 根据激进程度决定是否偏好大筹码
    # 0.3 到 0.7
    use_large_chips_probability = 0.3 + aggression * 0.4

    # 生成筹码组合
    remaining_amount = target_amount
    chip_combination = []

    # 第一阶段：使用大筹码（如果激进且金额足够大）
    {remaining_amount, chip_combination} =
      if remaining_amount >= 50000 and :rand.uniform() < use_large_chips_probability do
        add_large_chips(remaining_amount, sorted_chips, chip_combination)
      else
        {remaining_amount, chip_combination}
      end

    # 第二阶段：使用中等筹码
    {remaining_amount, chip_combination} =
      if remaining_amount >= 5000 do
        add_medium_chips(remaining_amount, sorted_chips, chip_combination)
      else
        {remaining_amount, chip_combination}
      end

    # 第三阶段：使用小筹码填补剩余金额
    chip_combination =
      if remaining_amount >= 1000 do
        add_small_chips(remaining_amount, sorted_chips, chip_combination)
      else
        chip_combination
      end

    # 如果没有筹码，至少添加一个最小筹码
    if Enum.empty?(chip_combination) do
      smallest_chip = Enum.min(chip_values)
      [smallest_chip]
    else
      chip_combination
    end
  end

  # 添加大筹码 (>=100,000)
  defp add_large_chips(remaining_amount, sorted_chips, chip_combination) do
    large_chips = Enum.filter(sorted_chips, &(&1 >= 100_000))

    if Enum.empty?(large_chips) do
      {remaining_amount, chip_combination}
    else
      # 随机选择一种大筹码
      selected_chip = Enum.random(large_chips)
      max_count = div(remaining_amount, selected_chip)

      if max_count > 0 do
        # 使用1-3个大筹码
        count = min(max_count, 1 + :rand.uniform(2))
        used_amount = selected_chip * count
        new_chips = List.duplicate(selected_chip, count)

        {remaining_amount - used_amount, chip_combination ++ new_chips}
      else
        {remaining_amount, chip_combination}
      end
    end
  end

  # 添加中等筹码 (5,000 - 50,000)
  defp add_medium_chips(remaining_amount, sorted_chips, chip_combination) do
    medium_chips = Enum.filter(sorted_chips, &(&1 >= 5000 and &1 < 100_000))

    if Enum.empty?(medium_chips) do
      {remaining_amount, chip_combination}
    else
      # 可能使用1-2种中等筹码
      chip_types_to_use = min(length(medium_chips), 1 + :rand.uniform(1))
      selected_chips = Enum.take_random(medium_chips, chip_types_to_use)

      Enum.reduce(selected_chips, {remaining_amount, chip_combination}, fn chip,
                                                                           {rem_amount, chips} ->
        if rem_amount >= chip do
          max_count = div(rem_amount, chip)
          # 使用1-5个中等筹码
          count = min(max_count, 1 + :rand.uniform(4))
          used_amount = chip * count
          new_chips = List.duplicate(chip, count)

          {rem_amount - used_amount, chips ++ new_chips}
        else
          {rem_amount, chips}
        end
      end)
    end
  end

  # 添加小筹码 (1,000 - 10,000)
  defp add_small_chips(remaining_amount, sorted_chips, chip_combination) do
    small_chips = Enum.filter(sorted_chips, &(&1 >= 1000 and &1 < 50_000))

    if Enum.empty?(small_chips) do
      chip_combination
    else
      # 使用小筹码填补剩余金额
      fill_with_small_chips(remaining_amount, small_chips, chip_combination)
    end
  end

  # 用小筹码填补剩余金额
  defp fill_with_small_chips(remaining_amount, small_chips, chip_combination) do
    if remaining_amount <= 0 do
      chip_combination
    else
      # 选择合适的小筹码
      suitable_chips = Enum.filter(small_chips, &(&1 <= remaining_amount))

      if Enum.empty?(suitable_chips) do
        chip_combination
      else
        # 优先选择能整除或接近的筹码
        best_chip =
          Enum.max_by(suitable_chips, fn chip ->
            # 计算使用这种筹码的效率
            count = div(remaining_amount, chip)
            efficiency = count * chip / remaining_amount
            # 添加一点随机性
            efficiency + :rand.uniform() * 0.1
          end)

        count = div(remaining_amount, best_chip)

        if count > 0 do
          new_chips = List.duplicate(best_chip, count)
          used_amount = best_chip * count

          # 递归处理剩余金额
          fill_with_small_chips(
            remaining_amount - used_amount,
            small_chips,
            chip_combination ++ new_chips
          )
        else
          chip_combination
        end
      end
    end
  end

  @doc """
  按权重选择筹码面值

  ## 参数
  - chip_values: 筹码面值列表
  - chip_weights: 筹码权重映射

  ## 返回
  选中的筹码面值
  """
  defp select_chip_by_weight(chip_values, chip_weights) do
    # 计算总权重
    total_weight =
      chip_values
      |> Enum.map(fn value -> Map.get(chip_weights, value, 1) end)
      |> Enum.sum()

    # 生成随机数
    random_value = :rand.uniform(total_weight)

    # 按权重选择筹码
    {selected_chip, _} =
      chip_values
      |> Enum.reduce_while({nil, 0}, fn chip_value, {_selected, accumulated_weight} ->
        weight = Map.get(chip_weights, chip_value, 1)
        new_accumulated = accumulated_weight + weight

        if random_value <= new_accumulated do
          {:halt, {chip_value, new_accumulated}}
        else
          {:cont, {chip_value, new_accumulated}}
        end
      end)

    # 备用方案
    selected_chip || Enum.random(chip_values)
  end

  @doc """
  更新机器人的虚拟积分

  ## 参数
  - state: 房间状态
  - robot_id: 机器人ID
  - amount_change: 积分变化量（正数为增加，负数为减少）

  ## 返回
  更新后的状态
  """
  def update_robot_money(state, robot_id, amount_change) do
    case Map.get(state.players, robot_id) do
      nil ->
        state

      robot_player ->
        current_points = Cypridina.Teen.GameSystem.PlayerData.get_points(robot_player)
        new_points = max(0, current_points + amount_change)

        # 如果机器人积分不足，让其退出房间
        if new_points <= 0 do
          Logger.info("🤖 [ROBOT_BROKE] 机器人积分耗尽，退出房间: #{robot_id}")
          remove_robot_from_room(state, robot_id)
        else
          # 更新积分和AI统计数据
          updated_robot =
            Cypridina.Teen.GameSystem.PlayerData.set_points(robot_player, new_points)

          # 更新AI统计（如果是结算）
          updated_robot =
            if amount_change != 0 do
              Cypridina.Teen.GameSystem.PlayerDataBuilder.update_robot_stats(
                updated_robot,
                abs(amount_change),
                max(0, amount_change)
              )
            else
              updated_robot
            end

          updated_players = Map.put(state.players, robot_id, updated_robot)
          %{state | players: updated_players}
        end
    end
  end

  @doc """
  移除机器人

  ## 参数
  - state: 房间状态
  - robot_id: 机器人ID

  ## 返回
  更新后的状态
  """
  def remove_robot_from_room(state, robot_id) do
    # 清除机器人的下注
    updated_bets = Map.delete(state.game_data.bets, robot_id)

    # 从玩家列表移除机器人
    updated_players = Map.delete(state.players, robot_id)

    # 重新计算总下注（移除机器人下注后）
    updated_total_bets = calculate_total_bets(updated_bets)

    Logger.info("🤖 [REMOVE_ROBOT] 机器人离开房间: #{robot_id}")

    %{
      state
      | players: updated_players,
        game_data: %{state.game_data | bets: updated_bets, total_bets: updated_total_bets}
    }
  end

  # ==================== 私有辅助函数 ====================

  # 获取当前最热门的下注区域
  defp get_popular_area(total_bets) do
    if map_size(total_bets) == 0 do
      Enum.random([:long, :hu, :he])
    else
      total_bets
      |> Enum.max_by(fn {_area, amount} -> amount end)
      |> elem(0)
    end
  end

  # 基于历史趋势选择区域
  defp get_trending_area(history) do
    if length(history) < 3 do
      Enum.random([:long, :hu, :he])
    else
      # 分析最近3局的结果
      recent_results =
        history
        |> Enum.take(3)
        |> Enum.map(& &1.result)

      # 统计各区域出现次数
      result_counts =
        Enum.reduce(recent_results, %{long: 0, hu: 0, he: 0}, fn result, acc ->
          Map.update(acc, result, 1, &(&1 + 1))
        end)

      # 选择出现次数最少的区域（反向投注策略）
      result_counts
      |> Enum.min_by(fn {_area, count} -> count end)
      |> elem(0)
    end
  end

  @doc """
  清理积分不足的机器人

  ## 参数
  - state: 房间状态
  - min_money: 最低积分要求（默认1000）

  ## 返回
  清理后的状态
  """
  def cleanup_broke_robots(state, min_points \\ nil) do
    # 使用统一配置的最低积分要求
    min_points = min_points || LongHuGame.robot_behavior().min_money_threshold

    broke_robots =
      state.players
      |> Enum.filter(fn {user_id, player} ->
        player.is_robot and
          is_integer(user_id) and user_id < 0 and
          Cypridina.Teen.GameSystem.PlayerData.get_points(player) < min_points
      end)

    if length(broke_robots) > 0 do
      Logger.info("🤖 [CLEANUP_ROBOTS] 清理 #{length(broke_robots)} 个积分不足的机器人")

      Enum.reduce(broke_robots, state, fn {robot_id, _player}, acc_state ->
        remove_robot_from_room(acc_state, robot_id)
      end)
    else
      state
    end
  end

  @doc """
  轮换老机器人

  ## 参数
  - state: 房间状态
  - rotation_probability: 轮换概率（默认0.1，即10%）

  ## 返回
  轮换后的状态
  """
  def rotate_old_robots(state, rotation_probability \\ nil) do
    # 使用统一配置的轮换参数
    robot_behavior = LongHuGame.robot_behavior()
    rotation_probability = rotation_probability || robot_behavior.rotation_probability
    max_lifetime_minutes = robot_behavior.max_lifetime_minutes

    old_robots =
      state.players
      |> Enum.filter(fn {user_id, player} ->
        player.is_robot and
          is_integer(user_id) and user_id < 0 and
          DateTime.diff(
            DateTime.utc_now(),
            Map.get(player.user, :created_at, DateTime.utc_now()),
            :minute
          ) > max_lifetime_minutes
      end)

    robots_to_rotate =
      old_robots
      |> Enum.filter(fn _ -> :rand.uniform() < rotation_probability end)

    if length(robots_to_rotate) > 0 do
      Logger.info("🤖 [ROTATE_ROBOTS] 轮换 #{length(robots_to_rotate)} 个老机器人")

      # 移除老机器人并添加新机器人
      state_after_removal =
        Enum.reduce(robots_to_rotate, state, fn {robot_id, _player}, acc_state ->
          remove_robot_from_room(acc_state, robot_id)
        end)

      # 添加相同数量的新机器人
      Enum.reduce(1..length(robots_to_rotate), state_after_removal, fn i, acc_state ->
        # 使用不同的索引避免ID冲突
        add_single_robot(acc_state, i + 1000)
      end)
    else
      state
    end
  end

  @doc """
  统计当前机器人数量

  ## 参数
  - state: 房间状态

  ## 返回
  机器人数量
  """
  def count_robots(state) do
    state.players
    |> Enum.count(fn {user_id, player} ->
      player.is_robot and is_integer(user_id) and user_id < 0
    end)
  end

  # 注意：generate_robot_initial_money 函数已移至 LongHuGame 统一配置模块

  # 计算总下注
  defp calculate_total_bets(bets) do
    Enum.reduce(bets, %{long: 0, hu: 0, he: 0}, fn {_user_id, user_bets}, acc ->
      Enum.reduce(user_bets, acc, fn {area, amount}, area_acc ->
        Map.update(area_acc, area, amount, &(&1 + amount))
      end)
    end)
  end
end
