defmodule Cypridina.Teen.GameSystem.Games.LongHu.LongHuLogic do
  @moduledoc """
  龙虎斗游戏逻辑模块

  游戏规则：
  - 使用标准52张扑克牌（不含大小王）
  - 每局发两张牌：龙牌和虎牌
  - 比较牌面大小，A最小(1)，K最大(13)
  - 龙大于虎 -> 龙赢
  - 虎大于龙 -> 虎赢
  - 龙等于虎 -> 和
  """

  require Logger

  # 扑克牌定义
  # 黑桃、红心、方块、梅花
  @suits [:spades, :hearts, :diamonds, :clubs]
  # A, 2-10, J, Q, K
  @ranks [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]

  # 游戏结果
  @results %{
    # 龙赢
    long: :long,
    # 虎赢
    hu: :hu,
    # 和
    he: :he
  }

  @doc """
  创建一副标准扑克牌

  ## 返回
  包含52张牌的列表，每张牌格式为 %{suit: :spades, rank: 1, value: 1}
  """
  def create_deck() do
    for suit <- @suits, rank <- @ranks do
      %{
        suit: suit,
        rank: rank,
        # 牌面值，用于比较大小
        value: rank
      }
    end
  end

  @doc """
  洗牌

  ## 参数
  - deck: 牌组

  ## 返回
  洗牌后的牌组
  """
  def shuffle_deck(deck) do
    Enum.shuffle(deck)
  end

  @doc """
  发牌 - 发出龙牌和虎牌

  ## 参数
  - opts: 选项列表
    - control_mode: 控制模式 (可选)
    - target_result: 目标结果 (可选)
    - game_id: 游戏ID (可选)

  ## 返回
  {龙牌, 虎牌} 元组
  """
  def deal_cards(opts \\ []) do
    control_mode = Keyword.get(opts, :control_mode)
    target_result = Keyword.get(opts, :target_result)
    game_id = Keyword.get(opts, :game_id)

    {long_card, hu_card} =
      if control_mode && target_result && control_mode != :random && target_result != :random do
        Logger.info("🐉 [LONGHU_LOGIC] 使用控制发牌: mode=#{control_mode}, target=#{target_result}")
        deal_controlled_cards(target_result)
      else
        Logger.info("🐉 [LONGHU_LOGIC] 使用随机发牌: mode=#{control_mode}, target=#{target_result}")
        deal_random_cards()
      end

    Logger.info("🐉 [LONGHU_LOGIC] 发牌: 龙=#{format_card(long_card)}, 虎=#{format_card(hu_card)}")

    # 注意：发牌时不发送结果回调到控制系统
    # 真正的库存更新在游戏结算完成后进行
    # 这里只记录发牌结果用于后续计算
    if game_id do
      result = calculate_result(long_card, hu_card)
      Logger.debug("🐉 [LONGHU_LOGIC] 发牌结果: #{result}, 将在结算时通知控制系统")
    end

    {long_card, hu_card}
  end

  @doc """
  随机发牌 - 原始逻辑
  """
  def deal_random_cards() do
    deck = create_deck() |> shuffle_deck()
    [long_card, hu_card | _rest] = deck
    {long_card, hu_card}
  end

  @doc """
  控制发牌 - 根据目标结果发牌
  """
  def deal_controlled_cards(target_result) do
    deck = create_deck() |> shuffle_deck()

    case target_result do
      :long ->
        # 龙赢：确保龙牌大于虎牌
        find_cards_for_result(deck, :long)

      :hu ->
        # 虎赢：确保虎牌大于龙牌
        find_cards_for_result(deck, :hu)

      :he ->
        # 和：确保龙牌等于虎牌
        find_cards_for_result(deck, :he)

      _ ->
        # 默认随机发牌
        [long_card, hu_card | _rest] = deck
        {long_card, hu_card}
    end
  end

  # 私有函数：根据目标结果查找合适的牌
  defp find_cards_for_result(deck, target_result) do
    case target_result do
      :long ->
        # 找到龙牌大于虎牌的组合
        find_long_win_cards(deck)

      :hu ->
        # 找到虎牌大于龙牌的组合
        find_hu_win_cards(deck)

      :he ->
        # 找到龙牌等于虎牌的组合
        find_tie_cards(deck)
    end
  end

  defp find_long_win_cards(deck) do
    Enum.reduce_while(deck, nil, fn long_card, _acc ->
      hu_card =
        Enum.find(deck, fn card ->
          card != long_card && card.value < long_card.value
        end)

      if hu_card do
        {:halt, {long_card, hu_card}}
      else
        {:cont, nil}
      end
    end) || deal_random_cards()
  end

  defp find_hu_win_cards(deck) do
    Enum.reduce_while(deck, nil, fn hu_card, _acc ->
      long_card =
        Enum.find(deck, fn card ->
          card != hu_card && card.value < hu_card.value
        end)

      if long_card do
        {:halt, {long_card, hu_card}}
      else
        {:cont, nil}
      end
    end) || deal_random_cards()
  end

  defp find_tie_cards(deck) do
    Enum.reduce_while(deck, nil, fn long_card, _acc ->
      hu_card =
        Enum.find(deck, fn card ->
          card != long_card && card.value == long_card.value
        end)

      if hu_card do
        {:halt, {long_card, hu_card}}
      else
        {:cont, nil}
      end
    end) || deal_random_cards()
  end

  @doc """
  计算游戏结果

  ## 参数
  - long_card: 龙牌
  - hu_card: 虎牌

  ## 返回
  :long | :hu | :he
  """
  def calculate_result(long_card, hu_card) do
    long_value = long_card.value
    hu_value = hu_card.value

    result =
      cond do
        long_value > hu_value -> @results.long
        hu_value > long_value -> @results.hu
        long_value == hu_value -> @results.he
      end

    Logger.info("🐉 [LONGHU_LOGIC] 游戏结果: 龙=#{long_value}, 虎=#{hu_value} -> #{result}")

    result
  end

  @doc """
  格式化扑克牌显示

  ## 参数
  - card: 扑克牌 %{suit: :spades, rank: 1, value: 1}

  ## 返回
  格式化的字符串，如 "♠A", "♥K"
  """
  def format_card(%{suit: suit, rank: rank}) do
    suit_symbol =
      case suit do
        :spades -> "♠"
        :hearts -> "♥"
        :diamonds -> "♦"
        :clubs -> "♣"
      end

    rank_symbol =
      case rank do
        1 -> "A"
        11 -> "J"
        12 -> "Q"
        13 -> "K"
        n -> to_string(n)
      end

    "#{suit_symbol}#{rank_symbol}"
  end
end
