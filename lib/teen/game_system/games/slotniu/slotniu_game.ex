defmodule Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuGame do
  @moduledoc """
  SlotNiu老虎机游戏定义模块

  实现游戏工厂行为，定义SlotNiu游戏的基本信息和配置
  这是一个单人在线老虎机游戏，玩家与RNG系统对战
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  alias Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig

  @impl true
  def game_type, do: :slotniu

  @impl true
  def game_name, do: "SlotNiu老虎机"

  @impl true
  def room_module, do: Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuRoom

  @impl true
  def default_config do
    # 获取配置化的游戏配置
    game_config = SlotNiuConfig.get_current_config()

    # 合并房间配置和游戏配置
    Map.merge(
      %{
        # 最大玩家数 (单人游戏但支持多个房间)
        max_players: 1,
        # 最小玩家数 (单人游戏)
        min_players: 1,
        # 立即开始
        auto_start_delay: 100,
        # 不启用机器人 (单人游戏)
        enable_robots: false,
        robot_count: 0,

        # SlotNiu游戏配置
        game_config: %{
          # 老虎机配置
          # 行数
          rows: 3,
          # 列数
          cols: 5,
          # 支付线数
          lines: 9,

          # 下注配置
          # 最小下注
          min_bet: 1.8,
          # 最大下注
          max_bet: 1800,
          # 下注金额选项
          bet_amounts: [1.8, 9, 18, 90, 180, 900, 1800],

          # 游戏配置
          # 返还率
          rtp: 96.5,
          # 最大线数
          max_lines: 9,

          # 图标配置 (11种图标)
          symbol_config: %{
            # WILD图标
            wild_symbol: 0,
            # 普通图标
            normal_symbols: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            # 牛头图标(用于触发特殊功能)
            scatter_symbol: 10
          },

          # 支付表配置 (参考IndiaGameServer的配置)
          paytable: %{
            # 图标ID => [3连, 4连, 5连]的倍率
            # 10
            1 => [3, 10, 75],
            # J
            2 => [3, 10, 90],
            # Q
            3 => [15, 40, 150],
            # K
            4 => [25, 50, 200],
            # A
            5 => [30, 70, 250],
            # 狼头
            6 => [35, 80, 300],
            # 虎头
            7 => [45, 100, 350],
            # 鹰头
            8 => [70, 170, 400],
            # 鹿头 (特殊图标)
            9 => [0, 0, 0],
            # 牛头 (Scatter)
            10 => [25, 40, 400],
            # WILD (最高倍率)
            0 => [100, 200, 1750]
          },

          # 免费游戏配置
          free_game_config: %{
            # 触发免费游戏需要的牛头数量
            trigger_count: 3,
            # 免费游戏次数
            free_spins: 10,
            # 免费游戏倍率
            multiplier: 2
          },

          # Jackpot配置 - 使用统一配置
          jackpot_config: SlotNiuConfig.get_jackpot_config(),

          # 转盘配置 - 使用统一配置
          turntable_config: %{
            rewards: SlotNiuConfig.get_turntable_rewards()
          }
        }
      },
      game_config
    )
  end

  @impl true
  def is_lobby_game?, do: false

  @impl true
  def supported_game_ids do
    [
      # SlotNiu游戏ID (与前端TpMasterClient匹配)
      41,
      # 协议中使用的ID
      5
    ]
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_stats do
    %{
      total_rooms: get_total_rooms(),
      active_players: get_active_players(),
      total_jackpot: get_total_jackpot()
    }
  end

  @doc """
  获取当前活跃房间数
  """
  def get_total_rooms do
    # 这里可以实现获取活跃房间数的逻辑
    0
  end

  @doc """
  获取当前活跃玩家数
  """
  def get_active_players do
    # 这里可以实现获取活跃玩家数的逻辑
    0
  end

  @doc """
  获取当前总Jackpot金额
  """
  def get_total_jackpot do
    # 这里可以实现获取总Jackpot金额的逻辑
    config = default_config()
    config.game_config.jackpot_config.seed_amount
  end

  @doc """
  验证游戏配置
  """
  def validate_config(config) do
    required_keys = [:max_players, :min_players, :game_config]

    case Enum.all?(required_keys, &Map.has_key?(config, &1)) do
      true -> {:ok, config}
      false -> {:error, "Missing required configuration keys"}
    end
  end

  @doc """
  获取游戏版本信息
  """
  def version_info do
    %{
      version: "1.0.0",
      build_date: "2024-01-01",
      features: [
        "单人老虎机游戏",
        "Jackpot系统",
        "免费游戏",
        "转盘功能",
        "Wild和Scatter图标",
        "多倍率下注",
        "RNG算法"
      ]
    }
  end
end
