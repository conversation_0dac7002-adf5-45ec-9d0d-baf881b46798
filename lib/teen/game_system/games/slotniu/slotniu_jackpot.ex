defmodule Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuJackpot do
  @moduledoc """
  SlotNiu Jackpot管理模块 - 全局内存存储
  负责：
  - 全局Jackpot记录管理
  - 中奖记录存储和排序
  - 所有SlotNiu房间共享访问
  """

  use GenServer
  require Logger

  # Jackpot状态结构
  defstruct [
    # 中奖记录列表
    :records,
    # 总记录数
    :total_records,
    # 最后中奖者
    :last_winner,
    # 最后更新时间
    :last_updated,
    # 假记录生成器定时器引用
    :fake_generator_ref,
    # 假记录生成器是否激活
    :fake_generator_active
  ]

  # 配置常量
  # 最多保存100条记录
  @max_records 100
  # GenServer进程名
  @server_name __MODULE__

  ## Client API

  @doc """
  启动SlotNiu Jackpot管理器
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: @server_name)
  end

  @doc """
  添加新的Jackpot中奖记录
  参考旧项目逻辑：只有真正的大奖才会被记录到全局Jackpot列表中
  """
  def add_jackpot_record(
        player_id,
        player_name,
        niunum,
        bet_amount,
        win_amount,
        player_data \\ nil
      ) do
    # 数据验证和清理
    safe_player_id =
      if is_integer(player_id) and player_id > 0, do: player_id, else: :rand.uniform(999_999)

    safe_player_name =
      if is_binary(player_name) and String.length(player_name) > 0,
        do: player_name,
        else: "玩家#{safe_player_id}"

    safe_niunum = if is_integer(niunum) and niunum >= 0, do: niunum, else: 0
    safe_bet_amount = if is_number(bet_amount) and bet_amount > 0, do: bet_amount, else: 0
    safe_win_amount = if is_number(win_amount) and win_amount > 0, do: win_amount, else: 0

    # 根据游戏配置计算正确的倍率
    safe_mult = calculate_correct_multiplier(safe_bet_amount, safe_niunum)

    # 获取玩家头像信息
    {headid, wxheadurl} = extract_player_avatar_info(player_data, safe_player_id)

    record = %{
      player_id: safe_player_id,
      player_name: safe_player_name,
      # 牛的个数（SlotNiu特有）
      niunum: safe_niunum,
      # 下注金额
      bet_amount: safe_bet_amount,
      # 中奖金额
      win_amount: safe_win_amount,
      # Jackpot倍数（根据配置计算）
      mult: safe_mult,
      # 玩家头像ID
      headid: headid,
      # 玩家自定义头像URL
      wxheadurl: wxheadurl,
      timestamp: DateTime.utc_now(),
      type: determine_jackpot_type(safe_niunum, safe_win_amount)
    }

    Logger.info(
      "🎰 [SLOTNIU_JACKPOT] 准备添加记录: #{safe_player_name}, 牛数: #{safe_niunum}, 奖金: #{safe_win_amount}, 倍率: #{safe_mult}"
    )

    GenServer.cast(@server_name, {:add_record, record})
  end

  @doc """
  获取所有Jackpot记录（按奖金从大到小排序）
  如果没有记录，会自动生成初始记录
  """
  def get_jackpot_records() do
    try do
      case GenServer.call(@server_name, :get_records, 5000) do
        {:ok, []} ->
          # 如果没有记录，生成初始记录
          Logger.info("🎰 [SLOTNIU_JACKPOT] 没有记录，生成初始记录")
          generate_initial_records(20)
          # 等待一下让记录生成完成，然后再次获取
          :timer.sleep(100)
          GenServer.call(@server_name, :get_records, 5000)

        result ->
          result
      end
    catch
      :exit, {:noproc, _} ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] Jackpot进程未启动，返回空记录")
        {:ok, []}

      :exit, {:timeout, _} ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] 获取记录超时")
        {:error, :timeout}

      :exit, reason ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] 获取记录失败: #{inspect(reason)}")
        {:error, :jackpot_unavailable}
    end
  end

  @doc """
  获取记录总数
  """
  def get_total_records() do
    try do
      GenServer.call(@server_name, :get_total_records, 5000)
    catch
      :exit, {:noproc, _} ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] Jackpot进程未启动")
        {:ok, 0}

      :exit, reason ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] 获取总数失败: #{inspect(reason)}")
        {:error, :jackpot_unavailable}
    end
  end

  @doc """
  清空所有记录（管理员功能）
  """
  def clear_all_records() do
    GenServer.cast(@server_name, :clear_records)
  end

  @doc """
  生成初始记录（当没有记录时调用）
  """
  def generate_initial_records(count \\ 20) do
    GenServer.cast(@server_name, {:generate_initial_records, count})
  end

  @doc """
  格式化记录用于前端显示
  参考前端FMWJackpotRecordLayer.ts的onUpdateRecordInfo方法期望的数据格式
  """
  def format_records_for_frontend(records) do
    Enum.map(records, fn record ->
      # 格式化时间为前端期望的格式
      formatted_time =
        record.timestamp
        |> DateTime.to_unix()
        |> format_timestamp_for_frontend()

      # 确保所有字段都存在且格式正确
      formatted_record = %{
        # 玩家ID（前端用于判断是否为空记录）
        "playerid" => record.player_id,
        # 玩家昵称
        "name" => record.player_name,
        # 玩家系统头像id
        "headid" => Map.get(record, :headid, 1),
        # 自定义头像url
        "wxheadurl" => Map.get(record, :wxheadurl, ""),
        # 牛的个数（SlotNiu特有字段，前端显示为"x#{niunum}"）
        "niunum" => record.niunum,
        # 前端下注倍率（前端显示为"₹#{bet}"）
        "bet" => record.bet_amount,
        # 玩家赢的金币（前端显示为"₹#{winscore}"）
        "winscore" => record.win_amount,
        # 当局时间（前端直接显示）
        "time" => formatted_time,
        # Jackpot倍数（前端显示为"#{mult}x"）
        "mult" => record.mult
      }

      formatted_record
    end)
  end

  ## Server Callbacks

  @impl true
  def init(_opts) do
    # 初始化空状态
    state = %__MODULE__{
      records: [],
      total_records: 0,
      last_winner: nil,
      last_updated: DateTime.utc_now(),
      fake_generator_ref: nil,
      fake_generator_active: false
    }

    Logger.info("🎰 [SLOTNIU_JACKPOT] SlotNiu Jackpot管理器启动")
    Logger.info("🎰 [SLOTNIU_JACKPOT] 初始记录数: #{state.total_records}")

    # 自动启动假记录生成器
    send(self(), :start_fake_generator)

    {:ok, state}
  end

  @impl true
  def handle_cast({:add_record, record}, state) do
    Logger.info(
      "🎰 [SLOTNIU_JACKPOT] 添加新记录 - 玩家: #{record.player_name}, 牛数: #{record.niunum}, 奖金: #{record.win_amount}"
    )

    # 添加新记录到列表开头
    new_records = [record | state.records]

    # 按奖金从大到小排序
    sorted_records = Enum.sort_by(new_records, & &1.win_amount, :desc)

    # 保持最多100条记录
    final_records = Enum.take(sorted_records, @max_records)

    new_state = %{
      state
      | records: final_records,
        total_records: state.total_records + 1,
        last_winner: record,
        last_updated: DateTime.utc_now()
    }

    Logger.info(
      "🎰 [SLOTNIU_JACKPOT] 记录已添加，当前记录数: #{length(final_records)}, 总记录数: #{new_state.total_records}"
    )

    {:noreply, new_state}
  end

  @impl true
  def handle_cast(:clear_records, state) do
    Logger.info("🎰 [SLOTNIU_JACKPOT] 清空所有记录")

    new_state = %{
      state
      | records: [],
        total_records: 0,
        last_winner: nil,
        last_updated: DateTime.utc_now()
    }

    {:noreply, new_state}
  end

  @impl true
  def handle_cast({:generate_initial_records, count}, state) do
    Logger.info("🎰 [SLOTNIU_JACKPOT] 开始生成 #{count} 条初始记录")

    # 🎯 修复：生成指定数量的假记录，每个记录都有不同的历史时间
    initial_records =
      Enum.map(1..count, fn i ->
        # 为了让初始记录时间更分散，给每个记录一个基础偏移
        base_record = generate_fake_jackpot_record()

        # 为初始记录生成更分散的时间分布
        # 让记录按时间倒序排列，最新的在前面
        # 每个记录额外偏移0-1小时
        additional_offset = (i - 1) * :rand.uniform(3600)
        adjusted_timestamp = DateTime.add(base_record.timestamp, -additional_offset, :second)

        %{base_record | timestamp: adjusted_timestamp}
      end)

    # 按时间从新到旧排序（最新的在前面）
    time_sorted_records = Enum.sort_by(initial_records, & &1.timestamp, {:desc, DateTime})

    # 再按奖金从大到小排序
    sorted_records = Enum.sort_by(time_sorted_records, & &1.win_amount, :desc)

    # 合并到现有记录中
    all_records = sorted_records ++ state.records
    final_records = Enum.take(all_records, @max_records)

    new_state = %{
      state
      | records: final_records,
        total_records: state.total_records + count,
        last_winner: List.first(sorted_records),
        last_updated: DateTime.utc_now()
    }

    Logger.info("🎰 [SLOTNIU_JACKPOT] 成功生成 #{count} 条初始记录，当前总记录数: #{length(final_records)}")

    {:noreply, new_state}
  end

  @impl true
  def handle_call(:get_records, _from, state) do
    {:reply, {:ok, state.records}, state}
  end

  @impl true
  def handle_call(:get_total_records, _from, state) do
    {:reply, {:ok, state.total_records}, state}
  end

  @impl true
  def handle_info(:start_fake_generator, state) do
    if not state.fake_generator_active do
      # 启动假记录生成器，随机间隔 3-8 秒生成一条记录（您已修改为秒）
      # 3-8秒转换为毫秒
      interval = (3 + :rand.uniform(5)) * 1000
      timer_ref = Process.send_after(self(), :generate_fake_record, interval)

      new_state = %{state | fake_generator_ref: timer_ref, fake_generator_active: true}

      Logger.info("🎰 [SLOTNIU_JACKPOT] 假记录生成器已启动，下次生成时间: #{interval / 1000} 秒后")
      {:noreply, new_state}
    else
      {:noreply, state}
    end
  end

  @impl true
  def handle_info(:generate_fake_record, state) do
    # 🎯 修复：生成一条假的中奖记录，但时间设置为稍早一些，模拟真实的历史记录
    base_fake_record = generate_fake_jackpot_record()

    # 为定时生成的记录设置一个较小的时间偏移（1-10分钟前）
    # 这样看起来像是刚刚发生的历史记录，而不是实时生成的
    # 1-10分钟
    time_offset = (1 + :rand.uniform(9)) * 60
    adjusted_timestamp = DateTime.add(DateTime.utc_now(), -time_offset, :second)

    fake_record = %{base_fake_record | timestamp: adjusted_timestamp}

    # 添加记录到列表开头
    new_records = [fake_record | state.records]

    # 按奖金从大到小排序
    sorted_records = Enum.sort_by(new_records, & &1.win_amount, :desc)

    # 保持最多100条记录
    final_records = Enum.take(sorted_records, @max_records)

    # 🎯 修复：增加生成间隔的随机性，从几分钟到几十分钟不等
    # 30% 概率 3-8秒（原来的频率）
    # 40% 概率 1-5分钟
    # 30% 概率 5-15分钟
    random_value = :rand.uniform(100)

    next_interval_ms =
      cond do
        random_value <= 30 ->
          # 3-8秒（保持一些快速生成）
          (3 + :rand.uniform(5)) * 1000

        random_value <= 70 ->
          # 1-5分钟
          (60 + :rand.uniform(240)) * 1000

        true ->
          # 5-15分钟
          (300 + :rand.uniform(600)) * 1000
      end

    timer_ref = Process.send_after(self(), :generate_fake_record, next_interval_ms)

    new_state = %{
      state
      | records: final_records,
        total_records: state.total_records + 1,
        last_winner: fake_record,
        last_updated: DateTime.utc_now(),
        fake_generator_ref: timer_ref
    }

    Logger.info(
      "🎰 [SLOTNIU_JACKPOT] 生成假记录 - 玩家: #{fake_record.player_name}, 牛数: #{fake_record.niunum}, 奖金: #{fake_record.win_amount}, 时间: #{DateTime.to_string(fake_record.timestamp)}, 下次生成: #{next_interval_ms / 1000} 秒后"
    )

    {:noreply, new_state}
  end

  @impl true
  def handle_info(
        {:timeout, timer_ref, :generate_fake_record},
        %{fake_generator_ref: timer_ref} = state
      ) do
    # 处理定时器超时消息（备用处理）
    send(self(), :generate_fake_record)
    {:noreply, state}
  end

  @impl true
  def handle_info(_msg, state) do
    {:noreply, state}
  end

  ## Private Functions

  # 根据前端下注金额和牛头数量计算正确的Jackpot倍率
  # bet_amount现在是前端下注金额（如1800, 900, 180等）
  defp calculate_correct_multiplier(bet_amount, niunum) do
    # 从SlotNiu配置文件获取Jackpot倍率配置
    config = Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig.get_default_config()
    jackpot_multipliers = get_in(config, [:jackpot, :multipliers])

    # 查找匹配的配置
    bet_config = Map.get(jackpot_multipliers, bet_amount)

    if bet_config do
      Map.get(bet_config, niunum, 1)
    else
      # 如果没有直接匹配，按比例计算（基于900的配置）
      base_config = Map.get(jackpot_multipliers, 900, %{})
      base_mult = Map.get(base_config, niunum, 1)

      if base_mult > 1 and bet_amount > 0 do
        # 按比例计算倍率
        calculated_mult = base_mult * bet_amount / 900
        # 保留两位小数
        Float.round(calculated_mult, 2)
      else
        1
      end
    end
  end

  # 根据牛数和奖金确定Jackpot类型
  # 参考旧项目逻辑和SlotNiu游戏特点
  defp determine_jackpot_type(niunum, win_amount) do
    cond do
      # 转盘Jackpot（niunum为0但有奖金）
      niunum == 0 and win_amount > 0 -> :turntable_jackpot
      # 5牛超级大奖
      niunum >= 5 and win_amount >= 50000 -> :grand_jackpot
      # 4牛大奖
      niunum >= 4 and win_amount >= 20000 -> :major_jackpot
      # 3牛中奖
      niunum >= 3 and win_amount >= 5000 -> :minor_jackpot
      # 其他情况（不应该被记录，但作为保险）
      true -> :normal_win
    end
  end

  # 格式化时间戳为前端期望的格式
  defp format_timestamp_for_frontend(unix_timestamp) do
    case DateTime.from_unix(unix_timestamp) do
      {:ok, datetime} ->
        # 转换为本地时间并格式化为 "MM-dd HH:mm" 格式
        datetime
        # 转换为UTC+8
        |> DateTime.add(8 * 3600, :second)
        |> DateTime.to_string()
        # 提取 "MM-dd HH:mm" 部分
        |> String.slice(5, 11)
        |> String.replace("T", " ")

      {:error, _} ->
        "00-00 00:00"
    end
  end

  # 提取玩家头像信息
  defp extract_player_avatar_info(player_data, default_player_id) do
    cond do
      # 如果有完整的玩家数据
      is_map(player_data) ->
        headid =
          cond do
            # 机器人玩家
            Map.get(player_data, :is_robot, false) ->
              Map.get(player_data, :headid, 1 + rem(abs(default_player_id), 10))

            # 真实玩家 - 从user对象获取
            Map.has_key?(player_data, :user) and is_map(player_data.user) ->
              Map.get(player_data.user, :avatar_id, 1)

            # 直接有headid字段
            Map.has_key?(player_data, :headid) ->
              Map.get(player_data, :headid, 1)

            # 默认情况
            true ->
              1
          end

        wxheadurl =
          cond do
            # 机器人没有自定义头像
            Map.get(player_data, :is_robot, false) ->
              ""

            # 真实玩家 - 从user对象获取
            Map.has_key?(player_data, :user) and is_map(player_data.user) ->
              Map.get(player_data.user, :avatar_url, "")

            # 直接有wxheadurl字段
            Map.has_key?(player_data, :wxheadurl) ->
              Map.get(player_data, :wxheadurl, "")

            # 默认情况
            true ->
              ""
          end

        {headid, wxheadurl}

      # 没有玩家数据，使用默认值
      true ->
        {1, ""}
    end
  end

  # 停止假记录生成器定时器
  defp stop_fake_generator_timer(state) do
    if state.fake_generator_ref do
      Process.cancel_timer(state.fake_generator_ref)
    end

    %{state | fake_generator_ref: nil, fake_generator_active: false}
  end

  # 生成假的Jackpot中奖记录 - 根据现有奖池倒推合理的历史记录
  defp generate_fake_jackpot_record(current_jackpot_amount \\ nil) do
    # 生成假玩家信息
    fake_player = generate_fake_player()

    # 🎯 使用传入的奖池金额，如果没有则使用默认值
    current_jackpot = current_jackpot_amount || get_default_jackpot_amount()

    # 🔧 根据奖池倍率表和现有奖池倒推合理的中奖记录
    {bet_amount, niunum, jackpot_mult, win_amount} =
      generate_reasonable_win_by_reverse_calculation(current_jackpot)

    # 🎯 修复：生成随机的历史时间戳，从几分钟到几天前不等
    random_timestamp = generate_random_historical_timestamp()

    Logger.info(
      "🎰 [FAKE_RECORD] 奖池倒推生成 - 当前奖池: #{current_jackpot}, 下注: #{bet_amount}, 牛数: #{niunum}, 奖池倍率: #{jackpot_mult}, 中奖: #{win_amount}, 时间: #{DateTime.to_string(random_timestamp)}"
    )

    %{
      player_id: fake_player.player_id,
      player_name: fake_player.player_name,
      niunum: niunum,
      bet_amount: bet_amount,
      win_amount: win_amount,
      mult: jackpot_mult,
      headid: fake_player.headid,
      wxheadurl: fake_player.wxheadurl,
      timestamp: random_timestamp,
      type: determine_jackpot_type(niunum, win_amount)
    }
  end

  # 生成假玩家信息
  defp generate_fake_player() do
    # 印度风格的名字前缀和后缀
    indian_prefixes = [
      "Raj",
      "Amit",
      "Suresh",
      "Vikram",
      "Arjun",
      "Rohit",
      "Sanjay",
      "Deepak",
      "Anil",
      "Manoj",
      "Ravi",
      "Ashok",
      "Vinod",
      "Ramesh",
      "Mukesh",
      "Ajay",
      "Priya",
      "Sunita",
      "Kavita",
      "Meera",
      "Pooja",
      "Neha",
      "Rekha",
      "Sita",
      "Lucky",
      "Happy",
      "Winner",
      "King",
      "Master",
      "Pro",
      "Star",
      "Hero"
    ]

    indian_suffixes = [
      "Kumar",
      "Singh",
      "Sharma",
      "Gupta",
      "Verma",
      "Agarwal",
      "Jain",
      "Shah",
      "Patel",
      "Reddy",
      "Rao",
      "Nair",
      "Iyer",
      "Menon",
      "Pillai",
      "Das",
      "123",
      "777",
      "999",
      "786",
      "007",
      "888",
      "555",
      "111"
    ]

    # 生成玩家ID（使用正数，模拟真实玩家）
    player_id = 100_000 + :rand.uniform(900_000)

    # 生成玩家名字
    prefix = Enum.random(indian_prefixes)
    suffix = Enum.random(indian_suffixes)
    player_name = "#{prefix}#{suffix}"

    # 生成头像ID（1-24之间）
    headid = 1 + :rand.uniform(23)

    %{
      player_id: player_id,
      player_name: player_name,
      headid: headid,
      # 假玩家不使用自定义头像
      wxheadurl: ""
    }
  end

  # 获取默认奖池金额
  defp get_default_jackpot_amount() do
    try do
      config = Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig.get_jackpot_config()
      Map.get(config, :initial_amount, 1_500_000)
    rescue
      _ -> 1_500_000
    end
  end

  # 🎯 核心函数：根据现有奖池倒推合理的中奖记录
  # 逻辑：之前奖池 = 现有奖池 + 中奖金额，所以中奖金额要合理
  # 🔧 使用奖池倍率公式：(奖池金额 / 10000) * 奖池倍率
  defp generate_reasonable_win_by_reverse_calculation(current_jackpot) do
    # 获取奖池倍率表（不是普通倍率表）
    config = Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig.get_default_config()
    jackpot_multipliers = get_in(config, [:jackpot, :multipliers])

    # 所有可能的下注金额
    bet_amounts = [180, 900, 1800, 9000, 18000, 90000, 180_000]

    # 根据奖池大小确定合理的中奖金额范围
    {min_win, max_win} = calculate_reasonable_win_range(current_jackpot)

    # 尝试找到合理的组合
    max_attempts = 50

    find_reasonable_combination = fn ->
      bet_amount = Enum.random(bet_amounts)
      bet_config = Map.get(jackpot_multipliers, bet_amount, %{})

      # 根据奖池大小选择合适的牛数范围
      available_niunums = get_available_niunums_for_jackpot(current_jackpot, bet_amount)

      if length(available_niunums) > 0 do
        niunum = Enum.random(available_niunums)
        jackpot_mult = Map.get(bet_config, niunum, 1)

        # 🎯 使用奖池倍率公式计算中奖金额
        # 假设这是一个历史奖池金额，应该比当前奖池大一些
        historical_jackpot = current_jackpot + :rand.uniform(trunc(current_jackpot * 0.1))
        calculated_win = trunc(historical_jackpot / 10000.0 * jackpot_mult)

        # 检查是否在合理范围内
        if calculated_win >= min_win and calculated_win <= max_win do
          # 添加一些随机变化让金额更真实
          final_win = trunc(calculated_win * (0.9 + :rand.uniform() * 0.2))
          {bet_amount, niunum, jackpot_mult, final_win, historical_jackpot}
        else
          nil
        end
      else
        nil
      end
    end

    # 尝试找到合理的组合
    result =
      Stream.repeatedly(find_reasonable_combination)
      |> Stream.take(max_attempts)
      |> Enum.find(&(&1 != nil))

    case result do
      {bet_amount, niunum, jackpot_mult, win_amount, historical_jackpot} ->
        {bet_amount, niunum, jackpot_mult, win_amount}

      nil ->
        # 如果找不到合理组合，使用保守的默认值
        Logger.warning("🎰 [REVERSE_CALC] 找不到合理组合，使用保守默认值，奖池: #{current_jackpot}")
        generate_conservative_jackpot_record(current_jackpot)
    end
  end

  # 计算合理的中奖金额范围
  defp calculate_reasonable_win_range(current_jackpot) do
    cond do
      # 很小奖池：中奖金额应该在奖池的1%-10%之间
      current_jackpot < 50000 ->
        {trunc(current_jackpot * 0.01), trunc(current_jackpot * 0.1)}

      # 小奖池：中奖金额应该在奖池的0.5%-8%之间
      current_jackpot < 200_000 ->
        {trunc(current_jackpot * 0.005), trunc(current_jackpot * 0.08)}

      # 中等奖池：中奖金额应该在奖池的0.2%-5%之间
      current_jackpot < 1_000_000 ->
        {trunc(current_jackpot * 0.002), trunc(current_jackpot * 0.05)}

      # 大奖池：中奖金额应该在奖池的0.1%-3%之间
      true ->
        {trunc(current_jackpot * 0.001), trunc(current_jackpot * 0.03)}
    end
  end

  # 根据奖池大小获取可用的牛数范围
  defp get_available_niunums_for_jackpot(current_jackpot, bet_amount) do
    cond do
      # 很小奖池：只允许低牛数
      current_jackpot < 50000 ->
        case bet_amount do
          # 高下注不允许
          amount when amount >= 90000 -> []
          # 只允许3牛
          amount when amount >= 18000 -> [3]
          # 3-4牛
          amount when amount >= 9000 -> [3, 4]
          # 3-5牛
          _ -> [3, 4, 5]
        end

      # 小奖池：限制高牛数
      current_jackpot < 200_000 ->
        case bet_amount do
          # 只允许3牛
          amount when amount >= 90000 -> [3]
          # 3-4牛
          amount when amount >= 18000 -> [3, 4]
          # 3-5牛
          amount when amount >= 9000 -> [3, 4, 5]
          # 3-6牛
          _ -> [3, 4, 5, 6]
        end

      # 中等奖池：适中范围
      current_jackpot < 1_000_000 ->
        case bet_amount do
          # 3-4牛
          amount when amount >= 90000 -> [3, 4]
          # 3-5牛
          amount when amount >= 18000 -> [3, 4, 5]
          # 3-6牛
          amount when amount >= 9000 -> [3, 4, 5, 6]
          # 3-7牛
          _ -> [3, 4, 5, 6, 7]
        end

      # 大奖池：允许更高牛数
      true ->
        case bet_amount do
          # 3-5牛
          amount when amount >= 90000 -> [3, 4, 5]
          # 3-6牛
          amount when amount >= 18000 -> [3, 4, 5, 6]
          # 3-7牛
          amount when amount >= 9000 -> [3, 4, 5, 6, 7]
          # 3-8牛
          _ -> [3, 4, 5, 6, 7, 8]
        end
    end
  end

  # 生成保守的奖池记录（当找不到合理组合时）
  defp generate_conservative_jackpot_record(current_jackpot) do
    # 使用最小的下注和牛数
    bet_amount = 180
    niunum = 3
    # 180下注3牛的奖池倍率
    jackpot_mult = 2000

    # 🎯 使用奖池倍率公式：(奖池金额 / 10000) * 奖池倍率
    # 假设历史奖池比当前奖池大5%
    historical_jackpot = current_jackpot + trunc(current_jackpot * 0.05)
    win_amount = trunc(historical_jackpot / 10000.0 * jackpot_mult)

    # 确保中奖金额不会太大
    max_reasonable = trunc(current_jackpot * 0.05)
    win_amount = min(win_amount, max_reasonable)

    {bet_amount, niunum, jackpot_mult, win_amount}
  end

  # 🎯 生成随机的历史时间戳，从几分钟到几天前不等
  defp generate_random_historical_timestamp() do
    now = DateTime.utc_now()

    # 生成随机的时间偏移量
    # 权重分布：
    # - 30% 几分钟前 (2-30分钟)
    # - 40% 几小时前 (1-12小时)
    # - 20% 一天内 (12-24小时)
    # - 10% 几天前 (1-7天)

    random_value = :rand.uniform(100)

    offset_seconds =
      cond do
        random_value <= 30 ->
          # 几分钟前 (2-30分钟)
          minutes = 2 + :rand.uniform(28)
          minutes * 60

        random_value <= 70 ->
          # 几小时前 (1-12小时)
          hours = 1 + :rand.uniform(11)
          hours * 3600

        random_value <= 90 ->
          # 一天内 (12-24小时)
          hours = 12 + :rand.uniform(12)
          hours * 3600

        true ->
          # 几天前 (1-7天)
          days = 1 + :rand.uniform(6)
          days * 24 * 3600
      end

    # 添加一些随机秒数让时间更分散
    # 0-1小时的随机秒数
    random_extra_seconds = :rand.uniform(3600)
    total_offset = offset_seconds + random_extra_seconds

    # 从当前时间减去偏移量
    DateTime.add(now, -total_offset, :second)
  end

  # 计算假记录的倍率
  defp calculate_fake_multiplier(bet_amount, niunum) do
    # 使用与真实游戏相同的倍率配置
    config = Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig.get_default_config()
    jackpot_multipliers = get_in(config, [:jackpot, :multipliers])

    # 查找匹配的配置
    bet_config = Map.get(jackpot_multipliers, bet_amount)

    if bet_config do
      Map.get(bet_config, niunum, 1)
    else
      # 如果没有直接匹配，按比例计算（基于900的配置）
      base_config = Map.get(jackpot_multipliers, 900, %{})
      base_mult = Map.get(base_config, niunum, 1)

      if base_mult > 1 and bet_amount > 0 do
        # 按比例计算倍率
        calculated_mult = base_mult * bet_amount / 900
        # 保留两位小数
        Float.round(calculated_mult, 2)
      else
        1
      end
    end
  end
end
