defmodule <PERSON><PERSON>ridina.Teen.GameSystem.Games.SlotCat.SlotCatTest do
  @moduledoc """
  SlotCat 游戏测试模块

  用于测试游戏逻辑的正确性和性能
  """

  alias Cypridina.Teen.GameSystem.Games.SlotCat.{SlotCatGameLogic, SlotCatConstants}
  require Logger

  @doc """
  运行基础游戏测试
  """
  def run_basic_test do
    Logger.info("开始 SlotCat 基础游戏测试...")

    # 测试正常游戏
    test_normal_game()

    # 测试免费游戏
    test_free_game()

    # 测试不同控制模式
    test_control_modes()

    # 测试中奖线计算
    test_win_line_calculation()

    Logger.info("SlotCat 基础游戏测试完成!")
  end

  # 测试正常游戏
  defp test_normal_game do
    Logger.info("测试正常游戏...")

    case SlotCatGameLogic.generate_game_result(
           12345,
           100,
           SlotCatConstants.control_normal(),
           false
         ) do
      {:ok, result} ->
        Logger.info("正常游戏测试成功:")
        Logger.info("  图标矩阵: #{inspect(result.icons)}")
        Logger.info("  中奖线数: #{length(result.win_lines)}")
        Logger.info("  总倍率: #{result.total_multiplier}")
        Logger.info("  免费次数: #{result.free_times}")
        Logger.info("  Jackpot数量: #{result.jackpot_count}")
        Logger.info("  中奖金额: #{result.win_amount}")

        # 验证结果格式
        formatted = SlotCatGameLogic.format_result_for_client(result)
        Logger.info("  前端格式: #{inspect(formatted)}")

      {:error, reason} ->
        Logger.error("正常游戏测试失败: #{reason}")
    end
  end

  # 测试免费游戏
  defp test_free_game do
    Logger.info("测试免费游戏...")

    case SlotCatGameLogic.generate_game_result(
           12345,
           100,
           SlotCatConstants.control_normal(),
           true
         ) do
      {:ok, result} ->
        Logger.info("免费游戏测试成功:")
        Logger.info("  图标矩阵: #{inspect(result.icons)}")
        Logger.info("  中奖线数: #{length(result.win_lines)}")
        Logger.info("  总倍率: #{result.total_multiplier}")
        Logger.info("  免费次数: #{result.free_times}")
        Logger.info("  Jackpot数量: #{result.jackpot_count}")
        Logger.info("  中奖金额: #{result.win_amount}")

        # 验证免费游戏期间不出现特殊图标
        all_icons = List.flatten(result.icons)
        has_free_icon = Enum.any?(all_icons, &SlotCatConstants.is_free_icon?/1)
        has_jackpot_icon = Enum.any?(all_icons, &SlotCatConstants.is_jackpot_icon?/1)

        if has_free_icon or has_jackpot_icon do
          Logger.warning("免费游戏期间出现了特殊图标!")
        else
          Logger.info("免费游戏特殊图标限制正确")
        end

      {:error, reason} ->
        Logger.error("免费游戏测试失败: #{reason}")
    end
  end

  # 测试不同控制模式
  defp test_control_modes do
    Logger.info("测试不同控制模式...")

    # 测试强杀模式
    test_control_mode(SlotCatConstants.control_kill(), "强杀模式")

    # 测试正常模式
    test_control_mode(SlotCatConstants.control_normal(), "正常模式")

    # 测试放分模式
    test_control_mode(SlotCatConstants.control_release(), "放分模式")
  end

  # 测试单个控制模式
  defp test_control_mode(control, mode_name) do
    Logger.info("测试#{mode_name}...")

    # 运行多次测试获取统计数据
    results =
      for _i <- 1..10 do
        case SlotCatGameLogic.generate_game_result(12345, 100, control, false) do
          {:ok, result} -> result
          {:error, _} -> nil
        end
      end
      |> Enum.filter(&(&1 != nil))

    if length(results) > 0 do
      total_win = Enum.sum(Enum.map(results, & &1.win_amount))
      total_bet = length(results) * 100
      win_rate = total_win / total_bet

      Logger.info("#{mode_name}统计:")
      Logger.info("  测试次数: #{length(results)}")
      Logger.info("  总下注: #{total_bet}")
      Logger.info("  总赢取: #{total_win}")
      Logger.info("  返还率: #{Float.round(win_rate * 100, 2)}%")
    else
      Logger.error("#{mode_name}测试失败")
    end
  end

  # 测试中奖线计算
  defp test_win_line_calculation do
    Logger.info("测试中奖线计算...")

    # 创建一个测试图标矩阵
    test_icons = [
      # 第1列
      [1, 2, 3],
      # 第2列
      [1, 2, 4],
      # 第3列
      [1, 2, 5],
      # 第4列
      [6, 7, 8],
      # 第5列
      [9, 10, 1]
    ]

    Logger.info("测试图标矩阵: #{inspect(test_icons)}")

    # 手动计算中奖线 (这里需要实现私有函数的测试)
    # 由于私有函数无法直接测试，我们通过生成游戏结果来间接测试
    case SlotCatGameLogic.generate_game_result(
           12345,
           100,
           SlotCatConstants.control_normal(),
           false
         ) do
      {:ok, result} ->
        Logger.info("中奖线计算测试:")
        Logger.info("  图标矩阵: #{inspect(result.icons)}")
        Logger.info("  中奖线详情:")

        Enum.each(result.win_lines, fn line ->
          Logger.info(
            "    线#{line.line_id}: 图标#{line.icon_id}, 数量#{line.count}, 倍率#{line.multiplier}"
          )
        end)

      {:error, reason} ->
        Logger.error("中奖线计算测试失败: #{reason}")
    end
  end

  @doc """
  运行性能测试
  """
  def run_performance_test(count \\ 1000) do
    Logger.info("开始 SlotCat 性能测试，测试次数: #{count}")

    start_time = System.monotonic_time(:millisecond)

    results =
      for i <- 1..count do
        case SlotCatGameLogic.generate_game_result(
               i,
               100,
               SlotCatConstants.control_normal(),
               false
             ) do
          {:ok, result} -> result
          {:error, _} -> nil
        end
      end
      |> Enum.filter(&(&1 != nil))

    end_time = System.monotonic_time(:millisecond)
    duration = end_time - start_time

    Logger.info("性能测试结果:")
    Logger.info("  成功次数: #{length(results)}")
    Logger.info("  总耗时: #{duration}ms")

    # 避免除零错误
    if count > 0 and duration > 0 do
      Logger.info("  平均耗时: #{Float.round(duration / count, 2)}ms/次")
      Logger.info("  每秒处理: #{Float.round(count * 1000 / duration, 0)}次")
    else
      Logger.info("  平均耗时: 无法计算")
      Logger.info("  每秒处理: 无法计算")
    end

    # 统计分析
    if length(results) > 0 do
      total_win = Enum.sum(Enum.map(results, & &1.win_amount))
      total_bet = length(results) * 100
      win_rate = total_win / total_bet

      free_games = Enum.count(results, &(&1.free_times > 0))
      jackpots = Enum.count(results, &(&1.jackpot_count >= 3))

      Logger.info("游戏统计:")
      Logger.info("  返还率: #{Float.round(win_rate * 100, 2)}%")
      Logger.info("  免费游戏触发率: #{Float.round(free_games / length(results) * 100, 2)}%")
      Logger.info("  Jackpot触发率: #{Float.round(jackpots / length(results) * 100, 2)}%")
    end
  end

  @doc """
  运行压力测试
  """
  def run_stress_test do
    Logger.info("开始 SlotCat 压力测试...")

    # 并发测试
    tasks =
      for i <- 1..100 do
        Task.async(fn ->
          for j <- 1..10 do
            SlotCatGameLogic.generate_game_result(
              i * 100 + j,
              100,
              SlotCatConstants.control_normal(),
              false
            )
          end
        end)
      end

    start_time = System.monotonic_time(:millisecond)
    results = Task.await_many(tasks, 30_000)
    end_time = System.monotonic_time(:millisecond)

    successful_results =
      results
      |> List.flatten()
      |> Enum.count(fn
        {:ok, _} -> true
        _ -> false
      end)

    Logger.info("压力测试结果:")
    Logger.info("  并发任务: 100")
    Logger.info("  每任务游戏数: 10")
    Logger.info("  总游戏数: 1000")
    Logger.info("  成功次数: #{successful_results}")
    Logger.info("  总耗时: #{end_time - start_time}ms")
    Logger.info("  成功率: #{Float.round(successful_results / 1000 * 100, 2)}%")
  end

  @doc """
  验证游戏公平性
  """
  def verify_fairness(count \\ 10000) do
    Logger.info("开始验证游戏公平性，测试次数: #{count}")

    results =
      for i <- 1..count do
        case SlotCatGameLogic.generate_game_result(
               i,
               100,
               SlotCatConstants.control_normal(),
               false
             ) do
          {:ok, result} -> result
          {:error, _} -> nil
        end
      end
      |> Enum.filter(&(&1 != nil))

    if length(results) > 0 do
      # 计算各种统计数据
      total_win = Enum.sum(Enum.map(results, & &1.win_amount))
      total_bet = length(results) * 100
      rtp = total_win / total_bet * 100

      # 图标分布统计
      all_icons =
        results
        |> Enum.flat_map(&List.flatten(&1.icons))

      icon_distribution =
        Enum.reduce(all_icons, %{}, fn icon, acc ->
          Map.update(acc, icon, 1, &(&1 + 1))
        end)

      Logger.info("公平性验证结果:")
      Logger.info("  测试次数: #{length(results)}")
      Logger.info("  实际RTP: #{Float.round(rtp, 2)}%")
      Logger.info("  目标RTP: 96.5%")
      Logger.info("  RTP偏差: #{Float.round(abs(rtp - 96.5), 2)}%")

      Logger.info("图标分布:")

      Enum.each(icon_distribution, fn {icon, count} ->
        percentage = count / length(all_icons) * 100
        Logger.info("  图标#{icon}: #{count}次 (#{Float.round(percentage, 2)}%)")
      end)

      # 判断是否公平
      if abs(rtp - 96.5) <= 2.0 do
        Logger.info("✅ 游戏公平性验证通过")
      else
        Logger.warning("⚠️  游戏公平性可能存在问题，RTP偏差过大")
      end
    else
      Logger.error("❌ 公平性验证失败，无有效结果")
    end
  end

  @doc """
  测试WILD符号替换逻辑
  """
  def test_wild_substitution do
    Logger.info("开始测试WILD符号替换逻辑...")

    # 测试用例1: WILD可以替换普通图标
    test_wild_with_normal_icons()

    # 测试用例2: WILD不能替换免费游戏符号
    test_wild_with_free_icons()

    # 测试用例3: WILD不能替换Jackpot符号
    test_wild_with_jackpot_icons()

    # 测试用例4: 多个WILD的情况
    test_multiple_wilds()

    Logger.info("WILD符号替换逻辑测试完成!")
  end

  # 测试WILD与普通图标的替换
  defp test_wild_with_normal_icons do
    Logger.info("测试WILD与普通图标的替换...")

    # 模拟包含WILD的图标序列
    test_cases = [
      # WILD + 4个相同普通图标
      [0, 1, 1, 1, 1],
      # 普通图标 + WILD + 3个相同图标
      [1, 0, 1, 1, 1],
      # 2个WILD + 3个相同图标
      [0, 0, 1, 1, 1]
    ]

    Enum.each(test_cases, fn icons ->
      Logger.info("  测试序列: #{inspect(icons)}")
      # 这里需要调用私有函数进行测试，实际实现中可能需要公开测试接口
    end)
  end

  # 测试WILD与免费游戏符号
  defp test_wild_with_free_icons do
    Logger.info("测试WILD与免费游戏符号...")

    test_cases = [
      # WILD + 4个免费符号
      [0, 9, 9, 9, 9],
      # 免费符号 + WILD + 3个免费符号
      [9, 0, 9, 9, 9]
    ]

    Enum.each(test_cases, fn icons ->
      Logger.info("  测试序列: #{inspect(icons)} - WILD不应替换免费符号")
    end)
  end

  # 测试WILD与Jackpot符号
  defp test_wild_with_jackpot_icons do
    Logger.info("测试WILD与Jackpot符号...")

    test_cases = [
      # WILD + 4个Jackpot符号
      [0, 10, 10, 10, 10],
      # Jackpot符号 + WILD + 3个Jackpot符号
      [10, 0, 10, 10, 10]
    ]

    Enum.each(test_cases, fn icons ->
      Logger.info("  测试序列: #{inspect(icons)} - WILD不应替换Jackpot符号")
    end)
  end

  # 测试多个WILD的情况
  defp test_multiple_wilds do
    Logger.info("测试多个WILD的情况...")

    test_cases = [
      # 3个WILD + 2个普通图标
      [0, 0, 0, 1, 1],
      # 4个WILD + 1个普通图标
      [0, 0, 0, 0, 1],
      # 5个WILD
      [0, 0, 0, 0, 0]
    ]

    Enum.each(test_cases, fn icons ->
      Logger.info("  测试序列: #{inspect(icons)}")
    end)
  end

  @doc """
  测试边界情况
  """
  def test_edge_cases do
    Logger.info("开始测试边界情况...")

    # 测试最小下注
    test_minimum_bet()

    # 测试最大下注
    test_maximum_bet()

    # 测试无效图标
    test_invalid_icons()

    # 测试空矩阵
    test_empty_matrix()

    # 测试异常输入
    test_invalid_inputs()

    Logger.info("边界情况测试完成!")
  end

  # 测试最小下注
  defp test_minimum_bet do
    Logger.info("测试最小下注...")

    case SlotCatGameLogic.generate_game_result(12345, 1, SlotCatConstants.control_normal(), false) do
      {:ok, result} ->
        Logger.info("  最小下注测试成功: 中奖金额 #{result.win_amount}")

      {:error, reason} ->
        Logger.error("  最小下注测试失败: #{reason}")
    end
  end

  # 测试最大下注
  defp test_maximum_bet do
    Logger.info("测试最大下注...")

    case SlotCatGameLogic.generate_game_result(
           12345,
           10000,
           SlotCatConstants.control_normal(),
           false
         ) do
      {:ok, result} ->
        Logger.info("  最大下注测试成功: 中奖金额 #{result.win_amount}")

      {:error, reason} ->
        Logger.error("  最大下注测试失败: #{reason}")
    end
  end

  # 测试无效图标
  defp test_invalid_icons do
    Logger.info("测试无效图标处理...")
    # 这里可以测试图标验证逻辑
  end

  # 测试空矩阵
  defp test_empty_matrix do
    Logger.info("测试空矩阵处理...")
    # 这里可以测试空矩阵的处理
  end

  # 测试异常输入
  defp test_invalid_inputs do
    Logger.info("测试异常输入处理...")

    # 测试负数下注
    case SlotCatGameLogic.generate_game_result(
           12345,
           -100,
           SlotCatConstants.control_normal(),
           false
         ) do
      {:ok, _result} ->
        Logger.warning("  负数下注应该失败但成功了")

      {:error, _reason} ->
        Logger.info("  负数下注正确失败")
    end

    # 测试零下注
    case SlotCatGameLogic.generate_game_result(12345, 0, SlotCatConstants.control_normal(), false) do
      {:ok, _result} ->
        Logger.warning("  零下注应该失败但成功了")

      {:error, _reason} ->
        Logger.info("  零下注正确失败")
    end
  end

  @doc """
  测试支付线配置
  """
  def test_paylines_configuration do
    Logger.info("开始测试支付线配置...")

    # 获取支付线配置
    paylines = SlotCatConstants.win_lines_config()

    Logger.info("支付线总数: #{length(paylines)}")

    # 验证每条支付线
    Enum.with_index(paylines, 1)
    |> Enum.each(fn {line, index} ->
      Logger.info("第#{index}条线: #{inspect(line)}")

      # 验证支付线的有效性
      if length(line) == 5 do
        # 转换为坐标并显示路径
        coordinates =
          Enum.map(line, fn pos ->
            {row, col} = SlotCatConstants.index_to_position(pos)
            "(#{row},#{col})"
          end)

        Logger.info("  坐标路径: #{Enum.join(coordinates, " -> ")}")

        # 验证索引范围
        valid_indices = Enum.all?(line, fn pos -> pos >= 1 and pos <= 15 end)

        if valid_indices do
          Logger.info("  ✅ 索引有效")
        else
          Logger.error("  ❌ 索引无效")
        end

        # 可视化支付线路径
        visualize_payline(line, index)
      else
        Logger.error("  ❌ 支付线长度错误: #{length(line)}")
      end
    end)

    # 测试特定的中奖场景
    test_specific_winning_scenarios()

    Logger.info("支付线配置测试完成!")
  end

  # 可视化支付线路径
  defp visualize_payline(line, line_number) do
    Logger.info("  第#{line_number}条线可视化:")

    # 创建3x5的网格
    grid =
      for row <- 0..2 do
        for col <- 0..4 do
          index = row * 5 + col + 1

          if index in line do
            # 支付线上的位置用实心圆表示
            "●"
          else
            # 其他位置用空心圆表示
            "○"
          end
        end
      end

    # 显示网格
    Enum.each(grid, fn row ->
      Logger.info("    #{Enum.join(row, " ")}")
    end)
  end

  # 测试特定的中奖场景
  defp test_specific_winning_scenarios do
    Logger.info("测试特定中奖场景...")

    # 场景1: 第1条线(中间行)全是相同符号
    test_icons_1 = [
      # 第1行
      [1, 2, 3, 4, 5],
      # 第2行 - 中间行全是7
      [7, 7, 7, 7, 7],
      # 第3行
      [8, 9, 1, 2, 3]
    ]

    Logger.info("场景1 - 中间行全7:")
    test_winning_scenario(test_icons_1, "应该在第1条线中奖")

    # 场景2: 第2条线(上行)全是相同符号
    test_icons_2 = [
      # 第1行 - 上行全是5
      [5, 5, 5, 5, 5],
      # 第2行
      [1, 2, 3, 4, 6],
      # 第3行
      [7, 8, 9, 1, 2]
    ]

    Logger.info("场景2 - 上行全5:")
    test_winning_scenario(test_icons_2, "应该在第2条线中奖")

    # 场景3: V形路径中奖
    test_icons_3 = [
      # 第1行 - 位置1和5是3
      [3, 2, 1, 4, 3],
      # 第2行 - 位置7和9是3
      [1, 3, 2, 3, 6],
      # 第3行 - 位置13是3
      [7, 8, 3, 1, 2]
    ]

    Logger.info("场景3 - V形路径:")
    test_winning_scenario(test_icons_3, "应该在第4条线(V形)中奖")
  end

  # 测试单个中奖场景
  defp test_winning_scenario(icons, description) do
    Logger.info("  图标矩阵:")

    Enum.each(icons, fn row ->
      Logger.info("    #{inspect(row)}")
    end)

    # 这里可以调用游戏逻辑来验证中奖线计算
    # 由于需要访问私有函数，实际实现中可能需要公开测试接口
    Logger.info("  #{description}")
  end

  @doc """
  测试中奖规则逻辑 (根据中奖规则图片)
  """
  def test_winning_rules_logic do
    Logger.info("开始测试中奖规则逻辑...")

    # 测试场景1: WIN示例 - 前3个相同 + WILD + 不同符号
    Logger.info("测试场景1: WIN示例")

    test_icons_win = [
      # 第1行: 前3个是3，第4个是WILD，第5个是5
      [3, 3, 3, 0, 5],
      # 第2行
      [1, 2, 4, 6, 7],
      # 第3行
      [8, 9, 1, 2, 3]
    ]

    Logger.info("  图标矩阵: #{inspect(test_icons_win)}")
    test_winning_logic(test_icons_win, "应该在第2条线中奖 (前3个相同 + WILD)")

    # 测试场景2: LOSE示例 - 第1个不同，后面相同
    Logger.info("测试场景2: LOSE示例")

    test_icons_lose = [
      # 第1行: 第1个是5，后面是3,3,WILD,3
      [5, 3, 3, 0, 3],
      # 第2行
      [1, 2, 4, 6, 7],
      # 第3行
      [8, 9, 1, 2, 3]
    ]

    Logger.info("  图标矩阵: #{inspect(test_icons_lose)}")
    test_winning_logic(test_icons_lose, "不应该在第2条线中奖 (第1个位置不匹配)")

    # 测试场景3: WILD在第1个位置
    Logger.info("测试场景3: WILD在第1个位置")

    test_icons_wild_first = [
      # 第1行: WILD + 3个相同符号
      [0, 3, 3, 3, 5],
      # 第2行
      [1, 2, 4, 6, 7],
      # 第3行
      [8, 9, 1, 2, 3]
    ]

    Logger.info("  图标矩阵: #{inspect(test_icons_wild_first)}")
    test_winning_logic(test_icons_wild_first, "应该在第2条线中奖 (WILD可以替换)")

    # 测试场景4: 5个相同符号
    Logger.info("测试场景4: 5个相同符号")

    test_icons_five = [
      # 第1行: 5个相同符号
      [7, 7, 7, 7, 7],
      # 第2行
      [1, 2, 4, 6, 8],
      # 第3行
      [8, 9, 1, 2, 3]
    ]

    Logger.info("  图标矩阵: #{inspect(test_icons_five)}")
    test_winning_logic(test_icons_five, "应该在第2条线中奖 (5个相同符号)")

    Logger.info("中奖规则逻辑测试完成!")
  end

  # 测试中奖逻辑的辅助函数
  defp test_winning_logic(icons, expected_description) do
    # 这里可以调用实际的中奖计算逻辑
    # 由于需要访问私有函数，我们模拟测试过程

    # 测试第2条线 (上行): [1, 2, 3, 4, 5]
    line_indices = [1, 2, 3, 4, 5]

    line_icons =
      Enum.map(line_indices, fn index ->
        {row, col} = SlotCatConstants.index_to_position(index)
        Enum.at(icons, row) |> Enum.at(col)
      end)

    Logger.info("    第2条线图标序列: #{inspect(line_icons)}")
    Logger.info("    预期结果: #{expected_description}")

    # 模拟连续计算逻辑
    {first_icon, count} = calculate_consecutive_mock(line_icons)
    Logger.info("    计算结果: 基准图标#{first_icon}, 连续数量#{count}")

    if count >= 3 do
      Logger.info("    ✅ 中奖 (连续数量 >= 3)")
    else
      Logger.info("    ❌ 不中奖 (连续数量 < 3)")
    end
  end

  # 模拟连续计算逻辑 (简化版本)
  defp calculate_consecutive_mock([]), do: {1, 0}

  defp calculate_consecutive_mock([first_icon | rest]) do
    # 如果第一个是 WILD (0)，找到第一个非 WILD 图标作为基准
    base_icon =
      if first_icon == 0 do
        Enum.find(rest, first_icon, fn icon -> icon != 0 end)
      else
        first_icon
      end

    # 计算连续数量
    count =
      Enum.reduce_while([first_icon | rest], 0, fn icon, acc ->
        if icon == base_icon or (icon == 0 and base_icon != 0) do
          {:cont, acc + 1}
        else
          {:halt, acc}
        end
      end)

    {base_icon, count}
  end

  @doc """
  运行完整的高级测试套件
  """
  def run_advanced_test_suite do
    Logger.info("🧪 ========== SlotCat 高级测试套件开始 ==========")

    # 基础功能测试
    run_basic_test()

    # 支付线配置测试
    test_paylines_configuration()

    # 中奖规则逻辑测试
    test_winning_rules_logic()

    # WILD符号测试
    test_wild_substitution()

    # 边界情况测试
    test_edge_cases()

    # 性能测试
    run_performance_test(1000)

    # 公平性验证
    verify_fairness(5000)

    # 压力测试
    run_stress_test()

    Logger.info("🧪 ========== SlotCat 高级测试套件完成 ==========")
  end
end
