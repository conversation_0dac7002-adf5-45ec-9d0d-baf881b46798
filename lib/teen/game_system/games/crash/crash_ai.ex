defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.Crash.CrashAI do
  @moduledoc """
  Crash游戏AI/机器人模块

  管理机器人的行为，包括：
  - 机器人下注决策
  - 机器人下车决策
  - 机器人行为模拟
  """

  alias Cypridina.Teen.GameSystem.Games.Crash.{CrashG<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CrashRobotManager}
  require Logger

  # 机器人类型定义
  @robot_types %{
    # 保守型：低倍数下车
    conservative: :conservative,
    # 激进型：高倍数下车
    aggressive: :aggressive,
    # 平衡型：中等倍数下车
    balanced: :balanced,
    # 随机型：随机行为
    random: :random
  }

  @doc """
  生成机器人下注决策
  """
  def should_robot_bet?(robot_player, state) do
    config = state.game_data.config
    bet_probability = Map.get(config, :robot_bet_probability, 0.3)

    # 基础概率判断
    base_should_bet = :rand.uniform() < bet_probability

    # 根据机器人类型调整概率
    robot_type = Map.get(robot_player, :robot_type, :balanced)
    adjusted_probability = adjust_bet_probability(robot_type, bet_probability, state)

    :rand.uniform() < adjusted_probability and base_should_bet
  end

  @doc """
  生成机器人下注金额
  确保机器人只使用配置的筹码金额下注
  """
  def generate_robot_bet_amount(robot_player, state) do
    config = state.game_data.config
    chips = Map.get(config, :chips, [1000, 5000, 10000, 100000, 500000])
    robot_type = Map.get(robot_player, :robot_type, :balanced)

    # 确保筹码列表不为空
    if length(chips) == 0 do
      Logger.warning("🤖 [CRASH_AI] 筹码配置为空，使用默认值")
      1000
    else
      case robot_type do
        :conservative ->
          # 保守型：选择较小的筹码（前50%）
          small_chips = Enum.take(chips, max(1, div(length(chips), 2)))
          Enum.random(small_chips)

        :aggressive ->
          # 激进型：选择较大的筹码（后50%）
          large_chips = Enum.drop(chips, div(length(chips), 2))
          if length(large_chips) > 0, do: Enum.random(large_chips), else: List.last(chips)

        :balanced ->
          # 平衡型：选择中等筹码（中间部分）
          if length(chips) <= 2 do
            Enum.random(chips)
          else
            middle_start = max(0, div(length(chips), 4))
            middle_count = max(1, div(length(chips), 2))
            middle_chips = Enum.slice(chips, middle_start, middle_count)
            Enum.random(middle_chips)
          end

        :random ->
          # 随机型：随机选择任意筹码
          Enum.random(chips)
      end
    end
  end

  @doc """
  生成机器人下车决策（旧版本，保留兼容性）
  """
  def should_robot_cash_out?(robot_player, current_multiplier, elapsed_time) do
    robot_type = Map.get(robot_player, :robot_type, :balanced)
    target_multiplier = get_robot_target_multiplier(robot_type)

    # 基础判断：达到目标倍数
    base_should_cash_out = current_multiplier >= target_multiplier

    # 添加一些随机性和时间因素
    time_pressure = calculate_time_pressure(elapsed_time)
    # 10%的随机下车概率
    random_factor = :rand.uniform() < 0.1

    base_should_cash_out or time_pressure or random_factor
  end

  @doc """
  改进的机器人下车决策（结合固定时间和智能决策）
  """
  def should_robot_cash_out_improved?(robot_player, current_multiplier, elapsed_time) do
    # 1. 检查预设的下车时间（参考旧项目的固定时间逻辑）
    planned_cash_out_time = Map.get(robot_player, :planned_cash_out_time)

    if planned_cash_out_time && elapsed_time >= planned_cash_out_time do
      # 达到预设时间，但还要检查倍数是否合理
      # 如果倍数太低（<1.1x），有30%概率继续等待
      if current_multiplier < 110 and :rand.uniform() < 0.3 do
        false
      else
        true
      end
    else
      # 2. 智能决策逻辑
      robot_type = Map.get(robot_player, :robot_type, :balanced)

      # 基于机器人类型的目标倍数
      target_multiplier = get_robot_target_multiplier(robot_type)

      # 3. 多重决策因子
      multiplier_reached = current_multiplier >= target_multiplier
      time_pressure = calculate_improved_time_pressure(elapsed_time, robot_type)
      risk_assessment = calculate_risk_assessment(robot_player, current_multiplier, elapsed_time)

      # 4. 综合决策
      decision_score = calculate_cash_out_decision_score(
        multiplier_reached,
        time_pressure,
        risk_assessment,
        robot_type
      )

      # 5. 基于决策分数和随机因子做最终决定
      random_threshold = get_cash_out_threshold(robot_type)
      decision_score > random_threshold
    end
  end

  @doc """
  获取机器人目标倍数
  """
  def get_robot_target_multiplier(robot_type) do
    case robot_type do
      :conservative ->
        # 保守型：1.2x - 1.8x
        120 + :rand.uniform(60)

      :aggressive ->
        # 激进型：2.0x - 5.0x
        200 + :rand.uniform(300)

      :balanced ->
        # 平衡型：1.5x - 2.5x
        150 + :rand.uniform(100)

      :random ->
        # 随机型：1.1x - 10.0x
        110 + :rand.uniform(890)
    end
  end

  @doc """
  创建机器人玩家 - 使用统一的PlayerData结构（参考longhu游戏）
  """
  def create_robot_player(robot_id) do
    robot_type = Enum.random(Map.values(@robot_types))

    # 使用统一的机器人数据构造方法（参考longhu游戏）
    robot_base_data = %{
      numeric_id: robot_id,
      id: robot_id,
      # 使用统一配置的机器人名称 - 确保每个机器人有不同的名字
      nickname: Teen.RobotManagement.RobotEntity.random_robot_name(),
      # 使用统一配置的头像 - 确保每个机器人有不同的头像
      avatar_id: Teen.RobotManagement.RobotEntity.random_robot_avatar(),
      # 使用统一配置的积分生成 - 确保每个机器人有不同的余额
      points: generate_robot_initial_balance(),
      # 创建时间
      created_at: DateTime.utc_now()
    }

    # 调用统一的数据构造方法
    robot_data = Cypridina.Teen.GameSystem.PlayerDataBuilder.create_robot_player_data(
      robot_base_data,
      is_ready: true
    )

    # 添加Crash游戏特有的字段
    Map.merge(robot_data, %{
      robot_type: robot_type,
      bet_amount: 0,
      cashed_out: false,
      cash_out_multiplier: nil,
      crashed: false,
      payout: 0,
      profit: 0,
      target_multiplier: get_robot_target_multiplier(robot_type)
    })
  end

  @doc """
  创建带工作时间管理的机器人玩家
  """
  def create_managed_robot_player(robot_id, robot_manager) do
    # 使用机器人管理器创建机器人
    {managed_robot, _robot_state} = CrashRobotManager.create_managed_robot(robot_manager, robot_id)
    managed_robot
  end

  @doc """
  更新机器人余额（游戏结算后调用）
  """
  def update_robot_balance(robot_manager, robot_id, new_balance, reason \\ "游戏结算") do
    if robot_manager do
      CrashRobotManager.update_robot_balance(robot_manager, robot_id, new_balance, reason)
    else
      Logger.warning("🤖 [CRASH_AI] 机器人管理器未初始化，无法更新余额: #{robot_id}")
      {:error, :manager_not_initialized}
    end
  end

  @doc """
  处理机器人下注阶段行为（旧版本，保留兼容性）
  """
  def handle_robot_betting_phase(state) do
    robots = get_robots(state)

    Enum.reduce(robots, state, fn {_numeric_id, robot}, acc_state ->
      if should_robot_bet?(robot, acc_state) and Map.get(robot, :bet_amount, 0) == 0 do
        bet_amount = generate_robot_bet_amount(robot, acc_state)

        case CrashLogic.place_bet(acc_state, robot, bet_amount) do
          {:ok, updated_state, _updated_robot} ->
            Logger.info("🤖 [CRASH_AI] 机器人下注: #{get_robot_name(robot)}, 金额: #{bet_amount}")
            updated_state

          {:error, _reason} ->
            acc_state
        end
      else
        acc_state
      end
    end)
  end

  @doc """
  为机器人安排下注时间（参考longhu游戏的调度机制）
  在下注阶段开始时调用，为每个机器人安排个性化的下注时间
  """
  def schedule_robot_bets(state, room_pid) do
    robots = get_robots(state)
    config = state.game_data.config
    betting_time_ms = Map.get(config, :betting_time, 15) * 1000

    Logger.info("🤖 [CRASH_AI] 调度 #{length(robots)} 个机器人在 #{betting_time_ms}ms内下注")

    # 为每个机器人安排个性化的下注时机
    Enum.each(robots, fn {robot_id, robot} ->
      if Map.get(robot, :bet_amount, 0) == 0 do
        # 根据机器人类型和个性计算下注时间
        bet_delay = calculate_robot_bet_delay(robot, betting_time_ms)

        # 65%概率下注（参考旧项目）
        if :rand.uniform(100) <= 65 do
          Process.send_after(room_pid, {:robot_bet, robot_id}, bet_delay)
          Logger.info("🤖 [CRASH_AI] 机器人 #{get_robot_name(robot)} 将在 #{bet_delay}ms 后下注")
        end
      end
    end)
  end

  @doc """
  执行单个机器人下注
  """
  def execute_robot_bet(state, robot_id) do
    case Map.get(state.players, robot_id) do
      nil ->
        Logger.warning("🤖 [CRASH_AI] 机器人不存在: #{robot_id}")
        state

      robot ->
        if state.game_data.phase == :betting and Map.get(robot, :bet_amount, 0) == 0 do
          # 生成下注金额（参考旧项目的分布）
          bet_amount = generate_robot_bet_amount_with_distribution(robot, state)

          case CrashLogic.place_bet(state, robot, bet_amount) do
            {:ok, updated_state, updated_robot} ->
              Logger.info("🤖 [CRASH_AI] 机器人下注成功: #{get_robot_name(robot)}, 金额: #{bet_amount}")

              # 为机器人预设下车时间（参考旧项目：3800 + rand() % 41660）
              cash_out_delay = 3800 + :rand.uniform(41660)
              updated_robot_with_cashout = Map.put(updated_robot, :planned_cash_out_time, cash_out_delay)

              # 更新机器人数据
              updated_players = Map.put(updated_state.players, robot_id, updated_robot_with_cashout)
              %{updated_state | players: updated_players}

            {:error, reason} ->
              Logger.warning("🤖 [CRASH_AI] 机器人下注失败: #{get_robot_name(robot)}, 原因: #{reason}")
              state
          end
        else
          state
        end
    end
  end



  @doc """
  处理机器人飞行阶段行为（改进版本，结合固定时间和智能决策）
  返回需要下车的机器人列表，让CrashRoom统一处理
  """
  def handle_robot_flying_phase(state, current_multiplier, elapsed_time) do
    robots = get_robots(state)

    # 收集需要下车的机器人
    robots_to_cash_out =
      robots
      |> Enum.filter(fn {_numeric_id, robot} ->
        Map.get(robot, :bet_amount, 0) > 0 and
        not Map.get(robot, :cashed_out, false) and
        should_robot_cash_out_improved?(robot, current_multiplier, elapsed_time)
      end)
      |> Enum.map(fn {_numeric_id, robot} -> robot end)

    # 返回状态和需要下车的机器人列表
    {state, robots_to_cash_out}
  end

  @doc """
  添加机器人到房间（基于C++的OnRobotEnter逻辑）
  """
  def add_robots_to_room(state, count \\ 5) do
    current_robot_count = count_robots(state)
    real_player_count = count_real_players(state)
    max_robots = Map.get(state.game_data.config, :max_robots, 60)

    # 基于真实玩家数量调整机器人数量
    target_robot_count = calculate_target_robot_count(real_player_count, state.game_data.config)

    robots_to_add =
      min(count, min(target_robot_count - current_robot_count, max_robots - current_robot_count))

    if robots_to_add > 0 do
      Logger.info("🤖 [CRASH_AI] 添加 #{robots_to_add} 个机器人，当前真实玩家: #{real_player_count}")

      Enum.reduce(1..robots_to_add, state, fn _i, acc_state ->
        robot_id = generate_robot_id(acc_state)
        robot = create_robot_player_with_bet(robot_id, acc_state)

        # 添加机器人并自动下注（如果在下注阶段）
        updated_state = add_robot_to_state(acc_state, robot_id, robot)

        # 如果在下注阶段，机器人自动下注
        if acc_state.game_data.phase == :betting do
          auto_bet_robot(updated_state, robot)
        else
          updated_state
        end
      end)
    else
      state
    end
  end

  @doc """
  延迟添加机器人到房间（新策略：先快速添加50%，剩余定时器慢慢添加）
  当真实玩家加入时，立即添加50%，剩余的分批慢慢添加
  """
  def schedule_delayed_robot_join(room_pid, real_player_count, config) do
    # 计算需要添加的机器人数量
    target_robot_count = calculate_target_robot_count(real_player_count, config)

    if target_robot_count > 0 do
      # 立即添加50%的机器人
      immediate_count = div(target_robot_count, 2)
      remaining_count = target_robot_count - immediate_count

      Logger.info("🤖 [CRASH_AI] 立即添加 #{immediate_count} 个机器人，剩余 #{remaining_count} 个将分批添加")

      # 立即添加50%
      if immediate_count > 0 do
        send(room_pid, {:immediate_add_robots, immediate_count})
      end

      # 剩余的分批添加，每隔3-8秒添加1-2个
      if remaining_count > 0 do
        schedule_gradual_robot_addition(room_pid, remaining_count)
      end
    end
  end

  @doc """
  分批逐步添加剩余机器人
  """
  def schedule_gradual_robot_addition(room_pid, remaining_count) do
    if remaining_count > 0 do
      # 每次添加1-2个机器人
      batch_size = min(remaining_count, 1 + :rand.uniform(2))
      # 延迟3-8秒
      delay = 3000 + :rand.uniform(5000)

      Process.send_after(room_pid, {:gradual_add_robots, batch_size, remaining_count - batch_size}, delay)

      Logger.info("🤖 [CRASH_AI] 计划在 #{delay}ms 后添加 #{batch_size} 个机器人，剩余: #{remaining_count - batch_size}")
    end
  end

  @doc """
  智能管理机器人进出（在游戏结算后调用）
  """
  def smart_robot_management(state) do
    current_robot_count = count_robots(state)
    real_player_count = count_real_players(state)
    config = state.game_data.config
    target_robot_count = calculate_target_robot_count(real_player_count, config)
    max_robots = Map.get(config, :max_robots, 60)
    min_target = Map.get(config, :min_target_robots, 10)
    max_target = Map.get(config, :max_target_robots, 20)

    cond do
      # 没有真实玩家且当前机器人数量超过配置的空闲数量
      real_player_count == 0 and current_robot_count > target_robot_count ->
        robots_to_remove = min(5, current_robot_count - target_robot_count)
        Logger.info("🤖 [CRASH_AI] 无真实玩家，移除 #{robots_to_remove} 个机器人到空闲状态")
        remove_robots_from_room(state, robots_to_remove)

      # 有真实玩家但机器人数量不在目标范围内
      real_player_count > 0 and current_robot_count < min_target ->
        robots_to_add = min(3, min(min_target - current_robot_count, max_robots - current_robot_count))
        Logger.info("🤖 [CRASH_AI] 机器人数量不足，智能添加 #{robots_to_add} 个机器人")
        add_robots_to_room(state, robots_to_add)

      # 机器人数量超过最大目标范围
      real_player_count > 0 and current_robot_count > max_target + 5 ->
        robots_to_remove = min(3, current_robot_count - max_target)
        Logger.info("🤖 [CRASH_AI] 机器人数量过多，智能移除 #{robots_to_remove} 个机器人")
        remove_robots_from_room(state, robots_to_remove)

      true ->
        state
    end
  end

  @doc """
  性能优化：批量处理机器人操作
  减少单个机器人操作的开销
  """
  def batch_process_robot_actions(state, action_type) do
    robots = get_robots(state)

    case action_type do
      :betting ->
        # 批量处理机器人下注
        batch_robot_betting(state, robots)

      :cash_out ->
        # 批量处理机器人下车
        batch_robot_cash_out(state, robots)

      _ ->
        state
    end
  end

  @doc """
  获取机器人统计信息（用于监控和调试）
  """
  def get_robot_statistics(state) do
    robots = get_robots(state)

    %{
      total_robots: length(robots),
      betting_robots: Enum.count(robots, fn {_id, robot} -> Map.get(robot, :bet_amount, 0) > 0 end),
      cashed_out_robots: Enum.count(robots, fn {_id, robot} -> Map.get(robot, :cashed_out, false) end),
      robot_types: count_robot_types(robots),
      average_bet_amount: calculate_average_robot_bet(robots)
    }
  end

  @doc """
  移除机器人从房间（基于C++的OnRobotLeave逻辑）
  """
  def remove_robots_from_room(state, count \\ 1) do
    robots = get_robots(state)

    # 优先移除没有下注的机器人，其次移除已下车的机器人
    removable_robots = prioritize_removable_robots(robots, count)

    if length(removable_robots) > 0 do
      Logger.info("🤖 [CRASH_AI] 移除 #{length(removable_robots)} 个机器人")

      updated_players =
        Enum.reduce(removable_robots, state.players, fn {numeric_id, robot}, acc_players ->
          Logger.info("🤖 [CRASH_AI] 机器人离开: #{get_robot_name(robot)} (ID: #{numeric_id})")
          Map.delete(acc_players, numeric_id)
        end)

      %{state | players: updated_players}
    else
      state
    end
  end

  @doc """
  处理机器人进出房间的智能管理（对应C++的机器人管理逻辑）
  """
  def manage_robot_population(state) do
    current_robot_count = count_robots(state)
    real_player_count = count_real_players(state)
    target_robot_count = calculate_target_robot_count(real_player_count, state.game_data.config)

    cond do
      # 需要添加机器人
      current_robot_count < target_robot_count ->
        robots_to_add = min(3, target_robot_count - current_robot_count)
        add_robots_to_room(state, robots_to_add)

      # 需要移除机器人
      current_robot_count > target_robot_count + 5 ->
        robots_to_remove = min(2, current_robot_count - target_robot_count)
        remove_robots_from_room(state, robots_to_remove)

      true ->
        state
    end
  end

  # ==================== 公开辅助函数 ====================

  @doc """
  获取房间中的机器人数量
  """
  def count_robots(state) do
    get_robots(state) |> length()
  end

  @doc """
  获取房间中的真实玩家数量
  """
  def count_real_players(state) do
    state.players
    |> Enum.count(fn {_numeric_id, player} ->
      not Map.get(player, :is_robot, false)
    end)
  end

  # ==================== 私有函数 ====================

  defp get_robots(state) do
    state.players
    |> Enum.filter(fn {_numeric_id, player} ->
      Map.get(player, :is_robot, false)
    end)
  end

  # 根据真实玩家数量计算目标机器人数量
  defp calculate_target_robot_count(real_player_count, config \\ %{}) do
    min_target = Map.get(config, :min_target_robots, 10)
    max_target = Map.get(config, :max_target_robots, 20)
    idle_robots = Map.get(config, :idle_robots, 0)

    case real_player_count do
      # 没有真实玩家，使用配置的空闲机器人数量
      0 -> idle_robots
      # 有真实玩家时，在配置的范围内随机
      _ -> min_target + :rand.uniform(max_target - min_target + 1) - 1
    end
  end

  # 创建带下注的机器人玩家 - 使用统一的PlayerData结构（参考longhu游戏）
  defp create_robot_player_with_bet(robot_id, state) do
    robot_type = Enum.random(Map.values(@robot_types))
    bet_amount = generate_robot_bet_amount_by_type(robot_type, state)

    # 使用统一的机器人数据构造方法（参考longhu游戏）
    robot_base_data = %{
      numeric_id: robot_id,
      id: robot_id,
      # 使用统一配置的机器人名称
      nickname: Teen.RobotManagement.RobotEntity.random_robot_name(),
      # 使用统一配置的头像
      avatar_id: Teen.RobotManagement.RobotEntity.random_robot_avatar(),
      # 使用统一配置的积分生成
      points: generate_robot_initial_balance(),
      # 创建时间
      created_at: DateTime.utc_now()
    }

    # 调用统一的数据构造方法
    robot_data = Cypridina.Teen.GameSystem.PlayerDataBuilder.create_robot_player_data(
      robot_base_data,
      is_ready: true
    )

    # 添加Crash游戏特有的字段
    Map.merge(robot_data, %{
      robot_type: robot_type,
      bet_amount: bet_amount,
      bet_time: System.system_time(:millisecond),
      cashed_out: false,
      cash_out_multiplier: nil,
      crashed: false,
      payout: 0,
      profit: 0,
      target_multiplier: get_robot_target_multiplier(robot_type),
      # 机器人行为参数
      patience_level: :rand.uniform(100),
      risk_tolerance: :rand.uniform(100)
    })
  end

  # 根据机器人类型生成下注金额
  defp generate_robot_bet_amount_by_type(robot_type, state) do
    config = state.game_data.config
    chips = Map.get(config, :chips, [1000, 5000, 10000, 100000, 500000])

    # 确保筹码列表不为空
    if length(chips) == 0 do
      Logger.warning("🤖 [CRASH_AI] 筹码配置为空，使用默认值")
      1000
    else
      case robot_type do
        :conservative ->
          # 保守型：选择较小的筹码（前50%）
          small_chips = Enum.take(chips, max(1, div(length(chips), 2)))
          Enum.random(small_chips)

        :aggressive ->
          # 激进型：选择较大的筹码（后50%）
          large_chips = Enum.drop(chips, div(length(chips), 2))
          if length(large_chips) > 0, do: Enum.random(large_chips), else: List.last(chips)

        :balanced ->
          # 平衡型：选择中等筹码（中间部分）
          if length(chips) <= 2 do
            Enum.random(chips)
          else
            middle_start = max(0, div(length(chips), 4))
            middle_count = max(1, div(length(chips), 2))
            middle_chips = Enum.slice(chips, middle_start, middle_count)
            Enum.random(middle_chips)
          end

        :random ->
          # 随机型：随机选择任意筹码
          Enum.random(chips)
      end
    end
  end

  # 添加机器人到状态
  defp add_robot_to_state(state, robot_id, robot) do
    updated_players = Map.put(state.players, robot_id, robot)
    %{state | players: updated_players}
  end

  # 机器人自动下注
  defp auto_bet_robot(state, robot) do
    if state.game_data.phase == :betting and robot.bet_amount > 0 do
      case CrashLogic.place_bet(state, robot, robot.bet_amount) do
        {:ok, updated_state, _updated_robot} ->
          Logger.info("🤖 [CRASH_AI] 机器人自动下注: #{get_robot_name(robot)}, 金额: #{robot.bet_amount}")
          updated_state

        {:error, reason} ->
          Logger.warning("🤖 [CRASH_AI] 机器人自动下注失败: #{get_robot_name(robot)}, 原因: #{reason}")
          state
      end
    else
      state
    end
  end

  # 优先选择可移除的机器人
  defp prioritize_removable_robots(robots, count) do
    # 按优先级排序：未下注 > 已下车 > 已爆炸 > 正在游戏中
    sorted_robots =
      Enum.sort_by(robots, fn {_id, robot} ->
        cond do
          # 最优先：未下注
          Map.get(robot, :bet_amount, 0) == 0 -> 1
          # 次优先：已下车
          Map.get(robot, :cashed_out, false) -> 2
          # 第三：已爆炸
          Map.get(robot, :crashed, false) -> 3
          # 最后：正在游戏中
          true -> 4
        end
      end)

    Enum.take(sorted_robots, count)
  end

  defp generate_robot_id(state) do
    # 生成一个不冲突的机器人ID（使用正数，但大于1000000以区分）
    existing_ids = Map.keys(state.players)

    Stream.iterate(1_000_000, &(&1 + 1))
    |> Enum.find(fn id -> id not in existing_ids end)
  end

  # 安全获取机器人名字
  defp get_robot_name(robot) do
    case robot do
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        Cypridina.Teen.GameSystem.PlayerData.get_display_name(p)
      %{nickname: nickname} when is_binary(nickname) ->
        nickname
      _ ->
        "Player#{Map.get(robot, :numeric_id, 0)}"
    end
  end

  # 生成机器人初始余额（参考longhu游戏的实现）
  defp generate_robot_initial_balance do
    # 根据不同策略生成积分，考虑到筹码面值
    strategy = :rand.uniform(100)

    cond do
      strategy <= 30 ->
        # 30% 概率：保守型机器人，积分较少
        # 50k-200k
        50_000 + :rand.uniform(150_000)

      strategy <= 70 ->
        # 40% 概率：中等型机器人，积分中等
        # 150k-500k
        150_000 + :rand.uniform(350_000)

      true ->
        # 30% 概率：富豪型机器人，积分较多
        # 400k-1000k
        400_000 + :rand.uniform(600_000)
    end
  end

  defp generate_robot_name do
    # 生成随机机器人名称（对应C++的机器人名称生成）
    prefixes = ["玩家", "用户", "游客", "Lucky", "Winner", "Player", "Guest", "User"]
    suffix = :rand.uniform(9999)

    Enum.random(prefixes) <> to_string(suffix)
  end

  # 计算机器人下注延迟时间
  defp calculate_robot_bet_delay(robot, betting_time_ms) do
    robot_type = Map.get(robot, :robot_type, :balanced)

    # 基础延迟：下注阶段的15%-85%之间
    base_delay_ratio = case robot_type do
      :aggressive -> 0.15 + :rand.uniform() * 0.3  # 激进型早下注
      :conservative -> 0.4 + :rand.uniform() * 0.4  # 保守型晚下注
      :balanced -> 0.25 + :rand.uniform() * 0.4     # 平衡型中等
      :random -> 0.15 + :rand.uniform() * 0.7       # 随机型全范围
    end

    # 添加个性化随机因子
    personality_factor = Map.get(robot, :patience_level, 50) / 100.0
    final_delay_ratio = base_delay_ratio + (personality_factor - 0.5) * 0.2

    # 确保在合理范围内
    clamped_ratio = max(0.1, min(0.9, final_delay_ratio))
    trunc(betting_time_ms * clamped_ratio)
  end

  # 生成机器人下注金额（参考旧项目的分布）
  defp generate_robot_bet_amount_with_distribution(robot, state) do
    config = state.game_data.config
    chips = Map.get(config, :chips, [1000, 5000, 10000, 100000, 500000])

    # 确保筹码列表不为空
    if length(chips) == 0 do
      Logger.warning("🤖 [CRASH_AI] 筹码配置为空，使用默认值")
      1000
    else
      # 参考旧项目的概率分布：
      # 60%选择小额, 30%中等, 7%较大, 3%最大
      rand_value = :rand.uniform(100)
      robot_type = Map.get(robot, :robot_type, :balanced)

      # 根据机器人类型和概率分布选择筹码
      selected_chip = cond do
        rand_value <= 60 ->
          # 60%概率：小额下注 - 选择前面的筹码
          small_count = max(1, div(length(chips), 3))
          small_chips = Enum.take(chips, small_count)
          Enum.random(small_chips)

        rand_value <= 90 ->
          # 30%概率：中等下注 - 选择中间的筹码
          if length(chips) <= 2 do
            Enum.random(chips)
          else
            middle_start = max(0, div(length(chips), 3))
            middle_count = max(1, div(length(chips), 3))
            middle_chips = Enum.slice(chips, middle_start, middle_count)
            if length(middle_chips) > 0, do: Enum.random(middle_chips), else: Enum.at(chips, 1)
          end

        rand_value <= 97 ->
          # 7%概率：较大下注 - 选择较大的筹码
          if length(chips) <= 2 do
            List.last(chips)
          else
            large_start = max(1, div(length(chips) * 2, 3))
            large_chips = Enum.drop(chips, large_start)
            if length(large_chips) > 0, do: Enum.random(large_chips), else: List.last(chips)
          end

        true ->
          # 3%概率：最大下注 - 选择最大的筹码
          List.last(chips)
      end

      # 根据机器人类型进行最终调整（在筹码列表内选择）
      final_chip = case robot_type do
        :conservative ->
          # 保守型：如果选中的是大筹码，有50%概率降级到小筹码
          if selected_chip == List.last(chips) and :rand.uniform() < 0.5 do
            small_chips = Enum.take(chips, max(1, div(length(chips), 2)))
            Enum.random(small_chips)
          else
            selected_chip
          end

        :aggressive ->
          # 激进型：如果选中的是小筹码，有30%概率升级到大筹码
          if selected_chip == List.first(chips) and :rand.uniform() < 0.3 do
            large_chips = Enum.drop(chips, div(length(chips), 2))
            if length(large_chips) > 0, do: Enum.random(large_chips), else: selected_chip
          else
            selected_chip
          end

        _ ->
          # 平衡型和随机型：保持原选择
          selected_chip
      end

      # 确保返回的金额在筹码列表中
      if final_chip in chips do
        final_chip
      else
        Logger.warning("🤖 [CRASH_AI] 生成的下注金额不在筹码列表中，使用默认筹码")
        List.first(chips)
      end
    end
  end



  defp adjust_bet_probability(robot_type, base_probability, _state) do
    case robot_type do
      # 保守型下注概率降低
      :conservative -> base_probability * 0.8
      # 激进型下注概率提高
      :aggressive -> base_probability * 1.2
      # 平衡型保持不变
      :balanced -> base_probability
      # 随机型随机调整
      :random -> base_probability * (0.5 + :rand.uniform())
    end
  end

  defp calculate_time_pressure(elapsed_time) do
    # 时间越长，机器人越倾向于下车
    # 超过30秒后开始有时间压力
    if elapsed_time > 30000 do
      # 60秒后压力最大
      pressure_factor = (elapsed_time - 30000) / 60000
      :rand.uniform() < min(pressure_factor, 0.8)
    else
      false
    end
  end

  # 改进的时间压力计算
  defp calculate_improved_time_pressure(elapsed_time, robot_type) do
    # 不同类型机器人对时间的敏感度不同
    pressure_threshold = case robot_type do
      :conservative -> 20000  # 保守型20秒后开始有压力
      :aggressive -> 45000    # 激进型45秒后才有压力
      :balanced -> 30000      # 平衡型30秒后有压力
      :random -> 15000 + :rand.uniform(30000)  # 随机型15-45秒
    end

    if elapsed_time > pressure_threshold do
      # 压力随时间线性增长
      pressure_factor = (elapsed_time - pressure_threshold) / 60000
      min(pressure_factor, 0.9)
    else
      0.0
    end
  end

  # 风险评估
  defp calculate_risk_assessment(robot_player, current_multiplier, elapsed_time) do
    risk_tolerance = Map.get(robot_player, :risk_tolerance, 50) / 100.0

    # 倍数越高，风险越大
    multiplier_risk = max(0, (current_multiplier - 100) / 500.0)

    # 时间越长，风险越大
    time_risk = elapsed_time / 60000.0

    # 综合风险评分
    total_risk = (multiplier_risk + time_risk) / 2

    # 风险承受能力低的机器人更容易下车
    total_risk * (1.0 - risk_tolerance)
  end

  # 计算下车决策分数
  defp calculate_cash_out_decision_score(multiplier_reached, time_pressure, risk_assessment, robot_type) do
    base_score = 0.0

    # 倍数达标加分
    base_score = if multiplier_reached, do: base_score + 0.4, else: base_score

    # 时间压力加分
    base_score = base_score + time_pressure * 0.3

    # 风险评估加分
    base_score = base_score + risk_assessment * 0.3

    # 机器人类型调整
    type_adjustment = case robot_type do
      :conservative -> 0.1   # 保守型更容易下车
      :aggressive -> -0.1    # 激进型不容易下车
      :balanced -> 0.0       # 平衡型不调整
      :random -> (:rand.uniform() - 0.5) * 0.2  # 随机型随机调整
    end

    base_score + type_adjustment
  end

  # 获取下车阈值
  defp get_cash_out_threshold(robot_type) do
    case robot_type do
      :conservative -> 0.3   # 保守型容易下车
      :aggressive -> 0.7     # 激进型不容易下车
      :balanced -> 0.5       # 平衡型中等
      :random -> :rand.uniform()  # 随机型完全随机
    end
  end



  # 批量处理机器人下注
  defp batch_robot_betting(state, robots) do
    # 过滤出需要下注的机器人
    betting_robots = Enum.filter(robots, fn {_id, robot} ->
      Map.get(robot, :bet_amount, 0) == 0 and should_robot_bet?(robot, state)
    end)

    # 批量执行下注
    Enum.reduce(betting_robots, state, fn {robot_id, _robot}, acc_state ->
      execute_robot_bet(acc_state, robot_id)
    end)
  end

  # 批量处理机器人下车
  defp batch_robot_cash_out(state, robots) do
    current_time = System.system_time(:millisecond)
    flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
    elapsed_time = current_time - flying_start_time
    config = state.game_data.config
    current_multiplier = CrashLogic.get_current_multiplier(elapsed_time, config)

    # 过滤出需要下车的机器人
    cash_out_robots = Enum.filter(robots, fn {_id, robot} ->
      Map.get(robot, :bet_amount, 0) > 0 and
      not Map.get(robot, :cashed_out, false) and
      should_robot_cash_out_improved?(robot, current_multiplier, elapsed_time)
    end)

    # 批量执行下车
    Enum.reduce(cash_out_robots, state, fn {_id, robot}, acc_state ->
      case CrashLogic.cash_out(acc_state, robot, elapsed_time) do
        {:ok, updated_state, _updated_robot, _payout_info} ->
          updated_state

        {:error, _reason} ->
          acc_state
      end
    end)
  end

  # 统计机器人类型分布
  defp count_robot_types(robots) do
    Enum.reduce(robots, %{}, fn {_id, robot}, acc ->
      robot_type = Map.get(robot, :robot_type, :balanced)
      Map.update(acc, robot_type, 1, &(&1 + 1))
    end)
  end

  # 计算机器人平均下注金额
  defp calculate_average_robot_bet(robots) do
    betting_robots = Enum.filter(robots, fn {_id, robot} ->
      Map.get(robot, :bet_amount, 0) > 0
    end)

    if length(betting_robots) > 0 do
      total_bets = Enum.sum(Enum.map(betting_robots, fn {_id, robot} ->
        Map.get(robot, :bet_amount, 0)
      end))
      div(total_bets, length(betting_robots))
    else
      0
    end
  end
end
