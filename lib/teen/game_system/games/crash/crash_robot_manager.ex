defmodule Cypridina.Teen.GameSystem.Games.Crash.CrashRobotManager do
  @moduledoc """
  Crash游戏机器人管理模块

  管理机器人的工作时间、个性化数据和状态，包括：
  - 机器人上班下班调度机制
  - 机器人个性化数据管理（名字、头像、余额等）
  - 机器人工作状态跟踪和更新
  - 与CrashAI模块的集成
  """

  alias Cypridina.Teen.GameSystem.Games.Crash.{CrashConfig, CrashAI}
  require Logger

  # 机器人工作状态
  @robot_states %{
    working: :working,      # 工作中（在房间内）
    resting: :resting,      # 休息中（不在房间内）
    offline: :offline       # 离线（完全不活动）
  }

  # 机器人工作模式
  @work_modes [:short, :medium, :long]

  @doc """
  初始化机器人管理器
  为房间创建机器人工作状态管理
  """
  def init_robot_manager(room_pid, config \\ nil) do
    config = config || CrashConfig.get_default_config()

    Logger.info("🤖 [CRASH_ROBOT_MANAGER] 初始化机器人管理器")

    # 创建机器人工作状态ETS表
    table_name = :"crash_robots_#{:erlang.phash2(room_pid)}"

    case :ets.info(table_name) do
      :undefined ->
        :ets.new(table_name, [:set, :public, :named_table])
        Logger.info("🤖 [CRASH_ROBOT_MANAGER] 创建机器人状态表: #{table_name}")
      _ ->
        Logger.info("🤖 [CRASH_ROBOT_MANAGER] 机器人状态表已存在: #{table_name}")
    end

    %{
      table_name: table_name,
      room_pid: room_pid,
      config: config,
      active_timers: %{}
    }
  end

  @doc """
  创建带工作时间管理的机器人
  """
  def create_managed_robot(manager, robot_id, opts \\ %{}) do
    # 使用现有的CrashAI创建机器人
    robot = CrashAI.create_robot_player(robot_id)

    # 选择工作模式
    work_mode = choose_work_mode(manager.config)

    # 生成个性化数据
    personal_data = generate_personal_data(robot, work_mode, manager.config)

    # 合并机器人数据
    managed_robot = Map.merge(robot, personal_data)

    # 记录到状态表
    robot_state = %{
      robot_id: robot_id,
      work_mode: work_mode,
      state: :working,
      work_start_time: DateTime.utc_now(),
      work_end_time: calculate_work_end_time(work_mode, manager.config),
      balance_history: [Map.get(managed_robot, :points, 0)],
      created_at: DateTime.utc_now(),
      last_activity: DateTime.utc_now()
    }

    :ets.insert(manager.table_name, {robot_id, robot_state})

    # 安排工作时间管理
    schedule_work_management(manager, robot_id, work_mode)

    Logger.info("🤖 [CRASH_ROBOT_MANAGER] 创建管理机器人: #{get_robot_name(managed_robot)}, 模式: #{work_mode}")

    {managed_robot, robot_state}
  end

  @doc """
  更新机器人余额
  """
  def update_robot_balance(manager, robot_id, new_balance, reason \\ "游戏结算") do
    case :ets.lookup(manager.table_name, robot_id) do
      [{^robot_id, robot_state}] ->
        # 更新余额历史
        updated_history = [new_balance | robot_state.balance_history] |> Enum.take(10)

        updated_state = %{robot_state |
          balance_history: updated_history,
          last_activity: DateTime.utc_now()
        }

        :ets.insert(manager.table_name, {robot_id, updated_state})

        Logger.info("🤖 [CRASH_ROBOT_MANAGER] 更新机器人余额: #{robot_id}, #{new_balance}, 原因: #{reason}")

        # 检查是否需要因余额不足而"下班"
        check_balance_threshold(manager, robot_id, new_balance)

        {:ok, updated_state}

      [] ->
        Logger.warning("🤖 [CRASH_ROBOT_MANAGER] 机器人不存在: #{robot_id}")
        {:error, :robot_not_found}
    end
  end

  @doc """
  获取机器人工作状态
  """
  def get_robot_work_status(manager, robot_id) do
    case :ets.lookup(manager.table_name, robot_id) do
      [{^robot_id, robot_state}] -> {:ok, robot_state}
      [] -> {:error, :robot_not_found}
    end
  end

  @doc """
  获取所有工作中的机器人
  """
  def get_working_robots(manager) do
    :ets.select(manager.table_name, [
      {{:"$1", :"$2"}, [{:==, {:map_get, :state, :"$2"}, :working}], [:"$2"]}
    ])
  end

  @doc """
  获取机器人统计信息
  """
  def get_robot_statistics(manager) do
    all_robots = :ets.tab2list(manager.table_name)

    stats = Enum.reduce(all_robots, %{working: 0, resting: 0, offline: 0}, fn {_id, state}, acc ->
      Map.update(acc, state.state, 1, &(&1 + 1))
    end)

    %{
      total_robots: length(all_robots),
      by_state: stats,
      by_work_mode: count_by_work_mode(all_robots),
      average_balance: calculate_average_balance(all_robots)
    }
  end

  @doc """
  处理机器人下班
  """
  def robot_end_work(manager, robot_id) do
    case :ets.lookup(manager.table_name, robot_id) do
      [{^robot_id, robot_state}] ->
        # 计算休息时间
        rest_duration = calculate_rest_duration(robot_state.work_mode, manager.config)
        rest_end_time = DateTime.add(DateTime.utc_now(), rest_duration * 60, :second)

        updated_state = %{robot_state |
          state: :resting,
          work_end_time: DateTime.utc_now(),
          rest_end_time: rest_end_time
        }

        :ets.insert(manager.table_name, {robot_id, updated_state})

        # 安排重新上班
        schedule_return_to_work(manager, robot_id, rest_duration)

        Logger.info("🤖 [CRASH_ROBOT_MANAGER] 机器人下班: #{robot_id}, 休息#{rest_duration}分钟")

        {:ok, :robot_end_work, robot_id}

      [] ->
        {:error, :robot_not_found}
    end
  end

  @doc """
  处理机器人上班
  """
  def robot_start_work(manager, robot_id) do
    case :ets.lookup(manager.table_name, robot_id) do
      [{^robot_id, robot_state}] ->
        # 可能恢复一些余额
        maybe_recover_balance(manager, robot_id, robot_state)

        # 计算新的工作结束时间
        work_end_time = calculate_work_end_time(robot_state.work_mode, manager.config)

        updated_state = %{robot_state |
          state: :working,
          work_start_time: DateTime.utc_now(),
          work_end_time: work_end_time
        }

        :ets.insert(manager.table_name, {robot_id, updated_state})

        # 安排下次下班
        schedule_work_management(manager, robot_id, robot_state.work_mode)

        Logger.info("🤖 [CRASH_ROBOT_MANAGER] 机器人上班: #{robot_id}")

        {:ok, :robot_start_work, robot_id}

      [] ->
        {:error, :robot_not_found}
    end
  end

  @doc """
  清理机器人管理器
  """
  def cleanup_robot_manager(manager) do
    # 取消所有定时器
    Enum.each(manager.active_timers, fn {_robot_id, timer_ref} ->
      Process.cancel_timer(timer_ref)
    end)

    # 删除ETS表
    :ets.delete(manager.table_name)

    Logger.info("🤖 [CRASH_ROBOT_MANAGER] 清理机器人管理器")
  end

  # ==================== 私有函数 ====================

  # 选择工作模式
  defp choose_work_mode(config) do
    schedules = Map.get(config, :robot_work_schedules, %{})

    # 根据概率选择工作模式
    rand_value = :rand.uniform()

    short_prob = Map.get(Map.get(schedules, :short, %{}), :probability, 0.4)
    medium_prob = Map.get(Map.get(schedules, :medium, %{}), :probability, 0.4)

    cond do
      rand_value <= short_prob -> :short
      rand_value <= short_prob + medium_prob -> :medium
      true -> :long
    end
  end

  # 生成个性化数据
  defp generate_personal_data(robot, work_mode, config) do
    balance_config = Map.get(config, :robot_balance_config, %{})
    {min_balance, max_balance} = Map.get(balance_config, :initial_balance_range, {50_000, 1_000_000})

    # 根据工作模式调整初始余额
    balance_multiplier = case work_mode do
      :short -> 0.8   # 短时间工作者余额较少
      :medium -> 1.0  # 中等时间工作者余额正常
      :long -> 1.3    # 长时间工作者余额较多
    end

    base_balance = min_balance + :rand.uniform(max_balance - min_balance)
    adjusted_balance = trunc(base_balance * balance_multiplier)

    %{
      # 更新余额
      points: adjusted_balance,
      # 工作模式标识
      work_mode: work_mode,
      # 个性化标识
      personality_traits: generate_personality_traits(work_mode),
      # 余额变化记录
      balance_changes: [],
      # 工作历史
      work_history: []
    }
  end

  # 生成个性特征
  defp generate_personality_traits(work_mode) do
    base_traits = %{
      patience: :rand.uniform(100),
      risk_tolerance: :rand.uniform(100),
      activity_level: :rand.uniform(100)
    }

    # 根据工作模式调整特征
    case work_mode do
      :short ->
        %{base_traits | activity_level: base_traits.activity_level * 0.7}
      :medium ->
        base_traits
      :long ->
        %{base_traits | patience: base_traits.patience * 1.2}
    end
  end

  # 计算工作结束时间
  defp calculate_work_end_time(work_mode, config) do
    schedules = Map.get(config, :robot_work_schedules, %{})
    work_schedule = Map.get(schedules, work_mode, %{})
    {min_work, max_work} = Map.get(work_schedule, :work_time_range, {30, 60})

    work_duration = min_work + :rand.uniform(max_work - min_work)
    DateTime.add(DateTime.utc_now(), work_duration * 60, :second)
  end

  # 计算休息时长
  defp calculate_rest_duration(work_mode, config) do
    schedules = Map.get(config, :robot_work_schedules, %{})
    work_schedule = Map.get(schedules, work_mode, %{})
    {min_rest, max_rest} = Map.get(work_schedule, :rest_time_range, {200, 300})

    min_rest + :rand.uniform(max_rest - min_rest)
  end

  # 安排工作时间管理
  defp schedule_work_management(manager, robot_id, work_mode) do
    schedules = Map.get(manager.config, :robot_work_schedules, %{})
    work_schedule = Map.get(schedules, work_mode, %{})
    {min_work, max_work} = Map.get(work_schedule, :work_time_range, {30, 60})

    work_duration_minutes = min_work + :rand.uniform(max_work - min_work)
    work_duration_ms = work_duration_minutes * 60 * 1000

    # 发送下班消息
    timer_ref = Process.send_after(manager.room_pid, {:robot_end_work, robot_id}, work_duration_ms)

    Logger.info("🤖 [CRASH_ROBOT_MANAGER] 安排机器人#{robot_id}在#{work_duration_minutes}分钟后下班")

    # 更新定时器记录
    updated_timers = Map.put(manager.active_timers, robot_id, timer_ref)
    %{manager | active_timers: updated_timers}
  end

  # 安排重新上班
  defp schedule_return_to_work(manager, robot_id, rest_duration_minutes) do
    rest_duration_ms = rest_duration_minutes * 60 * 1000

    # 发送上班消息
    timer_ref = Process.send_after(manager.room_pid, {:robot_start_work, robot_id}, rest_duration_ms)

    Logger.info("🤖 [CRASH_ROBOT_MANAGER] 安排机器人#{robot_id}在#{rest_duration_minutes}分钟后上班")

    # 更新定时器记录
    updated_timers = Map.put(manager.active_timers, robot_id, timer_ref)
    %{manager | active_timers: updated_timers}
  end

  # 检查余额阈值
  defp check_balance_threshold(manager, robot_id, new_balance) do
    balance_config = Map.get(manager.config, :robot_balance_config, %{})
    min_threshold = Map.get(balance_config, :min_balance_threshold, 10_000)

    if new_balance < min_threshold do
      Logger.info("🤖 [CRASH_ROBOT_MANAGER] 机器人#{robot_id}余额不足，安排下班")
      # 发送立即下班消息
      send(manager.room_pid, {:robot_end_work, robot_id, :insufficient_balance})
    end
  end

  # 可能恢复余额
  defp maybe_recover_balance(manager, robot_id, robot_state) do
    balance_config = Map.get(manager.config, :robot_balance_config, %{})
    recovery_config = Map.get(balance_config, :balance_recovery, %{})

    if Map.get(recovery_config, :enabled, true) do
      recovery_probability = Map.get(recovery_config, :recovery_probability, 0.3)

      if :rand.uniform() < recovery_probability do
        {min_target, max_target} = Map.get(recovery_config, :target_range, {100_000, 500_000})
        new_balance = min_target + :rand.uniform(max_target - min_target)

        update_robot_balance(manager, robot_id, new_balance, "休息恢复")
        Logger.info("🤖 [CRASH_ROBOT_MANAGER] 机器人#{robot_id}休息后恢复余额: #{new_balance}")
      end
    end
  end

  # 按工作模式统计
  defp count_by_work_mode(all_robots) do
    Enum.reduce(all_robots, %{short: 0, medium: 0, long: 0}, fn {_id, state}, acc ->
      work_mode = Map.get(state, :work_mode, :medium)
      Map.update(acc, work_mode, 1, &(&1 + 1))
    end)
  end

  # 计算平均余额
  defp calculate_average_balance(all_robots) do
    if length(all_robots) == 0 do
      0
    else
      total_balance = Enum.reduce(all_robots, 0, fn {_id, state}, acc ->
        latest_balance = List.first(state.balance_history) || 0
        acc + latest_balance
      end)

      div(total_balance, length(all_robots))
    end
  end

  # 安全获取机器人名字
  defp get_robot_name(robot) do
    case robot do
      %Cypridina.Teen.GameSystem.PlayerData{} = p ->
        Cypridina.Teen.GameSystem.PlayerData.get_display_name(p)
      %{nickname: nickname} when is_binary(nickname) ->
        nickname
      _ ->
        "Robot#{Map.get(robot, :numeric_id, 0)}"
    end
  end
end
