defmodule Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaGameLogic do
  @moduledoc """
  Jhandi Munda 游戏逻辑引擎

  实现核心游戏算法，包括：
  - 骰子投掷算法
  - 中奖计算
  - 赔率计算
  - 游戏结果生成
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaConstants

  @doc """
  投掷骰子，生成游戏结果

  返回格式：
  %{
    dice_results: [1, 2, 3, 4, 5, 6],  # 每个骰子的结果
    symbol_counts: %{1 => 2, 2 => 1, 3 => 1, 4 => 1, 5 => 1, 6 => 0},  # 每个符号出现的次数
    winning_symbols: [1, 2, 3, 4, 5]  # 有中奖的符号列表
  }
  """
  def roll_dice do
    # 生成6个骰子的结果，每个骰子随机选择1-6的符号
    dice_results =
      for _ <- 1..JhandiMundaConstants.dice_count() do
        Enum.random(JhandiMundaConstants.get_all_symbols())
      end

    # 统计每个符号出现的次数
    symbol_counts = count_symbols(dice_results)

    # 找出有中奖的符号（出现次数 > 0）
    winning_symbols =
      symbol_counts
      |> Enum.filter(fn {_symbol, count} -> count > 0 end)
      |> Enum.map(fn {symbol, _count} -> symbol end)

    %{
      dice_results: dice_results,
      symbol_counts: symbol_counts,
      winning_symbols: winning_symbols
    }
  end

  @doc """
  根据控制参数生成特定结果的骰子

  control_type:
  - :random - 随机结果
  - :favor_player - 偏向玩家的结果
  - :favor_house - 偏向庄家的结果
  - :specific_symbol - 指定符号多出现
  """
  def roll_dice_with_control(control_type, opts \\ []) do
    case control_type do
      :random ->
        roll_dice()

      :favor_player ->
        generate_player_favorable_result(opts)

      :favor_house ->
        generate_house_favorable_result(opts)

      :specific_symbol ->
        target_symbol = Keyword.get(opts, :target_symbol, JhandiMundaConstants.symbol_clubs())
        target_count = Keyword.get(opts, :target_count, 3)
        generate_specific_symbol_result(target_symbol, target_count)

      _ ->
        roll_dice()
    end
  end

  @doc """
  计算玩家的中奖金额

  参数：
  - player_bets: %{symbol => bet_amount} 玩家在各符号的下注
  - game_result: 游戏结果

  返回：
  %{
    total_win: 总中奖金额,
    win_details: %{symbol => %{bet: 下注金额, count: 出现次数, odds: 赔率, win: 中奖金额}},
    total_bet: 总下注金额
  }
  """
  def calculate_player_winnings(player_bets, game_result) do
    symbol_counts = game_result.symbol_counts

    win_details =
      player_bets
      |> Enum.map(fn {symbol, bet_amount} ->
        count = Map.get(symbol_counts, symbol, 0)
        odds = JhandiMundaConstants.get_odds(count)
        win_amount = if count > 0, do: bet_amount * odds, else: 0

        {symbol,
         %{
           bet: bet_amount,
           count: count,
           odds: odds,
           win: win_amount
         }}
      end)
      |> Enum.into(%{})

    total_win =
      win_details
      |> Enum.reduce(0, fn {_symbol, details}, acc -> acc + details.win end)

    total_bet =
      player_bets
      |> Enum.reduce(0, fn {_symbol, bet_amount}, acc -> acc + bet_amount end)

    %{
      total_win: total_win,
      win_details: win_details,
      total_bet: total_bet,
      net_win: total_win - total_bet
    }
  end

  @doc """
  计算所有玩家的结算结果

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}
  - game_result: 游戏结果

  返回：
  %{player_id => %{total_win: 总中奖金额, win_details: 详细信息, total_bet: 总下注金额, net_win: 净赢得}}
  """
  def calculate_settlements(all_player_bets, game_result) do
    all_player_bets
    |> Enum.reduce(%{}, fn {player_id, player_bets}, acc ->
      player_result = calculate_player_winnings(player_bets, game_result)
      Map.put(acc, player_id, player_result)
    end)
  end

  @doc """
  计算房间总的输赢情况

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}
  - game_result: 游戏结果

  返回房间的盈亏情况
  """
  def calculate_room_profit(all_player_bets, game_result) do
    total_bets = 0
    total_wins = 0
    player_results = %{}

    {total_bets, total_wins, player_results} =
      all_player_bets
      |> Enum.reduce({0, 0, %{}}, fn {player_id, player_bets},
                                     {acc_bets, acc_wins, acc_results} ->
        player_result = calculate_player_winnings(player_bets, game_result)

        {
          acc_bets + player_result.total_bet,
          acc_wins + player_result.total_win,
          Map.put(acc_results, player_id, player_result)
        }
      end)

    %{
      total_bets: total_bets,
      total_wins: total_wins,
      house_profit: total_bets - total_wins,
      player_results: player_results
    }
  end

  @doc """
  验证下注是否有效
  """
  def validate_bet(symbol, amount) do
    cond do
      not JhandiMundaConstants.valid_symbol?(symbol) ->
        {:error, :invalid_symbol}

      amount < JhandiMundaConstants.min_bet() ->
        {:error, :bet_too_small}

      amount > JhandiMundaConstants.max_bet() ->
        {:error, :bet_too_large}

      not JhandiMundaConstants.valid_chip?(amount) ->
        {:error, :invalid_chip}

      true ->
        :ok
    end
  end

  @doc """
  生成游戏历史记录格式
  """
  def format_game_record(game_result, round_id, big_winners \\ []) do
    %{
      round_id: round_id,
      dice_results: game_result.dice_results,
      symbol_counts: game_result.symbol_counts,
      winning_symbols: game_result.winning_symbols,
      # 添加大赢家信息
      big_winners: big_winners,
      timestamp: DateTime.utc_now()
    }
  end

  # 私有函数

  defp count_symbols(dice_results) do
    # 初始化所有符号的计数为0
    initial_counts =
      JhandiMundaConstants.get_all_symbols()
      |> Enum.map(fn symbol -> {symbol, 0} end)
      |> Enum.into(%{})

    # 统计每个符号出现的次数
    dice_results
    |> Enum.reduce(initial_counts, fn symbol, acc ->
      Map.update(acc, symbol, 1, &(&1 + 1))
    end)
  end

  defp generate_player_favorable_result(_opts) do
    # 生成对玩家有利的结果（更多高赔率组合）
    # 随机选择一个符号，让它出现3-4次
    target_symbol = Enum.random(JhandiMundaConstants.get_all_symbols())
    target_count = Enum.random(3..4)

    generate_specific_symbol_result(target_symbol, target_count)
  end

  defp generate_house_favorable_result(_opts) do
    # 生成对庄家有利的结果（更分散的结果，低赔率）
    symbols = JhandiMundaConstants.get_all_symbols()

    # 尽量让每个符号都出现1次，剩余的随机分配
    dice_results =
      symbols ++
        for _ <- 1..(JhandiMundaConstants.dice_count() - length(symbols)) do
          Enum.random(symbols)
        end

    # 打乱顺序
    dice_results = Enum.shuffle(dice_results)

    symbol_counts = count_symbols(dice_results)

    winning_symbols =
      symbol_counts
      |> Enum.filter(fn {_symbol, count} -> count > 0 end)
      |> Enum.map(fn {symbol, _count} -> symbol end)

    %{
      dice_results: dice_results,
      symbol_counts: symbol_counts,
      winning_symbols: winning_symbols
    }
  end

  defp generate_specific_symbol_result(target_symbol, target_count) do
    # 确保目标符号出现指定次数
    target_dice = for _ <- 1..target_count, do: target_symbol

    # 剩余骰子随机选择其他符号
    remaining_count = JhandiMundaConstants.dice_count() - target_count
    other_symbols = JhandiMundaConstants.get_all_symbols() -- [target_symbol]

    remaining_dice =
      for _ <- 1..remaining_count do
        Enum.random(other_symbols)
      end

    # 合并并打乱
    dice_results = Enum.shuffle(target_dice ++ remaining_dice)

    symbol_counts = count_symbols(dice_results)

    winning_symbols =
      symbol_counts
      |> Enum.filter(fn {_symbol, count} -> count > 0 end)
      |> Enum.map(fn {symbol, _count} -> symbol end)

    %{
      dice_results: dice_results,
      symbol_counts: symbol_counts,
      winning_symbols: winning_symbols
    }
  end

  @doc """
  根据控制数据生成骰子结果（与控制系统集成）

  参数：
  - control_result: 来自控制系统的结果 %{dice_results: [...], symbol_counts: %{}, ...}

  返回与 roll_dice/0 相同格式的结果
  """
  def roll_dice_with_control_data(control_result) do
    Logger.info("🎮 [JHANDI_MUNDA_LOGIC] 使用控制数据生成骰子结果")

    # 控制系统已经提供了完整的骰子结果，直接使用
    %{
      dice_results: control_result.dice_results,
      symbol_counts: control_result.symbol_counts,
      winning_symbols: control_result.winning_symbols
    }
  end

  @doc """
  分析投注数据以提供给控制系统

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}

  返回：
  %{symbol => total_bet_amount} - 每个符号的总投注额
  """
  def analyze_bets_for_control(all_player_bets) do
    # 统计每个符号的总投注额
    symbol_totals =
      JhandiMundaConstants.get_all_symbols()
      |> Enum.map(fn symbol -> {symbol, 0} end)
      |> Enum.into(%{})

    total_bets =
      all_player_bets
      |> Enum.reduce(symbol_totals, fn {_player_id, player_bets}, acc ->
        player_bets
        |> Enum.reduce(acc, fn {symbol, bet_amount}, inner_acc ->
          Map.update(inner_acc, symbol, bet_amount, &(&1 + bet_amount))
        end)
      end)

    Logger.info("🎮 [JHANDI_MUNDA_LOGIC] 投注分析: #{inspect(total_bets)}")

    total_bets
  end

  @doc """
  分析真实玩家投注数据以提供给控制系统（排除机器人投注）

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}
  - player_info: %{player_id => %{is_robot: boolean}}

  返回：
  %{symbol => total_bet_amount} - 每个符号的真实玩家总投注额
  """
  def analyze_real_player_bets_for_control(all_player_bets, player_info) do
    # 统计每个符号的总投注额
    symbol_totals =
      JhandiMundaConstants.get_all_symbols()
      |> Enum.map(fn symbol -> {symbol, 0} end)
      |> Enum.into(%{})

    # 只统计真实玩家的投注
    real_player_bets =
      all_player_bets
      |> Enum.filter(fn {player_id, _player_bets} ->
        case Map.get(player_info, player_id) do
          # 排除机器人
          %{is_robot: true} -> false
          # 包含真实玩家
          _ -> true
        end
      end)
      |> Enum.into(%{})

    total_bets =
      real_player_bets
      |> Enum.reduce(symbol_totals, fn {player_id, player_bets}, acc ->
        player_bets
        |> Enum.reduce(acc, fn {symbol, bet_amount}, inner_acc ->
          Map.update(inner_acc, symbol, bet_amount, &(&1 + bet_amount))
        end)
      end)

    robot_count = Enum.count(player_info, fn {_, info} -> Map.get(info, :is_robot, false) end)
    real_player_count = Enum.count(player_info) - robot_count

    Logger.info(
      "🎮 [JHANDI_MUNDA_LOGIC] 真实玩家投注分析: 真实玩家数量=#{real_player_count}, 机器人数量=#{robot_count}"
    )

    Logger.info("🎮 [JHANDI_MUNDA_LOGIC] 真实玩家投注: #{inspect(total_bets)}")

    total_bets
  end

  @doc """
  计算平台盈利（用于库存更新）

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}
  - game_result: 游戏结果

  返回：
  平台总盈利（正数表示平台赚钱，负数表示平台亏钱）
  """
  def calculate_platform_profit(all_player_bets, game_result) do
    room_profit = calculate_room_profit(all_player_bets, game_result)

    # 平台盈利 = 总投注 - 总赔付
    platform_profit = room_profit.house_profit

    Logger.info("🎮 [JHANDI_MUNDA_LOGIC] 平台盈利: #{platform_profit}")

    platform_profit
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_statistics(history_records) do
    if Enum.empty?(history_records) do
      %{
        total_rounds: 0,
        symbol_frequencies: %{},
        average_winning_symbols: 0
      }
    else
      symbol_frequencies =
        history_records
        |> Enum.reduce(%{}, fn record, acc ->
          record.symbol_counts
          |> Enum.reduce(acc, fn {symbol, count}, inner_acc ->
            Map.update(inner_acc, symbol, count, &(&1 + count))
          end)
        end)

      total_winning_symbols =
        history_records
        |> Enum.reduce(0, fn record, acc ->
          acc + length(record.winning_symbols)
        end)

      %{
        total_rounds: length(history_records),
        symbol_frequencies: symbol_frequencies,
        average_winning_symbols: total_winning_symbols / length(history_records)
      }
    end
  end

  @doc """
  应用税收机制到玩家中奖金额

  税收流程：
  1. 计算明税：gross_profit * winner_tax_rate
  2. 计算暗税：明税 * (dark_tax_rate / 1000)
  3. 平台收入：明税 - 暗税
  4. 中心线调整：+暗税
  5. 玩家实得：gross_profit - 明税

  参数：
  - gross_profit: 玩家总中奖金额
  - game_control_config: 游戏控制配置，包含税率信息

  返回：
  %{
    net_profit: 玩家实得金额,
    ming_tax: 明税金额,
    an_tax: 暗税金额,
    platform_income: 平台实际收入
  }
  """
  def apply_winner_tax(gross_profit, game_control_config) do
    # 获取配置参数
    winner_tax_rate = game_control_config.winner_tax_rate || Decimal.new("0.05")
    dark_tax_rate = game_control_config.dark_tax_rate || -50

    # 转换为Decimal确保精确计算
    gross_profit_decimal =
      if is_struct(gross_profit, Decimal), do: gross_profit, else: Decimal.new("#{gross_profit}")

    winner_tax_rate_decimal =
      if is_struct(winner_tax_rate, Decimal),
        do: winner_tax_rate,
        else: Decimal.new("#{winner_tax_rate}")

    # 1. 计算明税
    ming_tax = Decimal.mult(gross_profit_decimal, winner_tax_rate_decimal)

    # 2. 计算暗税（从明税中抽取）
    dark_tax_ratio = dark_tax_rate / 1000.0
    an_tax = Decimal.mult(ming_tax, Decimal.new("#{dark_tax_ratio}"))

    # 3. 计算平台实际收入（明税 - 暗税）
    platform_income = Decimal.sub(ming_tax, an_tax)

    # 4. 计算玩家实得（总赢利 - 明税）
    net_profit = Decimal.sub(gross_profit_decimal, ming_tax)

    Logger.info(
      "🎮 [JHANDI_MUNDA_LOGIC] 明税计算完成: gross_profit=#{Decimal.to_float(gross_profit_decimal)}, ming_tax=#{Decimal.to_float(ming_tax)}, an_tax=#{Decimal.to_float(an_tax)}, net_profit=#{Decimal.to_float(net_profit)}"
    )

    %{
      net_profit: Decimal.to_integer(net_profit),
      ming_tax: Decimal.to_float(ming_tax),
      an_tax: Decimal.to_float(an_tax),
      platform_income: Decimal.to_float(platform_income),
      tax_rate: Decimal.to_float(winner_tax_rate_decimal),
      dark_tax_rate: dark_tax_rate
    }
  end

  @doc """
  计算玩家的中奖金额（含税收）

  参数：
  - player_bets: %{symbol => bet_amount} 玩家在各符号的下注
  - game_result: 游戏结果
  - game_control_config: 游戏控制配置，包含税率信息（可选）

  返回：
  %{
    total_win: 总中奖金额（税前）,
    net_win: 总中奖金额（税后）,
    total_tax: 总税收,
    win_details: %{symbol => %{bet: 下注金额, count: 出现次数, odds: 赔率, win: 中奖金额}},
    total_bet: 总下注金额
  }
  """
  def calculate_player_winnings_with_tax(player_bets, game_result, game_control_config \\ nil) do
    # 先计算基础中奖金额
    basic_result = calculate_player_winnings(player_bets, game_result)

    # 如果没有提供税收配置或者没有中奖，直接返回基础结果
    if !game_control_config || basic_result.total_win == 0 do
      Map.merge(basic_result, %{
        net_win: basic_result.total_win,
        total_tax: 0,
        tax_details: %{ming_tax: 0, an_tax: 0, platform_income: 0}
      })
    else
      # 应用税收
      tax_result = apply_winner_tax(basic_result.total_win, game_control_config)

      Map.merge(basic_result, %{
        net_win: tax_result.net_profit,
        total_tax: tax_result.ming_tax,
        tax_details: %{
          ming_tax: tax_result.ming_tax,
          an_tax: tax_result.an_tax,
          platform_income: tax_result.platform_income,
          tax_rate: tax_result.tax_rate,
          dark_tax_rate: tax_result.dark_tax_rate
        }
      })
    end
  end

  @doc """
  计算所有玩家的结算结果（含税收）

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}
  - game_result: 游戏结果
  - game_control_config: 游戏控制配置，包含税率信息（可选）

  返回：
  %{player_id => %{total_win: 税前中奖金额, net_win: 税后中奖金额, total_tax: 税收, win_details: 详细信息, total_bet: 总下注金额}}
  """
  def calculate_settlements_with_tax(all_player_bets, game_result, game_control_config \\ nil) do
    all_player_bets
    |> Enum.reduce(%{}, fn {player_id, player_bets}, acc ->
      player_result =
        calculate_player_winnings_with_tax(player_bets, game_result, game_control_config)

      Map.put(acc, player_id, player_result)
    end)
  end

  @doc """
  计算房间总的输赢情况（含税收）

  参数：
  - all_player_bets: %{player_id => %{symbol => bet_amount}}
  - game_result: 游戏结果
  - game_control_config: 游戏控制配置，包含税率信息（可选）

  返回房间的盈亏情况
  """
  def calculate_room_profit_with_tax(all_player_bets, game_result, game_control_config \\ nil) do
    total_bets = 0
    total_wins = 0
    total_net_wins = 0
    total_taxes = 0
    player_results = %{}

    {total_bets, total_wins, total_net_wins, total_taxes, player_results} =
      all_player_bets
      |> Enum.reduce({0, 0, 0, 0, %{}}, fn {player_id, player_bets},
                                           {acc_bets, acc_wins, acc_net_wins, acc_taxes,
                                            acc_results} ->
        player_result =
          calculate_player_winnings_with_tax(player_bets, game_result, game_control_config)

        {
          acc_bets + player_result.total_bet,
          acc_wins + player_result.total_win,
          acc_net_wins + player_result.net_win,
          acc_taxes + player_result.total_tax,
          Map.put(acc_results, player_id, player_result)
        }
      end)

    %{
      total_bets: total_bets,
      total_wins: total_wins,
      total_net_wins: total_net_wins,
      total_taxes: total_taxes,
      house_profit: total_bets - total_net_wins,
      player_results: player_results
    }
  end
end
