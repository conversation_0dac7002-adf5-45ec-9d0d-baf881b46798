defmodule Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaDemo do
  @moduledoc """
  Jhandi Munda 游戏演示和测试模块

  用于演示游戏功能和验证实现正确性
  """

  require Logger

  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.{
    JhandiMundaConstants,
    JhandiMundaGame,
    JhandiMundaGameLogic,
    JhandiMundaHistory
  }

  @doc """
  运行完整的游戏演示
  """
  def run_demo do
    Logger.info("🎲 [JHANDI_MUNDA_DEMO] 开始 Jhandi Munda 游戏演示")

    show_game_info()
    show_game_config()
    demo_game_logic()
    demo_history_system()
    demo_betting_scenarios()

    Logger.info("🎲 [JHANDI_MUNDA_DEMO] 演示完成")
  end

  @doc """
  显示游戏基本信息
  """
  defp show_game_info do
    Logger.info("🎲 [JHANDI_MUNDA_DEMO] === 游戏基本信息 ===")
    Logger.info("🎲 游戏名称: #{JhandiMundaGame.game_name()}")
    Logger.info("🎲 游戏类型: #{JhandiMundaGame.game_type()}")
    Logger.info("🎲 支持的游戏ID: #{inspect(JhandiMundaGame.supported_game_ids())}")
    Logger.info("🎲 是否为大厅游戏: #{JhandiMundaGame.is_lobby_game?()}")

    version_info = JhandiMundaGame.version_info()
    Logger.info("🎲 版本信息: #{version_info.version} (#{version_info.build_date})")
    Logger.info("🎲 功能特性: #{inspect(version_info.features)}")
  end

  @doc """
  显示游戏配置
  """
  defp show_game_config do
    Logger.info("🎲 [JHANDI_MUNDA_DEMO] === 游戏配置信息 ===")

    config = JhandiMundaConstants.get_game_config()
    Logger.info("🎲 骰子数量: #{config.dice_count}")
    Logger.info("🎲 下注区域数量: #{config.area_count}")
    Logger.info("🎲 符号数量: #{config.symbol_count}")
    Logger.info("🎲 下注范围: #{config.min_bet} - #{config.max_bet}")
    Logger.info("🎲 可用筹码: #{inspect(config.bet_chips)}")
    Logger.info("🎲 赔率表: #{inspect(config.odds_table)}")
    Logger.info("🎲 下注时间: #{config.betting_time}秒")
    Logger.info("🎲 开奖时间: #{config.revealing_time}秒")
    Logger.info("🎲 最大玩家数: #{config.max_players}")

    # 显示符号信息
    Logger.info("🎲 游戏符号:")

    JhandiMundaConstants.get_all_symbols()
    |> Enum.each(fn symbol ->
      name = JhandiMundaConstants.get_symbol_name(symbol)
      Logger.info("🎲   #{symbol}: #{name}")
    end)
  end

  @doc """
  演示游戏逻辑
  """
  defp demo_game_logic do
    Logger.info("🎲 [JHANDI_MUNDA_DEMO] === 游戏逻辑演示 ===")

    # 演示随机投掷
    Logger.info("🎲 随机投掷骰子:")
    result = JhandiMundaGameLogic.roll_dice()
    Logger.info("🎲   骰子结果: #{inspect(result.dice_results)}")
    Logger.info("🎲   符号统计: #{inspect(result.symbol_counts)}")
    Logger.info("🎲   中奖符号: #{inspect(result.winning_symbols)}")

    # 演示控制投掷
    Logger.info("🎲 控制投掷 - 偏向玩家:")
    controlled_result = JhandiMundaGameLogic.roll_dice_with_control(:favor_player)
    Logger.info("🎲   骰子结果: #{inspect(controlled_result.dice_results)}")
    Logger.info("🎲   符号统计: #{inspect(controlled_result.symbol_counts)}")

    # 演示指定符号投掷
    target_symbol = JhandiMundaConstants.symbol_clubs()
    Logger.info("🎲 指定符号投掷 - 梅花出现4次:")

    specific_result =
      JhandiMundaGameLogic.roll_dice_with_control(:specific_symbol,
        target_symbol: target_symbol,
        target_count: 4
      )

    Logger.info("🎲   骰子结果: #{inspect(specific_result.dice_results)}")
    Logger.info("🎲   符号统计: #{inspect(specific_result.symbol_counts)}")

    # 演示中奖计算
    demo_winnings_calculation(result)
  end

  @doc """
  演示中奖计算
  """
  defp demo_winnings_calculation(game_result) do
    Logger.info("🎲 中奖计算演示:")

    # 模拟玩家下注
    player_bets = %{
      # 梅花下注100
      JhandiMundaConstants.symbol_clubs() => 100,
      # 皇冠下注200
      JhandiMundaConstants.symbol_crown() => 200,
      # 黑桃下注50
      JhandiMundaConstants.symbol_spades() => 50
    }

    Logger.info("🎲   玩家下注: #{inspect(player_bets)}")

    winnings = JhandiMundaGameLogic.calculate_player_winnings(player_bets, game_result)
    Logger.info("🎲   总下注: #{winnings.total_bet}")
    Logger.info("🎲   总中奖: #{winnings.total_win}")
    Logger.info("🎲   净盈亏: #{winnings.net_win}")
    Logger.info("🎲   中奖详情:")

    winnings.win_details
    |> Enum.each(fn {symbol, details} ->
      symbol_name = JhandiMundaConstants.get_symbol_name(symbol)

      Logger.info(
        "🎲     #{symbol_name}: 下注#{details.bet}, 出现#{details.count}次, 赔率#{details.odds}, 中奖#{details.win}"
      )
    end)
  end

  @doc """
  演示历史记录系统
  """
  defp demo_history_system do
    Logger.info("🎲 [JHANDI_MUNDA_DEMO] === 历史记录系统演示 ===")

    # 初始化历史记录
    history = JhandiMundaHistory.init_state()
    Logger.info("🎲 初始化历史记录状态")

    # 添加几轮游戏记录
    history =
      1..5
      |> Enum.reduce(history, fn round_id, acc_history ->
        game_result = JhandiMundaGameLogic.roll_dice()
        game_record = JhandiMundaGameLogic.format_game_record(game_result, round_id)
        JhandiMundaHistory.add_record(acc_history, game_record)
      end)

    Logger.info("🎲 添加了5轮游戏记录")

    # 获取统计信息
    stats = JhandiMundaHistory.get_statistics(history)
    Logger.info("🎲 统计信息:")
    Logger.info("🎲   总轮次: #{stats.total_rounds}")
    Logger.info("🎲   平均中奖符号数: #{Float.round(stats.average_winning_symbols, 2)}")
    Logger.info("🎲   最大相同符号数: #{stats.max_same_symbol_count}")

    # 获取走势数据
    trend_data = JhandiMundaHistory.get_trend_data(history)
    Logger.info("🎲 走势数据:")
    Logger.info("🎲   热门符号: #{inspect(trend_data.hot_symbols)}")
    Logger.info("🎲   冷门符号: #{inspect(trend_data.cold_symbols)}")

    # 格式化客户端数据
    client_data = JhandiMundaHistory.format_for_client(history, count: 3, include_trend: true)
    Logger.info("🎲 客户端数据格式: #{inspect(Map.keys(client_data))}")
  end

  @doc """
  演示下注场景
  """
  defp demo_betting_scenarios do
    Logger.info("🎲 [JHANDI_MUNDA_DEMO] === 下注场景演示 ===")

    # 测试有效下注
    valid_bets = [
      {JhandiMundaConstants.symbol_clubs(), 100},
      {JhandiMundaConstants.symbol_crown(), 1000},
      {JhandiMundaConstants.symbol_spades(), 50}
    ]

    Logger.info("🎲 有效下注测试:")

    valid_bets
    |> Enum.each(fn {symbol, amount} ->
      case JhandiMundaGameLogic.validate_bet(symbol, amount) do
        :ok ->
          symbol_name = JhandiMundaConstants.get_symbol_name(symbol)
          Logger.info("🎲   ✅ #{symbol_name} #{amount} - 有效")

        {:error, reason} ->
          Logger.info("🎲   ❌ 符号#{symbol} #{amount} - 错误: #{reason}")
      end
    end)

    # 测试无效下注
    invalid_bets = [
      # 无效符号
      {0, 100},
      # 下注太小
      {JhandiMundaConstants.symbol_clubs(), 5},
      # 下注太大
      {JhandiMundaConstants.symbol_clubs(), 1_000_000},
      # 无效筹码
      {JhandiMundaConstants.symbol_clubs(), 15}
    ]

    Logger.info("🎲 无效下注测试:")

    invalid_bets
    |> Enum.each(fn {symbol, amount} ->
      case JhandiMundaGameLogic.validate_bet(symbol, amount) do
        :ok ->
          Logger.info("🎲   ✅ 符号#{symbol} #{amount} - 有效")

        {:error, reason} ->
          Logger.info("🎲   ❌ 符号#{symbol} #{amount} - 错误: #{reason}")
      end
    end)
  end

  @doc """
  演示房间盈亏计算
  """
  def demo_room_profit do
    Logger.info("🎲 [JHANDI_MUNDA_DEMO] === 房间盈亏演示 ===")

    # 模拟多个玩家下注
    all_player_bets = %{
      "player1" => %{
        JhandiMundaConstants.symbol_clubs() => 100,
        JhandiMundaConstants.symbol_crown() => 200
      },
      "player2" => %{
        JhandiMundaConstants.symbol_spades() => 150,
        JhandiMundaConstants.symbol_diamonds() => 100
      },
      "player3" => %{
        JhandiMundaConstants.symbol_flag() => 300,
        JhandiMundaConstants.symbol_hearts() => 50
      }
    }

    # 生成游戏结果
    game_result = JhandiMundaGameLogic.roll_dice()

    Logger.info("🎲 游戏结果: #{inspect(game_result.dice_results)}")
    Logger.info("🎲 符号统计: #{inspect(game_result.symbol_counts)}")

    # 计算房间盈亏
    room_profit = JhandiMundaGameLogic.calculate_room_profit(all_player_bets, game_result)

    Logger.info("🎲 房间盈亏:")
    Logger.info("🎲   总下注: #{room_profit.total_bets}")
    Logger.info("🎲   总中奖: #{room_profit.total_wins}")
    Logger.info("🎲   庄家盈利: #{room_profit.house_profit}")

    Logger.info("🎲 玩家结果:")

    room_profit.player_results
    |> Enum.each(fn {player_id, result} ->
      Logger.info(
        "🎲   #{player_id}: 下注#{result.total_bet}, 中奖#{result.total_win}, 净盈亏#{result.net_win}"
      )
    end)
  end

  @doc """
  健康检查
  """
  def health_check do
    Logger.info("🎲 [JHANDI_MUNDA_DEMO] === 健康检查 ===")

    try do
      # 检查游戏工厂
      game_health = JhandiMundaGame.health_check()
      Logger.info("🎲 游戏模块健康状态: #{inspect(game_health)}")

      # 检查常量定义
      config = JhandiMundaConstants.get_game_config()
      Logger.info("🎲 配置检查: ✅ 所有常量正常")

      # 检查游戏逻辑
      _result = JhandiMundaGameLogic.roll_dice()
      Logger.info("🎲 游戏逻辑检查: ✅ 骰子投掷正常")

      # 检查历史记录
      _history = JhandiMundaHistory.init_state()
      Logger.info("🎲 历史记录检查: ✅ 初始化正常")

      Logger.info("🎲 ✅ 所有组件健康检查通过")
      :ok
    rescue
      error ->
        Logger.error("🎲 ❌ 健康检查失败: #{inspect(error)}")
        {:error, error}
    end
  end
end
