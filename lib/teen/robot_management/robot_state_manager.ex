defmodule Teen.RobotManagement.RobotStateManager do
  @moduledoc """
  机器人状态管理服务 - 简化版本

  状态说明：
  - 0: 空闲状态
  - 1: 游戏中状态
  - 2: 回收中状态（管理员回收，但仍在游戏中）
  - 3: 积分不足状态
  """

  require Logger
  alias Teen.RobotManagement.RobotEntity

  @doc """
  管理员回收机器人
  """
  def recycle_robot_by_admin(robot_id, admin_id, force \\ false) do
    Logger.info("🤖 [ADMIN_RECYCLE] 管理员 #{admin_id} 尝试回收机器人 #{robot_id}, 强制: #{force}")

    with {:ok, robot} <- get_robot(robot_id) do
      case robot.status do
        # 空闲状态，直接禁用
        0 ->
          Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 空闲状态，直接禁用")

          case Ash.update!(robot, %{}, action: :disable_robot) do
            updated_robot ->
              Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 被直接禁用")
              {:ok, updated_robot}
          end

        # 游戏中状态，标记为回收中
        1 ->
          Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 在游戏中，标记为回收中状态")

          case Ash.update!(robot, %{admin_id: admin_id}, action: :start_recycling) do
            updated_robot ->
              # 通知房间机器人即将被回收
              if robot.current_room_id do
                notify_room_robot_recycling(robot.current_room_id, robot_id, "后台回收中")
              end

              Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 标记为回收中，等待游戏结束")
              {:ok, updated_robot}
          end

        # 已在回收中
        2 ->
          Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 已在回收中")
          {:ok, robot}

        # 积分不足状态，直接禁用
        3 ->
          Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 积分不足状态，直接禁用")

          case Ash.update!(robot, %{}, action: :disable_robot) do
            updated_robot ->
              Logger.info("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 被直接禁用")
              {:ok, updated_robot}
          end

        # 其他状态
        status ->
          Logger.warning("🤖 [ADMIN_RECYCLE] 机器人 #{robot_id} 未知状态: #{status}")
          {:error, :unknown_status}
      end
    end
  end

  @doc """
  完成机器人回收 - 由游戏房间调用
  当游戏结束时，将回收中(2)的机器人变为空闲(0)并禁用
  """
  def complete_robot_recycling(robot_id, reason \\ "游戏结束自动回收") do
    Logger.info("🤖 [COMPLETE_RECYCLE] 完成机器人 #{robot_id} 的回收流程")

    with {:ok, robot} <- get_robot(robot_id) do
      if robot.status == 2 do
        # 先变回空闲状态，然后禁用
        case Ash.update!(robot, %{}, action: :complete_recycling) do
          updated_robot ->
            case Ash.update!(updated_robot, %{}, action: :disable_robot) do
              final_robot ->
                Logger.info("🤖 [COMPLETE_RECYCLE] 机器人 #{robot_id} 回收完成并已禁用")
                {:ok, final_robot}
            end
        end
      else
        Logger.debug("🤖 [COMPLETE_RECYCLE] 机器人 #{robot_id} 不在回收中状态(#{robot.status})，跳过")
        {:ok, robot}
      end
    end
  end

  @doc """
  检查并处理积分不足的机器人
  """
  def check_insufficient_funds(robot_id, required_amount) do
    Logger.info("🤖 [FUNDS_CHECK] 检查机器人 #{robot_id} 积分，需要: #{required_amount}")

    with {:ok, robot} <- get_robot(robot_id) do
      if robot.current_points < required_amount do
        Logger.warning(
          "🤖 [FUNDS_CHECK] 机器人 #{robot_id} 积分不足: #{robot.current_points} < #{required_amount}"
        )

        # 标记为积分不足
        updated_robot = Ash.update!(robot, %{}, action: :mark_insufficient_funds)

        {:error, :insufficient_funds, updated_robot}
      else
        {:ok, :sufficient_funds}
      end
    end
  end

  @doc """
  更新机器人状态 - 兼容性函数
  """
  def update_robot_status(robot_id, status_atom, context \\ %{}) do
    Logger.info("🤖 [UPDATE_STATUS] 更新机器人 #{robot_id} 状态为 #{status_atom}")

    # 转换atom状态为数字状态
    numeric_status =
      case status_atom do
        :idle ->
          0

        :in_game ->
          1

        :recycling ->
          2

        :insufficient_funds ->
          3

        # 兼容旧的状态
        :available ->
          0

        :busy ->
          1

        _ ->
          Logger.warning("🤖 [UPDATE_STATUS] 未知状态: #{status_atom}，默认为空闲")
          0
      end

    case numeric_status do
      1 ->
        # 进入游戏状态
        game_type = Map.get(context, :current_game_type, "unknown")
        room_id = Map.get(context, :current_room_id)
        seat_number = Map.get(context, :seat_number)
        robot_enter_game(robot_id, game_type, room_id, seat_number)

      0 ->
        # 空闲状态
        robot_exit_game(robot_id)

      2 ->
        # 回收状态
        admin_id = Map.get(context, :admin_id, "system")
        recycle_robot_by_admin(robot_id, admin_id)

      3 ->
        # 积分不足状态
        with {:ok, robot} <- get_robot(robot_id) do
          updated_robot = Ash.update!(robot, %{}, action: :mark_insufficient_funds)
          {:ok, updated_robot}
        end

      _ ->
        Logger.warning("🤖 [UPDATE_STATUS] 无效状态: #{numeric_status}")
        {:error, :invalid_status}
    end
  end

  @doc """
  机器人进入游戏
  """
  def robot_enter_game(robot_id, game_type, room_id, seat_number \\ nil) do
    Logger.info("🤖 [ENTER_GAME] 机器人 #{robot_id} 进入游戏 #{game_type}，房间 #{room_id}")

    with {:ok, robot} <- get_robot(robot_id) do
      if robot.status == 0 and robot.is_enabled do
        case Ash.update!(
               robot,
               %{game_type: game_type, room_id: room_id, seat_number: seat_number},
               action: :enter_game
             ) do
          updated_robot ->
            Logger.info("🤖 [ENTER_GAME] 机器人 #{robot_id} 成功进入游戏")
            {:ok, updated_robot}
        end
      else
        Logger.warning(
          "🤖 [ENTER_GAME] 机器人 #{robot_id} 状态不正确或已禁用: 状态#{robot.status}, 启用#{robot.is_enabled}"
        )

        {:error, :invalid_status}
      end
    end
  end

  @doc """
  机器人退出游戏
  """
  def robot_exit_game(robot_id) do
    Logger.info("🤖 [EXIT_GAME] 机器人 #{robot_id} 退出游戏")

    with {:ok, robot} <- get_robot(robot_id) do
      case robot.status do
        1 ->
          # 游戏中，正常退出
          case Ash.update!(robot, %{}, action: :exit_game) do
            updated_robot ->
              Logger.info("🤖 [EXIT_GAME] 机器人 #{robot_id} 成功退出游戏")
              {:ok, updated_robot}
          end

        2 ->
          # 回收中，完成回收流程
          complete_robot_recycling(robot_id, "游戏结束回收")

        _ ->
          Logger.debug("🤖 [EXIT_GAME] 机器人 #{robot_id} 不在游戏中，状态: #{robot.status}")
          {:ok, robot}
      end
    end
  end

  @doc """
  更新机器人积分
  """
  def update_robot_points(robot_id, new_points, bet_amount \\ 0) do
    Logger.info("🤖 [POINTS_UPDATE] 更新机器人 #{robot_id} 积分: #{new_points}")

    with {:ok, robot} <- get_robot(robot_id) do
      updated_robot =
        Ash.update!(robot, %{points: new_points, bet_amount: bet_amount}, action: :update_points)

      # 检查积分是否足够
      if new_points < robot.min_points_threshold do
        Logger.warning(
          "🤖 [POINTS_UPDATE] 机器人 #{robot_id} 积分低于阈值: #{new_points} < #{robot.min_points_threshold}"
        )

        # 标记为积分不足
        Ash.update!(updated_robot, %{}, action: :mark_insufficient_funds)
      end

      {:ok, updated_robot}
    end
  end

  @doc """
  启用机器人
  """
  def enable_robot(robot_id) do
    Logger.info("🤖 [ENABLE] 启用机器人 #{robot_id}")

    with {:ok, robot} <- get_robot(robot_id) do
      case Ash.update!(robot, %{}, action: :enable_robot) do
        updated_robot ->
          Logger.info("🤖 [ENABLE] 机器人 #{robot_id} 已启用")
          {:ok, updated_robot}
      end
    end
  end

  @doc """
  禁用机器人
  """
  def disable_robot(robot_id) do
    Logger.info("🤖 [DISABLE] 禁用机器人 #{robot_id}")

    with {:ok, robot} <- get_robot(robot_id) do
      case Ash.update!(robot, %{}, action: :disable_robot) do
        updated_robot ->
          Logger.info("🤖 [DISABLE] 机器人 #{robot_id} 已禁用")
          {:ok, updated_robot}
      end
    end
  end

  @doc """
  清理房间关闭时的机器人状态
  """
  def cleanup_robots_on_room_close(room_id) do
    Logger.info("🤖 [ROOM_CLOSE] 清理房间 #{room_id} 的机器人状态")

    # 查找在该房间的所有机器人
    room_robots =
      Ash.read!(RobotEntity)
      |> Enum.filter(fn robot -> robot.current_room_id == room_id end)

    Logger.info("🤖 [ROOM_CLOSE] 找到 #{length(room_robots)} 个在房间 #{room_id} 的机器人")

    # 处理每个机器人
    Enum.each(room_robots, fn robot ->
      case robot.status do
        1 ->
          # 游戏中，退出游戏
          Logger.info("🤖 [ROOM_CLOSE] 机器人 #{robot.robot_id} 退出游戏")
          Ash.update!(robot, %{}, action: :exit_game)

        2 ->
          # 回收中，完成回收
          Logger.info("🤖 [ROOM_CLOSE] 机器人 #{robot.robot_id} 完成回收")
          complete_robot_recycling(robot.robot_id, "房间关闭回收")

        _ ->
          # 其他状态，重置为空闲
          Logger.info("🤖 [ROOM_CLOSE] 机器人 #{robot.robot_id} 重置为空闲")
          Ash.update!(robot, %{reason: "房间关闭重置"}, action: :reset_to_idle)
      end
    end)

    Logger.info("🤖 [ROOM_CLOSE] 房间 #{room_id} 机器人状态清理完成")
    {:ok, length(room_robots)}
  end

  @doc """
  检查游戏开始时的机器人状态，踢出回收中和积分不足的机器人
  """
  def check_robots_on_game_start(room_id) do
    Logger.info("🤖 [GAME_START_CHECK] 检查房间 #{room_id} 的机器人状态")

    # 查找在该房间的所有机器人
    room_robots =
      Ash.read!(RobotEntity)
      |> Enum.filter(fn robot -> robot.current_room_id == room_id end)

    # 找出需要踢出的机器人 (状态2和3)
    robots_to_kick = Enum.filter(room_robots, fn robot -> robot.status in [2, 3] end)

    if length(robots_to_kick) > 0 do
      Logger.info("🤖 [GAME_START_CHECK] 发现 #{length(robots_to_kick)} 个需要踢出的机器人")

      # 踢出这些机器人
      kicked_robots =
        Enum.map(robots_to_kick, fn robot ->
          reason =
            case robot.status do
              2 -> "回收中"
              3 -> "积分不足"
            end

          Logger.info("🤖 [GAME_START_CHECK] 踢出机器人 #{robot.robot_id}，原因: #{reason}")

          case robot.status do
            2 ->
              # 回收中，完成回收
              complete_robot_recycling(robot.robot_id, "游戏开始检查回收")

            3 ->
              # 积分不足，退出游戏
              robot_exit_game(robot.robot_id)
          end

          robot.robot_id
        end)

      {:ok, kicked_robots}
    else
      Logger.debug("🤖 [GAME_START_CHECK] 房间 #{room_id} 所有机器人状态正常")
      {:ok, []}
    end
  end

  @doc """
  初始化机器人管理 - 服务启动时重置所有机器人状态
  """
  def initialize_robot_management do
    Logger.info("🤖 [INIT] 开始初始化机器人管理系统")

    # 获取所有机器人
    all_robots = Ash.read!(RobotEntity)

    Logger.info("🤖 [INIT] 找到 #{length(all_robots)} 个机器人，开始重置状态")

    # 重置所有机器人状态为空闲
    reset_count =
      Enum.reduce(all_robots, 0, fn robot, count ->
        case robot.status do
          0 ->
            # 已经是空闲状态，跳过
            count

          _ ->
            # 重置为空闲状态
            Logger.debug("🤖 [INIT] 重置机器人 #{robot.robot_id} 状态: #{robot.status} -> 0")
            Ash.update!(robot, %{reason: "系统启动重置"}, action: :reset_to_idle)
            count + 1
        end
      end)

    Logger.info("🤖 [INIT] 机器人管理系统初始化完成，重置了 #{reset_count} 个机器人")
    {:ok, %{total: length(all_robots), reset: reset_count}}
  end

  @doc """
  获取机器人状态统计
  """
  def get_robot_status_stats do
    robots = Ash.read!(RobotEntity)

    stats = %{
      total: length(robots),
      idle: Enum.count(robots, &(&1.status == 0 and &1.is_enabled)),
      in_game: Enum.count(robots, &(&1.status == 1 and &1.is_enabled)),
      recycling: Enum.count(robots, &(&1.status == 2 and &1.is_enabled)),
      insufficient_funds: Enum.count(robots, &(&1.status == 3 and &1.is_enabled)),
      enabled: Enum.count(robots, & &1.is_enabled),
      disabled: Enum.count(robots, &(not &1.is_enabled))
    }

    Logger.debug("🤖 [STATS] 机器人状态统计: #{inspect(stats)}")
    stats
  end

  @doc """
  批量启用机器人
  """
  def batch_enable_robots(robot_ids) when is_list(robot_ids) do
    Logger.info("🤖 [BATCH_ENABLE] 批量启用 #{length(robot_ids)} 个机器人")

    results =
      Enum.map(robot_ids, fn robot_id ->
        case enable_robot(robot_id) do
          {:ok, robot} -> {:ok, robot.robot_id}
          {:error, reason} -> {:error, robot_id, reason}
        end
      end)

    success_count =
      Enum.count(results, fn
        {:ok, _} -> true
        {:error, _, _} -> false
      end)

    Logger.info("🤖 [BATCH_ENABLE] 批量启用完成，成功 #{success_count} 个")

    {:ok, %{total: length(robot_ids), success: success_count, results: results}}
  end

  @doc """
  批量禁用机器人
  """
  def batch_disable_robots(robot_ids) when is_list(robot_ids) do
    Logger.info("🤖 [BATCH_DISABLE] 批量禁用 #{length(robot_ids)} 个机器人")

    results =
      Enum.map(robot_ids, fn robot_id ->
        case disable_robot(robot_id) do
          {:ok, robot} -> {:ok, robot.robot_id}
          {:error, reason} -> {:error, robot_id, reason}
        end
      end)

    success_count =
      Enum.count(results, fn
        {:ok, _} -> true
        {:error, _, _} -> false
      end)

    Logger.info("🤖 [BATCH_DISABLE] 批量禁用完成，成功 #{success_count} 个")

    {:ok, %{total: length(robot_ids), success: success_count, results: results}}
  end

  @doc """
  启用所有机器人
  """
  def enable_all_robots do
    Logger.info("🤖 [ENABLE_ALL] 启用所有机器人")

    all_robots = Ash.read!(RobotEntity)
    robot_ids = Enum.map(all_robots, & &1.robot_id)

    batch_enable_robots(robot_ids)
  end

  @doc """
  清理指定游戏类型的所有机器人 - 管理后台使用
  """
  def cleanup_robots_by_game(game_type) do
    Logger.info("🤖 [CLEANUP_BY_GAME] 清理游戏类型 #{game_type} 的所有机器人")

    # 查找该游戏类型的所有机器人
    game_robots =
      Ash.read!(RobotEntity)
      |> Enum.filter(fn robot -> robot.current_game_type == game_type end)

    if length(game_robots) > 0 do
      Logger.info("🤖 [CLEANUP_BY_GAME] 找到 #{length(game_robots)} 个在游戏 #{game_type} 中的机器人")

      # 处理每个机器人
      results =
        Enum.map(game_robots, fn robot ->
          case robot.status do
            1 ->
              # 游戏中，退出游戏
              Logger.info("🤖 [CLEANUP_BY_GAME] 机器人 #{robot.robot_id} 退出游戏 #{game_type}")

              case robot_exit_game(robot.robot_id) do
                {:ok, _} -> {:ok, robot.robot_id}
                error -> {:error, robot.robot_id, error}
              end

            2 ->
              # 回收中，完成回收
              Logger.info("🤖 [CLEANUP_BY_GAME] 机器人 #{robot.robot_id} 完成回收")

              case complete_robot_recycling(robot.robot_id, "管理员清理游戏") do
                {:ok, _} -> {:ok, robot.robot_id}
                error -> {:error, robot.robot_id, error}
              end

            _ ->
              # 其他状态，重置为空闲
              Logger.info("🤖 [CLEANUP_BY_GAME] 机器人 #{robot.robot_id} 重置为空闲")

              case Ash.update(robot, %{reason: "管理员清理游戏"}, action: :reset_to_idle) do
                {:ok, _} -> {:ok, robot.robot_id}
                error -> {:error, robot.robot_id, error}
              end
          end
        end)

      success_count =
        Enum.count(results, fn
          {:ok, _} -> true
          {:error, _, _} -> false
        end)

      Logger.info("🤖 [CLEANUP_BY_GAME] 游戏 #{game_type} 机器人清理完成，成功 #{success_count} 个")
      {:ok, %{total: length(game_robots), success: success_count, results: results}}
    else
      Logger.info("🤖 [CLEANUP_BY_GAME] 游戏 #{game_type} 中没有机器人")
      {:ok, %{total: 0, success: 0, results: []}}
    end
  end

  @doc """
  清理所有游戏中的机器人 - 管理后台使用
  """
  def cleanup_all_game_robots do
    Logger.info("🤖 [CLEANUP_ALL_GAMES] 清理所有游戏中的机器人")

    # 查找所有游戏中或回收中的机器人
    active_robots =
      Ash.read!(RobotEntity)
      |> Enum.filter(fn robot -> robot.status in [1, 2] end)

    if length(active_robots) > 0 do
      Logger.info("🤖 [CLEANUP_ALL_GAMES] 找到 #{length(active_robots)} 个活跃机器人")

      # 处理每个机器人
      results =
        Enum.map(active_robots, fn robot ->
          case robot.status do
            1 ->
              # 游戏中，退出游戏
              Logger.info("🤖 [CLEANUP_ALL_GAMES] 机器人 #{robot.robot_id} 退出游戏")

              case robot_exit_game(robot.robot_id) do
                {:ok, _} -> {:ok, robot.robot_id}
                error -> {:error, robot.robot_id, error}
              end

            2 ->
              # 回收中，完成回收
              Logger.info("🤖 [CLEANUP_ALL_GAMES] 机器人 #{robot.robot_id} 完成回收")

              case complete_robot_recycling(robot.robot_id, "管理员清理所有游戏") do
                {:ok, _} -> {:ok, robot.robot_id}
                error -> {:error, robot.robot_id, error}
              end
          end
        end)

      success_count =
        Enum.count(results, fn
          {:ok, _} -> true
          {:error, _, _} -> false
        end)

      Logger.info("🤖 [CLEANUP_ALL_GAMES] 所有游戏机器人清理完成，成功 #{success_count} 个")
      {:ok, %{total: length(active_robots), success: success_count, results: results}}
    else
      Logger.info("🤖 [CLEANUP_ALL_GAMES] 没有活跃的机器人")
      {:ok, %{total: 0, success: 0, results: []}}
    end
  end

  @doc """
  删除机器人
  """
  def delete_robot(robot_id) do
    Logger.info("🤖 [DELETE] 删除机器人 #{robot_id}")

    with {:ok, robot} <- get_robot(robot_id) do
      case Ash.destroy(robot) do
        :ok ->
          Logger.info("🤖 [DELETE] 机器人 #{robot_id} 已删除")
          {:ok, robot_id}

        error ->
          Logger.error("🤖 [DELETE] 删除机器人 #{robot_id} 失败: #{inspect(error)}")
          {:error, :delete_failed}
      end
    end
  end

  @doc """
  批量删除机器人
  """
  def batch_delete_robots(robot_ids) when is_list(robot_ids) do
    Logger.info("🤖 [BATCH_DELETE] 批量删除 #{length(robot_ids)} 个机器人")

    results =
      Enum.map(robot_ids, fn robot_id ->
        case delete_robot(robot_id) do
          {:ok, robot_id} -> {:ok, robot_id}
          {:error, reason} -> {:error, robot_id, reason}
        end
      end)

    success_count =
      Enum.count(results, fn
        {:ok, _} -> true
        {:error, _, _} -> false
      end)

    Logger.info("🤖 [BATCH_DELETE] 批量删除完成，成功 #{success_count} 个")

    {:ok, %{total: length(robot_ids), success: success_count, results: results}}
  end

  # 兼容性函数 - 保持与游戏的兼容性
  def robot_enter_round(robot_id) do
    Logger.debug("🤖 [COMPAT] robot_enter_round #{robot_id} - 在新系统中游戏中状态已包含回合状态")
    {:ok, nil}
  end

  def robot_exit_round(robot_id) do
    Logger.debug("🤖 [COMPAT] robot_exit_round #{robot_id} - 在新系统中游戏中状态已包含回合状态")
    {:ok, nil}
  end

  # ==================== 私有函数 ====================

  defp get_robot(robot_id) do
    case RobotEntity
         |> Ash.Query.for_read(:by_robot_id, %{robot_id: robot_id})
         |> Ash.read_one() do
      {:ok, robot} when not is_nil(robot) ->
        {:ok, robot}

      {:ok, nil} ->
        {:error, :robot_not_found}

      {:error, %Ash.Error.Query.NotFound{}} ->
        {:error, :robot_not_found}

      {:error, reason} ->
        Logger.error("🤖 [GET_ROBOT] 查询机器人 #{robot_id} 失败: #{inspect(reason)}")
        {:error, :robot_not_found}
    end
  rescue
    error ->
      Logger.error("🤖 [GET_ROBOT] 查询机器人 #{robot_id} 异常: #{inspect(error)}")
      {:error, :robot_not_found}
  end

  defp notify_room_robot_recycling(room_id, robot_id, reason) do
    # 通知房间进程机器人正在被回收，需要立即踢出
    Logger.info("🤖 [NOTIFY] 通知房间 #{room_id} 机器人 #{robot_id} 正在回收中, 原因: #{reason}")

    try do
      # 发送消息到房间进程，要求立即踢出机器人
      case Cypridina.RoomSystem.RoomManager.get_room_info(room_id) do
        {:ok, room_info} ->
          # 获取房间PID并发送踢出机器人的消息
          room_pid = Map.get(room_info, :pid)

          if room_pid && Process.alive?(room_pid) do
            send(room_pid, {:kick_robot, robot_id, reason})
            Logger.info("🤖 [NOTIFY] 已向房间 #{room_id} 发送踢出机器人 #{robot_id} 的消息")
          else
            Logger.warning("🤖 [NOTIFY] 房间 #{room_id} 进程不存在或已死亡")
          end

        {:error, reason} ->
          Logger.warning("🤖 [NOTIFY] 无法获取房间 #{room_id} 信息: #{inspect(reason)}")
      end
    rescue
      error ->
        Logger.error("🤖 [NOTIFY] 通知房间时出错: #{inspect(error)}")
    end
  end

  @doc """
  服务启动时初始化机器人状态
  重置所有机器人到空闲状态，清理异常状态
  """
  def initialize_robot_management do
    Logger.info("🤖 [STARTUP] 开始初始化机器人管理系统")

    try do
      # 查询所有机器人
      robots = Ash.read!(RobotEntity)

      reset_count = 0
      error_count = 0

      {reset_count, error_count} =
        Enum.reduce(robots, {0, 0}, fn robot, {reset_acc, error_acc} ->
          case robot.status do
            # 状态1(游戏中)和状态2(回收中)需要重置为空闲
            status when status in [1, 2] ->
              Logger.info("🤖 [STARTUP] 重置机器人 #{robot.robot_id} 从状态 #{status} 到空闲状态")

              case Ash.update(robot, %{reason: "服务启动重置"}, action: :reset_to_idle) do
                {:ok, _} ->
                  {reset_acc + 1, error_acc}

                {:error, error} ->
                  Logger.error("🤖 [STARTUP] 重置机器人 #{robot.robot_id} 失败: #{inspect(error)}")
                  {reset_acc, error_acc + 1}
              end

            # 状态0(空闲)和状态3(积分不足)保持不变
            _ ->
              {reset_acc, error_acc}
          end
        end)

      Logger.info(
        "🤖 [STARTUP] 机器人状态初始化完成 - 总数: #{length(robots)}, 重置: #{reset_count}, 错误: #{error_count}"
      )

      {:ok, %{total: length(robots), reset: reset_count, errors: error_count}}
    rescue
      error ->
        Logger.error("🤖 [STARTUP] 初始化机器人状态时发生异常: #{inspect(error)}")
        {:error, error}
    end
  end
end
