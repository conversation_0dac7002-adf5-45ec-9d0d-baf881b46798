defmodule Teen.RobotManagement.SimpleRobotProvider do
  @moduledoc """
  简化机器人提供服务
  """

  require Logger
  import Ash.Query
  alias Teen.RobotManagement.RobotEntity
  alias Cypridina.Teen.GameSystem.PlayerDataBuilder

  # 默认的性格分布配置（参考JhandiMunda的配置）
  @default_personality_distribution %{
    "conservative" => 0.25,
    "balanced" => 0.30,
    "aggressive" => 0.20,
    "trend_follower" => 0.15,
    "contrarian" => 0.10
  }

  # 各游戏的默认配置
  @game_default_configs %{
    "jhandi_munda" => %{
      "min_robots" => 3,
      "max_robots" => 6,
      "personality_distribution" => @default_personality_distribution,
      "behavior_defaults" => %{
        "bet_probability" => 0.95,
        "multi_bet_probability" => 0.80,
        "follow_trend_probability" => 0.60,
        "think_time_min" => 1000,
        "think_time_max" => 9000
      }
    },
    "teen_patti" => %{
      "min_robots" => 2,
      "max_robots" => 5,
      "personality_distribution" => %{
        "aggressive" => 0.30,
        "conservative" => 0.25,
        "balanced" => 0.25,
        "bluffer" => 0.20
      },
      "behavior_defaults" => %{
        "fold_probability" => 0.3,
        "raise_probability" => 0.4,
        "bluff_frequency" => 0.15,
        "think_time_min" => 2000,
        "think_time_max" => 8000
      }
    },
    "pot_blind" => %{
      "min_robots" => 2,
      "max_robots" => 6,
      "personality_distribution" => %{
        "aggressive" => 0.25,
        "conservative" => 0.20,
        "balanced" => 0.35,
        "tight" => 0.20
      },
      "behavior_defaults" => %{
        "bluff_frequency" => 0.2,
        "competition_rate" => 0.4
      }
    }
  }

  @doc """
  为游戏获取机器人
  """
  def get_robots_for_game(game_type, room_id, count, opts \\ []) do
    Logger.info("🤖 [SIMPLE_PROVIDER] 为游戏 #{game_type} 获取 #{count} 个机器人")

    with {:ok, available_robots} <- get_available_robots(count),
         needed_count <- max(0, count - length(available_robots)),
         new_robots <-
           if(needed_count > 0, do: create_robots_for_game(game_type, needed_count), else: []),
         all_robots <- available_robots ++ new_robots,
         {:ok, assigned_robots} <- assign_robots_to_game(all_robots, game_type, room_id) do
      # 转换为PlayerData格式
      robot_players = Enum.map(assigned_robots, &convert_to_player_data(&1, game_type))

      Logger.info("🤖 [SIMPLE_PROVIDER] 成功获取 #{length(robot_players)} 个机器人")
      {:ok, robot_players}
    else
      error ->
        Logger.error("🤖 [SIMPLE_PROVIDER] 获取机器人失败: #{inspect(error)}")
        error
    end
  end

  @doc """
  手动创建机器人
  """
  def create_robot_manually(params) do
    Logger.info("🤖 [SIMPLE_PROVIDER] 手动创建机器人")

    # 确保 nickname 不为空
    nickname =
      case Map.get(params, "nickname") do
        nil -> generate_robot_nickname()
        "" -> generate_robot_nickname()
        name when is_binary(name) -> String.trim(name)
        _ -> generate_robot_nickname()
      end

    # 如果 trim 后为空，也使用生成的名称
    nickname = if String.length(nickname) == 0, do: generate_robot_nickname(), else: nickname

    robot_data = %{
      robot_id: generate_robot_id(),
      nickname: nickname,
      avatar_id: Map.get(params, "avatar_id", :rand.uniform(20)),
      level: Map.get(params, "level", :rand.uniform(10)),
      current_points: Map.get(params, "initial_points", 100_000),
      robot_config: build_robot_config(params),
      is_auto_created: false,
      creator_admin_id: Map.get(params, "admin_id"),
      tags: Map.get(params, "tags", []),
      status: 0,
      status_changed_at: DateTime.utc_now(),
      last_activity_at: DateTime.utc_now()
    }

    case Ash.create(RobotEntity, robot_data) do
      {:ok, robot} ->
        Logger.info("🤖 [SIMPLE_PROVIDER] 手动创建机器人成功: #{robot.robot_id}")
        {:ok, robot}

      error ->
        Logger.error("🤖 [SIMPLE_PROVIDER] 手动创建机器人失败: #{inspect(error)}")
        error
    end
  end

  @doc """
  批量创建机器人
  """
  def batch_create_robots(game_type, count, custom_distribution \\ nil) do
    Logger.info("🤖 [SIMPLE_PROVIDER] 批量创建 #{count} 个机器人")

    distribution = custom_distribution || get_game_personality_distribution(game_type)

    robots =
      1..count
      |> Enum.map(fn index ->
        personality = select_personality_by_distribution(distribution, index)
        create_single_robot(game_type, personality)
      end)
      |> Enum.filter(&(not is_nil(&1)))

    Logger.info("🤖 [SIMPLE_PROVIDER] 批量创建成功: #{length(robots)} 个机器人")
    {:ok, robots}
  end

  @doc """
  释放机器人
  """
  def release_robots(robot_ids) when is_list(robot_ids) do
    Logger.info("🤖 [SIMPLE_PROVIDER] 释放 #{length(robot_ids)} 个机器人")

    Enum.each(robot_ids, fn robot_id ->
      # Use Ash.Query to find robot by robot_id
      query =
        RobotEntity
        |> Ash.Query.filter(robot_id == ^robot_id)
        |> Ash.Query.limit(1)

      case Ash.read_one(query) do
        {:ok, robot} ->
          Ash.update!(robot, %{reason: "释放到空闲"}, action: :reset_to_idle)
          Logger.debug("🤖 [SIMPLE_PROVIDER] 机器人 #{robot_id} 已释放")

        {:error, %Ash.Error.Query.NotFound{}} ->
          Logger.warning("🤖 [SIMPLE_PROVIDER] 机器人 #{robot_id} 不存在")

        {:error, error} ->
          Logger.warning("🤖 [SIMPLE_PROVIDER] 机器人 #{robot_id} 查询失败: #{inspect(error)}")
      end
    end)

    :ok
  end

  def release_robots(robot_id) when is_integer(robot_id) do
    release_robots([robot_id])
  end

  # ==================== 私有函数 ====================

  # 构建机器人配置（JSON对象格式）
  defp build_robot_config(params) do
    game_type = Map.get(params, "game_type", "general")
    personality = Map.get(params, "personality_type", "balanced")

    # 获取游戏默认配置
    game_config = Map.get(@game_default_configs, game_type, %{})
    behavior_defaults = Map.get(game_config, "behavior_defaults", %{})

    %{
      "personality_type" => personality,
      "behavior_params" => generate_behavior_params(personality, behavior_defaults),
      "game_configs" => generate_game_specific_configs(game_type, personality),
      "created_at" => DateTime.utc_now() |> DateTime.to_iso8601(),
      "version" => "1.0"
    }
  end

  # 生成行为参数
  defp generate_behavior_params(personality, defaults \\ %{}) do
    base_params =
      case personality do
        "aggressive" ->
          %{
            "aggression_level" => 0.8,
            "risk_tolerance" => 0.8,
            "patience_level" => 0.3,
            "confidence_level" => 0.9
          }

        "conservative" ->
          %{
            "aggression_level" => 0.3,
            "risk_tolerance" => 0.4,
            "patience_level" => 0.8,
            "confidence_level" => 0.4
          }

        "trend_follower" ->
          %{
            "aggression_level" => 0.5,
            "risk_tolerance" => 0.6,
            "patience_level" => 0.7,
            "confidence_level" => 0.6,
            "trend_sensitivity" => 0.8
          }

        "contrarian" ->
          %{
            "aggression_level" => 0.6,
            "risk_tolerance" => 0.7,
            "patience_level" => 0.4,
            "confidence_level" => 0.7,
            "contrarian_tendency" => 0.8
          }

        # balanced
        _ ->
          %{
            "aggression_level" => 0.5,
            "risk_tolerance" => 0.5,
            "patience_level" => 0.6,
            "confidence_level" => 0.6
          }
      end

    # 合并默认值和添加随机变化
    Map.merge(base_params, defaults)
    |> add_random_variance()
  end

  # 生成游戏特定配置
  defp generate_game_specific_configs(game_type, personality) do
    case game_type do
      "jhandi_munda" ->
        %{
          "favorite_symbols" => generate_favorite_symbols(personality),
          "bet_patterns" => map_personality_to_bet_pattern(personality),
          "multi_bet_probability" =>
            case personality do
              "aggressive" -> 0.9
              "conservative" -> 0.5
              "trend_follower" -> 0.8
              _ -> 0.7
            end,
          "symbol_bias" => generate_symbol_bias(personality),
          "hedge_betting" => personality in ["conservative", "balanced"],
          "pattern_recognition" =>
            case personality do
              "trend_follower" -> 0.9
              "contrarian" -> 0.8
              "conservative" -> 0.7
              _ -> 0.6
            end
        }

      "teen_patti" ->
        %{
          "play_style" =>
            case personality do
              "aggressive" -> "aggressive"
              "conservative" -> "tight"
              "bluffer" -> "loose_aggressive"
              _ -> "balanced"
            end,
          "bluff_frequency" =>
            case personality do
              "bluffer" -> 0.4
              "aggressive" -> 0.25
              "conservative" -> 0.05
              _ -> 0.15
            end,
          "fold_threshold" =>
            case personality do
              "aggressive" -> 0.15
              "conservative" -> 0.7
              "bluffer" -> 0.3
              _ -> 0.45
            end,
          "raise_aggression" =>
            case personality do
              "aggressive" -> 0.8
              "bluffer" -> 0.6
              "conservative" -> 0.2
              _ -> 0.4
            end
        }

      "pot_blind" ->
        %{
          "bluff_frequency" =>
            case personality do
              "aggressive" -> 0.3
              "conservative" -> 0.1
              _ -> 0.2
            end,
          "fold_threshold" =>
            case personality do
              "aggressive" -> 0.2
              "conservative" -> 0.6
              _ -> 0.4
            end,
          "competition_bonus" =>
            case personality do
              "aggressive" -> 0.25
              _ -> 0.15
            end
        }

      _ ->
        %{"general_config" => true}
    end
  end

  # 创建单个机器人
  defp create_single_robot(game_type, personality) do
    robot_data = %{
      robot_id: generate_robot_id(),
      nickname: generate_robot_nickname(),
      avatar_id: RobotEntity.random_robot_avatar(),
      level: :rand.uniform(10),
      current_points: generate_initial_points(),
      robot_config:
        build_robot_config(%{
          "game_type" => game_type,
          "personality_type" => personality
        }),
      is_auto_created: true,
      tags: [game_type, "auto_created"],
      status: 0,
      status_changed_at: DateTime.utc_now(),
      last_activity_at: DateTime.utc_now()
    }

    case Ash.create(RobotEntity, robot_data) do
      {:ok, robot} -> robot
      _ -> nil
    end
  end

  # 转换为PlayerData格式
  defp convert_to_player_data(robot_entity, game_type) do
    # 获取游戏特定配置
    game_config = get_in(robot_entity.robot_config, ["game_configs", game_type]) || %{}

    # 构建用户数据
    user_data = %{
      numeric_id: robot_entity.robot_id,
      id: robot_entity.robot_id,
      nickname: robot_entity.nickname,
      avatar_id: robot_entity.avatar_id,
      level: robot_entity.level,
      points: robot_entity.current_points,
      is_robot: true,

      # 添加机器人AI配置（JSON对象格式）
      robot_ai: robot_entity.robot_config,

      # 游戏特定配置
      game_specific_config: game_config
    }

    # 使用PlayerDataBuilder创建标准格式
    PlayerDataBuilder.create_robot_player_data(user_data,
      is_ready: true,
      is_robot: true
    )
  end

  # 辅助函数
  defp generate_robot_id do
    # 生成正数ID，避免与真实用户冲突
    base = 8_000_000 + :rand.uniform(1_000_000)
    abs(base)
  end

  defp generate_robot_nickname do
    # 使用 RobotEntity 中定义的统一名字列表
    RobotEntity.random_robot_name()
  end

  defp generate_initial_points do
    50_000 + :rand.uniform(150_000)
  end

  defp get_game_personality_distribution(game_type) do
    game_config = Map.get(@game_default_configs, game_type, %{})
    Map.get(game_config, "personality_distribution", @default_personality_distribution)
  end

  defp select_personality_by_distribution(distribution, index) do
    # 简单的轮询选择
    personalities = Map.keys(distribution)
    Enum.at(personalities, rem(index - 1, length(personalities)))
  end

  # JhandiMunda特定的辅助函数
  defp generate_favorite_symbols(personality) do
    all_symbols = ["spade", "heart", "diamond", "club", "crown", "anchor"]

    case personality do
      "aggressive" -> Enum.take_random(all_symbols, 2)
      "conservative" -> Enum.take_random(all_symbols, 1)
      # 根据趋势动态选择
      "trend_follower" -> []
      _ -> Enum.take_random(all_symbols, 1)
    end
  end

  defp map_personality_to_bet_pattern(personality) do
    case personality do
      "aggressive" -> "diversified"
      "conservative" -> "focused"
      "trend_follower" -> "adaptive"
      "contrarian" -> "random"
      _ -> "balanced"
    end
  end

  defp generate_symbol_bias(personality) do
    case personality do
      "aggressive" -> %{"crown" => 0.3, "anchor" => 0.2}
      "conservative" -> %{"spade" => 0.3, "heart" => 0.3}
      _ -> %{}
    end
  end

  defp choose_favorite_area("aggressive"), do: "dragon"
  defp choose_favorite_area("conservative"), do: "tiger"
  defp choose_favorite_area(_), do: nil

  defp map_personality_to_bet_style("aggressive"), do: "aggressive"
  defp map_personality_to_bet_style("conservative"), do: "conservative"
  defp map_personality_to_bet_style(_), do: "moderate"

  defp add_random_variance(params) do
    # 为数值参数添加±5%的随机变化
    variance_keys = ["aggression_level", "risk_tolerance", "patience_level", "confidence_level"]

    Enum.reduce(variance_keys, params, fn key, acc ->
      if Map.has_key?(acc, key) do
        original = Map.get(acc, key)
        # ±5%
        variance = 0.05
        min_val = max(0.0, original - variance)
        max_val = min(1.0, original + variance)
        new_val = min_val + :rand.uniform() * (max_val - min_val)
        Map.put(acc, key, Float.round(new_val, 3))
      else
        acc
      end
    end)
  end

  defp get_available_robots(count) do
    available = Ash.read!(RobotEntity, action: :available) |> Enum.take(count)
    {:ok, available}
  end

  defp create_robots_for_game(game_type, count) do
    distribution = get_game_personality_distribution(game_type)

    1..count
    |> Enum.map(fn index ->
      personality = select_personality_by_distribution(distribution, index)
      create_single_robot(game_type, personality)
    end)
    |> Enum.filter(&(not is_nil(&1)))
  end

  defp assign_robots_to_game(robots, game_type, room_id) do
    assigned =
      Enum.with_index(robots, 1)
      |> Enum.map(fn {robot, index} ->
        Ash.update!(robot, %{game_type: game_type, room_id: room_id, seat_number: index},
          action: :enter_game
        )
      end)

    {:ok, assigned}
  end
end
