defmodule Teen.RobotManagement.RobotEntity do
  use Ash.Resource,
    domain: Teen.RobotManagement,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "robot_entities"
    repo Cypridina.Repo

    # 查询优化索引
    custom_indexes do
      index [:status]
      index [:is_enabled]
      index [:current_game_type]
      index [:current_room_id]
      index [:status, :is_enabled]
    end
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      primary? true

      accept [
        :robot_id,
        :nickname,
        :avatar_id,
        :level,
        :status,
        :current_points,
        :min_points_threshold,
        :robot_config,
        :is_auto_created,
        :creator_admin_id,
        :tags,
        :is_enabled,
        :status_changed_at,
        :last_activity_at
      ]
    end

    # 状态查询 - 只有启用且空闲的机器人才可用
    read :available do
      filter expr(status == 0 and is_enabled == true)
    end

    read :enabled do
      filter expr(is_enabled == true)
    end

    read :disabled do
      filter expr(is_enabled == false)
    end

    read :in_games do
      filter expr(status == 1)
    end

    read :recycling do
      filter expr(status == 2)
    end

    read :insufficient_funds do
      filter expr(status == 3)
    end

    # 根据robot_id查询
    read :by_robot_id do
      argument :robot_id, :integer, allow_nil?: false
      filter expr(robot_id == ^arg(:robot_id))
    end

    # 状态转换操作
    update :enter_game do
      argument :game_type, :string, allow_nil?: false
      argument :room_id, :string, allow_nil?: false
      argument :seat_number, :integer

      change set_attribute(:status, 1)
      change set_attribute(:current_game_type, arg(:game_type))
      change set_attribute(:current_room_id, arg(:room_id))
      change set_attribute(:seat_number, arg(:seat_number))
      change set_attribute(:game_joined_at, &DateTime.utc_now/0)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end

    update :exit_game do
      change set_attribute(:status, 0)
      change set_attribute(:current_game_type, nil)
      change set_attribute(:current_room_id, nil)
      change set_attribute(:seat_number, nil)
      change set_attribute(:recycle_reason, nil)
      change set_attribute(:recycled_by, nil)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end

    update :start_recycling do
      argument :admin_id, :string, allow_nil?: false
      argument :reason, :string, default: "后台回收"

      change set_attribute(:status, 2)
      change set_attribute(:recycled_by, arg(:admin_id))
      change set_attribute(:recycle_reason, arg(:reason))
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end

    update :complete_recycling do
      change set_attribute(:status, 0)
      change set_attribute(:current_game_type, nil)
      change set_attribute(:current_room_id, nil)
      change set_attribute(:seat_number, nil)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end

    update :mark_insufficient_funds do
      change set_attribute(:status, 3)
      change set_attribute(:recycle_reason, "积分不足")
      change set_attribute(:recycled_by, "system")
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end

    update :update_points do
      argument :points, :integer, allow_nil?: false
      argument :bet_amount, :integer, default: 0

      change set_attribute(:current_points, arg(:points))
      change set_attribute(:last_bet_amount, arg(:bet_amount))
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end

    update :reset_to_idle do
      argument :reason, :string, default: "系统重置"

      change set_attribute(:status, 0)
      change set_attribute(:current_game_type, nil)
      change set_attribute(:current_room_id, nil)
      change set_attribute(:seat_number, nil)
      change set_attribute(:recycle_reason, arg(:reason))
      change set_attribute(:recycled_by, "system")
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
      change set_attribute(:last_activity_at, &DateTime.utc_now/0)
    end

    update :enable_robot do
      change set_attribute(:is_enabled, true)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end

    update :disable_robot do
      change set_attribute(:is_enabled, false)
      change set_attribute(:status_changed_at, &DateTime.utc_now/0)
    end
  end

  validations do
    validate numericality(:current_points, greater_than_or_equal_to: 0)
    validate present(:nickname)
    validate present(:robot_id)
  end

  attributes do
    uuid_primary_key :id

    attribute :robot_id, :integer, allow_nil?: false
    attribute :nickname, :string, allow_nil?: false
    attribute :avatar_id, :integer, default: 1
    attribute :level, :integer, default: 1

    # 简化的状态管理 - 0:空闲 1:游戏中 2:回收中 3:积分不足
    attribute :status, :integer,
      constraints: [min: 0, max: 3],
      default: 0

    # 游戏状态信息
    attribute :current_game_type, :string
    attribute :current_room_id, :string
    attribute :seat_number, :integer

    # 积分状态
    attribute :current_points, :integer, default: 100_000
    attribute :min_points_threshold, :integer, default: 5_000
    attribute :last_bet_amount, :integer, default: 0

    # 状态时间戳
    attribute :status_changed_at, :utc_datetime_usec
    attribute :game_joined_at, :utc_datetime_usec
    attribute :last_activity_at, :utc_datetime_usec

    # 回收状态
    attribute :recycle_reason, :string
    attribute :recycled_by, :string

    # 机器人配置
    attribute :robot_config, :map, default: %{}

    # 管理信息
    attribute :is_auto_created, :boolean, default: true
    attribute :creator_admin_id, :string
    attribute :tags, {:array, :string}, default: []
    attribute :is_enabled, :boolean, default: true

    timestamps()
  end

  identities do
    # robot_id 唯一性约束，确保每个机器人ID只对应一个实体
    identity :unique_robot_id, [:robot_id]
  end

  @robot_names [
    "Dheeraj",
    "Gaurav",
    "Kapil",
    "Jatin",
    "Kunal",
    "Rahul",
    "Sumit",
    "Varun",
    "Vikas",
    "Himani",
    "Nandana",
    "Ruchika",
    "Seema",
    "Neha",
    "Shivani",
    "Surbhi",
    "Rajan",
    "Vijay",
    "Sharma",
    "Prasad",
    "Singh",
    "Singha",
    "Chandra",
    "Kumar",
    "Priya",
    "Mani",
    "Sumitra",
    "Kavita",
    "Rani",
    "Devi",
    "Maya",
    "Karma",
    "धीराज",
    "गौरव",
    "कपिल",
    "जतीन",
    "कुनाल",
    "राहुल",
    "सुमित",
    "वरुण",
    "विकास",
    "हिमानी",
    "नंदना",
    "रुचिका",
    "सीमा",
    "नेहा",
    "शिवाणी",
    "सुर्भी",
    "राजन",
    "विजय",
    "शर्मा",
    "प्रसाद",
    "सिंह",
    "सिंघ",
    "चंद्र",
    "कुमार",
    "प्रिया",
    "मनी",
    "सुमित्रा",
    "कविता",
    "राणी",
    "देवी",
    "माया",
    "कर्मा"
  ]

  def robot_names, do: @robot_names

  @robot_avatars Enum.to_list(1..30)

  @doc """
  获取机器人头像配置
  """
  def robot_avatars, do: @robot_avatars

  @doc """
  获取随机机器人名称
  """
  def random_robot_name, do: Enum.random(robot_names())

  @doc """
  获取随机机器人头像
  """
  def random_robot_avatar, do: Enum.random(robot_avatars())
end
