defmodule Teen.PaymentSystem.GatewaySelector do
  @moduledoc """
  支付网关选择器
  根据金额、货币和用户选择最佳的支付网关
  """

  alias Teen.PaymentSystem.PaymentGateway
  require Logger

  @doc """
  选择充值网关
  """
  def select_recharge_gateway(amount, currency, user_id \\ nil) do
    Logger.info("💰 [GATEWAY_SELECTOR] 选择充值网关 - 金额: #{amount} #{currency}")

    case PaymentGateway.list_active() do
      {:ok, gateways} ->
        # 筛选充值网关
        recharge_gateways =
          gateways
          |> Enum.filter(&(&1.status == "active"))
          |> Enum.filter(&(currency in &1.supported_currencies))
          |> Enum.filter(&(Decimal.compare(amount, &1.min_amount) != :lt))
          |> Enum.filter(&(Decimal.compare(amount, &1.max_amount) != :gt))
          |> Enum.sort_by(& &1.priority, :desc)

        case recharge_gateways do
          [gateway | _] ->
            Logger.info("💰 [GATEWAY_SELECTOR] 选择网关: #{gateway.gateway_name}")
            {:ok, gateway}

          [] ->
            Logger.error("💰 [GATEWAY_SELECTOR] 没有可用的充值网关")
            {:error, "No suitable gateway found"}
        end

      {:error, reason} ->
        Logger.error("💰 [GATEWAY_SELECTOR] 获取网关列表失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取创建订单URL
  """
  def get_create_order_url(gateway_config) do
    Path.join(gateway_config.gateway_url, gateway_config.create_order_path)
  end

  @doc """
  获取查询订单URL
  """
  def get_query_order_url(gateway_config) do
    Path.join(gateway_config.gateway_url, gateway_config.query_order_path)
  end
end
