defmodule Teen.SystemSettings do
  @moduledoc """
  系统设置域

  包含管理员用户、角色管理、权限管理、操作日志、IP白名单等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  admin do
    show? true
  end

  resources do
    resource Teen.SystemSettings.AlertRecord
    resource Teen.SystemSettings.Role
    resource Teen.SystemSettings.Permission
    resource Teen.SystemSettings.RolePermission
    resource Teen.SystemSettings.OperationLog
    resource Teen.SystemSettings.IpWhitelist
  end
end
