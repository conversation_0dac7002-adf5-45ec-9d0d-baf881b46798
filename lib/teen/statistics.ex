defmodule Teen.Statistics do
  @moduledoc """
  统计分析域

  包含系统报表、在线统计、渠道统计、用户统计、金币统计、留存率统计等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.Statistics.SystemReport

  admin do
    show? false
  end

  resources do
    resource Teen.Statistics.SystemReport
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  生成日报
  """
  def generate_daily_report(report_date \\ Date.utc_today()) do
    SystemReport.generate_daily_report(%{report_date: report_date})
  end

  @doc """
  获取最新报表
  """
  def get_latest_report(report_type \\ "daily") do
    SystemReport.get_latest_report(report_type)
  end

  @doc """
  获取报表数据范围
  """
  def get_reports_by_date_range(start_date, end_date, report_type \\ "daily") do
    with {:ok, reports} <- SystemReport.list_by_date_range(start_date, end_date) do
      filtered_reports = Enum.filter(reports, &(&1.report_type == report_type))
      {:ok, filtered_reports}
    end
  end

  @doc """
  计算统计摘要
  """
  def calculate_statistics_summary(date_range \\ nil) do
    # 这里应该实现实际的统计计算逻辑
    {:ok,
     %{
       total_users: 10000,
       active_users_today: 1500,
       total_revenue: Decimal.new("500000"),
       revenue_today: Decimal.new("15000"),
       game_rounds_today: 5000,
       average_session_time: 45.5
     }}
  end
end
