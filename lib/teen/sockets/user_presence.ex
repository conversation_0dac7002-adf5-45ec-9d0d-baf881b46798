defmodule Teen.UserPresence do
  @moduledoc """
  用户在线状态管理 - 基于Phoenix Presence

  使用Phoenix Presence来跟踪用户的在线状态，支持顶号机制。
  Presence提供了分布式、容错和实时的在线状态管理能力。

  ## 功能
  - 跟踪用户在线状态
  - 支持多设备登录检测
  - 自动处理网络分区和节点故障
  - 实时状态同步

  ## 数据结构
  每个用户的Presence数据包含：
  - user_id: 用户ID
  - session_id: 会话ID
  - login_time: 登录时间
  - device_info: 设备信息
  - ip_address: IP地址
  """

  use Phoenix.Presence,
    otp_app: :cypridina,
    pubsub_server: Cypridina.PubSub

  require Logger

  # Client API

  @doc """
  跟踪用户在线状态
  根据Phoenix官方文档，使用user_id作为key，session_id作为metadata的一部分
  """
  def track_user(socket_or_pid, user_id, metadata \\ %{}) do
    # 构建用户的topic - 所有用户共享一个topic
    topic = "user_presence"

    # 默认元数据
    default_metadata = %{
      online_at: System.system_time(:second),
      login_time: System.system_time(:millisecond)
    }

    # 合并元数据
    full_metadata = Map.merge(default_metadata, metadata)

    Logger.info("👤 [USER_PRESENCE] 跟踪用户在线状态 - 用户: #{user_id}")

    # 使用user_id作为key来跟踪，这样同一用户的多个会话会合并在一起
    case track(socket_or_pid, topic, user_id, full_metadata) do
      {:ok, _ref} ->
        Logger.info("👤 [USER_PRESENCE] 用户状态跟踪成功 - 用户: #{user_id}")
        :ok

      {:error, reason} ->
        Logger.error("👤 [USER_PRESENCE] 用户状态跟踪失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  停止跟踪用户
  """
  def untrack_user(socket_or_pid, user_id) do
    topic = "user_presence"

    Logger.info("👤 [USER_PRESENCE] 停止跟踪用户 - 用户: #{user_id}")

    case untrack(socket_or_pid, topic, user_id) do
      :ok ->
        Logger.info("👤 [USER_PRESENCE] 用户状态跟踪已停止 - 用户: #{user_id}")
        :ok

      {:error, reason} ->
        Logger.error("👤 [USER_PRESENCE] 停止跟踪失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  更新用户活动时间
  """
  def update_user_activity(socket_or_pid, user_id, metadata_update) do
    topic = "user_presence"

    # 更新metadata
    case update(socket_or_pid, topic, user_id, metadata_update) do
      {:ok, _ref} ->
        :ok

      {:error, reason} ->
        Logger.error("👤 [USER_PRESENCE] 更新用户metadata失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取用户的所有在线会话
  根据Phoenix Presence的数据结构，一个用户可能有多个metas（多个设备/会话）
  """
  def get_user_sessions(user_id, exclude_session_id \\ nil) do
    topic = "user_presence"

    case get_by_key(topic, user_id) do
      [] ->
        Logger.debug("👤 [USER_PRESENCE] 用户无在线会话 - 用户: #{user_id}")
        []

      presences ->
        # 根据错误信息，get_by_key返回的是包含{:metas, [metadata_list]}的结构
        # 需要从中提取metas列表
        sessions =
          presences
          |> Enum.flat_map(fn
            {:metas, metas} when is_list(metas) ->
              # 从metas列表中提取session_id
              Enum.map(metas, fn metadata ->
                Map.get(metadata, :session_id)
              end)

            metadata when is_map(metadata) ->
              # 如果直接是metadata map，直接提取session_id
              [Map.get(metadata, :session_id)]

            _ ->
              # 其他情况返回空列表
              []
          end)
          |> Enum.filter(fn session_id ->
            # 排除指定的会话ID，并过滤掉nil值
            session_id != nil && session_id != exclude_session_id
          end)

        Logger.debug(
          "👤 [USER_PRESENCE] 获取用户会话 - 用户: #{user_id}, 会话数: #{length(sessions)}, 排除: #{exclude_session_id}"
        )

        sessions
    end
  end

  @doc """
  获取用户会话详情
  """
  def get_user_session_details(user_id) do
    topic = "user_presence"

    case get_by_key(topic, user_id) do
      [] ->
        {:error, :not_found}

      presences ->
        # 从presences中提取所有metadata
        all_metas =
          presences
          |> Enum.flat_map(fn
            {:metas, metas} when is_list(metas) ->
              metas

            metadata when is_map(metadata) ->
              [metadata]

            _ ->
              []
          end)

        {:ok, all_metas}
    end
  end

  @doc """
  获取所有在线用户统计
  """
  def get_online_stats do
    topic = "user_presence"

    case list(topic) do
      presences when is_map(presences) ->
        total_users = map_size(presences)

        # 计算总会话数（每个用户可能有多个会话）
        total_sessions =
          presences
          |> Enum.map(fn {_user_id, %{metas: metas}} -> length(metas) end)
          |> Enum.sum()

        %{
          total_users: total_users,
          total_sessions: total_sessions
        }

      _ ->
        %{total_users: 0, total_sessions: 0}
    end
  end

  @doc """
  检查用户是否在线
  """
  def user_online?(user_id) do
    topic = "user_presence"

    case get_by_key(topic, user_id) do
      [] -> false
      _presences -> true
    end
  end

  @doc """
  向用户的所有会话发送消息
  """
  def send_to_user_sessions(user_id, event, payload, exclude_session_id \\ nil) do
    sessions = get_user_sessions(user_id, exclude_session_id)

    Enum.each(sessions, fn session_id ->
      topic = "session:#{session_id}"

      case CypridinaWeb.Endpoint.broadcast(topic, event, payload) do
        :ok ->
          Logger.debug("📤 [USER_PRESENCE] 消息发送成功 - 会话: #{session_id}, 事件: #{event}")

        {:error, reason} ->
          Logger.error(
            "📤 [USER_PRESENCE] 消息发送失败 - 会话: #{session_id}, 事件: #{event}, 原因: #{inspect(reason)}"
          )
      end
    end)

    {:ok, length(sessions)}
  end

  # Phoenix.Presence callbacks

  @doc """
  初始化Presence状态
  """
  def init(_opts) do
    {:ok, %{}}
  end

  @doc """
  处理Presence元数据变化
  根据Phoenix官方文档，这个回调接收joins和leaves事件
  """
  def handle_metas(topic, %{joins: joins, leaves: leaves}, presences, state) do
    # 处理用户加入事件
    for {user_id, %{metas: metas}} <- joins do
      Logger.info("👤 [PRESENCE_JOIN] 用户上线 - 用户: #{user_id}, 会话数: #{length(metas)}")

      # 可以在这里添加额外的业务逻辑，比如通知其他系统用户上线
    end

    # 处理用户离开事件
    for {user_id, %{metas: metas}} <- leaves do
      Logger.info("👤 [PRESENCE_LEAVE] 用户下线 - 用户: #{user_id}, 剩余会话数: #{length(metas)}")

      # 可以在这里添加额外的业务逻辑，比如通知其他系统用户下线
    end

    {:ok, state}
  end

  @doc """
  扩展Presence信息
  可以在这里从数据库获取用户详细信息
  """
  def fetch(_topic, presences) do
    # 默认实现，直接返回原始数据
    # 如果需要从数据库获取用户信息，可以在这里实现
    presences
  end
end
