defmodule CypridinaWeb.PhoenixSocket do
  @moduledoc """
  Phoenix Framework Socket for Teen game.

  This module implements a Phoenix Socket to replace the original cowboy_websocket-based TeenSocket.
  It matches the client-side implementation using the Phoenix library.
  """

  use Phoenix.Socket
  require Logger

  ## Channels
  channel "game:lobby", CypridinaWeb.LobbyChannel
  channel "game:room_*", CypridinaWeb.GameChannel

  @impl true
  def connect(params, socket, connect_info) do
    peer_data = Map.get(connect_info, :peer_data, %{})
    session_id = :crypto.strong_rand_bytes(16) |> Base.encode16(case: :lower)

    Logger.info("🔌 [SOCKET] 连接请求 - IP: #{inspect(peer_data)}")
    Logger.info("🔌 [SOCKET] 连接参数: #{inspect(params)}")

    # 获取 token 参数
    token = Map.get(params, "token")

    case verify_token_and_get_user(token) do
      {:ok, user} ->
        Logger.info("🔌 [SOCKET] Token验证成功 - 用户ID: #{user.id}, 用户名: #{user.username}")

        # 使用Phoenix Presence跟踪用户在线状态
        if user.id do
          # 获取设备和IP信息
          device_id = Map.get(params, "device_id", "unknown")

          ip_address =
            case peer_data do
              %{address: {a, b, c, d}} -> "#{a}.#{b}.#{c}.#{d}"
              _ -> "unknown"
            end

          # 跟踪用户在线状态
          Teen.UserPresence.track_user(self(), user.id, %{
            device_id: device_id,
            ip_address: ip_address,
            connected_at: System.system_time(:millisecond)
          })
        end

        socket =
          assign(socket, %{
            session_id: session_id,
            user_id: user.id,
            current_user: user,
            connected_at: System.system_time(:millisecond),
            socket_info: %{
              peer_data: peer_data,
              x_headers: Map.get(connect_info, :x_headers),
              uri: Map.get(connect_info, :uri),
              session: Map.get(connect_info, :session)
            },
            authenticate: true,
            params: params
          })

        {:ok, socket}

      {:error, reason} ->
        Logger.warning("🔌 [SOCKET] Token验证失败 - 原因: #{inspect(reason)}")
        :error
    end
  end

  # 验证 token 并获取用户信息
  defp verify_token_and_get_user(nil) do
    Logger.warning("🔌 [TOKEN] Token为空")
    {:error, :missing_token}
  end

  defp verify_token_and_get_user("") do
    Logger.warning("🔌 [TOKEN] Token为空字符串")
    {:error, :missing_token}
  end

  defp verify_token_and_get_user(token) do
    Logger.info("🔌 [TOKEN] 验证Token: #{String.slice(token, 0, 20)}...")

    case AshAuthentication.Jwt.verify(token, Cypridina.Accounts.User) do
      {:ok, %{"sub" => sub, "tenant" => tenant} = claims, _} ->
        # Logger.info("🔌 [TOKEN] JWT验证成功 - Claims: #{inspect(claims)}")
        Logger.info(
          "🔌 [TOKEN] 提取到 - Sub: #{sub}, Tenant: #{inspect(tenant)} (类型: #{inspect(tenant |> to_string() |> String.length())})"
        )

        case AshAuthentication.subject_to_user(sub, Cypridina.Accounts.User, tenant: tenant) do
          {:ok, user} ->
            Logger.info(
              "🔌 [TOKEN] 用户查找成功 - 用户ID: #{user.id}, 用户名: #{user.username}, Channel ID: #{user.channel_id}"
            )

            {:ok, user}

          {:error, reason} ->
            Logger.error("🔌 [TOKEN] 用户查找失败 - 原因: #{inspect(reason)}")
            {:error, reason}
        end

      {:error, reason} ->
        Logger.error("🔌 [TOKEN] JWT验证失败 - 原因: #{inspect(reason)}")
        {:error, reason}

      :error ->
        Logger.error("🔌 [TOKEN] Token格式无效")
        {:error, :invalid_token}
    end
  end

  @impl true
  def id(socket), do: "socket:#{socket.assigns.session_id}"
end
