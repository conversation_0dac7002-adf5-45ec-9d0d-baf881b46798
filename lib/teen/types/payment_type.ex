defmodule Teen.Types.PaymentType do
  @moduledoc """
  支付类型枚举

  定义所有支持的支付方式类型及其描述
  根据MasterPay88支持的支付方式进行定义
  """

  use Ash.Type.Enum,
    values: [
      # 印度本地支付方式 (MasterPay88支持)
      upi: "UPI",
      net_banking: "Net Banking",
      wallet: "Wallet",
      card: "Card",

      # 系统内部支付方式
      balance: "余额支付",
      points: "积分支付"
    ]

  @doc """
  获取支付类型的描述
  """
  def description(type) do
    case type do
      :upi -> "UPI"
      :net_banking -> "Net Banking"
      :wallet -> "Wallet"
      :card -> "Card"
      :balance -> "余额支付"
      :points -> "积分支付"
      _ -> "未知支付方式"
    end
  end

  @doc """
  获取所有支付类型的选项列表，用于下拉选择
  """
  def options do
    values()
    |> Enum.map(fn
      {key, desc} -> {desc, Atom.to_string(key)}
      key when is_atom(key) -> {description(key), Atom.to_string(key)}
    end)
  end

  @doc """
  根据分类获取支付类型
  """
  def by_category(category) do
    case category do
      :external -> [:upi, :net_banking, :wallet, :card]
      :internal -> [:balance, :points]
      _ -> []
    end
  end

  @doc """
  获取支付类型的分类
  """
  def category(type) do
    cond do
      type in by_category(:external) -> :external
      type in by_category(:internal) -> :internal
      true -> :unknown
    end
  end

  @doc """
  检查是否为外部支付方式
  """
  def is_external?(type) do
    type in by_category(:external)
  end

  @doc """
  检查是否为内部支付方式
  """
  def is_internal?(type) do
    type in by_category(:internal)
  end
end
