defmodule CypridinaWeb.LobbyChannel do
  @moduledoc """
  Phoenix Channel for handling Teen game messages.

  This channel processes messages sent by the client, handles authentication,
  and manages game-related communications.

  Since the WebSocket serializer has been changed to msgpack, the channel now
  receives already unpacked client messages in the format:
  %{"mainId" => main_id, "subId" => sub_id, "data" => data}

  And sends responses as map data without manual encoding.
  """
  use Phoenix.Channel
  require Logger
  alias Teen.Protocol.ProtocolRouter
  alias Cypridina.Accounts.User

  @impl true
  def join("game:" <> topic, params, socket) do
    Logger.info("🎮 [CHANNEL_JOIN] 客户端参加大厅频道: #{topic}, 参数: #{inspect(params)}")

    # 检查用户是否已通过 socket 连接时的 token 验证
    case socket.assigns do
      %{authenticate: true, current_user: user} ->
        Logger.info("🎮 [CHANNEL_JOIN] 用户已验证 - 用户ID: #{user.id}, 用户名: #{user.username}")

        # 解析游戏频道类型
        {channel_type, channel_info} = parse_game_channel(topic)

        # Assign channel info to socket
        socket =
          socket
          |> assign(:game_id, channel_info[:game_id])
          |> assign(:room_id, nil)
          |> assign(:channel_type, channel_type)
          |> assign(:channel_info, channel_info)

        Logger.info(
          "🎮 [CHANNEL_JOIN] 频道类型: #{channel_type}, 信息: #{inspect(channel_info)}, 用户: #{user.username}"
        )

        # 订阅会话特定的topic，用于接收顶号消息
        session_id = socket.assigns.session_id

        if session_id do
          session_topic = "session:#{session_id}"
          CypridinaWeb.Endpoint.subscribe(session_topic)
          Logger.info("📡 [CHANNEL_SUBSCRIBE] 订阅会话topic: #{session_topic}")
          user_topic = "user:#{user.id}"
          CypridinaWeb.Endpoint.subscribe(user_topic)
        end

        # 更新用户在线状态 - 清空离线时间
        update_user_online_status(user.id, :online)

        # Send a message to self to push the welcome message after join
        send(self(), :after_join)

        {:ok, socket}

      _ ->
        Logger.warning("🎮 [CHANNEL_JOIN] 用户未通过验证，拒绝加入频道")
        {:error, %{reason: "unauthorized"}}
    end
  end

  # 处理客户端发送的消息 - 现在payload已经是msgpack解包后的map格式
  @impl true
  def handle_in("message", payload, socket) when is_map(payload) do
    Logger.info("🔵 [REQUEST] 收到客户端消息: #{inspect(payload)}")

    # 提取消息字段 - 客户端发送格式（data字段可能为空）:
    %{"mainId" => main_id, "subId" => sub_id} = payload
    data = Map.get(payload, "data", %{})
    # 构造标准化的消息格式给ProtocolRouter处理
    Logger.info("🔵 [REQUEST] 处理消息 - MainID: #{main_id}, SubID: #{sub_id}, Data: #{inspect(data)}")

    # 使用ProtocolRouter处理消息
    case ProtocolRouter.handle_message(
           %{main_id: main_id, sub_id: sub_id, data: data},
           socket.assigns
         ) do
      {:reply, response_map, new_assigns} ->
        Logger.info(
          "🟢 [RESPONSE] 处理消息成功，发送响应 - MainID: #{response_map["mainId"]}, SubID: #{response_map["subId"]}, Data: #{inspect(response_map["data"])}"
        )

        # 更新socket assigns
        socket = update_socket_assigns(socket, new_assigns)
        # 正确的Phoenix Channel响应格式 - 回复客户端请求
        {:reply, {:ok, response_map}, socket}

      {:ok, new_assigns} ->
        Logger.info("🟡 [NO_RESPONSE] 处理消息成功，无需响应 - MainID: #{main_id}, SubID: #{sub_id}")

        # 更新socket assigns但不发送响应
        socket = update_socket_assigns(socket, new_assigns)
        {:noreply, socket}

      other ->
        Logger.warning(
          "🔴 [ERROR] ProtocolRouter返回未知格式 - MainID: #{main_id}, SubID: #{sub_id}, Result: #{inspect(other)}"
        )

        # 返回错误响应给客户端
        {:reply, {:error, %{reason: "handler_error", details: inspect(other)}}, socket}
    end
  end

  @impl true
  def handle_in("heartbeat", _payload, socket) do
    # Handle heartbeat messages - 返回正确的Phoenix格式
    response = %{
      "mainId" => 0,
      "subId" => 19,
      "data" => %{
        "server_time" => System.system_time(:millisecond)
      }
    }

    {:reply, {:ok, response}, socket}
  end

  # Handle info messages from other processes
  @impl true
  def handle_info(:after_join, socket) do
    # 根据频道类型发送不同的欢迎消息
    welcome_data =
      %{
        "type" => "lobby_welcome",
        "message" => "欢迎来到游戏大厅",
        "session_id" => socket.assigns.session_id,
        "server_time" => System.system_time(:millisecond)
      }

    welcome_msg = %{
      "mainId" => 0,
      "subId" => 0,
      "data" => welcome_data
    }

    Logger.info("🎮 [WELCOME] 发送欢迎消息: #{socket.assigns.channel_type} - #{inspect(welcome_data)}")

    # Push welcome message - msgpack serializer will handle encoding
    push(socket, "message", welcome_msg)

    {:noreply, socket}
  end

  # 处理来自 PubSub 的广播消息
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: _, event: "room_message", payload: payload},
        socket
      ) do
    push(socket, "message", payload)
    {:noreply, socket}
  end

  def handle_info(
        %Phoenix.Socket.Broadcast{topic: _, event: "private_message", payload: payload},
        socket
      ) do
    push(socket, "message", payload)
    {:noreply, socket}
  end

  # 处理被挤下线消息
  def handle_info(
        %Phoenix.Socket.Broadcast{topic: _, event: "kick_message", payload: payload},
        socket
      ) do
    Logger.info("📤 [KICK_MESSAGE] 收到被挤下线消息，向客户端发送")
    push(socket, "message", payload)
    {:noreply, socket}
  end

  # 处理延迟发送的顶号消息
  def handle_info({:send_login_other_message, message}, socket) do
    Logger.info("📤 [LOGIN_OTHER] 发送顶号通知消息")
    push(socket, "message", message)
    {:noreply, socket}
  end

  @impl true
  def handle_info(info, socket) do
    Logger.debug("未处理的info消息: #{inspect(info)}")
    {:noreply, socket}
  end

  # Handle client disconnect
  @impl true
  def terminate(reason, socket) do
    session_id = socket.assigns.session_id
    user_id = socket.assigns[:user_id]

    Logger.info("Channel终止 [#{session_id}]: #{inspect(reason)}")

    # 使用Phoenix Presence停止跟踪用户状态
    if user_id do
      case Teen.UserPresence.untrack_user(self(), user_id) do
        :ok ->
          Logger.info("👤 [USER_PRESENCE] 用户状态跟踪已停止 - 用户: #{user_id}, 会话: #{session_id}")

        {:error, reason} ->
          Logger.error(
            "👤 [USER_PRESENCE] 停止跟踪失败 - 用户: #{user_id}, 会话: #{session_id}, 原因: #{inspect(reason)}"
          )
      end

      # 更新用户离线状态 - 设置离线时间
      update_user_online_status(user_id, :offline)
    end

    :ok
  end

  # 解析游戏频道类型和信息
  defp parse_game_channel("lobby"), do: {:lobby, %{}}
  defp parse_game_channel(other), do: {:unknown, %{raw: other}}

  # 辅助函数：更新socket assigns
  defp update_socket_assigns(socket, new_assigns) when is_map(new_assigns) do
    Enum.reduce(new_assigns, socket, fn {key, value}, acc ->
      assign(acc, key, value)
    end)
  end

  defp update_socket_assigns(socket, _), do: socket

  # 更新用户在线状态
  defp update_user_online_status(user_id, status) when status in [:online, :offline] do
    try do
      case status do
        :online ->
          case User |> Ash.get(user_id) do
            {:ok, user} ->
              case user |> Ash.Changeset.for_update(:set_online) |> Ash.update() do
                {:ok, _updated_user} ->
                  Logger.info("👤 [USER_STATUS] 用户上线状态已更新 - 用户: #{user_id}")
                  :ok

                {:error, reason} ->
                  Logger.error(
                    "👤 [USER_STATUS] 更新用户上线状态失败 - 用户: #{user_id}, 原因: #{inspect(reason)}"
                  )

                  {:error, reason}
              end

            {:error, reason} ->
              Logger.error("👤 [USER_STATUS] 获取用户失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
              {:error, reason}
          end

        :offline ->
          case User |> Ash.get(user_id) do
            {:ok, user} ->
              case user |> Ash.Changeset.for_update(:set_offline) |> Ash.update() do
                {:ok, _updated_user} ->
                  Logger.info("👤 [USER_STATUS] 用户离线状态已更新 - 用户: #{user_id}")
                  :ok

                {:error, reason} ->
                  Logger.error(
                    "👤 [USER_STATUS] 更新用户离线状态失败 - 用户: #{user_id}, 原因: #{inspect(reason)}"
                  )

                  {:error, reason}
              end

            {:error, reason} ->
              Logger.error("👤 [USER_STATUS] 获取用户失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
              {:error, reason}
          end
      end
    rescue
      error ->
        Logger.error(
          "👤 [USER_STATUS] 更新用户状态时发生异常 - 用户: #{user_id}, 状态: #{status}, 异常: #{inspect(error)}"
        )

        {:error, error}
    end
  end
end
