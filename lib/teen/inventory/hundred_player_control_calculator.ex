defmodule Teen.Inventory.HundredPlayerControlCalculator do
  @moduledoc """
  通用百人场控制计算器

  支持所有百人场游戏的控制线计算和决策：
  - longhu (龙虎斗)
  - jhandi_munda (骰子游戏)
  - crash (崩溃游戏)

  使用统一的控制理论：
  - 当前库存：所有玩家的总输赢值的总和
    * 正值：玩家总体输钱，平台盈利
    * 负值：玩家总体赢钱，平台亏损
    * 0值：盈亏平衡
  - 中心线机制基于暗税累计
  - 六级控制线：
    * 中心线（Center Line）
    * 收分线（Collect Line）
    * 前置收分线（Pre-Collect Line）
    * 放分线（Release Line）
    * 前置放分线（Pre-Release Line）
    * 绝对放分线（Absolute Release Line）
  - 权重概率控制：1-1000
  """

  require Logger
  alias Teen.Resources.Inventory.GameControlConfig
  alias Teen.Resources.Inventory.HundredPlayerGameState
  alias Teen.Inventory.LonghuCardController
  alias Teen.Inventory.JhandiMundaDiceController
  alias Cypridina.Ledger.AccountIdentifier
  alias Cypridina.Ledger.BalanceCache

  # 百人场游戏类型
  @hundred_player_games [:longhu, :jhandi_munda, :crash]

  # 默认浮动控制线配置（当配置不存在时使用）
  @default_floating_config %{
    # 收分线浮动配置
    collect_line_max: 30000,
    collect_line_min: 5000,
    collect_line_ratio: Decimal.new("0.2"),
    pre_collect_line_ratio: Decimal.new("0.7"),
    # 放分线浮动配置
    release_line_max: 30000,
    release_line_min: 5000,
    release_line_ratio: Decimal.new("0.2"),
    pre_release_line_ratio: Decimal.new("0.7")
  }

  # 权重概率配置（按文档二.术语定义）
  @weight_config %{
    # 绝对收分（强力收分机制）
    absolute_collect: 1000,
    # 前置收分（权重值判断）
    pre_collect: 700,
    # 随机
    random: 500,
    # 前置放分（权重值判断）
    pre_release: 300,
    # 绝对放分（强力放分机制）
    absolute_release: 100
  }

  @doc """
  获取游戏的控制决策

  ## 参数
  - game_id: 游戏ID
  - game_type: 游戏类型 (:longhu, :jhandi_munda, :crash)

  ## 返回
  {:ok, %{control_mode: atom, target_result: atom, weight: integer}} | {:error, reason}
  """
  def get_control_decision(game_id, game_type, bet_data \\ %{})
      when game_type in @hundred_player_games do
    Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] 获取控制决策: game_id=#{game_id}, type=#{game_type}")

    with {:ok, config} <- get_game_config(game_id, game_type),
         {:ok, game_state} <- get_game_state(game_id, game_type) do
      # 获取游戏当前库存（所有玩家的总输赢值的总和）
      current_inventory = get_game_current_inventory(game_id)

      # 计算浮动控制线
      control_lines = calculate_control_lines(game_state.center_line, config)

      # 获取控制决策（考虑下注分布）
      control_decision =
        determine_control_action(current_inventory, control_lines, config, bet_data)

      Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] 控制决策: #{inspect(control_decision)}")

      {:ok, control_decision}
    else
      error ->
        Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 获取控制决策失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  为龙虎斗游戏生成控制结果

  ## 参数
  - game_id: 游戏ID
  - bet_data: 投注数据 %{long: 总龙投注, hu: 总虎投注, he: 总和投注}

  ## 返回
  {:ok, %{result: :long | :hu | :he, long_card: {rank, suit}, hu_card: {rank, suit}, inventory_change: 数值}}
  """
  def generate_longhu_result(game_id, bet_data) do
    Logger.info(
      "🎮 [HUNDRED_PLAYER_CONTROL] 生成龙虎斗结果: game_id=#{game_id}, bets=#{inspect(bet_data)}"
    )

    with {:ok, config} <- get_game_config(game_id, :longhu),
         {:ok, game_state} <- get_game_state(game_id, :longhu) do
      # 获取游戏当前库存（所有玩家的总输赢值的总和）
      current_inventory = get_game_current_inventory(game_id)

      # 确定控制模式
      control_mode =
        determine_longhu_control_mode(current_inventory, config, game_state, bet_data)

      # 构建权重配置
      weight_config = %{
        long_weight: config.long_weight,
        hu_weight: config.hu_weight,
        he_weight: config.he_weight
      }

      # 生成控制结果
      result =
        LonghuCardController.generate_controlled_result(bet_data, control_mode, weight_config)

      Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] 龙虎斗结果: #{inspect(result)}")

      {:ok, result}
    else
      error ->
        Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 生成龙虎斗结果失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  生成 Jhandi Munda 控制结果

  ## 参数
  - game_id: 游戏ID
  - bet_data: 投注数据 %{symbol => total_bet_amount}

  ## 返回
  {:ok, result} | {:error, reason}
  """
  def generate_jhandi_munda_result(game_id, bet_data) do
    Logger.info(
      "🎮 [HUNDRED_PLAYER_CONTROL] 生成Jhandi Munda结果: game_id=#{game_id}, bets=#{inspect(bet_data)}"
    )

    with {:ok, config} <- get_game_config(game_id, :jhandi_munda),
         {:ok, game_state} <- get_game_state(game_id, :jhandi_munda) do
      # 获取游戏当前库存（所有玩家的总输赢值的总和）
      current_inventory = get_game_current_inventory(game_id)

      # 确定控制模式
      control_mode =
        determine_jhandi_munda_control_mode(current_inventory, config, game_state, bet_data)

      # 构建权重配置（根据骰子文档的符号权重配置）
      # 为骰子游戏提供默认权重，因为当前配置表只有龙虎游戏的权重字段
      weight_config = %{
        # 梅花权重
        clubs_weight: Map.get(config, :clubs_weight, 100),
        # 皇冠权重
        crown_weight: Map.get(config, :crown_weight, 100),
        # 黑桃权重
        spades_weight: Map.get(config, :spades_weight, 100),
        # 方块权重
        diamonds_weight: Map.get(config, :diamonds_weight, 100),
        # 国旗权重
        flag_weight: Map.get(config, :flag_weight, 100),
        # 红桃权重
        hearts_weight: Map.get(config, :hearts_weight, 100)
      }

      # 生成控制结果
      result =
        JhandiMundaDiceController.generate_controlled_result(
          bet_data,
          control_mode,
          weight_config
        )

      Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] Jhandi Munda结果: #{inspect(result)}")

      {:ok, result}
    else
      error ->
        Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 生成Jhandi Munda结果失败: #{inspect(error)}")
        {:error, error}
    end
  end

  @doc """
  处理游戏结果回调

  ## 参数
  - game_id: 游戏ID
  - game_type: 游戏类型
  - result: 游戏结果
  - game_data: 游戏详细数据
  """
  def handle_game_result(game_id, game_type, result, game_data)
      when game_type in @hundred_player_games do
    Logger.info(
      "🎮 [HUNDRED_PLAYER_CONTROL] 处理游戏结果: game_id=#{game_id}, type=#{game_type}, result=#{result}, game_data=#{inspect(game_data)}"
    )

    try do
      # 获取游戏状态
      {:ok, game_state} = get_game_state(game_id, game_type)
      {:ok, config} = get_game_config(game_id, game_type)

      # 计算库存变化（这里需要根据实际游戏结果计算）
      inventory_change = calculate_inventory_change(result, game_data, config)

      # 获取当前库存（所有玩家的总输赢值的总和）
      current_inventory = get_game_current_inventory(game_id)

      Logger.debug(
        "🎮 [HUNDRED_PLAYER_CONTROL] 库存变化: #{inventory_change}, 当前库存（玩家总输赢）: #{current_inventory}"
      )

      # 获取暗税数据（直接从游戏数据中获取）
      dark_tax_raw =
        case Map.get(game_data, :total_dark_tax) do
          nil ->
            # 如果没有暗税数据，根据库存变化计算
            calculate_dark_tax(inventory_change, config.dark_tax_rate)

          tax when is_struct(tax, Decimal) ->
            tax

          tax when is_number(tax) ->
            Decimal.new("#{tax}")

          _ ->
            Logger.warning("🎮 [HUNDRED_PLAYER_CONTROL] total_dark_tax格式异常，使用计算值")
            calculate_dark_tax(inventory_change, config.dark_tax_rate)
        end

      # JhandiMunda房间已经将暗税从分转换为元，这里直接使用
      dark_tax =
        if is_struct(dark_tax_raw, Decimal),
          do: dark_tax_raw,
          else: Decimal.new("#{dark_tax_raw}")

      # 更新中心线
      new_center_line = update_center_line(game_state.center_line, dark_tax)

      # 更新游戏状态
      # 确保暗税累计使用 Decimal 计算
      current_accumulated =
        if is_struct(game_state.dark_tax_accumulated, Decimal),
          do: game_state.dark_tax_accumulated,
          else: Decimal.new("#{game_state.dark_tax_accumulated || 0}")

      new_dark_tax_accumulated = Decimal.add(current_accumulated, dark_tax)

      update_attrs = %{
        # 使用玩家总输赢值作为库存
        game_type: to_string(game_type),
        current_inventory: current_inventory,
        center_line: new_center_line,
        dark_tax_accumulated: new_dark_tax_accumulated,
        last_result: to_string(result),
        last_update: DateTime.utc_now()
      }

      case HundredPlayerGameState.update(game_state, update_attrs) do
        {:ok, _updated_state} ->
          # 记录游戏历史
          record_game_history(
            game_id,
            game_type,
            result,
            inventory_change,
            current_inventory,
            game_data
          )

          Logger.info(
            "🎮 [HUNDRED_PLAYER_CONTROL] 游戏状态更新完成: inventory=#{current_inventory}, center_line=#{new_center_line}"
          )

          :ok

        {:error, update_error} ->
          Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 游戏状态更新失败: #{inspect(update_error)}")
          :error
      end
    rescue
      error ->
        Logger.error("🎮 [HUNDRED_PLAYER_CONTROL] 处理游戏结果失败: #{inspect(error)}")
        :error
    end
  end

  def handle_game_result(game_id, game_type, result, _game_data) do
    Logger.info(
      "🎮 [HUNDRED_PLAYER_CONTROL] 跳过非百人场游戏结果: game_id=#{game_id}, type=#{game_type}, result=#{result}"
    )

    :ok
  end

  @doc """
  获取游戏控制状态

  ## 参数
  - game_id: 游戏ID
  - game_type: 游戏类型

  ## 返回
  游戏控制状态映射
  """
  def get_control_status(game_id, game_type) when game_type in @hundred_player_games do
    with {:ok, config} <- get_game_config(game_id, game_type),
         {:ok, game_state} <- get_game_state(game_id, game_type) do
      # 获取游戏当前库存（所有玩家的总输赢值的总和）
      current_inventory = get_game_current_inventory(game_id)

      # 计算浮动控制线
      control_lines = calculate_control_lines(game_state.center_line, config)

      # 获取控制状态 - 后台管理界面显示理论状态（不检查真实玩家）
      control_status =
        determine_control_status_for_display(current_inventory, control_lines, config)

      # 计算库存百分比（用于界面显示）
      inventory_percentage =
        calculate_inventory_percentage(current_inventory, config.base_inventory)

      status = %{
        game_id: game_id,
        game_type: game_type,
        # 使用玩家总输赢值作为库存
        current_inventory: current_inventory,
        center_line: game_state.center_line,
        control_lines: control_lines,
        control_status: Map.put(control_status, :config, config),
        dark_tax_accumulated: game_state.dark_tax_accumulated,
        last_result: game_state.last_result,
        last_update: game_state.last_update,
        inventory_percentage: inventory_percentage
      }

      {:ok, status}
    else
      error -> {:error, error}
    end
  end

  def get_control_status(game_id, game_type) do
    Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] 跳过非百人场游戏状态: game_id=#{game_id}, type=#{game_type}")
    {:error, :not_hundred_player_game}
  end

  # ==================== 私有函数 ====================

  # 获取游戏配置
  defp get_game_config(game_id, game_type) do
    case GameControlConfig.get_by_game_id_and_type(game_id, 3) do
      {:ok, config} -> {:ok, config}
      {:error, _} -> create_default_config(game_id, game_type)
    end
  end

  # 获取游戏状态
  defp get_game_state(game_id, game_type) do
    case HundredPlayerGameState.get_by_game_id_and_type(game_id, to_string(game_type)) do
      {:ok, state} -> {:ok, state}
      {:error, _} -> create_default_game_state(game_id, game_type)
    end
  end

  # 创建默认游戏配置（使用新的浮动控制线配置）
  defp create_default_config(game_id, game_type) do
    attrs = %{
      game_id: game_id,
      # 百人场游戏
      game_type: 3,
      game_name: get_game_name(game_type),
      # 100万基础库存
      base_inventory: 1_000_000,
      # 默认权重
      control_weight: 500,
      # -50 = -5%暗税率（明税）
      dark_tax_rate: -50,
      # 新的浮动控制线配置
      collect_line_max: @default_floating_config.collect_line_max,
      collect_line_min: @default_floating_config.collect_line_min,
      collect_line_ratio: @default_floating_config.collect_line_ratio,
      pre_collect_line_ratio: @default_floating_config.pre_collect_line_ratio,
      release_line_max: @default_floating_config.release_line_max,
      release_line_min: @default_floating_config.release_line_min,
      release_line_ratio: @default_floating_config.release_line_ratio,
      pre_release_line_ratio: @default_floating_config.pre_release_line_ratio,
      # 龙虎和权重配置
      long_weight: 33,
      hu_weight: 33,
      he_weight: 34,
      is_active: true
    }

    GameControlConfig.create(attrs)
  end

  # 创建默认游戏状态
  defp create_default_game_state(game_id, game_type) do
    # 获取游戏当前库存（所有玩家的总输赢值的总和）
    current_inventory = get_game_current_inventory(game_id)

    Logger.info(
      "🎮 [HUNDRED_PLAYER_CONTROL] 创建默认游戏状态: game_id=#{game_id}, 当前库存（玩家总输赢）=#{current_inventory}"
    )

    attrs = %{
      game_id: game_id,
      game_type: to_string(game_type),
      current_inventory: current_inventory,
      # 根据用户说明，中心线初始值默认为0
      center_line: 0,
      dark_tax_accumulated: 0,
      last_result: "none",
      last_update: DateTime.utc_now()
    }

    HundredPlayerGameState.create(attrs)
  end

  # 计算库存百分比
  # 当前库存：所有玩家的总输赢值的总和（正值=平台盈利，负值=平台亏损）
  # 基础库存：游戏配置中的基础库存（通常是100万）
  # 百分比 = (当前库存 / 基础库存) * 100
  defp calculate_inventory_percentage(current_inventory, base_inventory) do
    # 确保使用 Decimal 计算
    current_decimal =
      if is_struct(current_inventory, Decimal),
        do: current_inventory,
        else: Decimal.new("#{current_inventory}")

    base_decimal =
      if is_struct(base_inventory, Decimal),
        do: base_inventory,
        else: Decimal.new("#{base_inventory}")

    # 避免除零，使用默认基础库存100万
    if Decimal.equal?(base_decimal, Decimal.new("0")) do
      current_decimal
      |> Decimal.div(Decimal.new("1000000"))
      |> Decimal.mult(100)
      |> Decimal.to_float()
    else
      current_decimal
      |> Decimal.div(base_decimal)
      |> Decimal.mult(100)
      |> Decimal.to_float()
    end
  end

  # 计算浮动控制线
  # 严格按照文档定义实现
  defp calculate_control_lines(center_line, config) do
    center_decimal =
      if is_struct(center_line, Decimal), do: center_line, else: Decimal.new("#{center_line}")

    # 1. 放分线计算逻辑（按文档五.1）
    # ① 计算浮动值：floating_value = clamp(C × release_line_ratio, release_line_min, release_line_max)
    release_floating_value =
      calculate_floating_value(
        center_decimal,
        config.release_line_ratio || @default_floating_config.release_line_ratio,
        config.release_line_min || @default_floating_config.release_line_min,
        config.release_line_max || @default_floating_config.release_line_max
      )

    # ② 绝对放分线 = C + floating_value
    absolute_release_line = Decimal.add(center_decimal, release_floating_value)

    # ③ 前置放分线 = C + (floating_value × pre_release_line_ratio)
    pre_release_ratio =
      config.pre_release_line_ratio || @default_floating_config.pre_release_line_ratio

    pre_release_line =
      Decimal.add(center_decimal, Decimal.mult(release_floating_value, pre_release_ratio))

    # 2. 收分线计算逻辑（按文档五.2）
    # ① 计算浮动值：floating_value = clamp(C × collect_line_ratio, collect_line_min, collect_line_max)
    collect_floating_value =
      calculate_floating_value(
        center_decimal,
        config.collect_line_ratio || @default_floating_config.collect_line_ratio,
        config.collect_line_min || @default_floating_config.collect_line_min,
        config.collect_line_max || @default_floating_config.collect_line_max
      )

    # ② 绝对收分线 = C - floating_value
    absolute_collect_line = Decimal.sub(center_decimal, collect_floating_value)

    # ③ 前置收分线 = C - (floating_value × pre_collect_line_ratio)
    pre_collect_ratio =
      config.pre_collect_line_ratio || @default_floating_config.pre_collect_line_ratio

    pre_collect_line =
      Decimal.sub(center_decimal, Decimal.mult(collect_floating_value, pre_collect_ratio))

    %{
      # 中心线（Center Line）
      center_line: center_decimal,

      # 放分线组（基于文档二.术语定义）
      # 绝对放分线
      absolute_release_line: absolute_release_line,
      # 界面兼容性别名
      absolute_release: absolute_release_line,
      # 前置放分线
      pre_release_line: pre_release_line,
      # 界面兼容性别名
      pre_release: pre_release_line,

      # 收分线组
      # 绝对收分线
      absolute_collect_line: absolute_collect_line,
      # 界面兼容性别名
      absolute_collect: absolute_collect_line,
      # 前置收分线
      pre_collect_line: pre_collect_line,
      # 界面兼容性别名
      pre_collect: pre_collect_line,

      # 调试信息
      release_floating_value: release_floating_value,
      collect_floating_value: collect_floating_value
    }
  end

  # 计算浮动值：floating_value = clamp(center_line × ratio, min_value, max_value)
  defp calculate_floating_value(center_line, ratio, min_value, max_value) do
    center_decimal =
      if is_struct(center_line, Decimal), do: center_line, else: Decimal.new("#{center_line}")

    ratio_decimal = if is_struct(ratio, Decimal), do: ratio, else: Decimal.new("#{ratio}")
    min_decimal = Decimal.new("#{min_value}")
    max_decimal = Decimal.new("#{max_value}")

    # floating_value = center_line × ratio
    floating_value = Decimal.mult(center_decimal, ratio_decimal)

    # 应用clamp函数：clamp(value, min, max)
    clamp(floating_value, min_decimal, max_decimal)
  end

  # Clamp函数：将值限制在min和max之间
  defp clamp(value, min_value, max_value) do
    value
    |> Decimal.max(min_value)
    |> Decimal.min(max_value)
  end

  # 确定控制状态用于显示（不检查真实玩家）
  defp determine_control_status_for_display(current_inventory, control_lines, config) do
    current_inventory_decimal =
      if is_struct(current_inventory, Decimal),
        do: current_inventory,
        else: Decimal.new("#{current_inventory}")

    # 汇率换算：将库存从分转换为元，与控制线统一单位
    current_inventory_yuan = Decimal.div(current_inventory_decimal, Decimal.new("100"))

    cond do
      # 库存 <= 绝对收分线 → 绝对收分
      Decimal.compare(current_inventory_yuan, control_lines.absolute_collect_line) != :gt ->
        %{
          control_mode: :absolute_collect,
          target_result: :long,
          weight: @weight_config.absolute_collect
        }

      # 绝对收分线 < 库存 <= 前置收分线 → 前置收分
      Decimal.compare(current_inventory_yuan, control_lines.pre_collect_line) != :gt ->
        %{
          control_mode: :pre_collect,
          target_result: :long,
          weight: @weight_config.pre_collect
        }

      # 库存 >= 绝对放分线 → 绝对放分
      Decimal.compare(current_inventory_yuan, control_lines.absolute_release_line) != :lt ->
        %{
          control_mode: :absolute_release,
          target_result: :hu,
          weight: @weight_config.absolute_release
        }

      # 前置放分线 <= 库存 < 绝对放分线 → 前置放分
      Decimal.compare(current_inventory_yuan, control_lines.pre_release_line) != :lt ->
        %{
          control_mode: :pre_release,
          target_result: :hu,
          weight: @weight_config.pre_release
        }

      # 前置收分线 < 库存 < 前置放分线 → 随机
      true ->
        %{control_mode: :random, target_result: :random, weight: @weight_config.random}
    end
  end

  # 确定控制动作（严格按照文档实现）
  defp determine_control_action(current_inventory, control_lines, config, bet_data \\ %{}) do
    # 按文档定义的权重值机制进行控制决策
    # 前置收分权重值：1-1000中的一个数字，当库存达到前置收分线的时候，随机产生一个数，如果产生的这个数字小于这个收分权重值时，就执行收分，否则执行随机操作
    # 前置放分权重值：1-1000中的一个数字，当库存达到前置放分线的时候，随机产生一个数，如果产生的这个数字小于这个放分权重值时，就执行放分，否则执行随机操作

    current_inventory_decimal =
      if is_struct(current_inventory, Decimal),
        do: current_inventory,
        else: Decimal.new("#{current_inventory}")

    # 汇率换算：将库存从分转换为元，与控制线统一单位
    current_inventory_yuan = Decimal.div(current_inventory_decimal, Decimal.new("100"))

    # 检查是否有真实玩家参与下注
    has_real_players = has_real_player_bets(bet_data)

    cond do
      # 库存 <= 绝对收分线 → 检查真实玩家后决定是否强力收分
      Decimal.compare(current_inventory_yuan, control_lines.absolute_collect_line) != :gt ->
        if has_real_players do
          %{
            control_mode: :absolute_collect,
            target_result: get_longhu_collect_result(bet_data),
            weight: @weight_config.absolute_collect
          }
        else
          Logger.info("🎯 [CONTROL_LOGIC] 绝对收分线触发但无真实玩家，切换为随机模式")
          %{control_mode: :random, target_result: :random, weight: @weight_config.random}
        end

      # 绝对收分线 < 库存 <= 前置收分线 → 权重值判断
      Decimal.compare(current_inventory_yuan, control_lines.pre_collect_line) != :gt ->
        random_weight = :rand.uniform(1000)

        if random_weight <= @weight_config.pre_collect do
          if has_real_players do
            %{
              control_mode: :pre_collect,
              target_result: get_longhu_collect_result(bet_data),
              weight: @weight_config.pre_collect
            }
          else
            Logger.info("🎯 [CONTROL_LOGIC] 前置收分线触发但无真实玩家，切换为随机模式")
            %{control_mode: :random, target_result: :random, weight: @weight_config.random}
          end
        else
          %{control_mode: :random, target_result: :random, weight: @weight_config.random}
        end

      # 库存 >= 绝对放分线 → 检查真实玩家后决定是否强力放分
      Decimal.compare(current_inventory_yuan, control_lines.absolute_release_line) != :lt ->
        if has_real_players do
          %{
            control_mode: :absolute_release,
            target_result: get_longhu_release_result(bet_data),
            weight: @weight_config.absolute_release
          }
        else
          Logger.info("🎯 [CONTROL_LOGIC] 绝对放分线触发但无真实玩家，切换为随机模式")
          %{control_mode: :random, target_result: :random, weight: @weight_config.random}
        end

      # 前置放分线 <= 库存 < 绝对放分线 → 权重值判断
      Decimal.compare(current_inventory_yuan, control_lines.pre_release_line) != :lt ->
        random_weight = :rand.uniform(1000)

        if random_weight <= @weight_config.pre_release do
          if has_real_players do
            %{
              control_mode: :pre_release,
              target_result: get_longhu_release_result(bet_data),
              weight: @weight_config.pre_release
            }
          else
            Logger.info("🎯 [CONTROL_LOGIC] 前置放分线触发但无真实玩家，切换为随机模式")
            %{control_mode: :random, target_result: :random, weight: @weight_config.random}
          end
        else
          %{control_mode: :random, target_result: :random, weight: @weight_config.random}
        end

      # 前置收分线 < 库存 < 前置放分线 → 随机
      true ->
        %{control_mode: :random, target_result: :random, weight: @weight_config.random}
    end
  end

  # 获取龙虎游戏收分结果（根据玩家下注分布智能选择）
  # 收分策略：选择玩家下注最少的区域赢，让平台收取更多筹码
  defp get_longhu_collect_result(bet_data \\ %{}) do
    if map_size(bet_data) == 0 do
      # 如果没有下注数据，使用默认策略
      :long
    else
      # 分析真实玩家下注分布，选择下注最少的区域
      real_player_bets = filter_real_player_bets(bet_data)

      # 计算各区域的真实玩家下注总额
      long_total = get_bet_total(real_player_bets, :long)
      hu_total = get_bet_total(real_player_bets, :hu)
      he_total = get_bet_total(real_player_bets, :he)

      Logger.info("🎯 [COLLECT_STRATEGY] 真实玩家下注分布 - 龙:#{long_total}, 虎:#{hu_total}, 和:#{he_total}")

      # 选择下注最少的区域赢（让平台收更多分）
      cond do
        long_total <= hu_total and long_total <= he_total -> :long
        hu_total <= long_total and hu_total <= he_total -> :hu
        true -> :he
      end
    end
  end

  # 获取龙虎游戏放分结果（根据玩家下注分布智能选择）
  # 放分策略：选择玩家下注最多的区域赢，让玩家获得更多收益
  defp get_longhu_release_result(bet_data \\ %{}) do
    if map_size(bet_data) == 0 do
      # 如果没有下注数据，使用默认策略
      :hu
    else
      # 分析真实玩家下注分布，选择下注最多的区域
      real_player_bets = filter_real_player_bets(bet_data)

      # 计算各区域的真实玩家下注总额
      long_total = get_bet_total(real_player_bets, :long)
      hu_total = get_bet_total(real_player_bets, :hu)
      he_total = get_bet_total(real_player_bets, :he)

      Logger.info("🎯 [RELEASE_STRATEGY] 真实玩家下注分布 - 龙:#{long_total}, 虎:#{hu_total}, 和:#{he_total}")

      # 选择下注最多的区域赢（让玩家获得更多收益）
      cond do
        long_total >= hu_total and long_total >= he_total -> :long
        hu_total >= long_total and hu_total >= he_total -> :hu
        true -> :he
      end
    end
  end

  # 检查是否有真实玩家参与下注
  defp has_real_player_bets(bet_data) do
    case bet_data do
      %{long: long_bets, hu: hu_bets, he: he_bets} when is_list(long_bets) ->
        # 龙虎游戏格式：检查所有区域是否有真实玩家下注
        real_long_bets =
          Enum.any?(long_bets, fn {_user_id, _amount, is_robot} -> not is_robot end)

        real_hu_bets = Enum.any?(hu_bets, fn {_user_id, _amount, is_robot} -> not is_robot end)
        real_he_bets = Enum.any?(he_bets, fn {_user_id, _amount, is_robot} -> not is_robot end)

        has_real = real_long_bets or real_hu_bets or real_he_bets

        Logger.info(
          "🎯 [REAL_PLAYER_CHECK] 龙虎游戏真实玩家下注检查 - 龙:#{real_long_bets}, 虎:#{real_hu_bets}, 和:#{real_he_bets}, 总计:#{has_real}"
        )

        has_real

      %{player_info: player_info_map} when is_map(player_info_map) ->
        # JhandiMunda游戏格式：包含玩家信息的结构
        has_real =
          player_info_map
          |> Enum.any?(fn {_player_id, player_data} ->
            not Map.get(player_data, :is_robot, true)
          end)

        real_player_count =
          player_info_map
          |> Enum.count(fn {_player_id, player_data} ->
            not Map.get(player_data, :is_robot, true)
          end)

        Logger.info(
          "🎯 [REAL_PLAYER_CHECK] JhandiMunda游戏真实玩家下注检查 - 真实玩家数量:#{real_player_count}, 总计:#{has_real}"
        )

        has_real

      _ when map_size(bet_data) == 0 ->
        # 空的下注数据意味着没有玩家
        Logger.info("🎯 [REAL_PLAYER_CHECK] 无下注数据，判定为无真实玩家")
        false

      _ ->
        # 对于简化的下注数据格式（仅包含符号统计），我们需要检查游戏状态
        # 如果bet_data只包含符号总额而没有玩家信息，我们无法判断真实玩家
        # 在这种情况下，为了安全起见，假设有真实玩家（避免误杀真实玩家的控制）
        total_bet_amount =
          bet_data
          |> Map.values()
          |> Enum.reduce(0, fn amount, acc ->
            cond do
              is_number(amount) -> acc + amount
              is_struct(amount, Decimal) -> acc + Decimal.to_float(amount)
              true -> acc
            end
          end)

        # 如果总下注金额为0，判定为无真实玩家
        if total_bet_amount > 0 do
          Logger.warning("🎯 [REAL_PLAYER_CHECK] 简化下注数据格式，有下注活动，假设有真实玩家: 总下注=#{total_bet_amount}")
          true
        else
          Logger.info("🎯 [REAL_PLAYER_CHECK] 简化下注数据格式，无下注活动，判定为无真实玩家")
          false
        end
    end
  end

  # 过滤真实玩家下注（排除机器人）
  defp filter_real_player_bets(bet_data) do
    # 假设bet_data格式为：%{user_id => %{bet_area => amount, is_robot => boolean}}
    # 或者 %{long => [{user_id, amount, is_robot}], hu => [...], he => [...]}

    case bet_data do
      %{long: long_bets, hu: hu_bets, he: he_bets} when is_list(long_bets) ->
        # 处理按区域分组的下注数据
        %{
          long: Enum.filter(long_bets, fn {_user_id, _amount, is_robot} -> not is_robot end),
          hu: Enum.filter(hu_bets, fn {_user_id, _amount, is_robot} -> not is_robot end),
          he: Enum.filter(he_bets, fn {_user_id, _amount, is_robot} -> not is_robot end)
        }

      _ ->
        # 处理其他格式的下注数据
        # 这里可以根据实际的bet_data格式进行适配
        Logger.warning("🎯 [FILTER_BETS] 未识别的下注数据格式，使用原始数据")
        bet_data
    end
  end

  # 获取某个区域的下注总额
  defp get_bet_total(bet_data, area) do
    case Map.get(bet_data, area, []) do
      bets when is_list(bets) ->
        # 格式：[{user_id, amount, is_robot}, ...]
        bets
        |> Enum.map(fn {_user_id, amount, _is_robot} -> amount end)
        |> Enum.reduce(Decimal.new("0"), fn amount, acc ->
          amount_decimal =
            if is_struct(amount, Decimal), do: amount, else: Decimal.new("#{amount}")

          Decimal.add(acc, amount_decimal)
        end)

      amount when is_number(amount) ->
        Decimal.new("#{amount}")

      amount when is_struct(amount, Decimal) ->
        amount

      _ ->
        Decimal.new("0")
    end
  end

  # 确定龙虎斗控制模式（按照文档的控制线逻辑）
  defp determine_longhu_control_mode(current_inventory, config, game_state, bet_data \\ %{}) do
    # 计算当前控制线
    control_lines = calculate_control_lines(game_state.center_line, config)

    # 使用与主控制决策相同的逻辑
    control_decision =
      determine_control_action(current_inventory, control_lines, config, bet_data)

    # 根据控制决策转换为龙虎控制模式
    case control_decision.control_mode do
      :absolute_collect -> :collect
      :pre_collect -> :collect
      :absolute_release -> :release
      :pre_release -> :release
      :random -> :random
    end
  end

  # 确定Jhandi Munda控制模式（按照文档的控制线逻辑）
  defp determine_jhandi_munda_control_mode(current_inventory, config, game_state, bet_data \\ %{}) do
    # 计算当前控制线
    control_lines = calculate_control_lines(game_state.center_line, config)

    # 使用与主控制决策相同的逻辑
    control_decision =
      determine_control_action(current_inventory, control_lines, config, bet_data)

    # 根据控制决策转换为Jhandi Munda控制模式
    case control_decision.control_mode do
      :absolute_collect -> :collect
      :pre_collect -> :collect
      :absolute_release -> :release
      :pre_release -> :release
      :random -> :random
    end
  end

  # 计算库存变化（使用实际游戏数据）
  defp calculate_inventory_change(_result, game_data, _config) do
    # 使用从龙虎房间传来的实际平台盈利数据
    case Map.get(game_data, :total_platform_profit) do
      nil ->
        Logger.warn("🎮 [HUNDRED_PLAYER_CONTROL] 游戏数据中缺少total_platform_profit，使用默认值0")
        Decimal.new("0")

      profit when is_struct(profit, Decimal) ->
        Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] 平台盈利(Decimal): #{profit}")
        profit

      profit when is_number(profit) ->
        Logger.info("🎮 [HUNDRED_PLAYER_CONTROL] 平台盈利(Number): #{profit}")
        Decimal.new("#{profit}")

      _ ->
        Logger.warn(
          "🎮 [HUNDRED_PLAYER_CONTROL] total_platform_profit格式异常: #{inspect(Map.get(game_data, :total_platform_profit))}，使用默认值0"
        )

        Decimal.new("0")
    end
  end

  # 计算暗税（使用新的比例配置）
  # 根据用户澄清：
  # - 10：代表明税的 1%
  # - 100：代表明税的 10%
  # - -10：代表明税的 -1%，即中心线将上升
  # 注意：负值暗税导致中心线上升（系统偏向收分）
  defp calculate_dark_tax(inventory_change, dark_tax_rate) do
    # 确保使用 Decimal 进行精确计算
    ming_tax_decimal =
      if is_struct(inventory_change, Decimal),
        do: inventory_change,
        else: Decimal.new("#{inventory_change}")

    # 暗税比例转换为实际比例：例如 10 = 1%, 100 = 10%, -10 = -1%
    dark_tax_ratio_decimal = Decimal.div(Decimal.new("#{dark_tax_rate}"), Decimal.new("1000"))

    # 暗税 = 明税 × 暗税比例
    dark_tax = Decimal.mult(ming_tax_decimal, dark_tax_ratio_decimal)

    Logger.info(
      "🎮 [DARK_TAX] 暗税计算: 明税=#{ming_tax_decimal}, 暗税率=#{dark_tax_rate} (#{Decimal.mult(dark_tax_ratio_decimal, 100)}%), 暗税=#{dark_tax}"
    )

    dark_tax
  end

  # 更新中心线
  # 根据用户说明：中心线 = 上一局中心线 + 本局暗税
  # 中心线初始值默认为0
  # 注意：负暗税率产生负暗税，负暗税应该让中心线上升（收分倾向）
  defp update_center_line(current_center_line, dark_tax) do
    current_decimal =
      if is_struct(current_center_line, Decimal),
        do: current_center_line,
        else: Decimal.new("#{current_center_line}")

    dark_tax_decimal =
      if is_struct(dark_tax, Decimal), do: dark_tax, else: Decimal.new("#{dark_tax}")

    # 修复：负暗税应该让中心线上升，所以用减法
    # 如果暗税为负值，减去负值 = 加上正值，中心线上升
    new_center_line = Decimal.sub(current_decimal, dark_tax_decimal)

    Logger.info(
      "🎮 [CENTER_LINE] 中心线更新: #{current_center_line} - (#{dark_tax}) = #{new_center_line}"
    )

    new_center_line
  end

  # 记录游戏历史
  defp record_game_history(game_id, game_type, result, inventory_change, new_inventory, game_data) do
    # 这里可以记录到专门的历史表中
    Logger.info(
      "🎮 [HUNDRED_PLAYER_CONTROL] 游戏历史: game_id=#{game_id}, type=#{game_type}, result=#{result}, change=#{inventory_change}, inventory=#{new_inventory}"
    )

    # 可以选择性地记录详细数据
    Logger.debug("🎮 [HUNDRED_PLAYER_CONTROL] 游戏数据: #{inspect(game_data)}")
  end

  # 获取游戏当前库存（所有玩家的总输赢值的总和）
  # 正值：玩家总体输钱，平台盈利
  # 负值：玩家总体赢钱，平台亏损
  # 0值：盈亏平衡
  defp get_game_current_inventory(game_id) do
    # 生成游戏账户标识符
    game_account_identifier = AccountIdentifier.game(game_id)

    Logger.debug("🎮 [HUNDRED_PLAYER_CONTROL] 获取游戏当前库存: identifier=#{game_account_identifier}")

    case BalanceCache.get_balance(game_account_identifier) do
      {:ok, balance} ->
        Logger.debug("🎮 [HUNDRED_PLAYER_CONTROL] 游戏当前库存（玩家总输赢）: #{balance}")
        balance

      {:error, reason} ->
        Logger.warn("🎮 [HUNDRED_PLAYER_CONTROL] 获取游戏当前库存失败: #{inspect(reason)}, 使用默认值0")
        Decimal.new("0")
    end
  end

  # 获取游戏名称
  defp get_game_name(:longhu), do: "龙虎斗"
  defp get_game_name(:jhandi_munda), do: "骰子游戏"
  defp get_game_name(:crash), do: "崩溃游戏"
  defp get_game_name(_), do: "未知游戏"
end
