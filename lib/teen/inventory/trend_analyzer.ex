defmodule Teen.Resources.Inventory.TrendAnalyzer do
  @moduledoc """
  钱包趋势分析器

  分析游戏钱包的历史趋势，包括：
  - 短期趋势（1小时）
  - 中期趋势（6小时）
  - 长期趋势（24小时）
  - 波动率计算
  - 大额玩家影响检测
  - 连续下降检测
  """

  require Logger
  alias Teen.Resources.Inventory.WalletControl

  @doc """
  分析游戏钱包趋势
  """
  def analyze_game_wallet_trend(game_id) do
    try do
      # 1. 获取钱包历史数据
      # 最近24小时
      wallet_history = get_wallet_history(game_id, 24)

      if length(wallet_history) < 2 do
        Logger.warn("游戏 #{game_id} 钱包历史数据不足")
        return_default_trend(game_id)
      else
        # 2. 计算各时间段趋势
        # 1小时
        short_trend = calculate_trend(wallet_history, 1)
        # 6小时
        medium_trend = calculate_trend(wallet_history, 6)
        # 24小时
        long_trend = calculate_trend(wallet_history, 24)

        # 3. 计算波动率
        volatility = calculate_volatility(wallet_history)

        # 4. 获取当前余额
        current_balance = get_current_balance(game_id)

        # 5. 检测大额玩家影响
        large_player_impact = detect_large_player_impact(wallet_history, game_id)

        %{
          game_id: game_id,
          short_trend: short_trend,
          medium_trend: medium_trend,
          long_trend: long_trend,
          volatility: volatility,
          current_balance: current_balance,
          large_player_impact: large_player_impact,
          data_points: length(wallet_history),
          calculated_at: DateTime.utc_now()
        }
      end
    rescue
      error ->
        Logger.error("钱包趋势分析失败: #{inspect(error)}")
        return_default_trend(game_id)
    end
  end

  # 计算指定时间段的趋势
  defp calculate_trend(wallet_history, hours) do
    # 获取指定时间段内的数据
    cutoff_time = DateTime.add(DateTime.utc_now(), -hours * 3600, :second)

    recent_data =
      wallet_history
      |> Enum.filter(fn snapshot ->
        DateTime.compare(snapshot.timestamp, cutoff_time) != :lt
      end)
      |> Enum.sort_by(& &1.timestamp, DateTime)

    if length(recent_data) < 2 do
      0
    else
      start_snapshot = List.first(recent_data)
      end_snapshot = List.last(recent_data)

      start_balance = start_snapshot.balance
      end_balance = end_snapshot.balance

      if start_balance != 0 do
        (end_balance - start_balance) / abs(start_balance) * 100
      else
        0
      end
    end
  end

  # 计算波动率（标准差）
  defp calculate_volatility(wallet_history) do
    if length(wallet_history) < 2 do
      0
    else
      balances = Enum.map(wallet_history, & &1.balance)
      mean_balance = Enum.sum(balances) / length(balances)

      variance =
        balances
        |> Enum.map(fn balance -> :math.pow(balance - mean_balance, 2) end)
        |> Enum.sum()
        |> Kernel./(length(balances))

      :math.sqrt(variance)
    end
  end

  # 检测大额玩家影响
  defp detect_large_player_impact(wallet_history, game_id) do
    if length(wallet_history) < 2 do
      %{impact_type: :insufficient_data, concentration_ratio: 0}
    else
      # 计算最近的余额变化
      latest = List.first(wallet_history)
      previous = Enum.at(wallet_history, 1)

      balance_change = latest.balance - previous.balance
      typical_change = calculate_typical_balance_change(wallet_history)

      # 检测是否为异常大额变化
      is_abnormal_change = abs(balance_change) > typical_change * 3

      # 分析变化的集中度
      concentration_ratio = analyze_transaction_concentration(game_id, latest.timestamp)

      impact_type = determine_impact_type(concentration_ratio, balance_change, is_abnormal_change)

      %{
        impact_type: impact_type,
        concentration_ratio: concentration_ratio,
        balance_change: balance_change,
        is_abnormal: is_abnormal_change,
        typical_change: typical_change
      }
    end
  end

  # 计算典型余额变化
  defp calculate_typical_balance_change(wallet_history) do
    if length(wallet_history) < 3 do
      # 默认值
      1000
    else
      changes =
        wallet_history
        |> Enum.chunk_every(2, 1, :discard)
        |> Enum.map(fn [current, previous] ->
          abs(current.balance - previous.balance)
        end)

      if length(changes) > 0 do
        Enum.sum(changes) / length(changes)
      else
        1000
      end
    end
  end

  # 分析交易集中度
  defp analyze_transaction_concentration(game_id, timestamp) do
    # TODO: 分析最近时间段内交易的集中度
    # 这里需要从交易记录中分析是否有少数大额交易
    # 临时返回中等集中度
    0.5
  end

  # 确定影响类型
  defp determine_impact_type(concentration_ratio, balance_change, is_abnormal) do
    cond do
      # 高度集中的大额输钱 (对平台有利)
      concentration_ratio > 0.7 and balance_change > 0 and is_abnormal ->
        :large_player_loss

      # 高度集中的大额赢钱 (对平台不利)  
      concentration_ratio > 0.7 and balance_change < 0 and is_abnormal ->
        :large_player_win

      # 分散的正常交易
      concentration_ratio < 0.3 ->
        :normal_distributed

      # 中等集中度
      true ->
        :moderate_concentration
    end
  end

  @doc """
  分析多天连续下降情况
  """
  def analyze_multi_day_decline(game_id) do
    try do
      # 获取最近7天的每日数据
      daily_trends = get_daily_trends(game_id, 7)

      if length(daily_trends) < 2 do
        %{
          consecutive_days: 0,
          cumulative_decline: 0,
          severity: :normal,
          daily_trends: []
        }
      else
        # 计算连续下降天数
        consecutive_days = count_consecutive_decline_days(daily_trends)

        # 计算累积下降幅度
        cumulative_decline = calculate_cumulative_decline(daily_trends, consecutive_days)

        # 确定严重程度
        severity = determine_decline_severity(consecutive_days, cumulative_decline)

        %{
          consecutive_days: consecutive_days,
          cumulative_decline: cumulative_decline,
          severity: severity,
          daily_trends: daily_trends
        }
      end
    rescue
      error ->
        Logger.error("多天下降分析失败: #{inspect(error)}")
        %{consecutive_days: 0, cumulative_decline: 0, severity: :normal, daily_trends: []}
    end
  end

  # 获取每日趋势数据
  defp get_daily_trends(game_id, days) do
    # 获取每日的钱包快照
    daily_snapshots = get_daily_wallet_snapshots(game_id, days)

    daily_snapshots
    |> Enum.chunk_every(2, 1, :discard)
    |> Enum.map(fn [today, yesterday] ->
      if yesterday.balance != 0 do
        (today.balance - yesterday.balance) / abs(yesterday.balance) * 100
      else
        0
      end
    end)
  end

  # 计算连续下降天数
  defp count_consecutive_decline_days(daily_trends) do
    daily_trends
    # 从最近的开始计算
    |> Enum.reverse()
    |> Enum.reduce_while(0, fn trend, acc ->
      if trend < 0 do
        {:cont, acc + 1}
      else
        {:halt, acc}
      end
    end)
  end

  # 计算累积下降幅度
  defp calculate_cumulative_decline(daily_trends, consecutive_days) do
    if consecutive_days > 0 do
      daily_trends
      |> Enum.take(consecutive_days)
      |> Enum.sum()
    else
      0
    end
  end

  # 确定下降严重程度
  defp determine_decline_severity(consecutive_days, cumulative_decline) do
    cond do
      # 连续3天以上下降，累积超过-10%：危险
      consecutive_days >= 3 and cumulative_decline < -10 ->
        :critical

      # 连续2-3天下降，累积超过-6%：严重
      consecutive_days >= 2 and cumulative_decline < -6 ->
        :severe

      # 连续2天下降，累积超过-3%：需要关注
      consecutive_days >= 2 and cumulative_decline < -3 ->
        :concerning

      # 其他情况：正常监控
      true ->
        :normal
    end
  end

  # 返回默认趋势数据
  defp return_default_trend(game_id) do
    %{
      game_id: game_id,
      short_trend: 0,
      medium_trend: 0,
      long_trend: 0,
      volatility: 0,
      current_balance: 0,
      large_player_impact: %{impact_type: :insufficient_data, concentration_ratio: 0},
      data_points: 0,
      calculated_at: DateTime.utc_now()
    }
  end

  # 占位函数 - 需要实现具体的数据获取逻辑
  defp get_wallet_history(game_id, hours) do
    # TODO: 从钱包快照表获取历史数据
    []
  end

  defp get_current_balance(game_id) do
    # TODO: 获取当前钱包余额
    case WalletControl.get_current_balance(game_id) do
      {:ok, balance} -> balance
      _ -> 0
    end
  end

  defp get_daily_wallet_snapshots(game_id, days) do
    # TODO: 获取每日钱包快照
    []
  end
end
