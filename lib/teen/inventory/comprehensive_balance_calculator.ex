defmodule Teen.Resources.Inventory.ComprehensiveBalanceCalculator do
  @moduledoc """
  综合余额计算器 - 考虑钱包和奖池的完整盈亏计算

  解决单人游戏（如 SlotCat）中奖池和钱包分离导致的放分/收分计算不准确问题。

  ## 核心功能
  - 计算游戏的综合余额（钱包余额 + 奖池偏差）
  - 基于综合余额提供更准确的概率调整建议

  ## 计算逻辑
  综合余额 = 钱包余额 + 奖池偏差
  奖池偏差 = Σ(奖池当前余额 - 奖池基础金额)

  ## 使用示例
  ```elixir
  # 获取综合概率调整建议（替代原有方法）
  adjustment = ComprehensiveBalanceCalculator.get_comprehensive_probability_adjustment(42, 1)
  ```
  """

  require Logger
  alias Teen.Resources.Inventory.{WalletControl, ProbabilityAdjustment}
  alias Teen.GameSystem.JackpotManager

  @doc """
  获取游戏的综合余额

  ## 参数
  - game_id: 游戏ID

  ## 返回值
  - {:ok, comprehensive_balance} - 综合余额（包含钱包和奖池影响）
  - {:error, reason} - 错误信息

  ## 计算公式
  综合余额 = 钱包余额 + 奖池偏差
  奖池偏差 = Σ(奖池当前余额 - 奖池基础金额)
  """
  def get_comprehensive_balance(game_id) do
    with {:ok, wallet_balance} <- WalletControl.get_current_balance(game_id),
         jackpot_deviation <- calculate_jackpot_deviation(game_id) do
      # 确保数值安全
      safe_wallet = ensure_number(wallet_balance)
      safe_deviation = ensure_number(jackpot_deviation)
      comprehensive_balance = safe_wallet + safe_deviation

      Logger.debug("""
      🧮 [COMPREHENSIVE_BALANCE] 游戏 #{game_id} 综合余额计算:
      - 钱包余额: #{safe_round(safe_wallet, 2)}
      - 奖池偏差: #{safe_round(safe_deviation, 2)}
      - 综合余额: #{safe_round(comprehensive_balance, 2)}
      """)

      {:ok, comprehensive_balance}
    else
      error -> error
    end
  end

  @doc """
  获取综合概率调整建议

  基于综合余额（钱包 + 奖池）计算概率调整，比单纯基于钱包余额更准确

  ## 参数
  - game_id: 游戏ID
  - game_class_type: 游戏类型 (1=单人, 2=多人, 3=百人)

  ## 返回值
  与 ProbabilityAdjustment.get_quick_adjustment 相同的格式
  """
  def get_comprehensive_probability_adjustment(game_id, game_class_type) do
    case get_comprehensive_balance(game_id) do
      {:ok, comprehensive_balance} ->
        # 使用综合余额计算概率调整
        result =
          calculate_probability_adjustment_with_balance(
            game_id,
            game_class_type,
            comprehensive_balance
          )

        Logger.debug("""
        🎯 [COMPREHENSIVE_ADJUSTMENT] 游戏 #{game_id} 综合概率调整:
        - 建议值: #{result.suggestion}
        - 综合余额: #{safe_round(comprehensive_balance, 2)}
        - 描述: #{result.description}
        """)

        result

      {:error, reason} ->
        Logger.warning("⚠️ [COMPREHENSIVE_ADJUSTMENT] 获取综合余额失败，回退到钱包余额: #{inspect(reason)}")
        # 回退到原始方法
        ProbabilityAdjustment.get_quick_adjustment(game_id, game_class_type)
    end
  end

  @doc """
  计算奖池偏差

  ## 计算逻辑
  对于每个奖池：
  偏差 = 当前余额 - 基础金额

  正值表示奖池增加（玩家贡献多于中奖支出）
  负值表示奖池减少（中奖支出多于玩家贡献）
  """
  def calculate_jackpot_deviation(game_id) do
    try do
      # 获取游戏的所有奖池配置
      jackpot_configs = get_game_jackpot_configs(game_id)

      if Enum.empty?(jackpot_configs) do
        # 没有奖池的游戏，偏差为0
        0.0
      else
        total_deviation =
          jackpot_configs
          |> Enum.map(&calculate_single_jackpot_deviation/1)
          # 过滤掉非数字值
          |> Enum.filter(&is_number/1)
          |> Enum.sum()

        ensure_number(total_deviation)
      end
    rescue
      error ->
        Logger.error("计算奖池偏差失败: #{inspect(error)}")
        0.0
    end
  end

  # 获取游戏的奖池配置列表
  defp get_game_jackpot_configs(game_id) do
    case game_id do
      # SlotCat
      42 ->
        [
          %{game_id: 42, jackpot_id: :center, base_amount: get_default_base_amount(42, :center)},
          %{game_id: 42, jackpot_id: :left, base_amount: get_default_base_amount(42, :left)},
          %{game_id: 42, jackpot_id: :right, base_amount: get_default_base_amount(42, :right)}
        ]

      # Slot777
      40 ->
        [
          %{game_id: 40, jackpot_id: :main, base_amount: get_default_base_amount(40, :main)}
        ]

      # SlotNiu
      41 ->
        [
          %{game_id: 41, jackpot_id: :jackpot, base_amount: get_default_base_amount(41, :jackpot)}
        ]

      _ ->
        # 其他游戏没有奖池
        []
    end
  end

  # 私有函数

  defp calculate_single_jackpot_deviation(%{
         game_id: game_id,
         jackpot_id: jackpot_id,
         base_amount: base_amount
       }) do
    current_balance = JackpotManager.get_jackpot_balance(game_id, jackpot_id)

    # 确保数值安全
    safe_current = ensure_number(current_balance)
    safe_base = ensure_number(base_amount)
    deviation = safe_current - safe_base

    Logger.debug(
      "🎰 [JACKPOT_DEVIATION] #{game_id}:#{jackpot_id} - 当前: #{safe_current}, 基础: #{safe_base}, 偏差: #{deviation}"
    )

    deviation
  end

  # 默认基础金额配置
  defp get_default_base_amount(game_id, jackpot_id) do
    defaults = %{
      # SlotCat 中心奖池 ₹2500
      {42, :center} => 250_000,
      # SlotCat 左奖池 ₹1000
      {42, :left} => 100_000,
      # SlotCat 右奖池 ₹1000
      {42, :right} => 100_000,
      # Slot777 主奖池 ₹5000
      {40, :main} => 500_000,
      # SlotNiu 奖池 ₹3000
      {41, :jackpot} => 300_000
    }

    Map.get(defaults, {game_id, jackpot_id}, 0)
  end

  defp calculate_probability_adjustment_with_balance(
         game_id,
         game_class_type,
         comprehensive_balance
       ) do
    # 基于现有系统的趋势分析方法，但使用综合余额
    try do
      # 1. 获取综合余额的历史趋势数据
      comprehensive_trend_data = get_comprehensive_trend_data(game_id)

      # 2. 获取游戏配置
      {:ok, config} = WalletControl.get_wallet_config(game_id)

      # 3. 使用现有的快速建议计算逻辑，但基于综合数据
      suggestion =
        calculate_comprehensive_fast_suggestion(
          comprehensive_trend_data,
          config,
          game_class_type,
          comprehensive_balance
        )

      description = generate_description(suggestion, comprehensive_balance)

      %{
        success: true,
        suggestion: suggestion,
        description: description,
        calculation_time: DateTime.utc_now()
      }
    rescue
      error ->
        Logger.error("综合概率调整计算失败: #{inspect(error)}")

        %{
          success: false,
          suggestion: 0,
          description: "计算失败，使用默认值",
          calculation_time: DateTime.utc_now()
        }
    end
  end

  # 获取综合余额的趋势数据（模拟现有的 get_trend_data 逻辑）
  defp get_comprehensive_trend_data(game_id) do
    try do
      # 获取钱包历史数据
      wallet_history =
        case WalletControl.get_game_balance_history(game_id, :hour) do
          # 最近10个数据点
          {:ok, history} -> Enum.take(history, -10)
          _ -> []
        end

      # 计算综合余额历史（钱包 + 奖池偏差）
      comprehensive_history =
        wallet_history
        |> Enum.map(fn wallet_point ->
          # 对于历史数据，我们假设奖池偏差相对稳定
          # 实际项目中可以存储奖池历史数据
          current_jackpot_deviation = calculate_jackpot_deviation(game_id)
          comprehensive_balance = wallet_point.balance + current_jackpot_deviation

          %{
            timestamp: wallet_point.timestamp,
            balance: comprehensive_balance,
            wallet_balance: wallet_point.balance,
            jackpot_deviation: current_jackpot_deviation
          }
        end)

      # 计算趋势指标（参考 TrendAnalyzer 的逻辑）
      %{
        current_balance: List.first(comprehensive_history)[:balance] || 0,
        recent_trend: calculate_recent_trend(comprehensive_history),
        volatility: calculate_balance_volatility(comprehensive_history),
        data_points: length(comprehensive_history),
        has_sufficient_data: length(comprehensive_history) >= 3
      }
    rescue
      error ->
        Logger.error("获取综合趋势数据失败: #{inspect(error)}")

        %{
          current_balance: 0,
          recent_trend: 0,
          volatility: 0,
          data_points: 0,
          has_sufficient_data: false
        }
    end
  end

  # 基于综合数据计算快速建议（参考现有的 calculate_fast_suggestion 逻辑）
  defp calculate_comprehensive_fast_suggestion(trend_data, config, game_type, current_balance) do
    if not trend_data.has_sufficient_data do
      # 数据不足时，基于当前综合余额做简单判断
      calculate_simple_balance_suggestion(current_balance, config)
    else
      # 有足够数据时，基于趋势分析
      calculate_trend_based_suggestion(trend_data, config, game_type)
    end
  end

  # 简单的余额建议（数据不足时使用）
  defp calculate_simple_balance_suggestion(balance, config) do
    balance_in_wan = ensure_number(balance) / 10000

    cond do
      balance_in_wan < -2.0 ->
        # 严重亏损，强烈收分
        min(8, max(3, round(abs(balance_in_wan))))

      balance_in_wan < -0.5 ->
        # 轻微亏损，适度收分
        min(3, max(1, round(abs(balance_in_wan) * 2)))

      balance_in_wan > 3.0 ->
        # 大幅盈利，强烈放分
        -min(8, max(3, round(balance_in_wan / 2)))

      balance_in_wan > 1.0 ->
        # 适度盈利，轻微放分
        -min(3, max(1, round(balance_in_wan)))

      true ->
        # 平衡状态
        0
    end
  end

  # 基于趋势的建议（有足够数据时使用）
  defp calculate_trend_based_suggestion(trend_data, config, game_type) do
    recent_trend = ensure_number(trend_data.recent_trend)
    current_balance = ensure_number(trend_data.current_balance)
    volatility = ensure_number(trend_data.volatility)

    # 趋势权重（趋势越明显，权重越大）
    # 标准化趋势权重
    trend_weight = min(1.0, abs(recent_trend) / 10000)

    # 余额权重（余额偏离越大，权重越大）
    # 标准化余额权重
    balance_weight = min(1.0, abs(current_balance) / 50000)

    # 综合建议计算
    trend_suggestion =
      cond do
        recent_trend < -5000 ->
          # 下降趋势，需要收分
          round(trend_weight * 5) + 1

        recent_trend > 5000 ->
          # 上升趋势，可以放分
          -(round(trend_weight * 5) + 1)

        true ->
          0
      end

    balance_suggestion = calculate_simple_balance_suggestion(current_balance, config)

    # 综合两个建议，趋势为主，余额为辅
    final_suggestion = round(trend_suggestion * 0.7 + balance_suggestion * 0.3)

    # 限制在 -10 到 +10 范围内
    max(-10, min(10, final_suggestion))
  end

  # 计算最近趋势（参考 TrendAnalyzer）
  defp calculate_recent_trend(history) when length(history) < 2, do: 0

  defp calculate_recent_trend(history) do
    sorted_history = Enum.sort_by(history, & &1.timestamp, DateTime)
    latest = List.last(sorted_history)
    earliest = List.first(sorted_history)

    if latest && earliest do
      latest.balance - earliest.balance
    else
      0
    end
  end

  # 计算余额波动率
  defp calculate_balance_volatility(history) when length(history) < 2, do: 0

  defp calculate_balance_volatility(history) do
    balances = Enum.map(history, & &1.balance)
    mean_balance = Enum.sum(balances) / length(balances)

    variance =
      balances
      |> Enum.map(fn balance -> :math.pow(balance - mean_balance, 2) end)
      |> Enum.sum()
      |> Kernel./(length(balances))

    :math.sqrt(variance)
  end

  defp generate_description(suggestion, balance) do
    balance_desc = "综合余额: ₹#{safe_round(balance / 100, 2)}"

    cond do
      suggestion > 0 ->
        "建议收分#{suggestion}% (#{balance_desc})"

      suggestion < 0 ->
        "建议放分#{abs(suggestion)}% (#{balance_desc})"

      true ->
        "保持正常 (#{balance_desc})"
    end
  end

  # 安全的数字格式化函数
  defp safe_round(value, precision) when is_number(value) do
    Float.round(value * 1.0, precision)
  rescue
    _ -> "#{value}"
  end

  defp safe_round(value, _precision) do
    "#{value}"
  end

  # 确保值是数字
  defp ensure_number(value) when is_number(value), do: value
  defp ensure_number(_), do: 0.0
end
