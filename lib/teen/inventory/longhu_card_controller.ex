defmodule Teen.Inventory.LonghuCardController do
  @moduledoc """
  龙虎斗卡牌控制模块

  实现龙虎斗游戏的收放分控制逻辑：
  1. 随机生成龙虎两张牌
  2. 计算初始结果和库存变化
  3. 根据控制策略决定是否交换牌
  4. 根据权重配置调整最终结果
  """

  require Logger

  # 扑克牌定义：点数1-13，花色1-4
  @card_ranks 1..13 |> Enum.to_list()
  @card_suits 1..4 |> Enum.to_list()

  # 龙虎斗结果类型
  @result_types [:long, :hu, :he]

  @doc """
  根据控制策略和权重配置生成龙虎斗结果

  ## 参数
  - bet_data: 投注数据 %{long: 总龙投注, hu: 总虎投注, he: 总和投注}
  - control_mode: 控制模式 (:random, :collect, :release)
  - weight_config: 权重配置 %{long_weight: 龙权重, hu_weight: 虎权重, he_weight: 和权重}

  ## 返回
  %{
    result: :long | :hu | :he,
    long_card: {rank, suit},
    hu_card: {rank, suit},
    inventory_change: 库存变化,
    control_applied: 是否应用了控制
  }
  """
  def generate_controlled_result(bet_data, control_mode, weight_config) do
    Logger.info("🎮 [LONGHU_CONTROL] 生成控制结果: mode=#{control_mode}, bets=#{inspect(bet_data)}")

    case control_mode do
      :random ->
        generate_weighted_result(bet_data, weight_config)

      :collect ->
        generate_collect_result(bet_data, weight_config)

      :release ->
        generate_release_result(bet_data, weight_config)

      _ ->
        Logger.warn("🎮 [LONGHU_CONTROL] 未知控制模式: #{control_mode}，使用随机模式")
        generate_weighted_result(bet_data, weight_config)
    end
  end

  # 生成加权随机结果
  defp generate_weighted_result(bet_data, weight_config) do
    # 根据权重配置随机选择结果
    result = weighted_random_result(weight_config)

    # 生成对应的卡牌
    {long_card, hu_card} = generate_cards_for_result(result)

    # 计算库存变化
    inventory_change = calculate_inventory_change(bet_data, result)

    Logger.info("🎮 [LONGHU_CONTROL] 随机结果: #{result}, 库存变化: #{inventory_change}")

    %{
      result: result,
      long_card: long_card,
      hu_card: hu_card,
      inventory_change: inventory_change,
      control_applied: false
    }
  end

  # 生成收分结果
  defp generate_collect_result(bet_data, weight_config) do
    # 首先随机生成两张牌
    long_card = random_card()
    hu_card = random_card()

    # 计算初始结果
    initial_result = compare_cards(long_card, hu_card)

    # 计算初始库存变化
    initial_inventory_change = calculate_inventory_change(bet_data, initial_result)

    Logger.debug(
      "🎮 [LONGHU_CONTROL] 收分模式 - 初始结果: #{initial_result}, 初始库存变化: #{initial_inventory_change}"
    )

    # 如果库存增加，满足收分需求，不做处理
    if initial_inventory_change >= 0 do
      Logger.info("🎮 [LONGHU_CONTROL] 收分模式 - 库存增加，不需要调整")

      %{
        result: initial_result,
        long_card: long_card,
        hu_card: hu_card,
        inventory_change: initial_inventory_change,
        control_applied: false
      }
    else
      # 如果库存减少，需要交换牌或调整结果
      Logger.info("🎮 [LONGHU_CONTROL] 收分模式 - 库存减少，需要调整")
      adjust_for_collect(bet_data, long_card, hu_card, weight_config)
    end
  end

  # 生成放分结果
  defp generate_release_result(bet_data, weight_config) do
    # 首先随机生成两张牌
    long_card = random_card()
    hu_card = random_card()

    # 计算初始结果
    initial_result = compare_cards(long_card, hu_card)

    # 计算初始库存变化
    initial_inventory_change = calculate_inventory_change(bet_data, initial_result)

    Logger.debug(
      "🎮 [LONGHU_CONTROL] 放分模式 - 初始结果: #{initial_result}, 初始库存变化: #{initial_inventory_change}"
    )

    # 如果库存减少，满足放分需求，不做处理
    if initial_inventory_change <= 0 do
      Logger.info("🎮 [LONGHU_CONTROL] 放分模式 - 库存减少，不需要调整")

      %{
        result: initial_result,
        long_card: long_card,
        hu_card: hu_card,
        inventory_change: initial_inventory_change,
        control_applied: false
      }
    else
      # 如果库存增加，需要交换牌或调整结果
      Logger.info("🎮 [LONGHU_CONTROL] 放分模式 - 库存增加，需要调整")
      adjust_for_release(bet_data, long_card, hu_card, weight_config)
    end
  end

  # 调整卡牌以实现收分
  defp adjust_for_collect(bet_data, long_card, hu_card, weight_config) do
    # 尝试交换两张牌
    swapped_result = compare_cards(hu_card, long_card)
    swapped_inventory_change = calculate_inventory_change(bet_data, swapped_result)

    if swapped_inventory_change >= 0 do
      Logger.info("🎮 [LONGHU_CONTROL] 收分调整 - 交换牌成功")

      %{
        result: swapped_result,
        long_card: hu_card,
        hu_card: long_card,
        inventory_change: swapped_inventory_change,
        control_applied: true
      }
    else
      # 如果交换也无法满足收分，选择最优收分结果
      Logger.info("🎮 [LONGHU_CONTROL] 收分调整 - 交换牌仍不满足，选择最优结果")
      find_optimal_collect_result(bet_data, weight_config)
    end
  end

  # 调整卡牌以实现放分
  defp adjust_for_release(bet_data, long_card, hu_card, weight_config) do
    # 尝试交换两张牌
    swapped_result = compare_cards(hu_card, long_card)
    swapped_inventory_change = calculate_inventory_change(bet_data, swapped_result)

    if swapped_inventory_change <= 0 do
      Logger.info("🎮 [LONGHU_CONTROL] 放分调整 - 交换牌成功")

      %{
        result: swapped_result,
        long_card: hu_card,
        hu_card: long_card,
        inventory_change: swapped_inventory_change,
        control_applied: true
      }
    else
      # 如果交换也无法满足放分，选择最优放分结果
      Logger.info("🎮 [LONGHU_CONTROL] 放分调整 - 交换牌仍不满足，选择最优结果")
      find_optimal_release_result(bet_data, weight_config)
    end
  end

  # 找到最优收分结果
  defp find_optimal_collect_result(bet_data, weight_config) do
    # 计算所有可能结果的库存变化
    results =
      Enum.map(@result_types, fn result_type ->
        inventory_change = calculate_inventory_change(bet_data, result_type)
        {result_type, inventory_change}
      end)

    # 选择库存变化最大的结果（最有利于收分）
    {optimal_result, optimal_change} = Enum.max_by(results, fn {_, change} -> change end)

    # 生成对应的卡牌
    {long_card, hu_card} = generate_cards_for_result(optimal_result)

    Logger.info("🎮 [LONGHU_CONTROL] 最优收分结果: #{optimal_result}, 库存变化: #{optimal_change}")

    %{
      result: optimal_result,
      long_card: long_card,
      hu_card: hu_card,
      inventory_change: optimal_change,
      control_applied: true
    }
  end

  # 找到最优放分结果
  defp find_optimal_release_result(bet_data, weight_config) do
    # 计算所有可能结果的库存变化
    results =
      Enum.map(@result_types, fn result_type ->
        inventory_change = calculate_inventory_change(bet_data, result_type)
        {result_type, inventory_change}
      end)

    # 选择库存变化最小的结果（最有利于放分）
    {optimal_result, optimal_change} = Enum.min_by(results, fn {_, change} -> change end)

    # 生成对应的卡牌
    {long_card, hu_card} = generate_cards_for_result(optimal_result)

    Logger.info("🎮 [LONGHU_CONTROL] 最优放分结果: #{optimal_result}, 库存变化: #{optimal_change}")

    %{
      result: optimal_result,
      long_card: long_card,
      hu_card: hu_card,
      inventory_change: optimal_change,
      control_applied: true
    }
  end

  # 根据权重配置随机选择结果
  defp weighted_random_result(weight_config) do
    total_weight = weight_config.long_weight + weight_config.hu_weight + weight_config.he_weight
    random_value = :rand.uniform(total_weight)

    cond do
      random_value <= weight_config.long_weight -> :long
      random_value <= weight_config.long_weight + weight_config.hu_weight -> :hu
      true -> :he
    end
  end

  # 生成随机卡牌
  defp random_card do
    rank = Enum.random(@card_ranks)
    suit = Enum.random(@card_suits)
    {rank, suit}
  end

  # 根据结果生成对应的卡牌
  defp generate_cards_for_result(:long) do
    # 龙赢：龙牌点数大于虎牌点数
    hu_rank = Enum.random(@card_ranks)
    long_rank = Enum.random((hu_rank + 1)..13)

    long_suit = Enum.random(@card_suits)
    hu_suit = Enum.random(@card_suits)

    {{long_rank, long_suit}, {hu_rank, hu_suit}}
  end

  defp generate_cards_for_result(:hu) do
    # 虎赢：虎牌点数大于龙牌点数
    long_rank = Enum.random(@card_ranks)
    hu_rank = Enum.random((long_rank + 1)..13)

    long_suit = Enum.random(@card_suits)
    hu_suit = Enum.random(@card_suits)

    {{long_rank, long_suit}, {hu_rank, hu_suit}}
  end

  defp generate_cards_for_result(:he) do
    # 和：两张牌点数相同
    rank = Enum.random(@card_ranks)
    long_suit = Enum.random(@card_suits)
    # 确保花色不同
    hu_suit = Enum.random(@card_suits -- [long_suit])

    {{rank, long_suit}, {rank, hu_suit}}
  end

  # 比较两张牌，返回结果
  defp compare_cards({long_rank, _long_suit}, {hu_rank, _hu_suit}) do
    cond do
      long_rank > hu_rank -> :long
      hu_rank > long_rank -> :hu
      true -> :he
    end
  end

  # 计算库存变化
  defp calculate_inventory_change(bet_data, result) do
    case result do
      :long ->
        # 龙赢：平台支付龙投注的赔付，收取虎和和的投注
        -bet_data.long + bet_data.hu + bet_data.he

      :hu ->
        # 虎赢：平台支付虎投注的赔付，收取龙和和的投注
        bet_data.long - bet_data.hu + bet_data.he

      :he ->
        # 和：平台支付和投注的赔付（通常1赔8），收取龙和虎的投注
        bet_data.long + bet_data.hu - bet_data.he * 8
    end
  end
end
