defmodule Teen.Resources.Inventory do
  @moduledoc """
  库存控制系统主模块

  提供游戏库存控制的统一接口，包括：
  - 钱包余额监控
  - 三线控制决策
  - 实时数据获取
  """

  alias Teen.Resources.Inventory.WalletControl

  @doc """
  获取游戏控制决策

  ## 参数
  - game_id: 游戏ID

  ## 返回值
  - {:ok, decision} - 成功返回决策
  - {:error, reason} - 失败返回错误
  """
  defdelegate get_control_decision(game_id), to: WalletControl

  @doc """
  获取游戏钱包余额
  """
  defdelegate get_game_balance(game_id), to: WalletControl, as: :get_current_balance

  @doc """
  获取所有游戏列表及其余额
  """
  def get_all_games_with_balance() do
    # 使用 WalletControl 的 get_enabled_game_ids 函数获取实际的游戏列表
    game_ids = WalletControl.get_enabled_game_ids()

    Enum.map(game_ids, fn game_id ->
      {:ok, balance} = WalletControl.get_current_balance(game_id)
      %{game_id: game_id, balance: balance}
    end)
  end

  @doc """
  获取总钱包余额
  """
  defdelegate get_total_balance(), to: WalletControl

  @doc """
  获取游戏配置
  """
  defdelegate get_game_config(game_id), to: WalletControl, as: :get_wallet_config

  @doc """
  获取钱包历史数据
  """
  defdelegate get_wallet_history(game_id, time_range \\ :hour), to: WalletControl

  @doc """
  获取总钱包历史数据
  """
  defdelegate get_total_wallet_history(time_range \\ :hour), to: WalletControl
end
