defmodule Teen.Inventory.JhandiMundaDiceController do
  @moduledoc """
  Jhandi Munda 骰子控制模块

  实现 Jhandi Munda 游戏的收放分控制逻辑：
  1. 根据控制策略生成骰子结果
  2. 计算平台盈亏变化
  3. 根据权重配置调整符号出现概率
  4. 根据收分/放分需求优化结果
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaConstants
  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaGameLogic

  # 游戏符号定义
  @symbols JhandiMundaConstants.get_all_symbols()
  @dice_count JhandiMundaConstants.dice_count()

  @doc """
  根据控制策略和权重配置生成骰子结果

  ## 参数
  - bet_data: 投注数据 %{symbol => total_bet_amount}
  - control_mode: 控制模式 (:random, :collect, :release)
  - weight_config: 权重配置 %{clubs_weight: 梅花权重, crown_weight: 皇冠权重, ...}

  ## 返回
  %{
    dice_results: [1, 2, 3, 4, 5, 6],  # 每个骰子的结果
    symbol_counts: %{1 => 2, 2 => 1, ...},  # 每个符号出现的次数
    winning_symbols: [1, 2, 3, 4, 5],  # 有中奖的符号列表
    inventory_change: 库存变化,
    control_applied: 是否应用了控制
  }
  """
  def generate_controlled_result(bet_data, control_mode, weight_config) do
    Logger.info(
      "🎮 [JHANDI_MUNDA_CONTROL] 生成控制结果: mode=#{control_mode}, bets=#{inspect(bet_data)}"
    )

    case control_mode do
      :random ->
        generate_weighted_result(bet_data, weight_config)

      :collect ->
        generate_collect_result(bet_data, weight_config)

      :release ->
        generate_release_result(bet_data, weight_config)

      _ ->
        Logger.warning("🎮 [JHANDI_MUNDA_CONTROL] 未知控制模式: #{control_mode}，使用随机模式")
        generate_weighted_result(bet_data, weight_config)
    end
  end

  # 生成加权随机结果
  defp generate_weighted_result(bet_data, weight_config) do
    # 根据权重配置生成骰子结果
    dice_results = generate_weighted_dice(weight_config)

    # 处理游戏结果
    game_result = process_dice_results(dice_results)

    # 计算库存变化
    inventory_change = calculate_inventory_change(bet_data, game_result)

    Logger.info(
      "🎮 [JHANDI_MUNDA_CONTROL] 随机结果: #{inspect(game_result.symbol_counts)}, 库存变化: #{inventory_change}"
    )

    Map.merge(game_result, %{
      inventory_change: inventory_change,
      control_applied: false
    })
  end

  # 生成收分结果
  defp generate_collect_result(bet_data, weight_config) do
    # 首先随机生成骰子结果
    initial_dice = generate_weighted_dice(weight_config)
    initial_result = process_dice_results(initial_dice)

    # 计算初始库存变化
    initial_inventory_change = calculate_inventory_change(bet_data, initial_result)

    Logger.debug(
      "🎮 [JHANDI_MUNDA_CONTROL] 收分模式 - 初始结果: #{inspect(initial_result.symbol_counts)}, 初始库存变化: #{initial_inventory_change}"
    )

    # 如果库存增加，满足收分需求，不做处理
    if initial_inventory_change >= 0 do
      Logger.info("🎮 [JHANDI_MUNDA_CONTROL] 收分模式 - 库存增加，不需要调整")

      Map.merge(initial_result, %{
        inventory_change: initial_inventory_change,
        control_applied: false
      })
    else
      # 如果库存减少，需要调整结果
      Logger.info("🎮 [JHANDI_MUNDA_CONTROL] 收分模式 - 库存减少，需要调整")
      adjust_for_collect(bet_data, weight_config)
    end
  end

  # 生成放分结果
  defp generate_release_result(bet_data, weight_config) do
    # 首先随机生成骰子结果
    initial_dice = generate_weighted_dice(weight_config)
    initial_result = process_dice_results(initial_dice)

    # 计算初始库存变化
    initial_inventory_change = calculate_inventory_change(bet_data, initial_result)

    Logger.debug(
      "🎮 [JHANDI_MUNDA_CONTROL] 放分模式 - 初始结果: #{inspect(initial_result.symbol_counts)}, 初始库存变化: #{initial_inventory_change}"
    )

    # 如果库存减少，满足放分需求，不做处理
    if initial_inventory_change <= 0 do
      Logger.info("🎮 [JHANDI_MUNDA_CONTROL] 放分模式 - 库存减少，不需要调整")

      Map.merge(initial_result, %{
        inventory_change: initial_inventory_change,
        control_applied: false
      })
    else
      # 如果库存增加，需要调整结果
      Logger.info("🎮 [JHANDI_MUNDA_CONTROL] 放分模式 - 库存增加，需要调整")
      adjust_for_release(bet_data, weight_config)
    end
  end

  # 调整结果以实现收分
  defp adjust_for_collect(bet_data, weight_config) do
    # 尝试生成多个结果，选择最有利于收分的
    attempts = 1..20

    best_result =
      Enum.map(attempts, fn _ ->
        dice_results = generate_weighted_dice(weight_config)
        game_result = process_dice_results(dice_results)
        inventory_change = calculate_inventory_change(bet_data, game_result)

        Map.merge(game_result, %{inventory_change: inventory_change})
      end)
      # 选择库存变化最大的（最有利于收分）
      |> Enum.max_by(& &1.inventory_change)

    if best_result.inventory_change >= 0 do
      Logger.info("🎮 [JHANDI_MUNDA_CONTROL] 收分调整成功，库存变化: #{best_result.inventory_change}")
      Map.put(best_result, :control_applied, true)
    else
      # 如果尝试多次仍无法满足收分，使用最优结果
      Logger.info("🎮 [JHANDI_MUNDA_CONTROL] 收分调整 - 选择最优结果，库存变化: #{best_result.inventory_change}")
      find_optimal_collect_result(bet_data)
    end
  end

  # 调整结果以实现放分
  defp adjust_for_release(bet_data, weight_config) do
    # 尝试生成多个结果，选择最有利于放分的
    attempts = 1..20

    best_result =
      Enum.map(attempts, fn _ ->
        dice_results = generate_weighted_dice(weight_config)
        game_result = process_dice_results(dice_results)
        inventory_change = calculate_inventory_change(bet_data, game_result)

        Map.merge(game_result, %{inventory_change: inventory_change})
      end)
      # 选择库存变化最小的（最有利于放分）
      |> Enum.min_by(& &1.inventory_change)

    if best_result.inventory_change <= 0 do
      Logger.info("🎮 [JHANDI_MUNDA_CONTROL] 放分调整成功，库存变化: #{best_result.inventory_change}")
      Map.put(best_result, :control_applied, true)
    else
      # 如果尝试多次仍无法满足放分，使用最优结果
      Logger.info("🎮 [JHANDI_MUNDA_CONTROL] 放分调整 - 选择最优结果，库存变化: #{best_result.inventory_change}")
      find_optimal_release_result(bet_data)
    end
  end

  # 找到最优收分结果
  defp find_optimal_collect_result(bet_data) do
    # 生成对庄家最有利的结果：尽量让每个符号都只出现1次（最低赔率）
    dice_results = generate_house_favorable_dice()
    game_result = process_dice_results(dice_results)
    inventory_change = calculate_inventory_change(bet_data, game_result)

    Logger.info(
      "🎮 [JHANDI_MUNDA_CONTROL] 最优收分结果: #{inspect(game_result.symbol_counts)}, 库存变化: #{inventory_change}"
    )

    Map.merge(game_result, %{
      inventory_change: inventory_change,
      control_applied: true
    })
  end

  # 找到最优放分结果
  defp find_optimal_release_result(bet_data) do
    # 找出投注最多的符号，让其出现多次（高赔率）
    max_bet_symbol =
      bet_data
      |> Enum.max_by(fn {_symbol, amount} -> amount end)
      |> elem(0)

    # 生成让该符号出现3-4次的结果
    target_count = Enum.random(3..4)
    dice_results = generate_specific_symbol_dice(max_bet_symbol, target_count)
    game_result = process_dice_results(dice_results)
    inventory_change = calculate_inventory_change(bet_data, game_result)

    Logger.info(
      "🎮 [JHANDI_MUNDA_CONTROL] 最优放分结果: #{inspect(game_result.symbol_counts)}, 库存变化: #{inventory_change}"
    )

    Map.merge(game_result, %{
      inventory_change: inventory_change,
      control_applied: true
    })
  end

  # 根据权重配置生成骰子结果
  defp generate_weighted_dice(weight_config) do
    # 计算权重总和
    total_weight =
      @symbols
      |> Enum.reduce(0, fn symbol, acc ->
        weight_key = get_weight_key(symbol)
        # 默认权重100
        weight = Map.get(weight_config, weight_key, 100)
        acc + weight
      end)

    # 生成6个骰子，每个根据权重随机选择符号
    for _ <- 1..@dice_count do
      random_value = :rand.uniform(total_weight)
      select_symbol_by_weight(random_value, weight_config, 0, @symbols)
    end
  end

  # 根据权重选择符号
  defp select_symbol_by_weight(target_value, weight_config, accumulated_weight, [symbol | rest]) do
    weight_key = get_weight_key(symbol)
    symbol_weight = Map.get(weight_config, weight_key, 100)
    new_accumulated = accumulated_weight + symbol_weight

    if target_value <= new_accumulated do
      symbol
    else
      select_symbol_by_weight(target_value, weight_config, new_accumulated, rest)
    end
  end

  defp select_symbol_by_weight(_target_value, _weight_config, _accumulated_weight, []) do
    # 如果没有找到符号，返回第一个符号作为默认值
    hd(@symbols)
  end

  # 生成对庄家有利的骰子结果（分散的结果，低赔率）
  defp generate_house_favorable_dice do
    # 让每个符号都出现1次，如果有剩余的骰子，随机分配
    symbols = @symbols
    base_dice = symbols

    # 如果骰子数量大于符号数量，随机分配剩余的骰子
    remaining_count = @dice_count - length(symbols)
    additional_dice = for _ <- 1..remaining_count, do: Enum.random(symbols)

    # 合并并打乱
    Enum.shuffle(base_dice ++ additional_dice)
  end

  # 生成特定符号出现指定次数的骰子结果
  defp generate_specific_symbol_dice(target_symbol, target_count) do
    # 确保目标符号出现指定次数
    target_dice = for _ <- 1..target_count, do: target_symbol

    # 剩余骰子随机选择其他符号
    remaining_count = @dice_count - target_count
    other_symbols = @symbols -- [target_symbol]
    remaining_dice = for _ <- 1..remaining_count, do: Enum.random(other_symbols)

    # 合并并打乱
    Enum.shuffle(target_dice ++ remaining_dice)
  end

  # 处理骰子结果，生成游戏结果格式
  defp process_dice_results(dice_results) do
    # 统计每个符号出现的次数
    symbol_counts =
      @symbols
      |> Enum.map(fn symbol -> {symbol, 0} end)
      |> Enum.into(%{})
      |> then(fn initial_counts ->
        Enum.reduce(dice_results, initial_counts, fn symbol, acc ->
          Map.update(acc, symbol, 1, &(&1 + 1))
        end)
      end)

    # 找出有中奖的符号（出现次数 > 0）
    winning_symbols =
      symbol_counts
      |> Enum.filter(fn {_symbol, count} -> count > 0 end)
      |> Enum.map(fn {symbol, _count} -> symbol end)

    %{
      dice_results: dice_results,
      symbol_counts: symbol_counts,
      winning_symbols: winning_symbols
    }
  end

  # 计算库存变化
  defp calculate_inventory_change(bet_data, game_result) do
    symbol_counts = game_result.symbol_counts

    # 计算每个符号的盈亏
    symbol_profits =
      @symbols
      |> Enum.map(fn symbol ->
        bet_amount = Map.get(bet_data, symbol, 0)
        symbol_count = Map.get(symbol_counts, symbol, 0)
        odds = JhandiMundaConstants.get_odds(symbol_count)

        if symbol_count > 0 do
          # 有中奖：平台支付赔付
          payout = bet_amount * odds
          # 收到的投注减去支付的赔付
          profit = bet_amount - payout
          {symbol, profit}
        else
          # 没中奖：平台收取全部投注
          {symbol, bet_amount}
        end
      end)

    # 计算总库存变化
    total_profit =
      symbol_profits
      |> Enum.reduce(0, fn {_symbol, profit}, acc -> acc + profit end)

    Logger.debug(
      "🎮 [JHANDI_MUNDA_CONTROL] 库存变化详情: #{inspect(symbol_profits)}, 总变化: #{total_profit}"
    )

    total_profit
  end

  # 获取符号对应的权重配置键名
  defp get_weight_key(symbol) do
    case symbol do
      # 梅花
      1 -> :clubs_weight
      # 皇冠  
      2 -> :crown_weight
      # 黑桃
      3 -> :spades_weight
      # 方块
      4 -> :diamonds_weight
      # 国旗
      5 -> :flag_weight
      # 红桃
      6 -> :hearts_weight
      # 默认
      _ -> :clubs_weight
    end
  end
end
