defmodule Teen.Protocol.MoneyProtocol do
  @moduledoc """
  金钱钱包协议处理器

  处理用户金钱、积分、钱包相关的协议，包括：
  - 获取余额信息
  - 充值记录查询
  - 提现申请
  - 转账操作
  - 交易记录
  - 钱包管理
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Lo<PERSON>
  alias Teen.Protocol.ProtocolUtils
  alias <PERSON><PERSON>ridina.{Accounts, Ledger}
  alias Teen.PaymentSystem.{RechargeRecord, WithdrawalRecord}

  # 主协议ID
  @protocol_id 7

  # 子协议常量定义 - 根据Protocol.ts定义
  # SC协议（服务端主动推送）
  # 金钱改变
  @sc_set_money_p 0
  # 钱包改变
  @sc_set_walletmoney_p 1
  # 存钱请求
  @cs_save_money_p 2
  # 存钱结果
  @sc_save_money_result_p 3
  # 取钱请求
  @cs_get_money_p 4
  # 取钱结果
  @sc_get_money_result_p 5
  # 游戏中设置金钱
  @cs_set_game_money_p 6
  # 游戏中金钱改变
  @sc_set_game_money_p 7
  # 游戏中钱包改变
  @sc_set_game_walletmoney_p 8
  # 荣誉点改变
  @sc_set_honor_value_p 9
  # 钻石改变
  @sc_set_diamonds_value_p 10
  # 钻石兑换金币
  @cs_diamonds_change_money_p 11
  # 钻石兑换金币结果
  @sc_diamonds_change_money_p 12
  # 钻石兑换会员
  @cs_diamonds_change_vip_p 13
  # 钻石兑换会员结果
  @sc_diamonds_change_vip_p 14
  # 获取钻石兑换金币配置
  @cs_diamonds_trans_money_config_p 15
  # 钻石兑换金币配置
  @sc_diamonds_trans_money_confing_p 16
  # 获取钻石兑换会员配置
  @cs_diamonds_trans_vip_config_p 17
  # 钻石兑换会员配置
  @sc_diamonds_trans_vip_confing_p 18
  # 获取人民币换钻石配置
  @cs_rmb_trans_diamonds_config_p 19
  # 人民币换钻石配置
  @sc_rmb_trans_diamonds_confing_p 20
  # 获取人民币换钻石配置_IOS
  @cs_rmb_trans_diamonds_config_ios_p 21
  # 人民币换钻石配置_IOS
  @sc_rmb_trans_diamonds_confing_ios_p 22
  # 玩家转账
  @cs_transfer_money_p 23
  # 玩家转账结果
  @sc_transfer_money_p 24
  # 绑定提取号返回
  @ds_bind_pickup_p 26
  # 修改银行密码返回
  @ds_bank_password_p 28
  # 游戏币兑换现金（提现）
  @cs_money_chang_rmb_p 29
  # 游戏币兑换现金返回
  @ds_money_chang_rmb_p 30
  # 发送消息给客服返回
  @dc_send_msg_guest_server_p 32
  # 绑定银行卡返回
  @ds_bind_bank_p 34
  # 领取救济金结果
  @sc_get_alms_result_p 36
  # 查询救济金
  @cs_request_alms_p 37
  # 查询救济金结果
  @sc_request_alms_result_p 38
  # 投诉结果
  @sc_complaint_result_p 48
  # 请求支付
  @cs_pay_p 49
  # 请求支付返回
  @sc_pay_p 50
  # 请求VIP支付列表
  @cs_vip_pay_list_p 51
  # 请求VIP支付列表返回
  @sc_vip_pay_list_p 52

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "Money",
      description: "金钱钱包协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      {@cs_save_money_p, "存钱请求"},
      {@cs_get_money_p, "取钱请求"},
      {@cs_transfer_money_p, "转账操作"},
      {@cs_money_chang_rmb_p, "游戏币兑换现金（提现）"},
      {@cs_request_alms_p, "查询救济金"},
      {@cs_pay_p, "请求支付"},
      {@cs_vip_pay_list_p, "请求VIP支付列表"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "MONEY", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "MONEY", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_save_money_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:amount]},
          {:number_range, :amount, 1, 1_000_000, "存钱金额"}
        ])

      @cs_get_money_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:amount]},
          {:number_range, :amount, 1, 1_000_000, "取钱金额"}
        ])

      @cs_transfer_money_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:to_user_id, :amount]},
          {:number_range, :amount, 1, 1_000_000, "转账金额"}
        ])

      @cs_money_chang_rmb_p ->
        # 标准化参数以支持旧客户端字段名
        normalized_data = normalize_withdrawal_params(data)

        ProtocolUtils.validate_params(normalized_data, [
          {:required, [:amount, :bank_id]},
          {:number_range, :amount, 1, 1_000_000_00, "提现金额"}
        ])

      @cs_pay_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:amount]},
          {:number_range, :amount, 1, 1_000_000_00, "支付金额"}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id

    case sub_protocol do
      @cs_save_money_p ->
        handle_save_money(user_id, data)

      @cs_get_money_p ->
        handle_get_money(user_id, data)

      @cs_transfer_money_p ->
        handle_transfer_money(user_id, data)

      @cs_money_chang_rmb_p ->
        handle_withdrawal_exchange(user_id, data)

      @cs_request_alms_p ->
        handle_request_alms(user_id)

      @cs_pay_p ->
        handle_pay_request(user_id, data)

      @cs_vip_pay_list_p ->
        handle_get_vip_pay_list(user_id)

      _ ->
        ProtocolUtils.log_protocol(:warning, "MONEY", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 处理存钱请求（保险箱功能）
  defp handle_save_money(user_id, data) do
    amount = Map.get(data, "amount", 0)

    # 目前暂未实现保险箱功能
    response_data = %{
      "code" => 1,
      "msg" => "保险箱功能暂未开放"
    }

    ProtocolUtils.log_protocol(:info, "MONEY", @cs_save_money_p, user_id, "存钱请求: #{amount}")
    {:ok, @sc_save_money_result_p, response_data}
  end

  # 处理取钱请求（保险箱功能）
  defp handle_get_money(user_id, data) do
    amount = Map.get(data, "amount", 0)

    # 目前暂未实现保险箱功能
    response_data = %{
      "code" => 1,
      "msg" => "保险箱功能暂未开放"
    }

    ProtocolUtils.log_protocol(:info, "MONEY", @cs_get_money_p, user_id, "取钱请求: #{amount}")
    {:ok, @sc_get_money_result_p, response_data}
  end

  # 处理查询救济金
  defp handle_request_alms(user_id) do
    # 获取用户余额
    points = Accounts.get_user_points(user_id) || 0

    # 救济金规则：余额低于100时可领取
    response_data =
      if points < 100 do
        %{
          "code" => 0,
          "available" => 1,
          "amount" => 1000,
          "msg" => "您可以领取救济金"
        }
      else
        %{
          "code" => 1,
          "available" => 0,
          "amount" => 0,
          "msg" => "您的余额充足，无需救济金"
        }
      end

    ProtocolUtils.log_protocol(:info, "MONEY", @cs_request_alms_p, user_id, "查询救济金")
    {:ok, @sc_request_alms_result_p, response_data}
  end

  # 处理获取VIP支付列表
  defp handle_get_vip_pay_list(user_id) do
    # 获取VIP充值配置
    vip_list = [
      %{
        "id" => 1,
        "name" => "VIP1",
        "price" => 100,
        "days" => 30,
        "benefits" => ["每日登录奖励加成10%"]
      },
      %{
        "id" => 2,
        "name" => "VIP2",
        "price" => 500,
        "days" => 30,
        "benefits" => ["每日登录奖励加成20%", "专属客服"]
      },
      %{
        "id" => 3,
        "name" => "VIP3",
        "price" => 1000,
        "days" => 30,
        "benefits" => ["每日登录奖励加成30%", "专属客服", "提现加速"]
      }
    ]

    response_data = %{
      "code" => 0,
      "vipList" => vip_list
    }

    ProtocolUtils.log_protocol(:info, "MONEY", @cs_vip_pay_list_p, user_id, "获取VIP支付列表")
    {:ok, @sc_vip_pay_list_p, response_data}
  end

  # 处理转账操作 - 保持原有实现但修正响应协议ID
  defp handle_transfer_money(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)

    case RechargeRecord.list_user_records(user_id, page, page_size) do
      {:ok, {records, total_count}} ->
        formatted_records = Enum.map(records, &format_recharge_record/1)

        response_data =
          ProtocolUtils.success_response(
            ProtocolUtils.build_pagination_response(
              formatted_records,
              page,
              page_size,
              total_count
            )
          )

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_get_recharge_history_p,
          user_id,
          "获取充值记录成功，共#{total_count}条"
        )

        {:ok, @sc_get_recharge_history_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_recharge_history_p,
          user_id,
          "获取充值记录失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取充值记录失败")
        {:ok, @sc_get_recharge_history_p, error_data}
    end
  end

  # 处理获取提现记录
  defp handle_get_withdrawal_history(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)

    case WithdrawalRecord.list_user_records(user_id, page, page_size) do
      {:ok, {records, total_count}} ->
        formatted_records = Enum.map(records, &format_withdrawal_record/1)

        response_data =
          ProtocolUtils.success_response(
            ProtocolUtils.build_pagination_response(
              formatted_records,
              page,
              page_size,
              total_count
            )
          )

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_get_withdrawal_history_p,
          user_id,
          "获取提现记录成功，共#{total_count}条"
        )

        {:ok, @sc_get_withdrawal_history_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_withdrawal_history_p,
          user_id,
          "获取提现记录失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取提现记录失败")
        {:ok, @sc_get_withdrawal_history_p, error_data}
    end
  end

  # 处理转账操作
  defp handle_transfer_money(user_id, data) do
    to_user_id = Map.get(data, "to_user_id")
    amount = Map.get(data, "amount")
    note = Map.get(data, "note", "")

    case Ledger.transfer_funds(user_id, to_user_id, amount, note) do
      {:ok, transfer} ->
        response_data =
          ProtocolUtils.success_response(%{
            "transferId" => transfer.id,
            "amount" => ProtocolUtils.format_money(amount),
            "toUserId" => to_user_id,
            "msg" => "转账成功"
          })

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_transfer_money_p,
          user_id,
          "转账成功: #{amount} -> #{to_user_id}"
        )

        {:ok, @sc_transfer_money_p, response_data}

      {:error, :insufficient_balance} ->
        error_data = ProtocolUtils.error_response(:insufficient_balance, "余额不足")
        {:ok, @sc_transfer_money_p, error_data}

      {:error, :user_not_found} ->
        error_data = ProtocolUtils.error_response(:not_found, "收款用户不存在")
        {:ok, @sc_transfer_money_p, error_data}

      {:error, :self_transfer} ->
        error_data = ProtocolUtils.error_response(:forbidden, "不能向自己转账")
        {:ok, @sc_transfer_money_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_transfer_money_p,
          user_id,
          "转账失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "转账失败")
        {:ok, @sc_transfer_money_p, error_data}
    end
  end

  # 处理获取交易记录
  defp handle_get_transaction_history(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)
    transaction_type = Map.get(data, "type", "all")

    case Ledger.get_user_transactions(user_id, transaction_type, page, page_size) do
      {:ok, {transactions, total_count}} ->
        formatted_transactions = Enum.map(transactions, &format_transaction/1)

        response_data =
          ProtocolUtils.success_response(
            ProtocolUtils.build_pagination_response(
              formatted_transactions,
              page,
              page_size,
              total_count
            )
          )

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_get_transaction_history_p,
          user_id,
          "获取交易记录成功，共#{total_count}条"
        )

        {:ok, @sc_get_transaction_history_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_transaction_history_p,
          user_id,
          "获取交易记录失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取交易记录失败")
        {:ok, @sc_get_transaction_history_p, error_data}
    end
  end

  # 处理获取钱包信息
  defp handle_get_wallet_info(user_id) do
    case Accounts.get_wallet_info(user_id) do
      {:ok, wallet_info} ->
        response_data =
          ProtocolUtils.success_response(%{
            "walletId" => wallet_info.wallet_id,
            "balance" => wallet_info.balance,
            "points" => wallet_info.points,
            "frozenBalance" => wallet_info.frozen_balance || 0,
            "creditLimit" => wallet_info.credit_limit || 0,
            "walletStatus" => wallet_info.status,
            "createdAt" => format_datetime(wallet_info.created_at),
            "lastTransactionAt" => format_datetime(wallet_info.last_transaction_at)
          })

        ProtocolUtils.log_protocol(:info, "MONEY", @cs_get_wallet_info_p, user_id, "获取钱包信息成功")
        {:ok, @sc_get_wallet_info_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_wallet_info_p,
          user_id,
          "获取钱包信息失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取钱包信息失败")
        {:ok, @sc_get_wallet_info_p, error_data}
    end
  end

  # 处理积分兑换
  defp handle_exchange_points(user_id, data) do
    points = Map.get(data, "points")
    # 100积分 = 1元
    exchange_rate = 1

    case Accounts.exchange_points_to_balance(user_id, points, exchange_rate) do
      {:ok, exchange_result} ->
        response_data =
          ProtocolUtils.success_response(%{
            "exchangedPoints" => points,
            "receivedBalance" => ProtocolUtils.format_money(exchange_result.balance_received),
            "exchangeRate" => exchange_rate,
            "remainingPoints" => exchange_result.remaining_points,
            "msg" => "积分兑换成功"
          })

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_exchange_points_p,
          user_id,
          "积分兑换成功: #{points}积分"
        )

        {:ok, @sc_exchange_points_p, response_data}

      {:error, :insufficient_points} ->
        error_data = ProtocolUtils.error_response(:insufficient_balance, "积分不足")
        {:ok, @sc_exchange_points_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_exchange_points_p,
          user_id,
          "积分兑换失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "积分兑换失败")
        {:ok, @sc_exchange_points_p, error_data}
    end
  end

  # 处理获取每日统计
  defp handle_get_daily_statistics(user_id) do
    case Accounts.get_daily_money_statistics(user_id) do
      {:ok, stats} ->
        response_data =
          ProtocolUtils.success_response(%{
            "todayRecharge" => ProtocolUtils.format_money(stats.today_recharge),
            "todayWithdrawal" => ProtocolUtils.format_money(stats.today_withdrawal),
            "todayGameWin" => ProtocolUtils.format_money(stats.today_game_win),
            "todayGameLoss" => ProtocolUtils.format_money(stats.today_game_loss),
            "weeklyRecharge" => ProtocolUtils.format_money(stats.weekly_recharge),
            "weeklyWithdrawal" => ProtocolUtils.format_money(stats.weekly_withdrawal),
            "monthlyRecharge" => ProtocolUtils.format_money(stats.monthly_recharge),
            "monthlyWithdrawal" => ProtocolUtils.format_money(stats.monthly_withdrawal)
          })

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_get_daily_statistics_p,
          user_id,
          "获取每日统计成功"
        )

        {:ok, @sc_get_daily_statistics_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_get_daily_statistics_p,
          user_id,
          "获取每日统计失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "获取每日统计失败")
        {:ok, @sc_get_daily_statistics_p, error_data}
    end
  end

  # 处理支付请求
  defp handle_pay_request(user_id, data) do
    Logger.info("💰 [MONEY_PAY] 收到支付请求: #{inspect(data)}")

    # 获取支付参数
    amount = Map.get(data, "amount", 0)
    pay_type = Map.get(data, "paytype", "alipay") |> to_string()
    charge_id = Map.get(data, "chargeid", "") |> to_string()

    # 验证支付参数
    cond do
      amount <= 0 ->
        error_data = ProtocolUtils.error_response(:invalid_params, "支付金额无效")
        {:ok, @sc_pay_p, error_data}

      String.length(charge_id) == 0 ->
        error_data = ProtocolUtils.error_response(:invalid_params, "充值配置ID无效")
        {:ok, @sc_pay_p, error_data}

      true ->
        # 调用支付系统创建订单
        case Teen.PaymentSystem.PaymentService.create_order(%{
               user_id: user_id,
               amount: Decimal.new(amount),
               currency: "INR",
               channel_id: map_pay_type_to_channel_id(pay_type),
               notify_url: get_payment_notify_url(),
               return_url: get_payment_return_url()
             }) do
          {:ok, payment_result} ->
            response_data =
              ProtocolUtils.success_response(%{
                "orderid" => payment_result.order_id,
                "msg" => payment_result.payment_url,
                "payurl" => payment_result.payment_url,
                "amount" => amount,
                "paytype" => pay_type,
                "status" => payment_result.status
              })

            ProtocolUtils.log_protocol(
              :info,
              "MONEY",
              @cs_pay_p,
              user_id,
              "支付订单创建成功: #{payment_result.order_id}"
            )

            {:ok, @sc_pay_p, response_data}

          {:error, reason} ->
            ProtocolUtils.log_protocol(
              :error,
              "MONEY",
              @cs_pay_p,
              user_id,
              "创建支付订单失败: #{inspect(reason)}"
            )

            error_data = ProtocolUtils.error_response(:internal_error, "支付订单创建失败")
            {:ok, @sc_pay_p, error_data}
        end
    end
  end

  # ============================================================================
  # 工具函数
  # ============================================================================

  defp format_recharge_record(record) do
    %{
      "orderId" => record.order_id,
      "amount" => ProtocolUtils.format_money(record.amount),
      "paymentMethod" => record.payment_method,
      "status" => record.status,
      "createdAt" => format_datetime(record.inserted_at),
      "completedAt" => format_datetime(record.completed_at)
    }
  end

  defp format_withdrawal_record(record) do
    %{
      "orderId" => record.order_id,
      "amount" => ProtocolUtils.format_money(record.withdrawal_amount),
      "actualAmount" => ProtocolUtils.format_money(record.actual_amount),
      "feeAmount" => ProtocolUtils.format_money(record.fee_amount),
      "paymentMethod" => record.payment_method,
      "auditStatus" => record.audit_status,
      "progressStatus" => record.progress_status,
      "resultStatus" => record.result_status,
      "createdAt" => format_datetime(record.inserted_at),
      "auditTime" => format_datetime(record.audit_time),
      "processTime" => format_datetime(record.process_time),
      "completedTime" => format_datetime(record.completed_time),
      "feedback" => record.feedback
    }
  end

  defp format_transaction(transaction) do
    %{
      "id" => transaction.id,
      "type" => transaction.type,
      "amount" => ProtocolUtils.format_money(transaction.amount),
      "description" => transaction.description,
      "createdAt" => format_datetime(transaction.inserted_at),
      "balanceAfter" => ProtocolUtils.format_money(transaction.balance_after)
    }
  end

  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)

  # 获取支付通知URL
  defp get_payment_notify_url do
    base_url = Application.get_env(:cypridina, :base_url, "http://localhost:4000")
    "#{base_url}/api/payment/notify"
  end

  # 获取支付返回URL
  defp get_payment_return_url do
    base_url = Application.get_env(:cypridina, :base_url, "http://localhost:4000")
    "#{base_url}/api/payment/return"
  end

  # 将支付类型映射为渠道ID
  defp map_pay_type_to_channel_id(pay_type) do
    case pay_type do
      "alipay" -> "3021"
      "wechat" -> "3021"
      "unionpay" -> "3021"
      "ebank" -> "3021"
      # 处理数字类型的paytype（客户端传递的遗留格式）
      # 支付宝
      "1" -> "3021"
      # 微信
      "2" -> "3021"
      # 银联
      "3" -> "3021"
      # 网银
      "4" -> "3021"
      # 第三方支付
      "5" -> "3021"
      # 默认使用第三方支付
      _ -> "3021"
    end
  end

  # 标准化提现参数（支持旧客户端字段名）
  defp normalize_withdrawal_params(data) do
    # 获取银行ID，支持两种格式：bank_code 或者 UUID
    bank_identifier = Map.get(data, "bank_id") || Map.get(data, "bankname")

    # 如果是UUID格式，尝试从数据库获取bank_code
    bank_id =
      if bank_identifier &&
           String.match?(
             bank_identifier,
             ~r/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
           ) do
        # 这是UUID，需要从数据库查找对应的bank_code
        case Ash.get(Teen.PaymentSystem.BankConfig, bank_identifier) do
          {:ok, bank} -> bank.bank_code
          # 如果找不到，保持原值
          _ -> bank_identifier
        end
      else
        # 直接使用bank_code
        bank_identifier
      end

    # 映射旧客户端字段名到新的标
    %{
      "amount" => Map.get(data, "money"),
      "bank_id" => Map.get(data, "bankname"),
      "bank_account_name" => Map.get(data, "bankaccountname"),
      "bank_account_number" => Map.get(data, "bankaccountname"),
      "bank_ifsc" => Map.get(data, "bankaccountnum", "")
    }
  end

  # 处理提现兑换 - 使用新的提现流程
  defp handle_withdrawal_exchange(user_id, data) do
    Logger.info("💰 [MONEY_WITHDRAWAL_EXCHANGE] 用户:#{user_id} 开始处理提现兑换")
    Logger.info("💰 [MONEY_WITHDRAWAL_EXCHANGE] 原始数据: #{inspect(data)}")

    # 标准化请求参数（支持旧客户端字段名）
    normalized_data = normalize_withdrawal_params(data)
    Logger.info("💰 [MONEY_WITHDRAWAL_EXCHANGE] 标准化数据: #{inspect(normalized_data)}")

    # 获取请求参数
    amount = Map.get(normalized_data, "amount")
    bank_id = Map.get(normalized_data, "bank_id")
    bank_account_name = Map.get(normalized_data, "bank_account_name", "")
    bank_account_number = Map.get(normalized_data, "bank_account_number", "")
    bank_ifsc = Map.get(normalized_data, "bankaccountnum", "")
    # 构建银行信息JSON
    bank_info = %{
      "bank_id" => bank_id,
      "account_name" => bank_account_name,
      "account_number" => bank_account_number,
      "ifsc" => bank_ifsc
    }

    # 构建提现参数，适配新的 WithdrawalService
    withdrawal_params = %{
      withdrawal_amount: Decimal.new(amount),
      payment_method: "bank_card",
      bank_info: bank_info,
      # 从请求上下文获取真实IP（如果有）
      ip_address: Map.get(data, "ip_address", "127.0.0.1")
    }

    # 使用新的提现服务创建提现申请
    case Teen.Services.WithdrawalService.create_withdrawal(user_id, withdrawal_params) do
      {:ok, withdrawal_record} ->
        # 构建成功响应
        response_data =
          ProtocolUtils.success_response(%{
            "orderId" => withdrawal_record.order_id,
            "amount" => ProtocolUtils.format_money(withdrawal_record.withdrawal_amount),
            "finalAmount" =>
              ProtocolUtils.format_money(
                withdrawal_record.actual_amount || withdrawal_record.withdrawal_amount
              ),
            "feeAmount" => ProtocolUtils.format_money(withdrawal_record.fee_amount || 0),
            "bankName" => bank_id,
            "bankAccountName" => bank_account_name,
            "bankAccountNumber" => bank_account_number,
            "bankIfsc" => bank_ifsc,
            "auditStatus" => withdrawal_record.audit_status,
            "progressStatus" => withdrawal_record.progress_status,
            "createdAt" => ProtocolUtils.current_timestamp(),
            "msg" => "提现申请已提交，请等待审核"
          })

        ProtocolUtils.log_protocol(
          :info,
          "MONEY",
          @cs_money_chang_rmb_p,
          user_id,
          "提现申请成功: #{withdrawal_record.order_id}, 金额: #{withdrawal_record.withdrawal_amount}"
        )

        {:ok, @ds_money_chang_rmb_p, response_data}

      {:error, "余额不足"} ->
        error_data = ProtocolUtils.error_response(:insufficient_balance, "余额不足")
        {:ok, @ds_money_chang_rmb_p, error_data}

      {:error, "流水未达标"} ->
        error_data = ProtocolUtils.error_response(:forbidden, "流水未达标，无法提现")
        {:ok, @ds_money_chang_rmb_p, error_data}

      {:error, "用户状态异常"} ->
        error_data = ProtocolUtils.error_response(:forbidden, "用户状态异常，无法提现")
        {:ok, @ds_money_chang_rmb_p, error_data}

      {:error, "用户支付功能被限制"} ->
        error_data = ProtocolUtils.error_response(:forbidden, "用户支付功能被限制")
        {:ok, @ds_money_chang_rmb_p, error_data}

      {:error, reason} when is_binary(reason) ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_money_chang_rmb_p,
          user_id,
          "提现申请失败: #{reason}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, reason)
        {:ok, @ds_money_chang_rmb_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "MONEY",
          @cs_money_chang_rmb_p,
          user_id,
          "提现申请失败: #{inspect(reason)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "提现申请失败")
        {:ok, @ds_money_chang_rmb_p, error_data}
    end
  end
end
