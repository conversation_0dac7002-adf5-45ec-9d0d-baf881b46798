defmodule Teen.Protocol.XCProtocol do
  @moduledoc """
  子游戏服务器协议处理器 (XC Protocol)

  处理子游戏服务器与客户端之间的交互协议，包括：
  - 游戏房间管理
  - 游戏状态同步
  - 玩家操作传递
  - 游戏结果处理
  - 断线重连机制
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Teen.GameManagement.{GameRoom, GameSession}

  # 主协议ID
  @protocol_id 5

  # 子协议常量定义
  @cs_game_room_join_p 0
  @sc_game_room_join_p 1
  @cs_game_room_leave_p 2
  @sc_game_room_leave_p 3
  @cs_game_action_p 4
  @sc_game_action_p 5
  @cs_game_state_sync_p 6
  @sc_game_state_sync_p 7
  @cs_reconnect_p 8
  @sc_reconnect_p 9
  @cs_heartbeat_p 10
  @sc_heartbeat_p 11
  @cs_game_ready_p 12
  @sc_game_ready_p 13
  @cs_game_start_p 14
  @sc_game_start_p 15
  @cs_game_end_p 16
  @sc_game_end_p 17
  @cs_spectate_request_p 18
  @sc_spectate_request_p 19

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "XC",
      description: "子游戏服务器协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      {@cs_game_room_join_p, "加入游戏房间"},
      {@cs_game_room_leave_p, "离开游戏房间"},
      {@cs_game_action_p, "游戏操作"},
      {@cs_game_state_sync_p, "游戏状态同步"},
      {@cs_reconnect_p, "断线重连"},
      {@cs_heartbeat_p, "心跳检测"},
      {@cs_game_ready_p, "游戏准备"},
      {@cs_game_start_p, "开始游戏"},
      {@cs_game_end_p, "结束游戏"},
      {@cs_spectate_request_p, "观战请求"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id
    session_id = context.session_id

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "XC", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "XC", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_game_room_join_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:room_id, :game_type]}
        ])

      @cs_game_action_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:action_type, :action_data]}
        ])

      @cs_reconnect_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:room_id]}
        ])

      @cs_spectate_request_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:room_id]}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id

    case sub_protocol do
      @cs_game_room_join_p ->
        handle_game_room_join(user_id, data, context)

      @cs_game_room_leave_p ->
        handle_game_room_leave(user_id, context)

      @cs_game_action_p ->
        handle_game_action(user_id, data, context)

      @cs_game_state_sync_p ->
        handle_game_state_sync(user_id, data, context)

      @cs_reconnect_p ->
        handle_reconnect(user_id, data, context)

      @cs_heartbeat_p ->
        handle_heartbeat(user_id, context)

      @cs_game_ready_p ->
        handle_game_ready(user_id, data, context)

      @cs_game_start_p ->
        handle_game_start(user_id, data, context)

      @cs_game_end_p ->
        handle_game_end(user_id, data, context)

      @cs_spectate_request_p ->
        handle_spectate_request(user_id, data, context)

      _ ->
        ProtocolUtils.log_protocol(:warning, "XC", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 处理加入游戏房间
  defp handle_game_room_join(user_id, data, context) do
    room_id = Map.get(data, "room_id")
    game_type = Map.get(data, "game_type")
    password = Map.get(data, "password", "")
    seat_index = Map.get(data, "seat_index")

    try do
      case GameRoom.join_room(user_id, room_id, game_type, password, seat_index) do
        {:ok, room_info} ->
          response_data =
            ProtocolUtils.success_response(%{
              "roomId" => room_info.room_id,
              "gameType" => room_info.game_type,
              "roomState" => room_info.state,
              "players" => format_players(room_info.players),
              "seatIndex" => room_info.user_seat,
              "gameConfig" => room_info.game_config,
              "spectators" => format_spectators(room_info.spectators),
              "currentRound" => room_info.current_round,
              "msg" => "成功加入游戏房间"
            })

          # 广播给房间内其他玩家
          broadcast_to_room(
            room_id,
            :player_joined,
            %{
              "userId" => user_id,
              "seatIndex" => room_info.user_seat
            },
            user_id
          )

          ProtocolUtils.log_protocol(
            :info,
            "XC",
            @cs_game_room_join_p,
            user_id,
            "加入游戏房间成功: #{room_id}"
          )

          {:ok, @sc_game_room_join_p, response_data}

        {:error, :room_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "游戏房间不存在")
          {:ok, @sc_game_room_join_p, error_data}

        {:error, :room_full} ->
          error_data = ProtocolUtils.error_response(:forbidden, "游戏房间已满")
          {:ok, @sc_game_room_join_p, error_data}

        {:error, :wrong_password} ->
          error_data = ProtocolUtils.error_response(:unauthorized, "房间密码错误")
          {:ok, @sc_game_room_join_p, error_data}

        {:error, :already_in_room} ->
          error_data = ProtocolUtils.error_response(:forbidden, "您已在游戏房间中")
          {:ok, @sc_game_room_join_p, error_data}

        {:error, :insufficient_balance} ->
          error_data = ProtocolUtils.error_response(:insufficient_balance, "余额不足，无法加入房间")
          {:ok, @sc_game_room_join_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_game_room_join_p,
            user_id,
            "加入游戏房间失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "加入游戏房间失败")
          {:ok, @sc_game_room_join_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_game_room_join_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_game_room_join_p, error_data}
    end
  end

  # 处理离开游戏房间
  defp handle_game_room_leave(user_id, context) do
    try do
      case GameRoom.leave_room(user_id) do
        {:ok, room_info} ->
          response_data =
            ProtocolUtils.success_response(%{
              "left" => true,
              "msg" => "成功离开游戏房间"
            })

          # 广播给房间内其他玩家
          if room_info.room_id do
            broadcast_to_room(
              room_info.room_id,
              :player_left,
              %{
                "userId" => user_id,
                "seatIndex" => room_info.user_seat
              },
              user_id
            )
          end

          ProtocolUtils.log_protocol(:info, "XC", @cs_game_room_leave_p, user_id, "离开游戏房间成功")
          {:ok, @sc_game_room_leave_p, response_data}

        {:error, :not_in_room} ->
          error_data = ProtocolUtils.error_response(:forbidden, "您不在游戏房间中")
          {:ok, @sc_game_room_leave_p, error_data}

        {:error, :game_in_progress} ->
          error_data = ProtocolUtils.error_response(:forbidden, "游戏进行中，无法离开房间")
          {:ok, @sc_game_room_leave_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_game_room_leave_p,
            user_id,
            "离开游戏房间失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "离开游戏房间失败")
          {:ok, @sc_game_room_leave_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_game_room_leave_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_game_room_leave_p, error_data}
    end
  end

  # 处理游戏操作
  defp handle_game_action(user_id, data, context) do
    action_type = Map.get(data, "action_type")
    action_data = Map.get(data, "action_data")
    room_id = Map.get(data, "room_id")

    try do
      case GameSession.process_player_action(user_id, room_id, action_type, action_data) do
        {:ok, action_result} ->
          response_data =
            ProtocolUtils.success_response(%{
              "actionType" => action_type,
              "actionResult" => action_result.result,
              "gameState" => action_result.game_state,
              "nextPlayer" => action_result.next_player,
              "actionId" => action_result.action_id
            })

          # 广播游戏操作给房间内所有玩家
          broadcast_to_room(room_id, :game_action, %{
            "userId" => user_id,
            "actionType" => action_type,
            "actionData" => action_data,
            "actionResult" => action_result.result,
            "gameState" => action_result.game_state
          })

          ProtocolUtils.log_protocol(
            :info,
            "XC",
            @cs_game_action_p,
            user_id,
            "游戏操作成功: #{action_type}"
          )

          {:ok, @sc_game_action_p, response_data}

        {:error, :invalid_action} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "无效的游戏操作")
          {:ok, @sc_game_action_p, error_data}

        {:error, :not_your_turn} ->
          error_data = ProtocolUtils.error_response(:forbidden, "还没轮到您操作")
          {:ok, @sc_game_action_p, error_data}

        {:error, :game_not_started} ->
          error_data = ProtocolUtils.error_response(:forbidden, "游戏尚未开始")
          {:ok, @sc_game_action_p, error_data}

        {:error, :insufficient_chips} ->
          error_data = ProtocolUtils.error_response(:insufficient_balance, "筹码不足")
          {:ok, @sc_game_action_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_game_action_p,
            user_id,
            "游戏操作失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "游戏操作失败")
          {:ok, @sc_game_action_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_game_action_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_game_action_p, error_data}
    end
  end

  # 处理游戏状态同步
  defp handle_game_state_sync(user_id, data, context) do
    room_id = Map.get(data, "room_id")

    try do
      case GameSession.get_game_state(user_id, room_id) do
        {:ok, game_state} ->
          response_data =
            ProtocolUtils.success_response(%{
              "roomId" => room_id,
              "gameState" => game_state.state,
              "currentRound" => game_state.current_round,
              "currentPlayer" => game_state.current_player,
              "players" => format_players_state(game_state.players),
              "gameData" => game_state.game_data,
              "lastAction" => game_state.last_action,
              "timestamp" => ProtocolUtils.current_timestamp()
            })

          ProtocolUtils.log_protocol(
            :info,
            "XC",
            @cs_game_state_sync_p,
            user_id,
            "游戏状态同步成功: #{room_id}"
          )

          {:ok, @sc_game_state_sync_p, response_data}

        {:error, :not_in_room} ->
          error_data = ProtocolUtils.error_response(:forbidden, "您不在游戏房间中")
          {:ok, @sc_game_state_sync_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_game_state_sync_p,
            user_id,
            "游戏状态同步失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "游戏状态同步失败")
          {:ok, @sc_game_state_sync_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_game_state_sync_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_game_state_sync_p, error_data}
    end
  end

  # 处理断线重连
  defp handle_reconnect(user_id, data, context) do
    room_id = Map.get(data, "room_id")
    last_action_id = Map.get(data, "last_action_id")

    try do
      case GameSession.reconnect_player(user_id, room_id, last_action_id) do
        {:ok, reconnect_info} ->
          response_data =
            ProtocolUtils.success_response(%{
              "reconnected" => true,
              "roomId" => room_id,
              "gameState" => reconnect_info.game_state,
              "missedActions" => reconnect_info.missed_actions,
              "currentPlayer" => reconnect_info.current_player,
              "yourSeat" => reconnect_info.user_seat,
              "msg" => "重连成功"
            })

          # 通知房间内其他玩家该玩家重连
          broadcast_to_room(
            room_id,
            :player_reconnected,
            %{
              "userId" => user_id,
              "seatIndex" => reconnect_info.user_seat
            },
            user_id
          )

          ProtocolUtils.log_protocol(:info, "XC", @cs_reconnect_p, user_id, "断线重连成功: #{room_id}")
          {:ok, @sc_reconnect_p, response_data}

        {:error, :room_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "游戏房间不存在")
          {:ok, @sc_reconnect_p, error_data}

        {:error, :not_in_room} ->
          error_data = ProtocolUtils.error_response(:forbidden, "您不在此游戏房间中")
          {:ok, @sc_reconnect_p, error_data}

        {:error, :game_ended} ->
          error_data = ProtocolUtils.error_response(:forbidden, "游戏已结束")
          {:ok, @sc_reconnect_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_reconnect_p,
            user_id,
            "断线重连失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "重连失败")
          {:ok, @sc_reconnect_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_reconnect_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_reconnect_p, error_data}
    end
  end

  # 处理心跳检测
  defp handle_heartbeat(user_id, context) do
    try do
      case GameSession.update_player_heartbeat(user_id) do
        {:ok, _} ->
          response_data =
            ProtocolUtils.success_response(%{
              "timestamp" => ProtocolUtils.current_timestamp(),
              "status" => "online"
            })

          {:ok, @sc_heartbeat_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_heartbeat_p,
            user_id,
            "心跳更新失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "心跳更新失败")
          {:ok, @sc_heartbeat_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_heartbeat_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_heartbeat_p, error_data}
    end
  end

  # 处理游戏准备
  defp handle_game_ready(user_id, data, context) do
    room_id = Map.get(data, "room_id")
    ready = Map.get(data, "ready", true)

    try do
      case GameSession.set_player_ready(user_id, room_id, ready) do
        {:ok, ready_info} ->
          response_data =
            ProtocolUtils.success_response(%{
              "ready" => ready,
              "allReady" => ready_info.all_ready,
              "readyPlayers" => ready_info.ready_players,
              "gameStarting" => ready_info.game_starting
            })

          # 广播准备状态给房间内所有玩家
          broadcast_to_room(room_id, :player_ready_changed, %{
            "userId" => user_id,
            "ready" => ready,
            "allReady" => ready_info.all_ready
          })

          ProtocolUtils.log_protocol(:info, "XC", @cs_game_ready_p, user_id, "游戏准备状态更新: #{ready}")
          {:ok, @sc_game_ready_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_game_ready_p,
            user_id,
            "游戏准备失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "游戏准备失败")
          {:ok, @sc_game_ready_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_game_ready_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_game_ready_p, error_data}
    end
  end

  # 处理开始游戏
  defp handle_game_start(user_id, data, context) do
    room_id = Map.get(data, "room_id")

    try do
      case GameSession.start_game(user_id, room_id) do
        {:ok, game_info} ->
          response_data =
            ProtocolUtils.success_response(%{
              "gameStarted" => true,
              "gameId" => game_info.game_id,
              "initialState" => game_info.initial_state,
              "firstPlayer" => game_info.first_player,
              "gameConfig" => game_info.game_config
            })

          # 广播游戏开始给房间内所有玩家
          broadcast_to_room(room_id, :game_started, %{
            "gameId" => game_info.game_id,
            "initialState" => game_info.initial_state,
            "firstPlayer" => game_info.first_player
          })

          ProtocolUtils.log_protocol(:info, "XC", @cs_game_start_p, user_id, "游戏开始成功: #{room_id}")
          {:ok, @sc_game_start_p, response_data}

        {:error, :not_authorized} ->
          error_data = ProtocolUtils.error_response(:forbidden, "您没有权限开始游戏")
          {:ok, @sc_game_start_p, error_data}

        {:error, :not_all_ready} ->
          error_data = ProtocolUtils.error_response(:forbidden, "还有玩家未准备")
          {:ok, @sc_game_start_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_game_start_p,
            user_id,
            "开始游戏失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "开始游戏失败")
          {:ok, @sc_game_start_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_game_start_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_game_start_p, error_data}
    end
  end

  # 处理结束游戏
  defp handle_game_end(user_id, data, context) do
    room_id = Map.get(data, "room_id")
    reason = Map.get(data, "reason", "normal")

    try do
      case GameSession.end_game(user_id, room_id, reason) do
        {:ok, game_result} ->
          response_data =
            ProtocolUtils.success_response(%{
              "gameEnded" => true,
              "gameResult" => game_result.result,
              "winners" => game_result.winners,
              "playerStats" => game_result.player_stats,
              "rewards" => game_result.rewards,
              "endReason" => reason
            })

          # 广播游戏结束给房间内所有玩家
          broadcast_to_room(room_id, :game_ended, %{
            "gameResult" => game_result.result,
            "winners" => game_result.winners,
            "endReason" => reason
          })

          ProtocolUtils.log_protocol(:info, "XC", @cs_game_end_p, user_id, "游戏结束: #{room_id}")
          {:ok, @sc_game_end_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_game_end_p,
            user_id,
            "结束游戏失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "结束游戏失败")
          {:ok, @sc_game_end_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_game_end_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_game_end_p, error_data}
    end
  end

  # 处理观战请求
  defp handle_spectate_request(user_id, data, context) do
    room_id = Map.get(data, "room_id")

    try do
      case GameRoom.add_spectator(user_id, room_id) do
        {:ok, spectate_info} ->
          response_data =
            ProtocolUtils.success_response(%{
              "spectating" => true,
              "roomId" => room_id,
              "gameState" => spectate_info.game_state,
              "players" => format_players_for_spectator(spectate_info.players),
              "spectatorCount" => spectate_info.spectator_count
            })

          # 通知房间内玩家有新观众
          broadcast_to_room(room_id, :spectator_joined, %{
            "spectatorCount" => spectate_info.spectator_count
          })

          ProtocolUtils.log_protocol(
            :info,
            "XC",
            @cs_spectate_request_p,
            user_id,
            "观战成功: #{room_id}"
          )

          {:ok, @sc_spectate_request_p, response_data}

        {:error, :room_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "游戏房间不存在")
          {:ok, @sc_spectate_request_p, error_data}

        {:error, :spectate_not_allowed} ->
          error_data = ProtocolUtils.error_response(:forbidden, "此房间不允许观战")
          {:ok, @sc_spectate_request_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "XC",
            @cs_spectate_request_p,
            user_id,
            "观战请求失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "观战请求失败")
          {:ok, @sc_spectate_request_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "XC",
          @cs_spectate_request_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_spectate_request_p, error_data}
    end
  end

  # ============================================================================
  # 工具函数
  # ============================================================================

  defp format_players(players) when is_list(players) do
    Enum.map(players, fn player ->
      %{
        "userId" => player.user_id,
        "username" => player.username,
        "avatar" => player.avatar,
        "seatIndex" => player.seat_index,
        "chips" => player.chips,
        "status" => player.status,
        "isReady" => player.is_ready || false,
        "isOnline" => player.is_online || true
      }
    end)
  end

  defp format_players(_), do: []

  defp format_spectators(spectators) when is_list(spectators) do
    Enum.map(spectators, fn spectator ->
      %{
        "userId" => spectator.user_id,
        "username" => spectator.username,
        "joinedAt" => format_datetime(spectator.joined_at)
      }
    end)
  end

  defp format_spectators(_), do: []

  defp format_players_state(players) when is_list(players) do
    Enum.map(players, fn player ->
      %{
        "userId" => player.user_id,
        "seatIndex" => player.seat_index,
        "chips" => player.chips,
        "status" => player.status,
        "currentBet" => player.current_bet || 0,
        "cards" => player.cards || [],
        "isFolded" => player.is_folded || false,
        "isAllIn" => player.is_all_in || false
      }
    end)
  end

  defp format_players_state(_), do: []

  defp format_players_for_spectator(players) when is_list(players) do
    Enum.map(players, fn player ->
      %{
        "userId" => player.user_id,
        "username" => player.username,
        "seatIndex" => player.seat_index,
        "chips" => player.chips,
        "status" => player.status,
        "currentBet" => player.current_bet || 0,
        # 观众不能看到玩家的手牌
        "cardCount" => length(player.cards || []),
        "isFolded" => player.is_folded || false
      }
    end)
  end

  defp format_players_for_spectator(_), do: []

  defp broadcast_to_room(room_id, event_type, data, exclude_user_id \\ nil) do
    try do
      Phoenix.PubSub.broadcast(
        Cypridina.PubSub,
        "game_room:#{room_id}",
        {:game_event, event_type, data, exclude_user_id}
      )
    rescue
      error ->
        Logger.error("Failed to broadcast to room #{room_id}: #{inspect(error)}")
    end
  end

  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)
end
