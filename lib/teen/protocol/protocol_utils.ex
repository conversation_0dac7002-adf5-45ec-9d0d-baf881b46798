defmodule Teen.Protocol.ProtocolUtils do
  @moduledoc """
  协议处理的通用工具函数

  提供统一的错误处理、数据验证、日志记录等功能
  """

  require Logger

  # 标准错误码定义
  @error_codes %{
    success: 0,
    invalid_params: 1,
    unauthorized: 2,
    forbidden: 3,
    not_found: 4,
    already_exists: 5,
    insufficient_balance: 6,
    rate_limited: 7,
    maintenance: 8,
    internal_error: 99
  }

  @doc """
  构建成功响应
  """
  def success_response(data \\ %{}) do
    Map.merge(%{"code" => @error_codes.success}, data)
  end

  @doc """
  构建错误响应
  """
  def error_response(error_code, message, extra_data \\ %{}) do
    code =
      case error_code do
        atom when is_atom(atom) -> Map.get(@error_codes, atom, @error_codes.internal_error)
        int when is_integer(int) -> int
        _ -> @error_codes.internal_error
      end

    %{"code" => code, "msg" => message}
    |> Map.merge(extra_data)
  end

  @doc """
  验证必需参数

  ## 示例
      validate_required_params(data, [:user_id, :amount])
  """
  def validate_required_params(data, required_fields) do
    missing_fields =
      required_fields
      |> Enum.filter(fn field ->
        field_str = to_string(field)
        value = Map.get(data, field_str) || Map.get(data, field)
        is_nil(value) || value == ""
      end)

    case missing_fields do
      [] -> :ok
      fields -> {:error, "缺少必需参数: #{Enum.join(fields, ", ")}"}
    end
  end

  @doc """
  验证数值范围
  """
  def validate_number_range(value, min, max, field_name) do
    num_value = to_number(value)

    cond do
      is_nil(num_value) ->
        {:error, "#{field_name}必须是数字"}

      num_value < min ->
        {:error, "#{field_name}不能小于#{min}"}

      num_value > max ->
        {:error, "#{field_name}不能大于#{max}"}

      true ->
        {:ok, num_value}
    end
  end

  @doc """
  验证字符串长度
  """
  def validate_string_length(value, min_length, max_length, field_name) do
    str_value = to_string(value)
    length = String.length(str_value)

    cond do
      length < min_length ->
        {:error, "#{field_name}长度不能少于#{min_length}个字符"}

      length > max_length ->
        {:error, "#{field_name}长度不能超过#{max_length}个字符"}

      true ->
        {:ok, str_value}
    end
  end

  @doc """
  安全转换为数字
  """
  def to_number(value) when is_number(value), do: value

  def to_number(value) when is_binary(value) do
    case Float.parse(value) do
      {num, ""} -> num
      _ -> nil
    end
  end

  def to_number(_), do: nil

  @doc """
  安全转换为整数
  """
  def to_integer(value) when is_integer(value), do: value
  def to_integer(value) when is_float(value), do: round(value)

  def to_integer(value) when is_binary(value) do
    case Integer.parse(value) do
      {num, ""} -> num
      _ -> nil
    end
  end

  def to_integer(_), do: nil

  @doc """
  记录协议处理日志
  """
  def log_protocol(level, protocol_name, sub_protocol, user_id, message, metadata \\ %{}) do
    log_message = "[#{protocol_name}:#{sub_protocol}] 用户:#{user_id} #{message}"

    metadata =
      Map.merge(metadata, %{
        protocol: protocol_name,
        sub_protocol: sub_protocol,
        user_id: user_id
      })

    case level do
      :debug -> Logger.debug(log_message, metadata)
      :info -> Logger.info(log_message, metadata)
      :warning -> Logger.warning(log_message, metadata)
      :error -> Logger.error(log_message, metadata)
      _ -> Logger.info(log_message, metadata)
    end
  end

  @doc """
  批量验证参数

  ## 示例
      validations = [
        {:required, [:user_id, :amount]},
        {:number_range, :amount, 1, 10000, "金额"},
        {:string_length, :nickname, 1, 20, "昵称"}
      ]
      
      validate_params(data, validations)
  """
  def validate_params(data, validations) do
    Enum.reduce_while(validations, :ok, fn validation, _acc ->
      case validate_single(data, validation) do
        :ok -> {:cont, :ok}
        {:error, _} = error -> {:halt, error}
      end
    end)
  end

  defp validate_single(data, {:required, fields}) do
    validate_required_params(data, fields)
  end

  defp validate_single(data, {:number_range, field, min, max, name}) do
    value = Map.get(data, to_string(field)) || Map.get(data, field)

    case validate_number_range(value, min, max, name) do
      {:ok, _} -> :ok
      error -> error
    end
  end

  defp validate_single(data, {:string_length, field, min, max, name}) do
    value = Map.get(data, to_string(field)) || Map.get(data, field)

    case validate_string_length(value, min, max, name) do
      {:ok, _} -> :ok
      error -> error
    end
  end

  defp validate_single(data, {:custom, field, validator_fn, name}) do
    value = Map.get(data, to_string(field)) || Map.get(data, field)

    case validator_fn.(value) do
      :ok -> :ok
      {:error, message} -> {:error, "#{name}: #{message}"}
      _ -> {:error, "#{name}验证失败"}
    end
  end

  @doc """
  格式化金额（分转元，保留2位小数）
  """
  def format_money(amount_in_cents) do
    amount = to_number(amount_in_cents) || 0
    Float.round(amount / 100, 2)
  end

  @doc """
  获取当前时间戳（毫秒）
  """
  def current_timestamp do
    System.system_time(:millisecond)
  end

  @doc """
  构建分页数据
  """
  def build_pagination_response(items, page, page_size, total_count) do
    %{
      "items" => items,
      "page" => page,
      "pageSize" => page_size,
      "totalCount" => total_count,
      "totalPages" => ceil(total_count / max(page_size, 1))
    }
  end
end
