defmodule Teen.Protocol.NoticeProtocol do
  @moduledoc """
  公告系统协议处理器

  处理系统公告相关的协议，包括：
  - 获取公告列表
  - 读取公告详情
  - 标记公告已读
  - 获取公告分类
  - 公告搜索功能
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Teen.NoticeSystem.{Notice, NoticeService}

  # 主协议ID
  @protocol_id 15

  # 子协议常量定义
  @cs_get_notice_list_p 0
  @sc_get_notice_list_p 1
  @cs_read_notice_p 2
  @sc_read_notice_p 3
  @cs_mark_notice_read_p 4
  @sc_mark_notice_read_p 5
  @cs_get_notice_categories_p 6
  @sc_get_notice_categories_p 7
  @cs_search_notices_p 8
  @sc_search_notices_p 9
  @cs_get_important_notices_p 10
  @sc_get_important_notices_p 11
  @cs_get_latest_notices_p 12
  @sc_get_latest_notices_p 13
  @cs_get_unread_count_p 14
  @sc_get_unread_count_p 15

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "Notice",
      description: "公告系统协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      {@cs_get_notice_list_p, "获取公告列表"},
      {1, "发送公告"},
      {@cs_read_notice_p, "读取公告详情"},
      {3, "请求公告需求"},
      {@cs_mark_notice_read_p, "标记公告已读"},
      {5, "请求系统公告"},
      {@cs_get_notice_categories_p, "获取公告分类"},
      {@cs_search_notices_p, "搜索公告"},
      {@cs_get_important_notices_p, "获取重要公告"},
      {@cs_get_latest_notices_p, "获取最新公告"},
      {@cs_get_unread_count_p, "获取未读公告数"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id || "anonymous"

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "NOTICE", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "NOTICE", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_get_notice_list_p ->
        ProtocolUtils.validate_params(data, [
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_read_notice_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:notice_id]}
        ])

      @cs_mark_notice_read_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:notice_id]}
        ])

      @cs_search_notices_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:keyword]},
          {:string_length, :keyword, 1, 50, "搜索关键词"},
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_get_latest_notices_p ->
        ProtocolUtils.validate_params(data, [
          {:number_range, :limit, 1, 50, "数量限制"}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    case sub_protocol do
      # @cs_get_notice_list_p
      0 ->
        handle_get_notice_list(data, context)

      # 发送公告
      1 ->
        handle_send_notice(data, context)

      # @cs_read_notice_p
      2 ->
        handle_read_notice(data, context)

      # 请求公告需求
      3 ->
        handle_request_notice_need(data, context)

      # @cs_mark_notice_read_p
      4 ->
        handle_mark_notice_read(data, context)

      # 请求系统公告
      5 ->
        handle_request_system_notice(data, context)

      # @cs_get_notice_categories_p
      6 ->
        handle_get_notice_categories()

      # @cs_search_notices_p
      8 ->
        handle_search_notices(data, context)

      # @cs_get_important_notices_p
      10 ->
        handle_get_important_notices(context)

      # @cs_get_latest_notices_p
      12 ->
        handle_get_latest_notices(data, context)

      # @cs_get_unread_count_p
      14 ->
        handle_get_unread_count(context)

      _ ->
        user_id = context.user_id || "anonymous"
        ProtocolUtils.log_protocol(:warning, "NOTICE", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 处理获取公告列表
  defp handle_get_notice_list(data, context) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)
    category = Map.get(data, "category", "all")
    user_id = context.user_id

    try do
      case NoticeService.get_notice_list(category, page, page_size, user_id) do
        {:ok, {notices, total_count}} ->
          formatted_notices = Enum.map(notices, &format_notice_item/1)

          response_data =
            ProtocolUtils.success_response(
              ProtocolUtils.build_pagination_response(
                formatted_notices,
                page,
                page_size,
                total_count
              )
            )

          ProtocolUtils.log_protocol(
            :info,
            "NOTICE",
            @cs_get_notice_list_p,
            user_id || "anonymous",
            "获取公告列表成功，共#{total_count}条"
          )

          {:ok, @sc_get_notice_list_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "NOTICE",
            @cs_get_notice_list_p,
            user_id || "anonymous",
            "获取公告列表失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取公告列表失败")
          {:ok, @sc_get_notice_list_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "NOTICE",
          @cs_get_notice_list_p,
          user_id || "anonymous",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_notice_list_p, error_data}
    end
  end

  # 处理读取公告详情
  defp handle_read_notice(data, context) do
    notice_id = Map.get(data, "notice_id")
    user_id = context.user_id

    try do
      case NoticeService.read_notice(notice_id, user_id) do
        {:ok, notice} ->
          response_data =
            ProtocolUtils.success_response(%{
              "notice" => format_notice_detail(notice)
            })

          ProtocolUtils.log_protocol(
            :info,
            "NOTICE",
            @cs_read_notice_p,
            user_id || "anonymous",
            "读取公告成功: #{notice_id}"
          )

          {:ok, @sc_read_notice_p, response_data}

        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "公告不存在")
          {:ok, @sc_read_notice_p, error_data}

        {:error, :expired} ->
          error_data = ProtocolUtils.error_response(:forbidden, "公告已过期")
          {:ok, @sc_read_notice_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "NOTICE",
            @cs_read_notice_p,
            user_id || "anonymous",
            "读取公告失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "读取公告失败")
          {:ok, @sc_read_notice_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "NOTICE",
          @cs_read_notice_p,
          user_id || "anonymous",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_read_notice_p, error_data}
    end
  end

  # 处理标记公告已读
  defp handle_mark_notice_read(data, context) do
    notice_id = Map.get(data, "notice_id")
    user_id = context.user_id

    if is_nil(user_id) do
      error_data = ProtocolUtils.error_response(:unauthorized, "用户未登录")
      {:ok, @sc_mark_notice_read_p, error_data}
    else
      try do
        case NoticeService.mark_notice_read(notice_id, user_id) do
          {:ok, _} ->
            response_data =
              ProtocolUtils.success_response(%{
                "marked" => true,
                "msg" => "公告已标记为已读"
              })

            ProtocolUtils.log_protocol(
              :info,
              "NOTICE",
              @cs_mark_notice_read_p,
              user_id,
              "标记公告已读成功: #{notice_id}"
            )

            {:ok, @sc_mark_notice_read_p, response_data}

          {:error, :not_found} ->
            error_data = ProtocolUtils.error_response(:not_found, "公告不存在")
            {:ok, @sc_mark_notice_read_p, error_data}

          {:error, reason} ->
            ProtocolUtils.log_protocol(
              :error,
              "NOTICE",
              @cs_mark_notice_read_p,
              user_id,
              "标记公告已读失败: #{inspect(reason)}"
            )

            error_data = ProtocolUtils.error_response(:internal_error, "标记失败")
            {:ok, @sc_mark_notice_read_p, error_data}
        end
      rescue
        error ->
          ProtocolUtils.log_protocol(
            :error,
            "NOTICE",
            @cs_mark_notice_read_p,
            user_id,
            "处理异常: #{inspect(error)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
          {:ok, @sc_mark_notice_read_p, error_data}
      end
    end
  end

  # 处理获取公告分类
  defp handle_get_notice_categories() do
    try do
      case NoticeService.get_notice_categories() do
        {:ok, categories} ->
          response_data =
            ProtocolUtils.success_response(%{
              "categories" => categories
            })

          {:ok, @sc_get_notice_categories_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "NOTICE",
            @cs_get_notice_categories_p,
            "system",
            "获取公告分类失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取公告分类失败")
          {:ok, @sc_get_notice_categories_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "NOTICE",
          @cs_get_notice_categories_p,
          "system",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_notice_categories_p, error_data}
    end
  end

  # 处理搜索公告
  defp handle_search_notices(data, context) do
    keyword = Map.get(data, "keyword")
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)
    category = Map.get(data, "category", "all")
    user_id = context.user_id

    try do
      case NoticeService.search_notices(keyword, category, page, page_size, user_id) do
        {:ok, {notices, total_count}} ->
          formatted_notices = Enum.map(notices, &format_notice_item/1)

          response_data =
            ProtocolUtils.success_response(
              ProtocolUtils.build_pagination_response(
                formatted_notices,
                page,
                page_size,
                total_count
              )
            )

          ProtocolUtils.log_protocol(
            :info,
            "NOTICE",
            @cs_search_notices_p,
            user_id || "anonymous",
            "搜索公告成功，关键词：#{keyword}，共#{total_count}条"
          )

          {:ok, @sc_search_notices_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "NOTICE",
            @cs_search_notices_p,
            user_id || "anonymous",
            "搜索公告失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "搜索公告失败")
          {:ok, @sc_search_notices_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "NOTICE",
          @cs_search_notices_p,
          user_id || "anonymous",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_search_notices_p, error_data}
    end
  end

  # 处理获取重要公告
  defp handle_get_important_notices(context) do
    user_id = context.user_id

    try do
      case NoticeService.get_important_notices(user_id) do
        {:ok, notices} ->
          formatted_notices = Enum.map(notices, &format_notice_item/1)

          response_data =
            ProtocolUtils.success_response(%{
              "notices" => formatted_notices,
              "count" => length(formatted_notices)
            })

          ProtocolUtils.log_protocol(
            :info,
            "NOTICE",
            @cs_get_important_notices_p,
            user_id || "anonymous",
            "获取重要公告成功，共#{length(notices)}条"
          )

          {:ok, @sc_get_important_notices_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "NOTICE",
            @cs_get_important_notices_p,
            user_id || "anonymous",
            "获取重要公告失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取重要公告失败")
          {:ok, @sc_get_important_notices_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "NOTICE",
          @cs_get_important_notices_p,
          user_id || "anonymous",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_important_notices_p, error_data}
    end
  end

  # 处理获取最新公告
  defp handle_get_latest_notices(data, context) do
    limit = Map.get(data, "limit", 10)
    user_id = context.user_id

    try do
      case NoticeService.get_latest_notices(limit, user_id) do
        {:ok, notices} ->
          formatted_notices = Enum.map(notices, &format_notice_item/1)

          response_data =
            ProtocolUtils.success_response(%{
              "notices" => formatted_notices,
              "count" => length(formatted_notices)
            })

          ProtocolUtils.log_protocol(
            :info,
            "NOTICE",
            @cs_get_latest_notices_p,
            user_id || "anonymous",
            "获取最新公告成功，共#{length(notices)}条"
          )

          {:ok, @sc_get_latest_notices_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "NOTICE",
            @cs_get_latest_notices_p,
            user_id || "anonymous",
            "获取最新公告失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取最新公告失败")
          {:ok, @sc_get_latest_notices_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "NOTICE",
          @cs_get_latest_notices_p,
          user_id || "anonymous",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_latest_notices_p, error_data}
    end
  end

  # 处理获取未读公告数
  defp handle_get_unread_count(context) do
    user_id = context.user_id

    if is_nil(user_id) do
      response_data =
        ProtocolUtils.success_response(%{
          "unreadCount" => 0
        })

      {:ok, @sc_get_unread_count_p, response_data}
    else
      try do
        case NoticeService.get_unread_count(user_id) do
          {:ok, count} ->
            response_data =
              ProtocolUtils.success_response(%{
                "unreadCount" => count
              })

            {:ok, @sc_get_unread_count_p, response_data}

          {:error, reason} ->
            ProtocolUtils.log_protocol(
              :error,
              "NOTICE",
              @cs_get_unread_count_p,
              user_id,
              "获取未读公告数失败: #{inspect(reason)}"
            )

            error_data = ProtocolUtils.error_response(:internal_error, "获取未读公告数失败")
            {:ok, @sc_get_unread_count_p, error_data}
        end
      rescue
        error ->
          ProtocolUtils.log_protocol(
            :error,
            "NOTICE",
            @cs_get_unread_count_p,
            user_id,
            "处理异常: #{inspect(error)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
          {:ok, @sc_get_unread_count_p, error_data}
      end
    end
  end

  # ============================================================================
  # 工具函数
  # ============================================================================

  defp format_notice_item(notice) do
    %{
      "id" => notice.id,
      "title" => notice.title,
      "category" => notice.category,
      "priority" => notice.priority,
      "isImportant" => notice.is_important || false,
      "isRead" => notice.is_read || false,
      "summary" => String.slice(notice.content || "", 0, 100),
      "imageUrl" => notice.image_url,
      "createdAt" => format_datetime(notice.inserted_at),
      "publishAt" => format_datetime(notice.publish_at),
      "expiresAt" => format_datetime(notice.expires_at)
    }
  end

  defp format_notice_detail(notice) do
    %{
      "id" => notice.id,
      "title" => notice.title,
      "content" => notice.content,
      "category" => notice.category,
      "priority" => notice.priority,
      "isImportant" => notice.is_important || false,
      "isRead" => notice.is_read || false,
      "imageUrl" => notice.image_url,
      "attachments" => notice.attachments || [],
      "author" => notice.author,
      "tags" => notice.tags || [],
      "viewCount" => notice.view_count || 0,
      "createdAt" => format_datetime(notice.inserted_at),
      "publishAt" => format_datetime(notice.publish_at),
      "expiresAt" => format_datetime(notice.expires_at),
      "updatedAt" => format_datetime(notice.updated_at)
    }
  end

  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)

  # 处理发送公告 (sub_id: 1)
  defp handle_send_notice(data, context) do
    title = Map.get(data, "title", "")
    content = Map.get(data, "content", "")
    category = Map.get(data, "category", "system")
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "NOTICE", 1, user_id, "发送公告: #{title}")

    # 模拟发送公告
    response_data =
      ProtocolUtils.success_response(%{
        "status" => 0,
        "message" => "公告发送成功",
        "notice_id" => :rand.uniform(10000),
        "title" => title,
        "category" => category
      })

    {:ok, 2, response_data}
  end

  # 处理请求公告需求 (sub_id: 3)
  defp handle_request_notice_need(data, context) do
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "NOTICE", 3, user_id, "请求公告需求")

    # 模拟获取公告需求信息
    response_data =
      ProtocolUtils.success_response(%{
        "status" => 0,
        "hasNewNotice" => :rand.uniform(10) > 5,
        "unreadCount" => :rand.uniform(5),
        "lastUpdateTime" => System.system_time(:millisecond)
      })

    {:ok, 4, response_data}
  end

  # 处理请求系统公告 (sub_id: 5)
  defp handle_request_system_notice(data, context) do
    category = Map.get(data, "category", "system")
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "NOTICE", 5, user_id, "请求系统公告: #{category}")

    # 模拟系统公告列表
    system_notices = [
      %{
        "id" => 1,
        "title" => "系统维护通知",
        "content" => "系统将于今晚进行维护更新",
        "category" => "system",
        "priority" => 1,
        "created_at" => System.system_time(:millisecond) - 3_600_000
      },
      %{
        "id" => 2,
        "title" => "新版本发布",
        "content" => "游戏已更新至v2.1.0版本",
        "category" => "update",
        "priority" => 2,
        "created_at" => System.system_time(:millisecond) - 7_200_000
      }
    ]

    response_data =
      ProtocolUtils.success_response(%{
        "status" => 0,
        "notices" => system_notices,
        "count" => length(system_notices),
        "category" => category
      })

    {:ok, 6, response_data}
  end
end
