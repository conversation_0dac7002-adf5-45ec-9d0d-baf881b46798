defmodule Teen.Protocol.RankProtocol do
  @moduledoc """
  排行榜协议处理器

  处理排行榜相关的协议，包括：
  - 获取各类排行榜
  - 查询用户排名
  - 排行榜历史记录
  - 排行榜奖励领取
  - 自定义排行榜查询
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Teen.RankSystem.{RankService, RankBoard}

  # 主协议ID
  @protocol_id 40

  # 子协议常量定义
  @cs_get_rank_list_p 0
  @sc_get_rank_list_p 1
  @cs_get_user_rank_p 2
  @sc_get_user_rank_p 3
  @cs_get_rank_types_p 4
  @sc_get_rank_types_p 5
  @cs_get_daily_rank_p 6
  @sc_get_daily_rank_p 7
  @cs_get_weekly_rank_p 8
  @sc_get_weekly_rank_p 9
  @cs_get_monthly_rank_p 10
  @sc_get_monthly_rank_p 11
  @cs_get_all_time_rank_p 12
  @sc_get_all_time_rank_p 13
  @cs_claim_rank_reward_p 14
  @sc_claim_rank_reward_p 15
  @cs_get_rank_history_p 16
  @sc_get_rank_history_p 17
  @cs_get_friends_rank_p 18
  @sc_get_friends_rank_p 19

  # 排行榜类型常量
  @rank_types %{
    "wealth" => "财富排行",
    "game_win" => "游戏胜利排行",
    "game_score" => "游戏积分排行",
    "vip_level" => "VIP等级排行",
    "daily_active" => "每日活跃排行",
    "total_recharge" => "总充值排行",
    "achievement" => "成就排行"
  }

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "Rank",
      description: "排行榜协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      {@cs_get_rank_list_p, "获取排行榜列表"},
      {@cs_get_user_rank_p, "获取用户排名"},
      {@cs_get_rank_types_p, "获取排行榜类型"},
      {@cs_get_daily_rank_p, "获取日榜"},
      {@cs_get_weekly_rank_p, "获取周榜"},
      {@cs_get_monthly_rank_p, "获取月榜"},
      {@cs_get_all_time_rank_p, "获取总榜"},
      {@cs_claim_rank_reward_p, "领取排行榜奖励"},
      {@cs_get_rank_history_p, "获取排行榜历史"},
      {@cs_get_friends_rank_p, "获取好友排行"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id || "anonymous"

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "RANK", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "RANK", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_get_rank_list_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type]},
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_get_user_rank_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type]}
        ])

      @cs_get_daily_rank_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type]},
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_get_weekly_rank_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type]},
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_get_monthly_rank_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type]},
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_get_all_time_rank_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type]},
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_claim_rank_reward_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type, :period]}
        ])

      @cs_get_rank_history_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type]},
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_get_friends_rank_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:rank_type]}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    case sub_protocol do
      @cs_get_rank_list_p ->
        handle_get_rank_list(data, context)

      @cs_get_user_rank_p ->
        handle_get_user_rank(data, context)

      @cs_get_rank_types_p ->
        handle_get_rank_types()

      @cs_get_daily_rank_p ->
        handle_get_daily_rank(data, context)

      @cs_get_weekly_rank_p ->
        handle_get_weekly_rank(data, context)

      @cs_get_monthly_rank_p ->
        handle_get_monthly_rank(data, context)

      @cs_get_all_time_rank_p ->
        handle_get_all_time_rank(data, context)

      @cs_claim_rank_reward_p ->
        handle_claim_rank_reward(data, context)

      @cs_get_rank_history_p ->
        handle_get_rank_history(data, context)

      @cs_get_friends_rank_p ->
        handle_get_friends_rank(data, context)

      _ ->
        user_id = context.user_id || "anonymous"
        ProtocolUtils.log_protocol(:warning, "RANK", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 处理获取排行榜列表
  defp handle_get_rank_list(data, context) do
    rank_type = Map.get(data, "rank_type")
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 50)
    # daily, weekly, monthly, all_time
    period = Map.get(data, "period", "all_time")
    user_id = context.user_id

    try do
      case RankService.get_rank_list(rank_type, period, page, page_size, user_id) do
        {:ok, {rank_data, total_count}} ->
          formatted_ranks = Enum.map(rank_data, &format_rank_item/1)

          response_data =
            ProtocolUtils.success_response(
              ProtocolUtils.build_pagination_response(
                formatted_ranks,
                page,
                page_size,
                total_count
              )
            )

          ProtocolUtils.log_protocol(
            :info,
            "RANK",
            @cs_get_rank_list_p,
            user_id || "anonymous",
            "获取排行榜成功: #{rank_type}/#{period}，共#{total_count}条"
          )

          {:ok, @sc_get_rank_list_p, response_data}

        {:error, :invalid_rank_type} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "无效的排行榜类型")
          {:ok, @sc_get_rank_list_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "RANK",
            @cs_get_rank_list_p,
            user_id || "anonymous",
            "获取排行榜失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取排行榜失败")
          {:ok, @sc_get_rank_list_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "RANK",
          @cs_get_rank_list_p,
          user_id || "anonymous",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_rank_list_p, error_data}
    end
  end

  # 处理获取用户排名
  defp handle_get_user_rank(data, context) do
    rank_type = Map.get(data, "rank_type")
    target_user_id = Map.get(data, "user_id", context.user_id)
    period = Map.get(data, "period", "all_time")
    user_id = context.user_id

    if is_nil(target_user_id) do
      error_data = ProtocolUtils.error_response(:invalid_params, "需要指定用户ID")
      {:ok, @sc_get_user_rank_p, error_data}
    else
      try do
        case RankService.get_user_rank(target_user_id, rank_type, period) do
          {:ok, rank_info} ->
            response_data =
              ProtocolUtils.success_response(%{
                "userId" => target_user_id,
                "rankType" => rank_type,
                "period" => period,
                "rank" => rank_info.rank,
                "score" => rank_info.score,
                "totalUsers" => rank_info.total_users,
                "percentile" => rank_info.percentile,
                "lastUpdated" => format_datetime(rank_info.last_updated)
              })

            ProtocolUtils.log_protocol(
              :info,
              "RANK",
              @cs_get_user_rank_p,
              user_id || "anonymous",
              "获取用户排名成功: #{target_user_id}/#{rank_type}"
            )

            {:ok, @sc_get_user_rank_p, response_data}

          {:error, :user_not_found} ->
            error_data = ProtocolUtils.error_response(:not_found, "用户不存在")
            {:ok, @sc_get_user_rank_p, error_data}

          {:error, :no_rank_data} ->
            response_data =
              ProtocolUtils.success_response(%{
                "userId" => target_user_id,
                "rankType" => rank_type,
                "period" => period,
                "rank" => nil,
                "score" => 0,
                "msg" => "暂无排名数据"
              })

            {:ok, @sc_get_user_rank_p, response_data}

          {:error, reason} ->
            ProtocolUtils.log_protocol(
              :error,
              "RANK",
              @cs_get_user_rank_p,
              user_id || "anonymous",
              "获取用户排名失败: #{inspect(reason)}"
            )

            error_data = ProtocolUtils.error_response(:internal_error, "获取用户排名失败")
            {:ok, @sc_get_user_rank_p, error_data}
        end
      rescue
        error ->
          ProtocolUtils.log_protocol(
            :error,
            "RANK",
            @cs_get_user_rank_p,
            user_id || "anonymous",
            "处理异常: #{inspect(error)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
          {:ok, @sc_get_user_rank_p, error_data}
      end
    end
  end

  # 处理获取排行榜类型
  defp handle_get_rank_types() do
    try do
      rank_types =
        Enum.map(@rank_types, fn {key, name} ->
          %{
            "id" => key,
            "name" => name,
            "description" => get_rank_description(key)
          }
        end)

      response_data =
        ProtocolUtils.success_response(%{
          "rankTypes" => rank_types
        })

      {:ok, @sc_get_rank_types_p, response_data}
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "RANK",
          @cs_get_rank_types_p,
          "system",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_rank_types_p, error_data}
    end
  end

  # 处理获取日榜
  defp handle_get_daily_rank(data, context) do
    handle_period_rank(data, context, "daily", @sc_get_daily_rank_p)
  end

  # 处理获取周榜
  defp handle_get_weekly_rank(data, context) do
    handle_period_rank(data, context, "weekly", @sc_get_weekly_rank_p)
  end

  # 处理获取月榜
  defp handle_get_monthly_rank(data, context) do
    handle_period_rank(data, context, "monthly", @sc_get_monthly_rank_p)
  end

  # 处理获取总榜
  defp handle_get_all_time_rank(data, context) do
    handle_period_rank(data, context, "all_time", @sc_get_all_time_rank_p)
  end

  # 通用的周期排行榜处理函数
  defp handle_period_rank(data, context, period, response_protocol) do
    rank_type = Map.get(data, "rank_type")
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 50)
    user_id = context.user_id

    try do
      case RankService.get_rank_list(rank_type, period, page, page_size, user_id) do
        {:ok, {rank_data, total_count}} ->
          formatted_ranks = Enum.map(rank_data, &format_rank_item/1)

          response_data =
            ProtocolUtils.success_response(%{
              "rankType" => rank_type,
              "period" => period,
              "ranks" => formatted_ranks,
              "totalCount" => total_count,
              "currentPage" => page,
              "pageSize" => page_size,
              "refreshTime" => get_next_refresh_time(period)
            })

          ProtocolUtils.log_protocol(
            :info,
            "RANK",
            response_protocol,
            user_id || "anonymous",
            "获取#{period}排行榜成功: #{rank_type}，共#{total_count}条"
          )

          {:ok, response_protocol, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "RANK",
            response_protocol,
            user_id || "anonymous",
            "获取#{period}排行榜失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取排行榜失败")
          {:ok, response_protocol, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "RANK",
          response_protocol,
          user_id || "anonymous",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, response_protocol, error_data}
    end
  end

  # 处理领取排行榜奖励
  defp handle_claim_rank_reward(data, context) do
    rank_type = Map.get(data, "rank_type")
    period = Map.get(data, "period")
    user_id = context.user_id

    if is_nil(user_id) do
      error_data = ProtocolUtils.error_response(:unauthorized, "用户未登录")
      {:ok, @sc_claim_rank_reward_p, error_data}
    else
      try do
        case RankService.claim_rank_reward(user_id, rank_type, period) do
          {:ok, reward_info} ->
            response_data =
              ProtocolUtils.success_response(%{
                "claimed" => true,
                "rewards" => format_rewards(reward_info.rewards),
                "rank" => reward_info.rank,
                "period" => period,
                "msg" => "排行榜奖励领取成功"
              })

            ProtocolUtils.log_protocol(
              :info,
              "RANK",
              @cs_claim_rank_reward_p,
              user_id,
              "领取排行榜奖励成功: #{rank_type}/#{period}"
            )

            {:ok, @sc_claim_rank_reward_p, response_data}

          {:error, :not_eligible} ->
            error_data = ProtocolUtils.error_response(:forbidden, "您不符合奖励领取条件")
            {:ok, @sc_claim_rank_reward_p, error_data}

          {:error, :already_claimed} ->
            error_data = ProtocolUtils.error_response(:forbidden, "奖励已领取")
            {:ok, @sc_claim_rank_reward_p, error_data}

          {:error, :expired} ->
            error_data = ProtocolUtils.error_response(:forbidden, "奖励已过期")
            {:ok, @sc_claim_rank_reward_p, error_data}

          {:error, reason} ->
            ProtocolUtils.log_protocol(
              :error,
              "RANK",
              @cs_claim_rank_reward_p,
              user_id,
              "领取排行榜奖励失败: #{inspect(reason)}"
            )

            error_data = ProtocolUtils.error_response(:internal_error, "领取奖励失败")
            {:ok, @sc_claim_rank_reward_p, error_data}
        end
      rescue
        error ->
          ProtocolUtils.log_protocol(
            :error,
            "RANK",
            @cs_claim_rank_reward_p,
            user_id,
            "处理异常: #{inspect(error)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
          {:ok, @sc_claim_rank_reward_p, error_data}
      end
    end
  end

  # 处理获取排行榜历史
  defp handle_get_rank_history(data, context) do
    rank_type = Map.get(data, "rank_type")
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)
    user_id = context.user_id

    if is_nil(user_id) do
      error_data = ProtocolUtils.error_response(:unauthorized, "用户未登录")
      {:ok, @sc_get_rank_history_p, error_data}
    else
      try do
        case RankService.get_user_rank_history(user_id, rank_type, page, page_size) do
          {:ok, {history_data, total_count}} ->
            formatted_history = Enum.map(history_data, &format_rank_history_item/1)

            response_data =
              ProtocolUtils.success_response(
                ProtocolUtils.build_pagination_response(
                  formatted_history,
                  page,
                  page_size,
                  total_count
                )
              )

            ProtocolUtils.log_protocol(
              :info,
              "RANK",
              @cs_get_rank_history_p,
              user_id,
              "获取排行榜历史成功: #{rank_type}，共#{total_count}条"
            )

            {:ok, @sc_get_rank_history_p, response_data}

          {:error, reason} ->
            ProtocolUtils.log_protocol(
              :error,
              "RANK",
              @cs_get_rank_history_p,
              user_id,
              "获取排行榜历史失败: #{inspect(reason)}"
            )

            error_data = ProtocolUtils.error_response(:internal_error, "获取排行榜历史失败")
            {:ok, @sc_get_rank_history_p, error_data}
        end
      rescue
        error ->
          ProtocolUtils.log_protocol(
            :error,
            "RANK",
            @cs_get_rank_history_p,
            user_id,
            "处理异常: #{inspect(error)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
          {:ok, @sc_get_rank_history_p, error_data}
      end
    end
  end

  # 处理获取好友排行
  defp handle_get_friends_rank(data, context) do
    rank_type = Map.get(data, "rank_type")
    period = Map.get(data, "period", "all_time")
    user_id = context.user_id

    if is_nil(user_id) do
      error_data = ProtocolUtils.error_response(:unauthorized, "用户未登录")
      {:ok, @sc_get_friends_rank_p, error_data}
    else
      try do
        case RankService.get_friends_rank(user_id, rank_type, period) do
          {:ok, friends_rank} ->
            formatted_ranks = Enum.map(friends_rank, &format_rank_item/1)

            response_data =
              ProtocolUtils.success_response(%{
                "rankType" => rank_type,
                "period" => period,
                "friendsRank" => formatted_ranks,
                "count" => length(formatted_ranks)
              })

            ProtocolUtils.log_protocol(
              :info,
              "RANK",
              @cs_get_friends_rank_p,
              user_id,
              "获取好友排行成功: #{rank_type}，共#{length(friends_rank)}人"
            )

            {:ok, @sc_get_friends_rank_p, response_data}

          {:error, reason} ->
            ProtocolUtils.log_protocol(
              :error,
              "RANK",
              @cs_get_friends_rank_p,
              user_id,
              "获取好友排行失败: #{inspect(reason)}"
            )

            error_data = ProtocolUtils.error_response(:internal_error, "获取好友排行失败")
            {:ok, @sc_get_friends_rank_p, error_data}
        end
      rescue
        error ->
          ProtocolUtils.log_protocol(
            :error,
            "RANK",
            @cs_get_friends_rank_p,
            user_id,
            "处理异常: #{inspect(error)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
          {:ok, @sc_get_friends_rank_p, error_data}
      end
    end
  end

  # ============================================================================
  # 工具函数
  # ============================================================================

  defp format_rank_item(rank_item) do
    %{
      "rank" => rank_item.rank,
      "userId" => rank_item.user_id,
      "username" => rank_item.username,
      "avatar" => rank_item.avatar,
      "score" => rank_item.score,
      # 相比上期的排名变化
      "change" => rank_item.rank_change || 0,
      "vipLevel" => rank_item.vip_level || 0,
      # 用户称号
      "title" => rank_item.title,
      "lastUpdated" => format_datetime(rank_item.last_updated)
    }
  end

  defp format_rank_history_item(history_item) do
    %{
      "period" => history_item.period,
      "rank" => history_item.rank,
      "score" => history_item.score,
      "totalUsers" => history_item.total_users,
      "rewardClaimed" => history_item.reward_claimed || false,
      "createdAt" => format_datetime(history_item.inserted_at)
    }
  end

  defp format_rewards(rewards) when is_list(rewards) do
    Enum.map(rewards, fn reward ->
      %{
        "type" => reward.type,
        "amount" => reward.amount,
        "itemId" => reward.item_id,
        "description" => reward.description
      }
    end)
  end

  defp format_rewards(_), do: []

  defp get_rank_description(rank_type) do
    case rank_type do
      "wealth" -> "根据用户总财富排序"
      "game_win" -> "根据游戏胜利次数排序"
      "game_score" -> "根据游戏积分排序"
      "vip_level" -> "根据VIP等级排序"
      "daily_active" -> "根据每日活跃度排序"
      "total_recharge" -> "根据总充值金额排序"
      "achievement" -> "根据成就点数排序"
      _ -> "未知排行榜类型"
    end
  end

  defp get_next_refresh_time(period) do
    now = DateTime.utc_now()

    case period do
      "daily" ->
        # 下一天0点
        now
        |> DateTime.add(1, :day)
        |> DateTime.beginning_of_day()
        |> DateTime.to_unix(:millisecond)

      "weekly" ->
        # 下周一0点
        days_until_monday = 7 - Date.day_of_week(DateTime.to_date(now)) + 1

        now
        |> DateTime.add(days_until_monday, :day)
        |> DateTime.beginning_of_day()
        |> DateTime.to_unix(:millisecond)

      "monthly" ->
        # 下月1号0点
        now
        |> DateTime.add(1, :month)
        |> DateTime.beginning_of_month()
        |> DateTime.to_unix(:millisecond)

      _ ->
        # 全时榜不刷新
        nil
    end
  end

  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)
end
