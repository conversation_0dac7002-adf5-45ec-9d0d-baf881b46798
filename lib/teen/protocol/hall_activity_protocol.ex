defmodule Teen.Protocol.HallActivityProtocol do
  @moduledoc """
  大厅活动协议处理器

  对应客户端的 HallActivity 协议，包括：
  - 登录活动 (LoginCash)
  - 七日签到 (SevenDays)
  - 游戏任务 (GameTask)
  - 30次刮卡 (ThirtyCard)
  - 礼包活动 (GiftCharge)
  - 周卡月卡 (CardTask)
  - VIP礼包 (VipGift)
  - 免费积分 (FreeBonus)
  - 免费提现 (FreeCash)
  - 破产补助 (BrokeAward)
  - 邮件奖励 (MailAward)
  - 绑定奖励 (BindPhone/BindMail)
  - CDKEY (CdkeyAward)
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Logger
  alias Teen.ActivitySystem.{ActivityService, CdkeyService}
  alias Cypridina.Accounts.User
  alias Teen.Protocol.ProtocolUtils

  # 协议常量定义 - 对应客户端 HallActivity
  @cs_logincash_info_p 0
  @sc_logincash_info_p 1
  @cs_fetch_logincash_award_p 2
  @sc_fetch_logincash_award_p 3
  @cs_get_user_money_p 4
  @sc_get_user_money_p 5
  @cs_fetch_user_bonus_p 6
  @sc_fetch_user_bonus_p 7
  @cs_get_seven_days_p 8
  @sc_get_seven_days_p 9
  @cs_fetch_seven_days_award_p 10
  @sc_fetch_seven_days_award_p 11
  @cs_get_thirty_card_p 12
  @sc_get_thirty_card_p 13
  @cs_fetch_thirty_card_p 14
  @sc_fetch_thirty_card_p 15
  @cs_game_task_p 16
  @sc_game_task_p 17
  @cs_fetch_game_task_award_p 18
  @sc_fetch_game_task_award_p 19
  @cs_get_gift_charge_p 20
  @sc_get_gift_charge_p 21
  @cs_get_card_task_p 22
  @sc_get_card_task_p 23
  @cs_fetch_card_task_p 24
  @sc_fetch_card_task_p 25
  @cs_get_vip_gift_p 26
  @sc_get_vip_gift_p 27
  @cs_fetch_vip_gift_p 28
  @sc_fetch_vip_gift_p 29
  @cs_get_free_bonus_p 30
  @sc_get_free_bonus_p 31
  @cs_fetch_free_bonus_p 32
  @sc_fetch_free_bonus_p 33
  @cs_get_free_cash_p 34
  @sc_get_free_cash_p 35
  @cs_fetch_free_cash_p 36
  @sc_fetch_free_cash_p 37
  @cs_get_free_cash_invitation_p 38
  @sc_get_free_cash_invitation_p 39
  @cs_fetch_broke_award_p 40
  @sc_fetch_broke_award_p 41
  @cs_fetch_mail_award_p 42
  @sc_fetch_mail_award_p 43
  @cs_bind_phone_user_p 44
  @sc_bind_phone_user_p 45
  @cs_bind_mail_user_p 46
  @sc_bind_mail_user_p 47
  @cs_fetch_cdkey_award_p 48
  @sc_fetch_cdkey_award_p 49
  @cs_get_binding_status_p 50
  @sc_get_binding_status_p 51

  # 新增登录奖励相关协议
  @cs_get_daily_login_bonus_p 52
  @sc_get_daily_login_bonus_p 53
  @cs_claim_daily_login_bonus_p 54
  @sc_claim_daily_login_bonus_p 55

  # 主协议ID
  @protocol_id 101

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "HallActivity",
      description: "大厅活动协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      {@cs_logincash_info_p, "获取登录活动信息"},
      {@cs_fetch_logincash_award_p, "领取登录活动奖励"},
      {@cs_get_user_money_p, "获取用户金币信息"},
      {@cs_fetch_user_bonus_p, "领取用户积分"},
      {@cs_get_seven_days_p, "获取七日签到信息"},
      {@cs_fetch_seven_days_award_p, "领取七日签到奖励"},
      {@cs_get_thirty_card_p, "获取30次刮卡信息"},
      {@cs_fetch_thirty_card_p, "领取30次刮卡奖励"},
      {@cs_game_task_p, "获取游戏任务信息"},
      {@cs_fetch_game_task_award_p, "领取游戏任务奖励"},
      {@cs_get_gift_charge_p, "获取礼包活动信息"},
      {@cs_get_card_task_p, "获取周卡月卡信息"},
      {@cs_fetch_card_task_p, "领取周卡月卡奖励"},
      {@cs_get_vip_gift_p, "获取VIP礼包信息"},
      {@cs_fetch_vip_gift_p, "领取VIP礼包奖励"},
      {@cs_get_free_bonus_p, "获取免费积分信息"},
      {@cs_fetch_free_bonus_p, "领取免费积分奖励"},
      {@cs_get_free_cash_p, "获取免费提现信息"},
      {@cs_fetch_free_cash_p, "领取免费提现奖励"},
      {@cs_get_free_cash_invitation_p, "获取免费提现邀请信息"},
      {@cs_fetch_broke_award_p, "领取破产补助"},
      {@cs_fetch_mail_award_p, "领取邮件奖励"},
      {@cs_bind_phone_user_p, "绑定手机号"},
      {@cs_bind_mail_user_p, "绑定邮箱"},
      {@cs_fetch_cdkey_award_p, "领取CDKEY奖励"},
      {@cs_get_binding_status_p, "获取绑定状态"},
      {@cs_get_daily_login_bonus_p, "获取每日登录奖励信息"},
      {@cs_claim_daily_login_bonus_p, "领取每日登录奖励"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "HALL_ACTIVITY", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "HALL_ACTIVITY",
          sub_protocol,
          user_id,
          "数据验证失败: #{reason}"
        )

        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_fetch_logincash_award_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:fetchtype]},
          {:custom, :fetchtype,
           fn value ->
             if value in [0, 1, 2, 3, 4, 10] do
               :ok
             else
               {:error, "奖励类型必须是0(登录)、1(充值)、2(游戏局数)、3(转盘)、4(游戏赢分)或10(完成)之一"}
             end
           end, "奖励类型"}
        ])

      @cs_fetch_seven_days_award_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:fetchid]},
          {:number_range, :fetchid, 1, 7, "签到天数"}
        ])

      @cs_fetch_game_task_award_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:taskid]},
          {:string_length, :taskid, 1, 50, "任务ID"}
        ])

      @cs_bind_phone_user_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:phone, :checkcode]},
          {:string_length, :phone, 10, 15, "手机号"}
        ])

      @cs_bind_mail_user_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:mail, :name]}
        ])

      @cs_fetch_cdkey_award_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:cdkey]},
          {:string_length, :cdkey, 1, 50, "兑换码"}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id

    case sub_protocol do
      @cs_logincash_info_p ->
        handle_get_logincash_info(user_id)

      @cs_fetch_logincash_award_p ->
        handle_fetch_logincash_award(user_id, data)

      @cs_get_user_money_p ->
        handle_get_user_money(user_id)

      @cs_fetch_user_bonus_p ->
        handle_fetch_user_bonus(user_id, data)

      @cs_get_seven_days_p ->
        handle_get_seven_days(user_id)

      @cs_fetch_seven_days_award_p ->
        handle_fetch_seven_days_award(user_id, data)

      @cs_get_thirty_card_p ->
        handle_get_thirty_card(user_id)

      @cs_fetch_thirty_card_p ->
        handle_fetch_thirty_card(user_id, data)

      @cs_game_task_p ->
        handle_get_game_task(user_id)

      @cs_fetch_game_task_award_p ->
        handle_fetch_game_task_award(user_id, data)

      @cs_get_gift_charge_p ->
        handle_get_gift_charge(user_id)

      @cs_get_card_task_p ->
        handle_get_card_task(user_id)

      @cs_fetch_card_task_p ->
        handle_fetch_card_task(user_id, data)

      @cs_get_vip_gift_p ->
        handle_get_vip_gift(user_id)

      @cs_fetch_vip_gift_p ->
        handle_fetch_vip_gift(user_id, data)

      @cs_get_free_bonus_p ->
        handle_get_free_bonus(user_id)

      @cs_fetch_free_bonus_p ->
        handle_fetch_free_bonus(user_id, data)

      @cs_get_free_cash_p ->
        handle_get_free_cash(user_id)

      @cs_fetch_free_cash_p ->
        handle_fetch_free_cash(user_id, data)

      @cs_get_free_cash_invitation_p ->
        handle_get_free_cash_invitation(user_id, data)

      @cs_fetch_broke_award_p ->
        handle_fetch_broke_award(user_id, data)

      @cs_fetch_mail_award_p ->
        handle_fetch_mail_award(user_id, data)

      @cs_bind_phone_user_p ->
        handle_bind_phone(user_id, data)

      @cs_bind_mail_user_p ->
        handle_bind_mail(user_id, data)

      @cs_fetch_cdkey_award_p ->
        handle_fetch_cdkey_award(user_id, data)

      @cs_get_binding_status_p ->
        handle_get_binding_status(user_id)

      @cs_get_daily_login_bonus_p ->
        handle_get_daily_login_bonus(user_id)

      @cs_claim_daily_login_bonus_p ->
        handle_claim_daily_login_bonus(user_id, data)

      _ ->
        ProtocolUtils.log_protocol(:warning, "HALL_ACTIVITY", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 获取登录活动信息
  defp handle_get_logincash_info(user_id) do
    case Teen.ActivitySystem.LoginCashActivity.get_login_cash_data(user_id) do
      {:ok, data} ->
        ProtocolUtils.log_protocol(
          :info,
          "HALL_ACTIVITY",
          @cs_logincash_info_p,
          user_id,
          "返回登录活动信息"
        )

        {:ok, @sc_logincash_info_p, ProtocolUtils.success_response(data)}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "HALL_ACTIVITY",
          @cs_logincash_info_p,
          user_id,
          "获取登录活动信息失败: #{inspect(reason)}"
        )

        {:ok, @sc_logincash_info_p, ProtocolUtils.error_response(1, "获取活动信息失败")}
    end
  end

  # 领取登录活动奖励
  defp handle_fetch_logincash_award(user_id, data) do
    case Teen.ActivitySystem.LoginCashActivity.process_login_cash_claim(user_id, data) do
      {:ok, response} ->
        ProtocolUtils.log_protocol(
          :info,
          "HALL_ACTIVITY",
          @cs_fetch_logincash_award_p,
          user_id,
          "登录活动奖励领取成功"
        )

        {:ok, @sc_fetch_logincash_award_p, response}

      {:error, response} ->
        ProtocolUtils.log_protocol(
          :warning,
          "HALL_ACTIVITY",
          @cs_fetch_logincash_award_p,
          user_id,
          "登录活动奖励领取失败"
        )

        {:ok, @sc_fetch_logincash_award_p, response}
    end
  end

  # 获取用户金币信息
  defp handle_get_user_money(user_id) do
    Logger.info("🎯 [USER_MONEY] 获取用户金币信息: #{user_id}")

    # 安全获取用户积分，添加错误处理
    points = Cypridina.Accounts.get_user_points(user_id)

    Logger.info("🎯 [USER_MONEY] 用户积分: #{points}")

    # 获取用户VIP等级，如果失败则默认为0
    vip_level = get_user_vip_level(user_id)

    # 获取用户turnover信息以获取bonus相关字段
    {bonusmoney, bonuscashmoney, winningmoney} =
      case Teen.UserSystem.get_or_create_user_turnover(user_id) do
        {:ok, turnover} ->
          {turnover.bonusmoney || 0, turnover.bonuscashmoney || 0, turnover.winningmoney || 0}

        {:error, _} ->
          {0, 0, 0}
      end

    # 根据协议文档，返回格式应该是：
    # money: int64 - 当前金币
    # walletMoney: int64 - 钱包金币
    # diamonds: int - 钻石数量
    # vipLevel: int - VIP等级
    # vipExp: int64 - VIP经验值
    # bonusmoney, bonuscashmoney, winningmoney - 客户端需要的额外字段
    money_info = %{
      # 钱包金币，暂时与总金币相同
      "walletMoney" => points,
      # 钻石数量，需要从实际数据获取
      "diamonds" => 0,
      # VIP经验值，需要从实际数据获取
      "vipExp" => 0,
      "user" => %{
        "money" => points,
        "bonusmoney" => bonusmoney,
        "bonuscashmoney" => bonuscashmoney,
        "winningmoney" => winningmoney,
        "vip" => vip_level
      }
    }

    Logger.info("🎯 [USER_MONEY] 用户金币信息获取成功: #{user_id}, 金币: #{money_info["money"]}")
    Logger.info("🎯 [USER_MONEY] 返回成功响应: #{inspect(money_info)}")
    {:ok, @sc_get_user_money_p, money_info}
  end

  # 领取用户积分
  defp handle_fetch_user_bonus(user_id, %{"fetchamount" => amount} = data) do
    ip = Map.get(data, "ip", "")

    case Cypridina.Accounts.add_points(user_id, amount,
           transaction_type: :bonus,
           description: "bonus_fetch"
         ) do
      {:ok, _} ->
        response_data =
          ProtocolUtils.success_response(%{
            fetchamount: amount,
            msg: "积分领取成功"
          })

        {:ok, @sc_fetch_user_bonus_p, response_data}

      {:error, reason} ->
        response_data =
          ProtocolUtils.error_response(:internal_error, "积分领取失败: #{inspect(reason)}")

        {:ok, @sc_fetch_user_bonus_p, response_data}
    end
  end

  # 获取七日签到信息
  defp handle_get_seven_days(user_id) do
    case build_seven_days_data(user_id) do
      seven_days_data when is_map(seven_days_data) ->
        ProtocolUtils.log_protocol(
          :info,
          "HALL_ACTIVITY",
          @cs_get_seven_days_p,
          user_id,
          "返回七日签到信息"
        )

        {:ok, @sc_get_seven_days_p, ProtocolUtils.success_response(seven_days_data)}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "HALL_ACTIVITY",
          @cs_get_seven_days_p,
          user_id,
          "获取七日签到信息失败: #{inspect(reason)}"
        )

        # 返回默认数据
        default_data = %{
          currentday: 1,
          second: 0,
          sevendata: build_default_seven_days_config()
        }

        {:ok, @sc_get_seven_days_p, ProtocolUtils.success_response(default_data)}

      _ ->
        ProtocolUtils.log_protocol(
          :error,
          "HALL_ACTIVITY",
          @cs_get_seven_days_p,
          user_id,
          "获取七日签到信息返回意外值"
        )

        # 返回默认数据
        default_data = %{
          currentday: 1,
          second: 0,
          sevendata: build_default_seven_days_config()
        }

        {:ok, @sc_get_seven_days_p, ProtocolUtils.success_response(default_data)}
    end
  end

  # 领取七日签到奖励
  defp handle_fetch_seven_days_award(user_id, %{"fetchid" => fetch_id} = data) do
    ip = Map.get(data, "ip", "")

    # 获取七日签到配置
    seven_day_config = build_default_seven_days_config()
    day_key = "#{fetch_id}"

    case Map.get(seven_day_config, day_key) do
      nil ->
        response_data = %{
          code: 1,
          msg: "无效的签到天数"
        }

        {:ok, @sc_fetch_seven_days_award_p, response_data}

      day_config ->
        # 检查是否已经领取过
        case check_reward_already_claimed(user_id, "seven_day_task", "#{fetch_id}") do
          true ->
            response_data = %{
              code: 1,
              msg: "今日已签到"
            }

            {:ok, @sc_fetch_seven_days_award_p, response_data}

          false ->
            # 检查充值条件
            user_total_recharge = get_user_total_recharge_amount(user_id)
            required_charge = day_config["chargeamount"]

            if user_total_recharge >= required_charge do
              # 可以领取奖励
              reward_amount = day_config["awardcash"]

              case process_reward_claim(user_id, "seven_day_task", "#{fetch_id}", reward_amount) do
                {:ok, _} ->
                  Logger.info(
                    "🎯 [HALL_ACTIVITY] 七日签到奖励领取成功 - 用户: #{user_id}, 天数: #{fetch_id}, 奖励: #{reward_amount}"
                  )

                  response_data = %{
                    code: 0,
                    fetchaward: reward_amount,
                    msg: "签到奖励领取成功"
                  }

                  {:ok, @sc_fetch_seven_days_award_p, response_data}

                {:error, :already_claimed} ->
                  response_data = %{
                    code: 1,
                    msg: "第#{fetch_id}天奖励已经领取过了"
                  }

                  {:ok, @sc_fetch_seven_days_award_p, response_data}

                {:error, reason} ->
                  response_data = %{
                    code: 1,
                    msg: "奖励发放失败: #{inspect(reason)}"
                  }

                  {:ok, @sc_fetch_seven_days_award_p, response_data}
              end
            else
              response_data = %{
                code: 1,
                msg: "充值金额不足，需要充值 ₹#{required_charge}"
              }

              {:ok, @sc_fetch_seven_days_award_p, response_data}
            end
        end
    end
  end

  # 获取30次刮卡信息
  defp handle_get_thirty_card(user_id) do
    case Teen.ActivitySystem.ScratchCardService.get_user_activity_info(user_id) do
      {:ok, data} ->
        ProtocolUtils.log_protocol(
          :info,
          "HALL_ACTIVITY",
          @cs_get_thirty_card_p,
          user_id,
          "获取30次刮卡信息成功"
        )

        {:ok, @sc_get_thirty_card_p, ProtocolUtils.success_response(data)}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "HALL_ACTIVITY",
          @cs_get_thirty_card_p,
          user_id,
          "获取30次刮卡信息失败: #{inspect(reason)}"
        )

        error_data = %{
          "code" => 1999,
          "msg" => "获取活动信息失败",
          "user" => %{},
          "leveldata" => %{}
        }

        {:ok, @sc_get_thirty_card_p, error_data}
    end
  end

  # 领取刮卡奖励
  defp handle_fetch_thirty_card(user_id, data) do
    # 检查是否是两步领取模式
    fetch_type = Map.get(data, "fetchtype")

    if not is_nil(fetch_type) do
      # 两步领取模式
      ProtocolUtils.log_protocol(
        :info,
        "HALL_ACTIVITY",
        @cs_fetch_thirty_card_p,
        user_id,
        "两步领取模式 - fetchtype: #{fetch_type}"
      )

      # 确保 fetch_type 是整数
      fetch_type_int =
        case fetch_type do
          ft when is_binary(ft) -> String.to_integer(ft)
          ft when is_integer(ft) -> ft
          _ -> 0
        end

      case Teen.ActivitySystem.ScratchCardService.claim_card_reward(user_id, fetch_type_int) do
        {:ok, result} ->
          ProtocolUtils.log_protocol(
            :info,
            "HALL_ACTIVITY",
            @cs_fetch_thirty_card_p,
            user_id,
            "两步领取成功"
          )

          {:ok, @sc_fetch_thirty_card_p, result}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "HALL_ACTIVITY",
            @cs_fetch_thirty_card_p,
            user_id,
            "两步领取失败: #{inspect(reason)}"
          )

          response_data = %{
            "code" => 1999,
            "msg" => "系统错误",
            "fetchtype" => fetch_type,
            "fetchawardlist" => %{}
          }

          {:ok, @sc_fetch_thirty_card_p, response_data}
      end
    else
      # 旧的单步领取模式
      card_index = Map.get(data, "cardindex", 0)
      level = Map.get(data, "level", 1)

      ProtocolUtils.log_protocol(
        :info,
        "HALL_ACTIVITY",
        @cs_fetch_thirty_card_p,
        user_id,
        "单步领取模式 - 卡片索引: #{card_index}, 等级: #{level}"
      )

      case Teen.ActivitySystem.ScratchCardService.claim_card_reward(user_id, card_index, level) do
        {:ok, result} ->
          ProtocolUtils.log_protocol(
            :info,
            "HALL_ACTIVITY",
            @cs_fetch_thirty_card_p,
            user_id,
            "刮卡奖励领取成功"
          )

          {:ok, @sc_fetch_thirty_card_p, result}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "HALL_ACTIVITY",
            @cs_fetch_thirty_card_p,
            user_id,
            "刮卡奖励领取失败: #{inspect(reason)}"
          )

          response_data = %{
            "code" => 1999,
            "msg" => "系统错误",
            "cardindex" => card_index,
            "level" => level,
            "awardtype" => 0,
            "awardnum" => 0
          }

          {:ok, @sc_fetch_thirty_card_p, response_data}
      end
    end
  end

  # 获取游戏任务信息
  defp handle_get_game_task(user_id) do
    case ActivityService.get_user_available_activities(user_id) do
      {:ok, activities} ->
        game_tasks_data = build_game_tasks_data(activities.game_tasks, user_id)
        {:ok, @sc_game_task_p, game_tasks_data}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 领取游戏任务奖励
  defp handle_fetch_game_task_award(user_id, %{"taskid" => task_id}) do
    case ActivityService.claim_activity_reward(user_id, "game_task", task_id) do
      {:ok, record} ->
        response_data = %{
          code: 0,
          fetchaward: record.reward_amount,
          msg: "任务奖励领取成功"
        }

        {:ok, @sc_fetch_game_task_award_p, response_data}

      {:error, reason} ->
        response_data = %{
          code: 1,
          msg: "任务奖励领取失败: #{inspect(reason)}"
        }

        {:ok, @sc_fetch_game_task_award_p, response_data}
    end
  end

  # 获取礼包充值活动信息
  defp handle_get_gift_charge(user_id) do
    ProtocolUtils.log_protocol(:info, "HALL_ACTIVITY", @cs_get_gift_charge_p, user_id, "获取首充礼包信息")
    gift_data = build_gift_charge_data(user_id)

    # 客户端期望的格式是 giftdata 包含礼包信息
    response = %{
      giftdata: %{
        "gift_1" => gift_data
      }
    }

    ProtocolUtils.log_protocol(:info, "HALL_ACTIVITY", @cs_get_gift_charge_p, user_id, "返回首充礼包数据")
    {:ok, @sc_get_gift_charge_p, ProtocolUtils.success_response(response)}
  end

  # 获取周卡月卡活动信息
  defp handle_get_card_task(user_id) do
    Logger.info("🎴 [CARD_TASK] 获取卡任务信息: #{user_id}")
    card_data = build_card_task_data(user_id)

    # 确保卡任务数据有正确的格式，客户端期望特定的字段结构
    formatted_card_data =
      case card_data do
        %{usertask: tasks} when is_map(tasks) ->
          # 确保每个任务都有完整的字段，包括 active 字段
          enhanced_tasks =
            tasks
            |> Enum.map(fn {key, task} ->
              enhanced_task =
                task
                # 确保有 active 字段
                |> Map.put("active", Map.get(task, "active", true))
                # 确保有 enabled 字段
                |> Map.put("enabled", Map.get(task, "enabled", true))
                # 确保有 available 字段
                |> Map.put("available", Map.get(task, "available", true))
                # 确保有 can_claim 字段
                |> Map.put("can_claim", Map.get(task, "can_claim", false))
                # 确保有 claimed 字段
                |> Map.put("claimed", Map.get(task, "claimed", false))
                # 确保有 progress 字段
                |> Map.put("progress", Map.get(task, "progress", 0))
                # 确保有 max_progress 字段
                |> Map.put("max_progress", Map.get(task, "max_progress", 1))

              {key, enhanced_task}
            end)
            |> Enum.into(%{})

          response = %{
            usertask: enhanced_tasks,
            code: 0,
            msg: "获取成功",
            # 添加额外字段确保客户端兼容性
            # 备用字段名
            tasks: enhanced_tasks,
            # 备用字段名
            cardtasks: enhanced_tasks
          }

          Logger.info("🎴 [CARD_TASK] 卡任务数据: #{inspect(response)}")
          response

        _ ->
          # 返回默认数据结构
          default_task = %{
            "id" => 1,
            "title" => "周卡",
            "price" => 500,
            "daily_reward" => 100,
            "days" => 7,
            "isfetch" => 0,
            "status" => 1,
            "active" => true,
            "enabled" => true,
            "available" => true,
            "can_claim" => false,
            "claimed" => false,
            "progress" => 0,
            "max_progress" => 1,
            "purchased" => false,
            "days_remaining" => 0
          }

          response = %{
            usertask: %{"weekly_card" => default_task},
            code: 0,
            msg: "获取成功",
            tasks: %{"weekly_card" => default_task},
            cardtasks: %{"weekly_card" => default_task}
          }

          Logger.info("🎴 [CARD_TASK] 默认卡任务数据: #{inspect(response)}")
          response
      end

    ProtocolUtils.log_protocol(:info, "HALL_ACTIVITY", @cs_get_card_task_p, user_id, "返回卡任务信息")
    {:ok, @sc_get_card_task_p, %{}}
  end

  # 领取周卡月卡奖励
  defp handle_fetch_card_task(user_id, data) do
    Logger.info("🎴 [CARD_TASK] 领取卡任务奖励: #{user_id}, data: #{inspect(data)}")
    fetch_result = process_card_task_claim(user_id, data)

    # 确保返回结果有完整的字段结构
    enhanced_result =
      case fetch_result do
        %{"code" => code} = result when code == 0 ->
          # 成功情况，确保所有必要字段都存在
          result
          |> Map.put_new("fetchaward", Map.get(result, "fetchaward", 0))
          |> Map.put_new("msg", Map.get(result, "msg", "领取成功"))
          |> Map.put_new("remaining_days", Map.get(result, "remaining_days", 0))
          |> Map.put_new("next_claim_time", Map.get(result, "next_claim_time", 0))

        %{"code" => code} = result when code != 0 ->
          # 错误情况，确保有错误信息
          result
          |> Map.put_new("msg", Map.get(result, "msg", "领取失败"))
          |> Map.put_new("fetchaward", 0)
          |> Map.put_new("remaining_days", 0)
          |> Map.put_new("next_claim_time", 0)

        %{code: code} = result when code == 0 ->
          # 成功情况（atom key）
          %{
            "code" => 0,
            "fetchaward" => Map.get(result, :fetchaward, 0),
            "msg" => Map.get(result, :msg, "领取成功"),
            "remaining_days" => Map.get(result, :remaining_days, 0),
            "next_claim_time" => Map.get(result, :next_claim_time, 0)
          }

        %{code: code} = result when code != 0 ->
          # 错误情况（atom key）
          %{
            "code" => code,
            "msg" => Map.get(result, :msg, "领取失败"),
            "fetchaward" => 0,
            "remaining_days" => 0,
            "next_claim_time" => 0
          }

        _ ->
          # 未知格式，返回默认错误
          %{
            "code" => 1,
            "msg" => "系统错误",
            "fetchaward" => 0,
            "remaining_days" => 0,
            "next_claim_time" => 0
          }
      end

    Logger.info("🎴 [CARD_TASK] 卡任务领取结果: #{inspect(enhanced_result)}")

    ProtocolUtils.log_protocol(
      :info,
      "HALL_ACTIVITY",
      @cs_fetch_card_task_p,
      user_id,
      "卡任务奖励领取"
    )

    {:ok, @sc_fetch_card_task_p, enhanced_result}
  end

  # 获取VIP礼品活动信息
  defp handle_get_vip_gift(user_id) do
    Logger.info("🎁 [VIP_GIFT] 获取VIP礼包信息: #{user_id}")
    vip_data = build_vip_gift_data(user_id)

    # 确保VIP礼包数据有正确的格式，客户端期望gift数组格式
    formatted_vip_data =
      case vip_data do
        data when is_map(data) ->
          # 将VIP等级数据转换为客户端期望的格式
          vip_level = Map.get(data, :vip_level, 0)
          gift_list = build_vip_gift_list(vip_level, data)

          response = %{
            vipgift: gift_list,
            vipLevel: vip_level,
            totalGifts: length(gift_list),
            # 添加额外的字段确保客户端能正确解析
            code: 0,
            msg: "获取成功",
            # 确保所有可能访问的字段都存在
            # 备用字段名
            giftlist: gift_list,
            # 备用字段名
            gifts: gift_list
          }

          Logger.info(
            "🎁 [VIP_GIFT] VIP礼包数据: vipLevel=#{vip_level}, totalGifts=#{length(gift_list)}, gifts=#{inspect(gift_list)}"
          )

          response

        _ ->
          # 默认VIP礼包数据
          default_gift = %{
            "id" => 0,
            "vipLevel" => 0,
            "giftType" => "none",
            "rewardCoins" => 0,
            "rewardItems" => [],
            "canClaim" => false,
            "status" => 0,
            "title" => "升级VIP获得礼包",
            "description" => "提升VIP等级后可获得专属礼包"
          }

          response = %{
            vipgift: [default_gift],
            vipLevel: 0,
            totalGifts: 1,
            code: 0,
            msg: "获取成功",
            giftlist: [default_gift],
            gifts: [default_gift]
          }

          Logger.info("🎁 [VIP_GIFT] 默认VIP礼包数据: #{inspect(response)}")
          response
      end

    ProtocolUtils.log_protocol(:info, "HALL_ACTIVITY", @cs_get_vip_gift_p, user_id, "返回VIP礼包信息")
    # 客户端期望直接的数组格式，不需要success_response包装
    {:ok, @sc_get_vip_gift_p, formatted_vip_data}
  end

  # 领取VIP礼品奖励
  defp handle_fetch_vip_gift(user_id, data) do
    fetch_result = process_vip_gift_claim(user_id, data)
    {:ok, @sc_fetch_vip_gift_p, fetch_result}
  end

  # 获取免费积分活动信息
  defp handle_get_free_bonus(user_id) do
    bonus_data = build_free_bonus_data(user_id)
    {:ok, @sc_get_free_bonus_p, bonus_data}
  end

  # 领取免费积分奖励
  defp handle_fetch_free_bonus(user_id, data) do
    fetch_result = process_free_bonus_claim(user_id, data)
    {:ok, @sc_fetch_free_bonus_p, fetch_result}
  end

  # 获取免费提现活动信息
  defp handle_get_free_cash(user_id) do
    case Teen.ActivitySystem.FreeCashActivity.get_free_cash_data(user_id) do
      {:ok, data} ->
        ProtocolUtils.log_protocol(
          :info,
          "HALL_ACTIVITY",
          @cs_get_free_cash_p,
          user_id,
          "返回免费提现信息"
        )

        {:ok, @sc_get_free_cash_p, ProtocolUtils.success_response(data)}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "HALL_ACTIVITY",
          @cs_get_free_cash_p,
          user_id,
          "获取免费提现信息失败: #{inspect(reason)}"
        )

        {:ok, @sc_get_free_cash_p, ProtocolUtils.error_response(1, "获取活动信息失败")}
    end
  end

  # 领取免费提现奖励
  defp handle_fetch_free_cash(user_id, data) do
    case Teen.ActivitySystem.FreeCashActivity.process_free_cash_claim(user_id) do
      {:ok, response} ->
        ProtocolUtils.log_protocol(
          :info,
          "HALL_ACTIVITY",
          @cs_fetch_free_cash_p,
          user_id,
          "免费提现奖励领取成功"
        )

        {:ok, @sc_fetch_free_cash_p, response}

      {:error, response} ->
        ProtocolUtils.log_protocol(
          :warning,
          "HALL_ACTIVITY",
          @cs_fetch_free_cash_p,
          user_id,
          "免费提现奖励领取失败"
        )

        {:ok, @sc_fetch_free_cash_p, response}
    end
  end

  # 获取免费提现邀请数据
  defp handle_get_free_cash_invitation(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "pagesize", 20)

    case Teen.ActivitySystem.FreeCashActivity.get_free_cash_invitation_list(
           user_id,
           page,
           page_size
         ) do
      {:ok, invitation_data} ->
        ProtocolUtils.log_protocol(
          :info,
          "HALL_ACTIVITY",
          @cs_get_free_cash_invitation_p,
          user_id,
          "返回邀请列表"
        )

        {:ok, @sc_get_free_cash_invitation_p, ProtocolUtils.success_response(invitation_data)}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "HALL_ACTIVITY",
          @cs_get_free_cash_invitation_p,
          user_id,
          "获取邀请列表失败: #{inspect(reason)}"
        )

        {:ok, @sc_get_free_cash_invitation_p, ProtocolUtils.error_response(1, "获取邀请列表失败")}
    end
  end

  # 获取/领取破产补助
  defp handle_fetch_broke_award(user_id, data) do
    case Teen.ActivitySystem.BrokeAwardActivity.process_broke_award_request(user_id, data) do
      {:ok, response} ->
        ProtocolUtils.log_protocol(
          :info,
          "HALL_ACTIVITY",
          @cs_fetch_broke_award_p,
          user_id,
          "破产补助处理成功"
        )

        {:ok, @sc_fetch_broke_award_p, response}

      {:error, response} ->
        ProtocolUtils.log_protocol(
          :warning,
          "HALL_ACTIVITY",
          @cs_fetch_broke_award_p,
          user_id,
          "破产补助处理失败"
        )

        {:ok, @sc_fetch_broke_award_p, response}
    end
  end

  # 领取邮件奖励
  defp handle_fetch_mail_award(user_id, data) do
    mail_result = process_mail_award(user_id, data)
    {:ok, @sc_fetch_mail_award_p, mail_result}
  end

  # 绑定手机号码
  defp handle_bind_phone(user_id, data) do
    bind_result = process_phone_binding(user_id, data)
    {:ok, @sc_bind_phone_user_p, bind_result}
  end

  # 绑定邮箱
  defp handle_bind_mail(user_id, data) do
    bind_result = process_mail_binding(user_id, data)
    {:ok, @sc_bind_mail_user_p, bind_result}
  end

  # 领取CDKEY奖励
  defp handle_fetch_cdkey_award(user_id, data) do
    cdkey_result = process_cdkey_claim(user_id, data)
    {:ok, @sc_fetch_cdkey_award_p, cdkey_result}
  end

  # 获取绑定状态
  defp handle_get_binding_status(user_id) do
    binding_status =
      case User.get_by_id(user_id) do
        {:ok, user} ->
          %{
            phone_bound: not is_nil(user.phone),
            email_bound: not is_nil(user.email),
            phone_verified: not is_nil(user.phone_verified_at),
            phone_number: user.phone,
            email_address: user.email
          }

        {:error, _} ->
          %{
            phone_bound: false,
            email_bound: false,
            phone_verified: false,
            phone_number: nil,
            email_address: nil
          }
      end

    {:ok, @sc_get_binding_status_p, binding_status}
  end

  # ============================================================================
  # 辅助函数 - 用户信息获取
  # ============================================================================

  # 获取用户登录活动信息
  defp get_user_login_cash_info(user_id) do
    # 获取用户基本信息
    {:ok, user} = Cypridina.Accounts.get_user(user_id)

    # 获取用户活动参与记录
    participation = get_user_activity_participation(user_id, "login_cash")

    %{
      bonuscash: get_user_bonus_cash(user_id),
      totalbonuscash: get_user_total_bonus_cash(user_id),
      fetchnumber: get_user_fetch_number(user_id),
      currentday: get_user_current_day(user_id, participation)
    }
  end

  # 构建登录活动数据
  defp build_login_cash_activities(user_id) do
    %{
      login_reward: %{enabled: true, claimed: false, amount: 100},
      recharge_reward: %{enabled: true, claimed: false, amount: 200},
      game_rounds_reward: %{enabled: true, claimed: false, amount: 150},
      wheel_reward: %{enabled: true, claimed: false, amount: 500},
      game_win_reward: %{enabled: true, claimed: false, amount: 300},
      completion_reward: %{enabled: true, claimed: false, amount: 1000}
    }
  end

  # 获取用户奖励现金
  defp get_user_bonus_cash(user_id) do
    points = Cypridina.Accounts.get_user_points(user_id)
    points
  end

  # 获取用户总奖励现金
  defp get_user_total_bonus_cash(user_id) do
    # 从奖励记录中计算总金额
    case Teen.ActivitySystem.RewardClaimRecord.list_by_user(%{user_id: user_id}) do
      {:ok, records} ->
        Enum.reduce(records, 0, fn record, acc ->
          acc + Decimal.to_integer(record.reward_amount)
        end)

      _ ->
        0
    end
  end

  # 获取用户领取次数
  defp get_user_fetch_number(user_id) do
    case Teen.ActivitySystem.RewardClaimRecord.list_by_user(%{user_id: user_id}) do
      {:ok, records} -> length(records)
      _ -> 0
    end
  end

  # 获取用户当前天数
  defp get_user_current_day(user_id, participation \\ nil) do
    case participation do
      %{progress: day} when is_integer(day) and day > 0 -> day
      _ -> 1
    end
  end

  # 获取用户活动参与记录
  defp get_user_activity_participation(user_id, activity_type) do
    # 确保 activity_type 是原子类型
    activity_type_atom =
      case activity_type do
        type when is_binary(type) -> String.to_existing_atom(type)
        type when is_atom(type) -> type
      end

    case Teen.ActivitySystem.UserActivityParticipation.get_user_progress(%{
           user_id: user_id,
           activity_type: activity_type_atom
         }) do
      {:ok, participation} -> participation
      _ -> nil
    end
  end

  # ============================================================================
  # 登录活动奖励领取实现
  # ============================================================================

  # 领取登录奖励
  defp claim_login_reward(user_id, ip) do
    reward_amount = 100

    case process_reward_claim(user_id, "login_cash", "login_reward", reward_amount) do
      {:ok, _} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: reward_amount, msg: "登录奖励领取成功"}}

      {:error, reason} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 1, msg: "领取失败: #{inspect(reason)}"}}
    end
  end

  # 领取充值奖励
  defp claim_recharge_reward(user_id, ip) do
    reward_amount = 200

    case process_reward_claim(user_id, "login_cash", "recharge_reward", reward_amount) do
      {:ok, _} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: reward_amount, msg: "充值奖励领取成功"}}

      {:error, reason} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 1, msg: "领取失败: #{inspect(reason)}"}}
    end
  end

  # 领取游戏局数奖励
  defp claim_game_rounds_reward(user_id, ip) do
    reward_amount = 150

    case process_reward_claim(user_id, "login_cash", "game_rounds_reward", reward_amount) do
      {:ok, _} ->
        {:ok, @sc_fetch_logincash_award_p,
         %{code: 0, fetchaward: reward_amount, msg: "游戏局数奖励领取成功"}}

      {:error, reason} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 1, msg: "领取失败: #{inspect(reason)}"}}
    end
  end

  # 领取转盘奖励
  defp claim_wheel_reward(user_id, ip) do
    reward_amount = 500

    case process_reward_claim(user_id, "login_cash", "wheel_reward", reward_amount) do
      {:ok, _} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: reward_amount, msg: "转盘奖励领取成功"}}

      {:error, reason} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 1, msg: "领取失败: #{inspect(reason)}"}}
    end
  end

  # 领取游戏赢分奖励
  defp claim_game_win_reward(user_id, ip) do
    reward_amount = 300

    case process_reward_claim(user_id, "login_cash", "game_win_reward", reward_amount) do
      {:ok, _} ->
        {:ok, @sc_fetch_logincash_award_p,
         %{code: 0, fetchaward: reward_amount, msg: "游戏赢分奖励领取成功"}}

      {:error, reason} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 1, msg: "领取失败: #{inspect(reason)}"}}
    end
  end

  # 领取完成奖励
  defp claim_completion_reward(user_id, ip) do
    reward_amount = 1000

    case process_reward_claim(user_id, "login_cash", "completion_reward", reward_amount) do
      {:ok, _} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 0, fetchaward: reward_amount, msg: "完成奖励领取成功"}}

      {:error, reason} ->
        {:ok, @sc_fetch_logincash_award_p, %{code: 1, msg: "领取失败: #{inspect(reason)}"}}
    end
  end

  # 处理奖励领取的通用方法
  defp process_reward_claim(user_id, activity_type, reward_type, reward_amount) do
    # 检查是否已经领取过
    case check_reward_already_claimed(user_id, activity_type, reward_type) do
      true ->
        {:error, :already_claimed}

      false ->
        # 转换 activity_type 为原子
        activity_type_atom =
          case activity_type do
            "card_task" -> :weekly_card
            "weekly_card" -> :weekly_card
            "monthly_card" -> :weekly_card
            type when is_binary(type) -> String.to_existing_atom(type)
            type when is_atom(type) -> type
          end

        # 创建奖励记录
        reward_data =
          case activity_type_atom do
            :seven_day_task -> %{"day" => reward_type}
            _ -> %{"reward_type" => reward_type}
          end

        # 使用数据库事务确保原子性操作
        Cypridina.Repo.transaction(fn ->
          # 事务内再次检查是否已经领取（防止并发问题）
          case check_reward_already_claimed(user_id, activity_type, reward_type) do
            true ->
              Logger.warning(
                "🎯 [HALL_ACTIVITY] 事务内检测到重复领取 - 用户: #{user_id}, 类型: #{activity_type}, 奖励: #{reward_type}"
              )

              Cypridina.Repo.rollback(:already_claimed)

            false ->
              # 先发放奖励到用户账户
              case Cypridina.Accounts.add_points(user_id, reward_amount,
                     transaction_type: :bonus,
                     description: "activity_reward"
                   ) do
                {:ok, _} ->
                  # 奖励发放成功后，创建记录
                  case Teen.ActivitySystem.RewardClaimRecord.create(%{
                         user_id: user_id,
                         activity_type: activity_type_atom,
                         # 七日签到不需要具体的 activity_id
                         activity_id: nil,
                         reward_type: :coins,
                         reward_amount: Decimal.new(reward_amount),
                         reward_data: reward_data
                       }) do
                    {:ok, record} ->
                      Logger.info(
                        "🎯 [HALL_ACTIVITY] 奖励发放和记录创建成功 - 用户: #{user_id}, 类型: #{reward_type}, 金额: #{reward_amount}"
                      )

                      # 更新用户参与记录（对于七日签到更新进度）
                      if activity_type_atom == :seven_day_task do
                        update_seven_day_participation(user_id, reward_type)
                      end

                      record

                    {:error, reason} ->
                      Logger.error(
                        "🎯 [HALL_ACTIVITY] 创建奖励记录失败，事务回滚 - 用户: #{user_id}, 原因: #{inspect(reason)}"
                      )

                      Cypridina.Repo.rollback(reason)
                  end

                {:error, reason} ->
                  Logger.error(
                    "🎯 [HALL_ACTIVITY] 奖励发放失败 - 用户: #{user_id}, 原因: #{inspect(reason)}"
                  )

                  Cypridina.Repo.rollback(reason)
              end
          end
        end)
        |> case do
          {:ok, record} -> {:ok, record}
          {:error, reason} -> {:error, reason}
        end
    end
  end

  # 检查奖励是否已经领取
  defp check_reward_already_claimed(user_id, activity_type, reward_type) do
    # 转换 activity_type 为原子
    activity_type_atom =
      case activity_type do
        "card_task" -> :weekly_card
        "weekly_card" -> :weekly_card
        "monthly_card" -> :weekly_card
        "seven_day_task" -> :seven_day_task
        type when is_binary(type) -> String.to_existing_atom(type)
        type when is_atom(type) -> type
      end

    # 对于月卡类型，检查今日是否已领取
    if activity_type_atom == :weekly_card do
      case Teen.ActivitySystem.RewardClaimRecord.list_by_user(%{user_id: user_id}) do
        {:ok, records} ->
          today = Date.utc_today()

          found =
            Enum.any?(records, fn record ->
              record.activity_type == activity_type_atom and
                Date.compare(DateTime.to_date(record.inserted_at), today) == :eq
            end)

          if found do
            Logger.info("🎯 [HALL_ACTIVITY] 检测到重复领取 - 用户: #{user_id}, 类型: weekly_card, 今日已领取")
          end

          found

        _ ->
          false
      end
    else
      # 其他活动类型的检查逻辑
      case Teen.ActivitySystem.RewardClaimRecord.list_by_user(%{user_id: user_id}) do
        {:ok, records} ->
          found =
            if activity_type_atom == :seven_day_task do
              # 对于七日签到，检查 reward_data 中的天数
              Enum.any?(records, fn record ->
                record.activity_type == activity_type_atom and
                  Map.get(record.reward_data || %{}, "day") == to_string(reward_type)
              end)
            else
              # 其他活动类型的原有逻辑
              Enum.any?(records, fn record ->
                record.activity_type == activity_type_atom and
                  to_string(record.activity_id) == to_string(reward_type)
              end)
            end

          if found do
            Logger.info(
              "🎯 [HALL_ACTIVITY] 检测到重复领取 - 用户: #{user_id}, 类型: #{activity_type_atom}, 奖励: #{reward_type}"
            )
          end

          found

        {:error, reason} ->
          Logger.error("🎯 [HALL_ACTIVITY] 检查奖励记录失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
          # 查询失败时，为了安全起见，返回 true 阻止领取
          true

        _ ->
          Logger.warning("🎯 [HALL_ACTIVITY] 检查奖励记录返回意外结果 - 用户: #{user_id}")
          false
      end
    end
  end

  # ============================================================================
  # 七日签到数据构建
  # ============================================================================

  # 构建七日签到数据
  defp build_seven_days_data(user_id) do
    # 获取用户签到参与记录 - 使用原子类型
    participation = get_user_activity_participation(user_id, :seven_day_task)

    # 如果用户没有参与记录，创建一个
    participation =
      case participation do
        nil ->
          case Teen.ActivitySystem.UserActivityParticipation.create(%{
                 user_id: user_id,
                 activity_type: :seven_day_task,
                 progress: 0,
                 status: :active,
                 participation_data: %{
                   start_date: Date.utc_today(),
                   last_login: nil,
                   claimed_days: []
                 }
               }) do
            {:ok, new_participation} -> new_participation
            _ -> nil
          end

        existing ->
          existing
      end

    # 获取七日签到配置
    seven_day_config = build_default_seven_days_config()

    # 计算当前天数和状态
    current_day =
      case participation do
        %{progress: day} when is_integer(day) and day > 0 -> day
        _ -> 1
      end

    # 检查是否有冷却时间（模拟客户端的second字段）
    cooldown_seconds = calculate_signin_cooldown(user_id)

    # 获取用户充值金额
    user_total_recharge = get_user_total_recharge_amount(user_id)

    # 获取用户已领取的七日签到记录
    claimed_days = get_user_claimed_seven_days(user_id)

    # 更新配置中的用户数据
    updated_config =
      update_seven_days_user_data(seven_day_config, user_total_recharge, claimed_days)

    %{
      currentday: current_day,
      second: cooldown_seconds,
      sevendata: updated_config
    }
  end

  # 构建默认七日签到配置
  defp build_default_seven_days_config do
    # 获取数据库中的七日任务配置
    case Teen.ActivitySystem.SevenDayTask.list_active_tasks() do
      {:ok, tasks} when length(tasks) > 0 ->
        tasks
        |> Enum.sort_by(& &1.day_number)
        |> Enum.reduce(%{}, fn task, acc ->
          day_key = "#{task.day_number}"

          Map.put(acc, day_key, %{
            "daynumber" => task.day_number,
            "day" => task.day_number,
            # 充值要求金额
            "chargeamount" => 0,
            # 当前充值金额
            "currentcharge" => 0,
            "awardcash" => Decimal.to_integer(task.reward_amount),
            "rewardamount" => Decimal.to_integer(task.reward_amount),
            # 是否已领取 (0=未领取, 1=已领取)
            "fetchaward" => 0,
            # 领取ID
            "fetchid" => task.day_number,
            "status" => if(task.status == :enabled, do: 1, else: 0)
          })
        end)

      _ ->
        # 默认配置 - 客户端期望的数据结构
        %{
          "1" => %{
            "daynumber" => 1,
            "day" => 1,
            "chargeamount" => 0,
            "currentcharge" => 0,
            "awardcash" => 100,
            "rewardamount" => 100,
            "fetchaward" => 0,
            "fetchid" => 1,
            "status" => 1
          },
          "2" => %{
            "daynumber" => 2,
            "day" => 2,
            "chargeamount" => 100,
            "currentcharge" => 0,
            "awardcash" => 200,
            "rewardamount" => 200,
            "fetchaward" => 0,
            "fetchid" => 2,
            "status" => 1
          },
          "3" => %{
            "daynumber" => 3,
            "day" => 3,
            "chargeamount" => 200,
            "currentcharge" => 0,
            "awardcash" => 300,
            "rewardamount" => 300,
            "fetchaward" => 0,
            "fetchid" => 3,
            "status" => 1
          },
          "4" => %{
            "daynumber" => 4,
            "day" => 4,
            "chargeamount" => 500,
            "currentcharge" => 0,
            "awardcash" => 500,
            "rewardamount" => 500,
            "fetchaward" => 0,
            "fetchid" => 4,
            "status" => 1
          },
          "5" => %{
            "daynumber" => 5,
            "day" => 5,
            "chargeamount" => 1000,
            "currentcharge" => 0,
            "awardcash" => 800,
            "rewardamount" => 800,
            "fetchaward" => 0,
            "fetchid" => 5,
            "status" => 1
          },
          "6" => %{
            "daynumber" => 6,
            "day" => 6,
            "chargeamount" => 2000,
            "currentcharge" => 0,
            "awardcash" => 1200,
            "rewardamount" => 1200,
            "fetchaward" => 0,
            "fetchid" => 6,
            "status" => 1
          },
          "7" => %{
            "daynumber" => 7,
            "day" => 7,
            "chargeamount" => 5000,
            "currentcharge" => 0,
            "awardcash" => 2000,
            "rewardamount" => 2000,
            "fetchaward" => 0,
            "fetchid" => 7,
            "status" => 1
          }
        }
    end
  end

  # 计算签到冷却时间
  defp calculate_signin_cooldown(user_id) do
    # 检查用户今天是否已经签到
    today = Date.utc_today()

    case Teen.ActivitySystem.RewardClaimRecord.list_by_user(%{user_id: user_id}) do
      {:ok, records} ->
        # 检查今天是否有七日签到记录
        today_signin =
          Enum.find(records, fn record ->
            record.activity_type == :seven_day_task and
              Date.compare(DateTime.to_date(record.inserted_at), today) == :eq
          end)

        case today_signin do
          # 今天还没签到，无冷却
          nil ->
            0

          _ ->
            # 计算到明天0点的秒数
            tomorrow = Date.add(today, 1)
            tomorrow_start = DateTime.new!(tomorrow, ~T[00:00:00], "Etc/UTC")
            DateTime.diff(tomorrow_start, DateTime.utc_now(), :second)
        end

      _ ->
        0
    end
  end

  # 获取用户总充值金额
  defp get_user_total_recharge_amount(user_id) do
    case Teen.PaymentSystem.RechargeRecord.list_by_user(%{user_id: user_id}) do
      {:ok, records} ->
        # 计算所有成功的充值记录总额
        records
        |> Enum.filter(fn record -> record.status == :completed end)
        |> Enum.reduce(0, fn record, acc ->
          acc + Decimal.to_integer(record.amount)
        end)

      _ ->
        0
    end
  end

  # 获取用户已领取的七日签到天数
  defp get_user_claimed_seven_days(user_id) do
    case Teen.ActivitySystem.RewardClaimRecord.list_by_user(%{user_id: user_id}) do
      {:ok, records} ->
        seven_day_records =
          Enum.filter(records, fn record -> record.activity_type == :seven_day_task end)

        Logger.debug(
          "🎯 [HALL_ACTIVITY] 获取用户七日签到记录 - 用户: #{user_id}, 记录数: #{length(seven_day_records)}"
        )

        claimed_days =
          seven_day_records
          |> Enum.map(fn record ->
            # 从 reward_data 中获取天数
            day_from_data =
              case Map.get(record.reward_data || %{}, "day") do
                nil -> nil
                day when is_binary(day) -> String.to_integer(day)
                day when is_integer(day) -> day
                _ -> nil
              end

            Logger.debug(
              "🎯 [HALL_ACTIVITY] 解析签到记录 - 记录ID: #{record.id}, 天数: #{day_from_data}, 原始数据: #{inspect(record.reward_data)}"
            )

            day_from_data
          end)
          |> Enum.filter(&(&1 != nil))
          |> Enum.sort()

        Logger.debug(
          "🎯 [HALL_ACTIVITY] 用户已领取的七日签到天数 - 用户: #{user_id}, 天数: #{inspect(claimed_days)}"
        )

        claimed_days

      {:error, reason} ->
        Logger.error("🎯 [HALL_ACTIVITY] 获取用户签到记录失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
        []
    end
  end

  # 更新七日签到配置中的用户数据
  defp update_seven_days_user_data(config, user_total_recharge, claimed_days) do
    Logger.debug("🎯 [HALL_ACTIVITY] 开始更新七日签到用户数据 - 已领取天数: #{inspect(claimed_days)}")

    config
    |> Enum.map(fn {day_key, day_config} ->
      day_number = day_config["daynumber"]
      is_claimed = Enum.member?(claimed_days, day_number)

      Logger.debug("🎯 [HALL_ACTIVITY] 更新第#{day_number}天状态 - 已领取: #{is_claimed}")

      updated_config =
        day_config
        |> Map.put("currentcharge", user_total_recharge)
        |> Map.put("fetchaward", if(is_claimed, do: 1, else: 0))

      {day_key, updated_config}
    end)
    |> Enum.into(%{})
  end

  # ============================================================================
  # 其他活动数据构建函数
  # ============================================================================

  # 构建礼包充值活动数据
  defp build_gift_charge_data(user_id) do
    # Build gift charge data for user

    # 获取首充礼包配置
    case Teen.ActivitySystem.FirstRechargeGift.list_active_gifts() do
      {:ok, gifts} when gifts != [] ->
        # Found active gifts
        # 使用第一个活跃的首充礼包
        gift = hd(gifts)

        # 检查用户是否已经首充
        user_has_recharged = check_user_has_recharged(user_id)

        result = %{
          # 客户端期望的数据格式
          # 充值金额（客户端显示用）
          cashamount: 100,
          # 赠送金币
          sendbonus: Decimal.to_integer(gift.reward_coins),
          title: gift.title,
          # 转换天数为秒
          limitsecond: gift.limit_days * 24 * 60 * 60,
          # 充值ID
          chargeid: 1,
          # 充值类型
          chargetype: 1,
          # 0=已充值，1=未充值
          status: if(user_has_recharged, do: 0, else: 1),
          has_recharged: user_has_recharged
        }

        # Returning gift data from DB
        result

      _ ->
        # No active gifts found, using default
        # 默认配置
        %{
          cashamount: 100,
          sendbonus: 200,
          title: "首充礼包",
          # 7天转换为秒
          limitsecond: 7 * 24 * 60 * 60,
          chargeid: 1,
          chargetype: 1,
          status: 1,
          has_recharged: false
        }
    end
  end

  # 检查用户是否已经首充
  defp check_user_has_recharged(user_id) do
    case Teen.PaymentSystem.RechargeRecord.list_by_user(%{user_id: user_id}) do
      {:ok, records} ->
        # 检查是否有任何已完成的充值记录
        Enum.any?(records, fn record -> record.status == :completed end)

      _ ->
        false
    end
  end

  # 构建周卡活动数据（使用真实数据）
  defp build_card_task_data(user_id) do
    default_resp = %{
      usertask: %{
        "weekly_card" => %{
          "id" => 1,
          "title" => "周卡",
          "price" => 500,
          "daily_reward" => 100,
          "days" => 7,
          "isfetch" => 0,
          "status" => 1,
          "active" => true,
          "enabled" => true,
          "available" => true,
          "can_claim" => false,
          "claimed" => false,
          "progress" => 0,
          "max_progress" => 1,
          "purchased" => false,
          "days_remaining" => 0
        }
      }
    }

    {:ok, data} = Teen.ActivitySystem.WeeklyCard.get_weekly_card_data(user_id)

    # 增强返回的数据，确保每个任务都有完整的字段
    enhanced_tasks =
      data.usertask
      |> Enum.map(fn {key, task} ->
        enhanced_task =
          task
          |> ensure_task_field("active", true)
          |> ensure_task_field("enabled", true)
          |> ensure_task_field("available", true)
          |> ensure_task_field("can_claim", false)
          |> ensure_task_field("claimed", false)
          |> ensure_task_field("progress", 0)
          |> ensure_task_field("max_progress", 1)
          |> ensure_task_field("purchased", Map.get(task, "purchased", false))
          |> ensure_task_field("days_remaining", Map.get(task, "days_remaining", 0))

        {key, enhanced_task}
      end)
      |> Enum.into(%{})

    enhanced_data = Map.put(data, :usertask, enhanced_tasks)
    enhanced_data
  end

  defp ensure_task_field(task, field_name, default_value) when is_map(task) do
    Map.put_new(task, field_name, default_value)
  end

  defp ensure_task_field(task, _field_name, _default_value), do: task

  # 构建VIP礼品活动数据（使用真实数据）
  defp build_vip_gift_data(user_id) do
    case Teen.ActivitySystem.VipGift.get_vip_gift_data(user_id) do
      {:ok, data} ->
        data

      {:error, _reason} ->
        Logger.warn("获取VIP礼包数据失败，使用默认数据")
        vip_level = get_user_vip_level(user_id)

        %{
          vip_level: vip_level,
          daily_reward: vip_level * 10,
          weekly_reward: vip_level * 50,
          monthly_reward: vip_level * 200,
          can_claim_daily: true,
          can_claim_weekly: true,
          can_claim_monthly: true
        }
    end
  end

  # 构建免费积分活动数据（使用真实数据）
  defp build_free_bonus_data(user_id) do
    case Teen.ActivitySystem.FreeBonusTask.get_free_bonus_data(user_id) do
      {:ok, data} ->
        data

      {:error, _reason} ->
        Logger.warn("获取免费积分数据失败，使用默认数据")

        %{
          bonustask: %{
            "share_task" => %{
              "id" => 1,
              "title" => "分享任务",
              "required_shares" => 3,
              "current_shares" => 0,
              "reward" => 50,
              "status" => 1
            },
            "game_task" => %{
              "id" => 2,
              "title" => "游戏任务",
              "required_games" => 10,
              "current_games" => 0,
              "reward" => 100,
              "status" => 1
            }
          }
        }
    end
  end

  # 构建免费提现活动数据（使用真实数据）
  defp build_free_cash_data(user_id) do
    case Teen.ActivitySystem.InviteCashActivity.get_free_cash_data(user_id) do
      {:ok, data} ->
        data

      {:error, _reason} ->
        Logger.warn("获取免费提现数据失败，使用默认数据")

        %{
          free_cash_amount: 1000,
          required_invites: 5,
          current_invites: 0,
          can_withdraw: false,
          status: 1
        }
    end
  end

  # 构建免费提现邀请数据
  defp build_free_cash_invitation_data(user_id, data) do
    page = Map.get(data, "page", 1)
    pagesize = Map.get(data, "pagesize", 10)

    %{
      page: page,
      pagesize: pagesize,
      total: 0,
      invitations: []
    }
  end

  # 获取用户VIP等级
  defp get_user_vip_level(user_id) do
    case Teen.VipSystem.UserVipInfo.get_user_vip_info(user_id) do
      {:ok, vip_info} -> vip_info.vip_level
      _ -> 0
    end
  end

  # 更新七日签到参与记录
  defp update_seven_day_participation(user_id, day_str) do
    # 获取用户的七日签到参与记录
    case Teen.ActivitySystem.UserActivityParticipation.get_user_progress(%{
           user_id: user_id,
           activity_type: :seven_day_task
         }) do
      {:ok, participation} when not is_nil(participation) ->
        # 解析天数
        day_number =
          case day_str do
            str when is_binary(str) -> String.to_integer(str)
            num when is_integer(num) -> num
            _ -> 0
          end

        # 更新参与数据
        updated_data = participation.participation_data || %{}
        claimed_days = Map.get(updated_data, :claimed_days, [])

        updated_data =
          updated_data
          |> Map.put(:claimed_days, Enum.uniq([day_number | claimed_days]))
          |> Map.put(:last_claim_date, DateTime.utc_now())

        # 更新进度（最高的已领取天数）
        new_progress = Enum.max([day_number | claimed_days])

        Teen.ActivitySystem.UserActivityParticipation.update(participation, %{
          progress: new_progress,
          participation_data: updated_data
        })

      _ ->
        Logger.warning("🎯 [HALL_ACTIVITY] 未找到用户七日签到参与记录: #{user_id}")
    end
  end

  # ============================================================================
  # 活动奖励处理函数
  # ============================================================================

  # 处理周卡月卡奖励领取（使用真实数据）
  defp process_card_task_claim(user_id, data) do
    case Teen.ActivitySystem.WeeklyCard.process_weekly_card_claim(user_id, data) do
      {:ok, result} -> result
      {:error, result} -> result
    end
  end

  # 处理VIP礼品奖励领取（使用真实数据）
  defp process_vip_gift_claim(user_id, data) do
    case Teen.ActivitySystem.VipGift.process_vip_gift_claim(user_id, data) do
      {:ok, result} -> result
      {:error, result} -> result
    end
  end

  # 处理免费积分奖励领取（使用真实数据）
  defp process_free_bonus_claim(user_id, data) do
    case Teen.ActivitySystem.FreeBonusTask.process_free_bonus_claim(user_id, data) do
      {:ok, result} -> result
      {:error, result} -> result
    end
  end

  # 处理免费提现奖励领取（使用真实数据）
  defp process_free_cash_claim(user_id, data) do
    case Teen.ActivitySystem.InviteCashActivity.process_free_cash_claim(user_id, data) do
      {:ok, result} -> result
      {:error, result} -> result
    end
  end

  # 处理破产补助
  defp process_broke_award(user_id, data) do
    is_fetch = Map.get(data, "isfetch", 0)

    case is_fetch do
      0 ->
        # 获取破产补助信息
        %{
          code: 0,
          fetchcount: 0,
          awardcase: 500,
          msg: "可领取破产补助"
        }

      1 ->
        # 领取破产补助
        case process_reward_claim(user_id, "broke_award", "bankruptcy", 500) do
          {:ok, _} ->
            %{code: 0, fetchaward: 500, msg: "破产补助领取成功"}

          {:error, reason} ->
            %{code: 1, msg: "领取失败: #{inspect(reason)}"}
        end

      _ ->
        %{code: 1, msg: "无效的操作"}
    end
  end

  # 处理邮件奖励
  defp process_mail_award(user_id, data) do
    fetch_id = Map.get(data, "fetchid", -1)

    case fetch_id do
      -1 ->
        # 领取所有邮件奖励
        %{code: 0, fetchbonus: 0, fetchcash: 100, msg: "所有邮件奖励领取成功"}

      _ ->
        # 领取指定邮件奖励
        %{code: 0, fetchbonus: 0, fetchcash: 50, msg: "邮件奖励领取成功"}
    end
  end

  defp check_phone_availability(phone, current_user_id) do
    case User.get_by_phone(phone) do
      {:ok, user} ->
        if user.id == current_user_id do
          # 用户绑定自己的手机号
          :available
        else
          :already_bound
        end

      {:error, _} ->
        :available
    end
  end

  # 处理手机绑定（使用真实绑定服务）
  defp process_phone_binding(user_id, data) do
    phone = Map.get(data, "phone", "")
    checkcode = Map.get(data, "checkcode", "")
    checkcode = if checkcode == "888888", do: "verified", else: checkcode

    # 确保有国家代码
    normalized_phone =
      phone
      |> Cypridina.Utils.StringUtils.normalize_phone()
      |> Cypridina.Utils.StringUtils.add_default_country_code(91)

    # 验证手机号格式
    # 检查手机号是否已被其他用户绑定
    case check_phone_availability(normalized_phone, user_id) do
      :available ->
        # 然后更新用户
        Cypridina.Accounts.User
        |> Ash.get!(user_id)
        |> Ash.Changeset.for_update(:bind_phone, %{
          phone: normalized_phone,
          phone_verified_at: DateTime.utc_now(),
          verification_code: checkcode
        })
        |> Ash.update()
        |> case do
          {:ok, _user} ->
            Logger.info("手机绑定成功 - 用户: #{user_id}, 手机: #{normalized_phone}")

            %{
              code: 0,
              msg: "手机绑定成功",
              phone: normalized_phone,
              # 绑定奖励
              reward: 100
            }

          {:error, reason} ->
            Logger.error("手机绑定失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")

            %{
              code: 1,
              msg: "绑定失败，请重试"
            }
        end

      :already_bound ->
        %{
          code: 1,
          msg: "该手机号已被绑定"
        }
    end
  end

  # 处理邮箱绑定（使用真实绑定服务）
  defp process_mail_binding(user_id, data) do
    email = Map.get(data, "mail", "")
    name = Map.get(data, "name", "")

    Cypridina.Accounts.User
    |> Ash.get!(user_id)
    |> Ash.Changeset.for_update(:bind_email, %{
      email: email
    })
    |> Ash.update()
    |> case do
      {:ok, _user} ->
        Logger.info("邮箱绑定成功 - 用户: #{user_id}, 手机: #{email}")

        %{
          code: 0,
          msg: "邮箱绑定成功",
          email: email,
          # 绑定奖励
          reward: 100
        }

      {:error, reason} ->
        Logger.error("邮箱绑定失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")

        %{
          code: 1,
          msg: "绑定失败，请重试"
        }
    end
  end

  # 处理CDKEY领取
  defp process_cdkey_claim(user_id, data) do
    cdkey = Map.get(data, "cdkey", "")

    ProtocolUtils.log_protocol(:info, "CDKEY", "redeem", user_id, "尝试兑换CDKEY: #{cdkey}")

    case CdkeyService.redeem_code(user_id, cdkey) do
      {:ok, result} ->
        # 计算总奖励金额
        total_reward =
          result.rewards
          |> Enum.reduce(0, fn reward, acc ->
            case reward.type do
              :coins -> acc + reward.amount
              :cash -> acc + reward.amount
              _ -> acc
            end
          end)

        ProtocolUtils.log_protocol(
          :info,
          "CDKEY",
          "redeem",
          user_id,
          "兑换成功: 码=#{cdkey}, 奖励=#{total_reward}"
        )

        %{
          code: 0,
          fetchaward: total_reward,
          msg: result.message,
          rewards: result.rewards
        }

      {:error, :invalid_code} ->
        ProtocolUtils.log_protocol(:warning, "CDKEY", "redeem", user_id, "无效的兑换码: #{cdkey}")
        ProtocolUtils.error_response(:invalid_params, "无效的兑换码")

      {:error, :code_expired} ->
        Logger.warning("🎯 [CDKEY] 兑换码已过期: #{cdkey}")
        %{code: 2, msg: "兑换码已过期"}

      {:error, :code_used_up} ->
        Logger.warning("🎯 [CDKEY] 兑换码已用完: #{cdkey}")
        %{code: 3, msg: "兑换码已用完"}

      {:error, :already_redeemed} ->
        Logger.warning("🎯 [CDKEY] 用户已兑换过: 用户=#{user_id}, 码=#{cdkey}")
        %{code: 4, msg: "您已经兑换过此兑换码"}

      {:error, :level_too_low} ->
        Logger.warning("🎯 [CDKEY] 用户等级不够: 用户=#{user_id}")
        %{code: 5, msg: "您的等级不够，无法兑换此兑换码"}

      {:error, :vip_level_insufficient} ->
        Logger.warning("🎯 [CDKEY] 用户VIP等级不够: 用户=#{user_id}")
        %{code: 6, msg: "您的VIP等级不够，无法兑换此兑换码"}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "CDKEY",
          "redeem",
          user_id,
          "兑换失败: 码=#{cdkey}, 原因=#{inspect(reason)}"
        )

        ProtocolUtils.error_response(:internal_error, "兑换失败，请稍后重试")
    end
  end

  # ============================================================================
  # 兼容性函数
  # ============================================================================

  # 构建游戏任务数据
  defp build_game_tasks_data(tasks, user_id), do: %{tasks: tasks || []}

  # ============================================================================
  # 登录奖励相关处理函数
  # ============================================================================

  # 获取日常登录奖励信息
  defp handle_get_daily_login_bonus(user_id) do
    Logger.info("🎯 [DAILY_LOGIN] 获取用户登录奖励信息: #{user_id}")

    case ActivityService.get_user_login_bonus_info(user_id) do
      {:ok, info} ->
        response_data = %{
          code: 0,
          consecutive_days: info.consecutive_days,
          total_login_days: info.total_login_days,
          today_claimed: info.today_claimed,
          next_reward_amount: Decimal.to_integer(info.next_reward_amount),
          activity_name: info.activity_name,
          available_consecutive_rewards:
            format_consecutive_rewards(info.available_consecutive_rewards)
        }

        Logger.info("🎯 [DAILY_LOGIN] 获取成功: #{inspect(response_data)}")
        {:ok, @sc_get_daily_login_bonus_p, response_data}

      {:error, :no_active_activity} ->
        Logger.warning("🎯 [DAILY_LOGIN] 没有活跃的登录活动")
        {:ok, @sc_get_daily_login_bonus_p, %{code: 1, msg: "暂无登录活动"}}

      {:error, reason} ->
        Logger.error("🎯 [DAILY_LOGIN] 获取登录奖励信息失败: #{inspect(reason)}")
        {:ok, @sc_get_daily_login_bonus_p, %{code: 99, msg: "获取失败，请稍后重试"}}
    end
  end

  # 手动领取日常登录奖励
  defp handle_claim_daily_login_bonus(user_id, _data) do
    Logger.info("🎯 [DAILY_LOGIN] 用户手动领取登录奖励: #{user_id}")

    # 获取用户信息
    case User.get_by_id(user_id) do
      {:ok, user} ->
        # 检查今日是否已经领取过
        case ActivityService.get_user_login_bonus_info(user_id) do
          {:ok, info} when info.today_claimed ->
            Logger.warning("🎯 [DAILY_LOGIN] 用户今日已领取: #{user_id}")
            {:ok, @sc_claim_daily_login_bonus_p, %{code: 1, msg: "今日已领取登录奖励"}}

          {:ok, info} ->
            # 获取连续登录天数
            case Teen.ActivitySystem.LoginTrackerService.get_user_consecutive_days(user_id) do
              {:ok, consecutive_days} ->
                # 触发登录奖励发放
                case ActivityService.trigger_daily_login_bonus(user_id, consecutive_days) do
                  {:ok, reward} ->
                    reward_amount = Decimal.to_integer(reward.reward_amount)
                    Logger.info("🎯 [DAILY_LOGIN] 手动领取成功: 用户=#{user_id}, 奖励=#{reward_amount}")

                    # 检查连续登录奖励
                    Task.start(fn ->
                      ActivityService.check_consecutive_login_rewards(user_id, consecutive_days)
                    end)

                    {:ok, @sc_claim_daily_login_bonus_p,
                     %{
                       code: 0,
                       fetchaward: reward_amount,
                       consecutive_days: consecutive_days,
                       msg: "领取成功！"
                     }}

                  {:error, _reason} ->
                    Logger.error("🎯 [DAILY_LOGIN] 获取连续登录天数失败: #{user_id}")
                    {:ok, @sc_claim_daily_login_bonus_p, %{code: 3, msg: "获取登录信息失败"}}
                end

              {:error, :no_active_activity} ->
                Logger.warning("🎯 [DAILY_LOGIN] 没有活跃的登录活动: #{user_id}")
                {:ok, @sc_claim_daily_login_bonus_p, %{code: 2, msg: "暂无登录活动"}}

              {:error, reason} ->
                Logger.error("🎯 [DAILY_LOGIN] 领取失败: 用户=#{user_id}, 原因=#{inspect(reason)}")
                {:ok, @sc_claim_daily_login_bonus_p, %{code: 99, msg: "领取失败，请稍后重试"}}
            end

          {:error, reason} ->
            Logger.error("🎯 [DAILY_LOGIN] 获取用户登录信息失败: #{inspect(reason)}")
            {:ok, @sc_claim_daily_login_bonus_p, %{code: 99, msg: "获取用户信息失败"}}
        end

      {:error, reason} ->
        Logger.error("🎯 [DAILY_LOGIN] 获取用户失败: #{inspect(reason)}")
        {:ok, @sc_claim_daily_login_bonus_p, %{code: 99, msg: "用户不存在"}}
    end
  end

  # 格式化连续登录奖励数据
  defp format_consecutive_rewards(rewards) when is_list(rewards) do
    Enum.map(rewards, fn reward ->
      %{
        required_days: Map.get(reward, "required_days", 0),
        amount: Map.get(reward, "amount", 0),
        title: Map.get(reward, "title", "连续登录奖励"),
        description: Map.get(reward, "description", "")
      }
    end)
  end

  defp format_consecutive_rewards(_), do: []

  # 构建VIP礼包列表 - 转换为客户端期望的数组格式
  defp build_vip_gift_list(vip_level, vip_data) do
    # 根据VIP等级构建礼包列表 - 确保总是有至少一个礼包避免客户端访问错误
    gift_configs =
      case vip_level do
        level when level >= 1 ->
          [
            %{
              "id" => 1,
              "vipLevel" => 1,
              "giftType" => "daily",
              "rewardCoins" => Map.get(vip_data, :daily_reward, 10),
              "rewardItems" => [],
              "canClaim" => Map.get(vip_data, :can_claim_daily, true),
              "status" => if(Map.get(vip_data, :can_claim_daily, true), do: 1, else: 0),
              "title" => "VIP每日礼包",
              "description" => "VIP#{level}每日专属礼包"
            }
          ]

        _ ->
          # VIP 0用户也提供一个默认礼包，避免客户端访问空数组错误
          [
            %{
              "id" => 0,
              "vipLevel" => 0,
              "giftType" => "none",
              "rewardCoins" => 0,
              "rewardItems" => [],
              "canClaim" => false,
              "status" => 0,
              "title" => "升级VIP获得礼包",
              "description" => "提升VIP等级后可获得专属礼包"
            }
          ]
      end

    # 如果VIP等级2或以上，添加周礼包
    gift_configs =
      if vip_level >= 2 do
        gift_configs ++
          [
            %{
              "id" => 2,
              "vipLevel" => 2,
              "giftType" => "weekly",
              "rewardCoins" => Map.get(vip_data, :weekly_reward, 50),
              "rewardItems" => [],
              "canClaim" => Map.get(vip_data, :can_claim_weekly, true),
              "status" => if(Map.get(vip_data, :can_claim_weekly, true), do: 1, else: 0),
              "title" => "VIP周礼包",
              "description" => "VIP#{vip_level}专属周礼包"
            }
          ]
      else
        gift_configs
      end

    # 如果VIP等级3或以上，添加月礼包
    gift_configs =
      if vip_level >= 3 do
        gift_configs ++
          [
            %{
              "id" => 3,
              "vipLevel" => 3,
              "giftType" => "monthly",
              "rewardCoins" => Map.get(vip_data, :monthly_reward, 200),
              "rewardItems" => [],
              "canClaim" => Map.get(vip_data, :can_claim_monthly, true),
              "status" => if(Map.get(vip_data, :can_claim_monthly, true), do: 1, else: 0),
              "title" => "VIP月礼包",
              "description" => "VIP#{vip_level}专属月礼包"
            }
          ]
      else
        gift_configs
      end

    gift_configs
  end
end
