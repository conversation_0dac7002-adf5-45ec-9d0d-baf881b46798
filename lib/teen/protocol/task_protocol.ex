defmodule Teen.Protocol.TaskProtocol do
  @moduledoc """
  活动任务协议处理器

  处理活动任务相关的协议，包括：
  - 获取任务列表
  - 查看任务详情
  - 完成任务
  - 领取任务奖励
  - 任务进度查询
  - 每日/周任务管理
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Teen.ActivitySystem.{ActivityService, ActivityTaskService}

  # 主协议ID
  @protocol_id 42

  # 子协议常量定义
  @cs_get_task_list_p 0
  @sc_get_task_list_p 1
  @cs_get_task_detail_p 2
  @sc_get_task_detail_p 3
  @cs_complete_task_p 4
  @sc_complete_task_p 5
  @cs_claim_task_reward_p 6
  @sc_claim_task_reward_p 7
  @cs_get_task_progress_p 8
  @sc_get_task_progress_p 9
  @cs_get_daily_tasks_p 10
  @sc_get_daily_tasks_p 11
  @cs_get_weekly_tasks_p 12
  @sc_get_weekly_tasks_p 13
  @cs_get_achievement_tasks_p 14
  @sc_get_achievement_tasks_p 15
  # 处理来自客户端的成就任务请求 - 使用15作为客户端请求协议
  @cs_fetch_achievement_tasks_p 15
  @cs_refresh_daily_tasks_p 16
  @sc_refresh_daily_tasks_p 17
  @cs_get_task_categories_p 18
  @sc_get_task_categories_p 19

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "Task",
      description: "活动任务协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      # websocket_handler迁移
      {0, "更新任务列表"},
      # websocket_handler迁移
      {2, "领取任务奖励"},
      {@cs_get_task_list_p, "获取任务列表"},
      {@cs_get_task_detail_p, "获取任务详情"},
      {@cs_complete_task_p, "完成任务"},
      {@cs_claim_task_reward_p, "领取任务奖励"},
      {@cs_get_task_progress_p, "获取任务进度"},
      {@cs_get_daily_tasks_p, "获取每日任务"},
      {@cs_get_weekly_tasks_p, "获取每周任务"},
      # websocket_handler迁移
      {11, "获取比赛列表"},
      {@cs_get_achievement_tasks_p, "获取成就任务"},
      # websocket_handler迁移
      {13, "获取比赛排名奖励"},
      # 客户端请求协议
      {15, "获取今日比赛列表"},
      {@cs_fetch_achievement_tasks_p, "请求成就任务列表"},
      {@cs_refresh_daily_tasks_p, "刷新每日任务"},
      {@cs_get_task_categories_p, "获取任务分类"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "TASK", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "TASK", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_get_task_list_p ->
        ProtocolUtils.validate_params(data, [
          {:number_range, :page, 1, 1000, "页码"},
          {:number_range, :page_size, 1, 100, "每页大小"}
        ])

      @cs_get_task_detail_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:task_id]}
        ])

      @cs_complete_task_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:task_id]}
        ])

      @cs_claim_task_reward_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:task_id]}
        ])

      @cs_get_task_progress_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:task_id]}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id

    case sub_protocol do
      # 更新任务列表 (从websocket_handler迁移)
      0 ->
        handle_update_task_list(data, context)

      # 领取任务奖励 (从websocket_handler迁移)
      2 ->
        handle_get_task_reward_legacy(data, context)

      @cs_get_task_list_p ->
        handle_get_task_list(user_id, data)

      @cs_get_task_detail_p ->
        handle_get_task_detail(user_id, data)

      @cs_complete_task_p ->
        handle_complete_task(user_id, data)

      @cs_claim_task_reward_p ->
        handle_claim_task_reward(user_id, data)

      @cs_get_task_progress_p ->
        handle_get_task_progress(user_id, data)

      @cs_get_daily_tasks_p ->
        handle_get_daily_tasks(user_id)

      @cs_get_weekly_tasks_p ->
        handle_get_weekly_tasks(user_id)

      # 获取比赛列表 (从websocket_handler迁移)
      11 ->
        handle_get_match_list(data, context)

      @cs_get_achievement_tasks_p ->
        handle_get_achievement_tasks(user_id)

      @cs_fetch_achievement_tasks_p ->
        handle_get_achievement_tasks(user_id)

      # 获取比赛排名奖励 (从websocket_handler迁移)
      13 ->
        handle_get_match_rank_reward(data, context)

      # 获取今日比赛列表 (从websocket_handler迁移)
      15 ->
        handle_get_today_match_list(data, context)

      @cs_refresh_daily_tasks_p ->
        handle_refresh_daily_tasks(user_id)

      @cs_get_task_categories_p ->
        handle_get_task_categories()

      _ ->
        ProtocolUtils.log_protocol(:warning, "TASK", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 处理获取任务列表
  defp handle_get_task_list(user_id, data) do
    page = Map.get(data, "page", 1)
    page_size = Map.get(data, "page_size", 20)
    category = Map.get(data, "category", "all")
    # all, available, completed, claimed
    status = Map.get(data, "status", "all")

    try do
      case ActivityTaskService.get_user_task_list(user_id, category, status, page, page_size) do
        {:ok, {tasks, total_count}} ->
          formatted_tasks = Enum.map(tasks, &format_task_item/1)

          response_data =
            ProtocolUtils.success_response(
              ProtocolUtils.build_pagination_response(
                formatted_tasks,
                page,
                page_size,
                total_count
              )
            )

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_get_task_list_p,
            user_id,
            "获取任务列表成功，共#{total_count}条"
          )

          {:ok, @sc_get_task_list_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_get_task_list_p,
            user_id,
            "获取任务列表失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取任务列表失败")
          {:ok, @sc_get_task_list_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_get_task_list_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_task_list_p, error_data}
    end
  end

  # 处理获取任务详情
  defp handle_get_task_detail(user_id, data) do
    task_id = Map.get(data, "task_id")

    try do
      case ActivityTaskService.get_task_detail(user_id, task_id) do
        {:ok, task} ->
          response_data =
            ProtocolUtils.success_response(%{
              "task" => format_task_detail(task)
            })

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_get_task_detail_p,
            user_id,
            "获取任务详情成功: #{task_id}"
          )

          {:ok, @sc_get_task_detail_p, response_data}

        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "任务不存在")
          {:ok, @sc_get_task_detail_p, error_data}

        {:error, :expired} ->
          error_data = ProtocolUtils.error_response(:forbidden, "任务已过期")
          {:ok, @sc_get_task_detail_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_get_task_detail_p,
            user_id,
            "获取任务详情失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取任务详情失败")
          {:ok, @sc_get_task_detail_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_get_task_detail_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_task_detail_p, error_data}
    end
  end

  # 处理完成任务
  defp handle_complete_task(user_id, data) do
    task_id = Map.get(data, "task_id")
    completion_data = Map.get(data, "completion_data", %{})

    try do
      case ActivityTaskService.complete_task(user_id, task_id, completion_data) do
        {:ok, completion_result} ->
          response_data =
            ProtocolUtils.success_response(%{
              "taskId" => task_id,
              "completed" => true,
              "progress" => completion_result.progress,
              "canClaimReward" => completion_result.can_claim_reward,
              "msg" => "任务完成成功"
            })

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_complete_task_p,
            user_id,
            "完成任务成功: #{task_id}"
          )

          {:ok, @sc_complete_task_p, response_data}

        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "任务不存在")
          {:ok, @sc_complete_task_p, error_data}

        {:error, :already_completed} ->
          error_data = ProtocolUtils.error_response(:forbidden, "任务已完成")
          {:ok, @sc_complete_task_p, error_data}

        {:error, :requirements_not_met} ->
          error_data = ProtocolUtils.error_response(:forbidden, "任务完成条件不满足")
          {:ok, @sc_complete_task_p, error_data}

        {:error, :expired} ->
          error_data = ProtocolUtils.error_response(:forbidden, "任务已过期")
          {:ok, @sc_complete_task_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_complete_task_p,
            user_id,
            "完成任务失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "完成任务失败")
          {:ok, @sc_complete_task_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_complete_task_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_complete_task_p, error_data}
    end
  end

  # 处理领取任务奖励
  defp handle_claim_task_reward(user_id, data) do
    task_id = Map.get(data, "task_id")

    try do
      case ActivityTaskService.claim_task_reward(user_id, task_id) do
        {:ok, reward_info} ->
          response_data =
            ProtocolUtils.success_response(%{
              "taskId" => task_id,
              "claimed" => true,
              "rewards" => format_rewards(reward_info.rewards),
              "totalValue" => reward_info.total_value,
              "msg" => "任务奖励领取成功"
            })

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_claim_task_reward_p,
            user_id,
            "领取任务奖励成功: #{task_id}"
          )

          {:ok, @sc_claim_task_reward_p, response_data}

        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "任务不存在")
          {:ok, @sc_claim_task_reward_p, error_data}

        {:error, :not_completed} ->
          error_data = ProtocolUtils.error_response(:forbidden, "任务尚未完成")
          {:ok, @sc_claim_task_reward_p, error_data}

        {:error, :already_claimed} ->
          error_data = ProtocolUtils.error_response(:forbidden, "奖励已领取")
          {:ok, @sc_claim_task_reward_p, error_data}

        {:error, :expired} ->
          error_data = ProtocolUtils.error_response(:forbidden, "奖励已过期")
          {:ok, @sc_claim_task_reward_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_claim_task_reward_p,
            user_id,
            "领取任务奖励失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "领取奖励失败")
          {:ok, @sc_claim_task_reward_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_claim_task_reward_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_claim_task_reward_p, error_data}
    end
  end

  # 处理获取任务进度
  defp handle_get_task_progress(user_id, data) do
    task_id = Map.get(data, "task_id")

    try do
      case ActivityTaskService.get_task_progress(user_id, task_id) do
        {:ok, progress_info} ->
          response_data =
            ProtocolUtils.success_response(%{
              "taskId" => task_id,
              "currentProgress" => progress_info.current_progress,
              "targetProgress" => progress_info.target_progress,
              "progressPercentage" => progress_info.progress_percentage,
              "isCompleted" => progress_info.is_completed,
              "isRewardClaimed" => progress_info.is_reward_claimed,
              "lastUpdated" => format_datetime(progress_info.last_updated)
            })

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_get_task_progress_p,
            user_id,
            "获取任务进度成功: #{task_id}"
          )

          {:ok, @sc_get_task_progress_p, response_data}

        {:error, :not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "任务不存在")
          {:ok, @sc_get_task_progress_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_get_task_progress_p,
            user_id,
            "获取任务进度失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取任务进度失败")
          {:ok, @sc_get_task_progress_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_get_task_progress_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_task_progress_p, error_data}
    end
  end

  # 处理获取每日任务
  defp handle_get_daily_tasks(user_id) do
    try do
      case ActivityTaskService.get_daily_tasks(user_id) do
        {:ok, tasks} ->
          formatted_tasks = Enum.map(tasks, &format_task_item/1)

          response_data =
            ProtocolUtils.success_response(%{
              "dailyTasks" => formatted_tasks,
              "refreshTime" => get_next_daily_refresh_time(),
              "totalTasks" => length(tasks),
              "completedTasks" => count_completed_tasks(tasks)
            })

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_get_daily_tasks_p,
            user_id,
            "获取每日任务成功，共#{length(tasks)}个"
          )

          {:ok, @sc_get_daily_tasks_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_get_daily_tasks_p,
            user_id,
            "获取每日任务失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取每日任务失败")
          {:ok, @sc_get_daily_tasks_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_get_daily_tasks_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_daily_tasks_p, error_data}
    end
  end

  # 处理获取每周任务
  defp handle_get_weekly_tasks(user_id) do
    try do
      case ActivityTaskService.get_weekly_tasks(user_id) do
        {:ok, tasks} ->
          formatted_tasks = Enum.map(tasks, &format_task_item/1)

          response_data =
            ProtocolUtils.success_response(%{
              "weeklyTasks" => formatted_tasks,
              "refreshTime" => get_next_weekly_refresh_time(),
              "totalTasks" => length(tasks),
              "completedTasks" => count_completed_tasks(tasks)
            })

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_get_weekly_tasks_p,
            user_id,
            "获取每周任务成功，共#{length(tasks)}个"
          )

          {:ok, @sc_get_weekly_tasks_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_get_weekly_tasks_p,
            user_id,
            "获取每周任务失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取每周任务失败")
          {:ok, @sc_get_weekly_tasks_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_get_weekly_tasks_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_weekly_tasks_p, error_data}
    end
  end

  # 处理获取成就任务
  defp handle_get_achievement_tasks(user_id) do
    try do
      case ActivityTaskService.get_achievement_tasks(user_id) do
        {:ok, tasks} ->
          formatted_tasks = Enum.map(tasks, &format_task_item/1)

          response_data =
            ProtocolUtils.success_response(%{
              "achievementTasks" => formatted_tasks,
              "totalTasks" => length(tasks),
              "completedTasks" => count_completed_tasks(tasks),
              "totalAchievementPoints" => calculate_total_achievement_points(tasks)
            })

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_get_achievement_tasks_p,
            user_id,
            "获取成就任务成功，共#{length(tasks)}个"
          )

          {:ok, @sc_get_achievement_tasks_p, response_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_get_achievement_tasks_p,
            user_id,
            "获取成就任务失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "获取成就任务失败")
          {:ok, @sc_get_achievement_tasks_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_get_achievement_tasks_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_achievement_tasks_p, error_data}
    end
  end

  # 处理刷新每日任务
  defp handle_refresh_daily_tasks(user_id) do
    try do
      case ActivityTaskService.refresh_daily_tasks(user_id) do
        {:ok, new_tasks} ->
          formatted_tasks = Enum.map(new_tasks, &format_task_item/1)

          response_data =
            ProtocolUtils.success_response(%{
              "refreshed" => true,
              "newTasks" => formatted_tasks,
              "msg" => "每日任务刷新成功"
            })

          ProtocolUtils.log_protocol(
            :info,
            "TASK",
            @cs_refresh_daily_tasks_p,
            user_id,
            "刷新每日任务成功，获得#{length(new_tasks)}个新任务"
          )

          {:ok, @sc_refresh_daily_tasks_p, response_data}

        {:error, :already_refreshed} ->
          error_data = ProtocolUtils.error_response(:forbidden, "今日已刷新过任务")
          {:ok, @sc_refresh_daily_tasks_p, error_data}

        {:error, :insufficient_resources} ->
          error_data = ProtocolUtils.error_response(:insufficient_balance, "刷新费用不足")
          {:ok, @sc_refresh_daily_tasks_p, error_data}

        {:error, reason} ->
          ProtocolUtils.log_protocol(
            :error,
            "TASK",
            @cs_refresh_daily_tasks_p,
            user_id,
            "刷新每日任务失败: #{inspect(reason)}"
          )

          error_data = ProtocolUtils.error_response(:internal_error, "刷新任务失败")
          {:ok, @sc_refresh_daily_tasks_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_refresh_daily_tasks_p,
          user_id,
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_refresh_daily_tasks_p, error_data}
    end
  end

  # 处理获取任务分类
  defp handle_get_task_categories() do
    try do
      categories = [
        %{
          "id" => "daily",
          "name" => "每日任务",
          "description" => "每天刷新的任务",
          "icon" => "daily_icon"
        },
        %{
          "id" => "weekly",
          "name" => "每周任务",
          "description" => "每周刷新的任务",
          "icon" => "weekly_icon"
        },
        %{
          "id" => "achievement",
          "name" => "成就任务",
          "description" => "长期成就目标",
          "icon" => "achievement_icon"
        },
        %{
          "id" => "event",
          "name" => "活动任务",
          "description" => "限时活动任务",
          "icon" => "event_icon"
        },
        %{
          "id" => "newbie",
          "name" => "新手任务",
          "description" => "新手引导任务",
          "icon" => "newbie_icon"
        }
      ]

      response_data =
        ProtocolUtils.success_response(%{
          "categories" => categories
        })

      {:ok, @sc_get_task_categories_p, response_data}
    rescue
      error ->
        ProtocolUtils.log_protocol(
          :error,
          "TASK",
          @cs_get_task_categories_p,
          "system",
          "处理异常: #{inspect(error)}"
        )

        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_get_task_categories_p, error_data}
    end
  end

  # ============================================================================
  # 工具函数
  # ============================================================================

  defp format_task_item(task) do
    %{
      "id" => task.id,
      "title" => task.title,
      "description" => task.description,
      "category" => task.category,
      "difficulty" => task.difficulty,
      "currentProgress" => task.current_progress || 0,
      "targetProgress" => task.target_progress,
      "progressPercentage" =>
        calculate_progress_percentage(task.current_progress, task.target_progress),
      "isCompleted" => task.is_completed || false,
      "isRewardClaimed" => task.is_reward_claimed || false,
      "rewards" => format_task_rewards(task.rewards),
      "icon" => task.icon,
      "priority" => task.priority || 0,
      "expiresAt" => format_datetime(task.expires_at),
      "createdAt" => format_datetime(task.inserted_at)
    }
  end

  defp format_task_detail(task) do
    base_info = format_task_item(task)

    Map.merge(base_info, %{
      "requirements" => task.requirements || [],
      "hints" => task.hints || [],
      "conditions" => task.conditions || %{},
      "completionData" => task.completion_data || %{},
      "tags" => task.tags || [],
      "estimatedTime" => task.estimated_time,
      "lastUpdated" => format_datetime(task.updated_at)
    })
  end

  defp format_task_rewards(rewards) when is_list(rewards) do
    Enum.map(rewards, fn reward ->
      %{
        "type" => reward.type,
        "amount" => reward.amount,
        "itemId" => reward.item_id,
        "description" => reward.description,
        "icon" => reward.icon
      }
    end)
  end

  defp format_task_rewards(_), do: []

  defp format_rewards(rewards) when is_list(rewards) do
    Enum.map(rewards, fn reward ->
      %{
        "type" => reward.type,
        "amount" => reward.amount,
        "itemId" => reward.item_id,
        "description" => reward.description
      }
    end)
  end

  defp format_rewards(_), do: []

  defp calculate_progress_percentage(current, target)
       when is_number(current) and is_number(target) and target > 0 do
    min(100, round(current / target * 100))
  end

  defp calculate_progress_percentage(_, _), do: 0

  defp count_completed_tasks(tasks) do
    Enum.count(tasks, fn task -> task.is_completed || false end)
  end

  defp calculate_total_achievement_points(tasks) do
    tasks
    |> Enum.filter(fn task -> task.is_reward_claimed || false end)
    |> Enum.reduce(0, fn task, acc ->
      achievement_points =
        task.rewards
        |> Enum.filter(fn reward -> reward.type == "achievement_points" end)
        |> Enum.map(fn reward -> reward.amount end)
        |> Enum.sum()

      acc + achievement_points
    end)
  end

  defp get_next_daily_refresh_time() do
    DateTime.utc_now()
    |> DateTime.add(1, :day)
    |> DateTime.beginning_of_day()
    |> DateTime.to_unix(:millisecond)
  end

  defp get_next_weekly_refresh_time() do
    now = DateTime.utc_now()
    days_until_monday = 7 - Date.day_of_week(DateTime.to_date(now)) + 1

    now
    |> DateTime.add(days_until_monday, :day)
    |> DateTime.beginning_of_day()
    |> DateTime.to_unix(:millisecond)
  end

  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)

  # ============================================================================
  # 从websocket_handler迁移的处理函数
  # ============================================================================

  # 处理更新任务列表 (sub_id: 0)
  defp handle_update_task_list(data, context) do
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "TASK", 0, user_id, "更新任务列表信息")

    # 模拟任务列表
    task_list = [
      %{
        "id" => 1,
        "name" => "每日登录",
        "description" => "每日登录游戏获得奖励",
        # 1-每日任务, 2-周任务, 3-成就任务
        "type" => 1,
        "progress" => 1,
        "target" => 1,
        # 0-未完成, 1-已完成, 2-已领取
        "status" => 2,
        "rewards" => [
          %{"type" => "money", "amount" => 1000},
          %{"type" => "exp", "amount" => 100}
        ]
      },
      %{
        "id" => 2,
        "name" => "游戏5局",
        "description" => "完成5局游戏",
        "type" => 1,
        "progress" => 3,
        "target" => 5,
        "status" => 0,
        "rewards" => [
          %{"type" => "money", "amount" => 2000}
        ]
      },
      %{
        "id" => 3,
        "name" => "胜利3局",
        "description" => "获得3局胜利",
        "type" => 1,
        "progress" => 1,
        "target" => 3,
        "status" => 0,
        "rewards" => [
          %{"type" => "money", "amount" => 3000},
          %{"type" => "item", "item_id" => 1, "count" => 1}
        ]
      }
    ]

    response_data =
      ProtocolUtils.success_response(%{
        "status" => 0,
        "task_list" => task_list,
        "total_tasks" => length(task_list),
        "completed_tasks" => Enum.count(task_list, fn task -> task["status"] >= 1 end),
        "claimed_tasks" => Enum.count(task_list, fn task -> task["status"] == 2 end)
      })

    {:ok, 1, response_data}
  end

  # 处理领取任务奖励 (sub_id: 2) - 兼容旧版本
  defp handle_get_task_reward_legacy(data, context) do
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "TASK", 2, user_id, "领取任务奖励")

    task_id = Map.get(data, "task_id", 0)

    response_data =
      cond do
        task_id <= 0 ->
          ProtocolUtils.error_response(:invalid_params, "无效的任务ID")

        true ->
          # 模拟任务奖励
          rewards = [
            %{"type" => "money", "amount" => 1000},
            %{"type" => "exp", "amount" => 100}
          ]

          ProtocolUtils.success_response(%{
            "status" => 0,
            "task_id" => task_id,
            "rewards" => rewards,
            "msg" => "任务奖励领取成功"
          })
      end

    {:ok, 3, response_data}
  end

  # 处理获取比赛列表 (sub_id: 11)
  defp handle_get_match_list(data, context) do
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "TASK", 11, user_id, "获取比赛列表信息")

    # 模拟比赛列表
    match_list = [
      %{
        "id" => 1,
        "name" => "每日金币赛",
        "description" => "每日金币排行赛",
        # 1-金币赛, 2-积分赛
        "type" => 1,
        "start_time" => System.system_time(:millisecond),
        "end_time" => System.system_time(:millisecond) + 86_400_000,
        "entry_fee" => 1000,
        "max_players" => 100,
        "current_players" => 45,
        # 0-未开始, 1-进行中, 2-已结束
        "status" => 1,
        "rewards" => [
          %{"rank" => 1, "reward" => [%{"type" => "money", "amount" => 50000}]},
          %{"rank" => 2, "reward" => [%{"type" => "money", "amount" => 30000}]},
          %{"rank" => 3, "reward" => [%{"type" => "money", "amount" => 20000}]}
        ]
      },
      %{
        "id" => 2,
        "name" => "周末大奖赛",
        "description" => "周末特别大奖赛",
        "type" => 2,
        "start_time" => System.system_time(:millisecond) + 86_400_000,
        "end_time" => System.system_time(:millisecond) + 86_400_000 * 3,
        "entry_fee" => 5000,
        "max_players" => 500,
        "current_players" => 0,
        "status" => 0,
        "rewards" => [
          %{"rank" => 1, "reward" => [%{"type" => "money", "amount" => 500_000}]},
          %{"rank" => 2, "reward" => [%{"type" => "money", "amount" => 300_000}]},
          %{"rank" => 3, "reward" => [%{"type" => "money", "amount" => 200_000}]}
        ]
      }
    ]

    response_data =
      ProtocolUtils.success_response(%{
        "status" => 0,
        "match_list" => match_list,
        "total_matches" => length(match_list),
        "active_matches" => Enum.count(match_list, fn match -> match["status"] == 1 end)
      })

    {:ok, 12, response_data}
  end

  # 处理获取今日比赛列表 (sub_id: 15)
  defp handle_get_today_match_list(data, context) do
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "TASK", 15, user_id, "获取今日比赛列表")

    # 模拟今日比赛列表
    today_matches = [
      %{
        "id" => 1,
        "name" => "今日金币赛",
        "description" => "今日特别金币赛",
        "type" => 1,
        "start_time" => System.system_time(:millisecond),
        "end_time" => System.system_time(:millisecond) + 86_400_000,
        "entry_fee" => 1000,
        "max_players" => 100,
        "current_players" => 45,
        "status" => 1,
        "rewards" => [
          %{"rank" => 1, "reward" => [%{"type" => "money", "amount" => 50000}]},
          %{"rank" => 2, "reward" => [%{"type" => "money", "amount" => 30000}]},
          %{"rank" => 3, "reward" => [%{"type" => "money", "amount" => 20000}]}
        ]
      }
    ]

    response_data =
      ProtocolUtils.success_response(%{
        "status" => 0,
        "today_matches" => today_matches,
        "total_matches" => length(today_matches),
        "active_matches" => Enum.count(today_matches, fn match -> match["status"] == 1 end)
      })

    {:ok, 16, response_data}
  end

  # 处理获取比赛排名奖励 (sub_id: 13)
  defp handle_get_match_rank_reward(data, context) do
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "TASK", 13, user_id, "获取比赛排名和奖励")

    match_id = Map.get(data, "match_id", 0)

    # 模拟比赛排名
    rank_list = [
      %{
        "rank" => 1,
        "user_id" => "user_001",
        "nickname" => "比赛冠军",
        "score" => 100_000,
        "reward_claimed" => true
      },
      %{
        "rank" => 2,
        "user_id" => "user_002",
        "nickname" => "比赛亚军",
        "score" => 80000,
        "reward_claimed" => false
      },
      %{
        "rank" => 3,
        "user_id" => user_id,
        "nickname" => "我的昵称",
        "score" => 60000,
        "reward_claimed" => false
      }
    ]

    my_rank = Enum.find_index(rank_list, fn player -> player["user_id"] == user_id end)
    my_rank = if my_rank, do: my_rank + 1, else: nil

    response_data =
      ProtocolUtils.success_response(%{
        "status" => 0,
        "match_id" => match_id,
        "rank_list" => rank_list,
        "my_rank" => my_rank,
        "my_score" => if(my_rank, do: 60000, else: 0),
        "can_claim_reward" => my_rank != nil and my_rank <= 10
      })

    {:ok, 14, response_data}
  end

  # 处理获取今日比赛列表 (sub_id: 15)
  defp handle_get_today_match_list(data, context) do
    user_id = context.user_id || "anonymous"

    ProtocolUtils.log_protocol(:info, "TASK", 15, user_id, "获取今日比赛列表")

    # 模拟今日比赛数据
    today_matches = [
      %{
        "match_id" => 1,
        "match_name" => "每日竞技赛",
        "game_type" => "teenpatti",
        "start_time" => System.system_time(:millisecond),
        # 24小时后
        "end_time" => System.system_time(:millisecond) + 86_400_000,
        "entry_fee" => 100,
        "prize_pool" => 10000,
        "max_players" => 100,
        "current_players" => 45,
        "status" => "active"
      },
      %{
        "match_id" => 2,
        "match_name" => "黄金联赛",
        "game_type" => "crash",
        # 1小时后
        "start_time" => System.system_time(:millisecond) + 3_600_000,
        # 25小时后
        "end_time" => System.system_time(:millisecond) + 90_000_000,
        "entry_fee" => 500,
        "prize_pool" => 50000,
        "max_players" => 50,
        "current_players" => 12,
        "status" => "upcoming"
      }
    ]

    response_data =
      ProtocolUtils.success_response(%{
        "matches" => today_matches,
        "total_matches" => length(today_matches),
        "server_time" => System.system_time(:millisecond)
      })

    {:ok, 16, response_data}
  end
end
