defmodule Teen.PaymentSystem do
  @moduledoc """
  支付系统域

  包含支付配置、兑换配置、银行信息、支付渠道、支付网关等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  alias Teen.PaymentSystem.{
    PaymentConfig,
    WithdrawalConfig,
    PaymentGateway,
    PaymentOrder,
    PaymentService,
    BankConfig,
    UserBankCard,
    RechargeRecord,
    WithdrawalRecord
  }

  admin do
    show? true
  end

  resources do
    resource Teen.PaymentSystem.PaymentGateway

    resource Teen.PaymentSystem.PaymentConfig
    resource Teen.PaymentSystem.PaymentService
    resource Teen.PaymentSystem.RechargeRecord

    resource Teen.PaymentSystem.PaymentOrder

    resource Teen.PaymentSystem.WithdrawalConfig
    resource Teen.PaymentSystem.BankConfig
    resource Teen.PaymentSystem.UserBankCard
    resource Teen.PaymentSystem.WithdrawalRecord
  end

  # ==================== 业务逻辑函数 ====================
  @doc """
  更新支付网关状态（单个或批量）
  """
  def update_gateway_status(gateway_id_or_ids, status)

  def update_gateway_status(gateway_id, status) when is_binary(gateway_id) do
    case Ash.get(PaymentGateway, gateway_id) do
      {:ok, gateway} ->
        case status do
          :enable -> PaymentGateway.enable(gateway)
          :disable -> PaymentGateway.disable(gateway)
          _ -> {:error, "Invalid status"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  def update_gateway_status(gateway_ids, status) when is_list(gateway_ids) do
    results =
      Enum.map(gateway_ids, fn gateway_id ->
        case Ash.get(PaymentGateway, gateway_id) do
          {:ok, gateway} ->
            case status do
              :enable -> PaymentGateway.enable(gateway)
              :disable -> PaymentGateway.disable(gateway)
              _ -> {:error, "Invalid status"}
            end

          {:error, reason} ->
            {:error, {gateway_id, reason}}
        end
      end)

    {successes, failures} =
      Enum.split_with(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  测试支付网关连接
  """
  def test_gateway_connection(gateway_id) do
    case Ash.get(PaymentGateway, gateway_id) do
      {:ok, gateway} ->
        # 这里应该实现实际的网关连接测试
        test_result =
          case :rand.uniform(10) do
            n when n <= 8 ->
              %{
                status: :success,
                response_time: :rand.uniform(1000),
                message: "Gateway connection successful"
              }

            _ ->
              %{
                status: :failed,
                response_time: nil,
                message: "Gateway connection failed"
              }
          end

        # 更新最后测试时间
        PaymentGateway.test_connection(gateway)

        {:ok, test_result}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # ==================== 辅助函数 ====================

  @doc """
  获取默认的充值档位（客户端兼容格式）
  """
  defp get_default_recharge_sections do
    # 默认充值档位（分为单位），转换为客户端期望的字符串格式
    [1, 100_000, 500_000, 1_000_000, 2_000_000, 5_000_000, 10_000_000, 20_000_000]
    |> Enum.join("|")
  end

  @doc """
  获取默认的奖励档位（客户端兼容格式）
  """
  defp get_default_bonus_sections do
    # 默认奖励档位（对应充值档位的奖励金额），转换为客户端期望的字符串格式
    [0, 0, 0, 0, 0, 0, 0, 0]
    |> Enum.join("|")
  end

  @doc """
  获取用户银行配置信息
  包含银行列表和用户绑定的银行卡信息
  """
  def get_user_bank_config(user_id) do
    with {:ok, banks} <- BankConfig.list_active_banks(),
         {:ok, user_cards} <- get_user_cards(user_id) do
      # 构建银行配置映射
      bank_config =
        banks
        |> Enum.with_index()
        |> Enum.map(fn {bank, index} ->
          # 查找用户绑定的银行卡信息
          user_card = Enum.find(user_cards, fn card -> card.bank_id == bank.id end)

          bank_info = %{
            "id" => bank.id,
            "Name" => bank.name,
            "BankCode" => bank.bank_code,
            "MinAmount" => Decimal.to_string(bank.min_amount),
            "MaxAmount" => Decimal.to_string(bank.max_amount),
            "FeeRate" => Decimal.to_string(bank.fee_rate),
            "IconUrl" => bank.icon_url,
            "Status" => bank.status
          }

          # 如果用户绑定了这个银行的卡，添加账户信息
          bank_info =
            if user_card do
              Map.merge(bank_info, %{
                "BankAccountName" => user_card.account_name,
                "BankAccountNum" => user_card.account_number,
                "IsDefault" => user_card.is_default
              })
            else
              bank_info
            end

          {index, bank_info}
        end)
        |> Enum.into(%{})

      # 构建绑定银行卡配置
      bind_bank_config =
        user_cards
        |> Enum.with_index()
        |> Enum.map(fn {card, index} ->
          {index,
           %{
             "BankID" => card.bank_id,
             "BankAccountName" => card.account_name,
             "BankAccountNum" => card.account_number,
             "IsDefault" => card.is_default,
             "Status" => card.status
           }}
        end)
        |> Enum.into(%{})

      {:ok,
       %{
         "BankConfig" => bank_config,
         "BindBankConfig" => bind_bank_config
       }}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取系统配置信息
  包含支付配置、银行配置等
  """
  def get_system_config(user_id \\ nil) do
    # 获取支付配置 - 现在从 PaymentGateway 获取
    {:ok, payment_configs} = PaymentConfig.list_active_configs()

    # 获取银行配置
    bank_config =
      case get_user_bank_config(user_id) do
        {:ok, config} -> config
        _ -> %{"BankConfig" => %{}, "BindBankConfig" => %{}}
      end

    # 构建充值配置 (RechargeConfig)
    recharge_config =
      payment_configs
      |> Enum.with_index()
      |> Enum.map(fn {config, index} ->
        {index,
         %{
           "ChargeID" => index,
           # 默认充值类型
           "ChargeType" => 1,
           "GatewayId" => index,
           "GatewayName" => config.gateway_name,
           "MaxPrice" => Decimal.to_integer(config.max_amount),
           "MinPrice" => Decimal.to_integer(config.min_amount),
           "PayTypeCode" => index,
           "PayTypeId" => index,
           "Rate" => Decimal.to_float(config.fee_rate),
           "Section" => generate_recharge_sections(config.min_amount, config.max_amount),
           "SendBonus" => generate_bonus_sections(config.min_amount, config.max_amount),
           "Sort" => index,
           "SubRate" => Decimal.to_float(config.deduction_rate || Decimal.new("0")),
           "TypeName" => config.payment_type,
           "ShowName" => config.payment_type_name,
           "TypeSort" => index
         }}
      end)
      |> Enum.into(%{})

    # 构建完整的系统配置
    config =
      Map.merge(bank_config, %{
        "PaymentConfig" =>
          payment_configs
          |> Enum.with_index()
          |> Enum.map(fn {config, index} ->
            {index,
             %{
               "ChargeID" => config.id,
               # 默认充值类型
               "ChargeType" => 1,
               "GatewayId" => config.gateway_id,
               "GatewayName" => config.gateway_name,
               # 客户端期望的字段名
               "PayTypeCode" => config.payment_type,
               "PayTypeId" => index,
               "TypeName" => config.payment_type_name,
               "ShowName" => config.payment_type_name,
               "MinPrice" => Decimal.to_integer(config.min_amount),
               "MaxPrice" => Decimal.to_integer(config.max_amount),
               "Rate" => Decimal.to_float(config.fee_rate),
               "SubRate" => 0,
               "Sort" => config.sort_order || index,
               "TypeSort" => config.sort_order || index,
               "Section" => get_default_recharge_sections(),
               "SendBonus" => get_default_bonus_sections(),
               "status" => config.status,
               # 保持向后兼容的字段
               "id" => config.id,
               "gateway_name" => config.gateway_name,
               "payment_type" => config.payment_type,
               "payment_type_name" => config.payment_type_name,
               "min_amount" => Decimal.to_string(config.min_amount),
               "max_amount" => Decimal.to_string(config.max_amount),
               "fee_rate" => Decimal.to_string(config.fee_rate)
             }}
          end)
          |> Enum.into(%{}),
        "RechargeConfig" => recharge_config,
        "Expansion" => get_withdrawal_amount_options()
      })

    {:ok, config}
  end

  # 辅助函数：获取用户银行卡
  defp get_user_cards(nil), do: {:ok, []}
  defp get_user_cards(user_id), do: UserBankCard.list_by_user(%{user_id: user_id})

  # 辅助函数：生成充值区间（客户端兼容格式）
  defp generate_recharge_sections(min_amount, max_amount) do
    min = Decimal.to_integer(min_amount)
    max = Decimal.to_integer(max_amount)

    # 生成常见的充值金额区间
    base_sections = [
      1,
      100,
      500_000,
      1_000_000,
      2_000_000,
      5_000_000,
      10_000_000,
      20_000_000
    ]

    # 过滤出在最小和最大金额范围内的区间
    sections =
      base_sections
      |> Enum.filter(fn amount -> amount >= min and amount <= max end)
      |> case do
        # 如果没有合适的区间，使用最小和最大值
        [] -> [min, max]
        sections -> sections
      end

    # 转换为客户端期望的字符串格式
    sections |> Enum.join("|")
  end

  # 辅助函数：生成奖励区间（客户端兼容格式）
  defp generate_bonus_sections(min_amount, max_amount) do
    min = Decimal.to_integer(min_amount)
    max = Decimal.to_integer(max_amount)

    # 生成常见的充值金额区间（用于计算奖励）
    base_sections = [
      1,
      100_000,
      500_000,
      1_000_000,
      2_000_000,
      5_000_000,
      10_000_000,
      20_000_000
    ]

    # 过滤出在最小和最大金额范围内的区间
    sections =
      base_sections
      |> Enum.filter(fn amount -> amount >= min and amount <= max end)
      |> case do
        # 如果没有合适的区间，使用最小和最大值
        [] -> [min, max]
        sections -> sections
      end

    # 生成对应的奖励金额（这里使用5%作为示例）并转换为字符串格式
    sections
    |> Enum.map(fn amount -> trunc(amount * 0.05) end)
    |> Enum.join("|")
  end

  # 获取提现金额选项配置
  defp get_withdrawal_amount_options do
    [100, 200, 5000, 20000]
    |> Enum.join("|")
  end
end
