defmodule Teen.CustomerService do
  @moduledoc """
  客服管理域

  包含客服聊天、用户问题、兑换订单、支付订单等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  import Ash.Expr

  alias Teen.CustomerService.{CustomerChat, ExchangeOrder, SensitiveWord, VerificationCode}
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Cypridina.Accounts.User

  admin do
    show? true
  end

  resources do
    resource Teen.CustomerService.CustomerChat
    resource Teen.CustomerService.UserQuestion
    resource Teen.CustomerService.ExchangeOrder
    resource Teen.CustomerService.SensitiveWord
    resource Teen.CustomerService.UserTag
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  处理客服聊天消息，包括敏感词过滤
  """
  def process_customer_chat(attrs) do
    with {:ok, filtered_question} <- filter_sensitive_words(attrs["question"]),
         attrs <- Map.put(attrs, "question", filtered_question),
         {:ok, chat} <- CustomerChat.create(attrs) do
      # 自动分配客服
      assign_customer_service(chat)
      {:ok, chat}
    end
  end

  @doc """
  过滤敏感词
  """
  def filter_sensitive_words(content) when is_binary(content) do
    case SensitiveWord.list_active_words() do
      {:ok, words} ->
        filtered_content =
          Enum.reduce(words, content, fn word, acc ->
            case word.action_type do
              1 -> String.replace(acc, word.keyword, word.replacement || "***")
              2 -> String.replace(acc, word.keyword, "***")
              # 警告但不替换
              3 -> acc
            end
          end)

        {:ok, filtered_content}

      {:error, reason} ->
        {:error, reason}
    end
  end

  def filter_sensitive_words(_), do: {:error, "Invalid content"}

  @doc """
  批量回复客服消息
  """
  def batch_reply_messages(chat_ids, reply_content) when is_list(chat_ids) do
    results =
      Enum.map(chat_ids, fn chat_id ->
        case CustomerChat.read(chat_id) do
          {:ok, chat} ->
            CustomerChat.batch_reply(chat, %{reply_content: reply_content})

          {:error, reason} ->
            {:error, {chat_id, reason}}
        end
      end)

    {successes, failures} =
      Enum.split_with(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  # ==================== 私有函数 ====================

  defp assign_customer_service(chat) do
    # 简单的轮询分配策略
    case get_available_customer_service() do
      {:ok, cs_user} ->
        CustomerChat.mark_as_processed(chat, %{
          customer_service_id: cs_user.id,
          reply_content: "您好，我是客服，请问有什么可以帮助您的？"
        })

      {:error, _} ->
        # 没有可用客服，保持未处理状态
        :ok
    end
  end

  defp get_available_customer_service do
    # 这里应该查询可用的客服人员
    {:error, "No available customer service"}
  end

  defp notify_user_order_approved(_user, _order), do: :ok
  defp notify_user_order_rejected(_user_id, _order, _feedback), do: :ok

  # ==================== 聊天相关函数 ====================

  @doc """
  获取最近的客服聊天记录
  """
  def list_recent_customer_chats(opts \\ []) do
    limit = Keyword.get(opts, :limit, 50)

    CustomerChat
    |> Ash.Query.new()
    |> Ash.Query.sort(inserted_at: :desc)
    |> Ash.Query.limit(limit)
    |> Ash.Query.load([:user])
    |> Ash.read!()
  end

  @doc """
  获取用户的聊天历史记录
  """
  def list_user_chat_history(user_id) do
    require Ash.Query
    import Ash.Expr

    CustomerChat
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> Ash.Query.sort(inserted_at: :asc)
    |> Ash.Query.load([:user, :customer_service])
    |> Ash.read!()
  end

  @doc """
  统计未读聊天数量
  """
  def count_unread_chats do
    require Ash.Query
    import Ash.Expr

    case CustomerChat
         |> Ash.Query.filter(expr(status == 0))
         |> Ash.read() do
      {:ok, chats} -> length(chats)
      _ -> 0
    end
  end

  @doc """
  创建客服聊天记录
  """
  def create_customer_chat(attrs) do
    CustomerChat.create(attrs)
  end

  @doc """
  更新聊天状态为已处理
  """
  def mark_chat_as_processed(chat_id, customer_service_id) do
    case CustomerChat.read(chat_id) do
      {:ok, chat} ->
        CustomerChat.update(chat, %{
          status: 1,
          customer_service_id: customer_service_id,
          processed_at: DateTime.utc_now()
        })

      error ->
        error
    end
  end

  @doc """
  获取在线客服列表
  """
  def list_online_customer_service do
    # 这里应该从 Presence 或其他状态管理获取在线客服
    # 暂时返回空列表
    []
  end

  @doc """
  检查用户是否已有活跃的客服聊天会话
  """
  def has_active_chat_session?(user_id) do
    require Ash.Query
    import Ash.Expr

    case CustomerChat
         # 未处理状态表示活跃会话
         |> Ash.Query.filter(expr(user_id == ^user_id and status == 0))
         |> Ash.Query.sort(inserted_at: :desc)
         |> Ash.Query.limit(1)
         |> Ash.read() do
      {:ok, []} -> false
      {:ok, [_chat | _]} -> true
      {:error, _} -> false
    end
  end

  @doc """
  获取用户的活跃聊天会话
  """
  def get_active_chat_session(user_id) do
    require Ash.Query
    import Ash.Expr

    CustomerChat
    # 未处理状态
    |> Ash.Query.filter(expr(user_id == ^user_id and status == 0))
    |> Ash.Query.sort(inserted_at: :desc)
    |> Ash.Query.limit(1)
    |> Ash.Query.load([:user, :customer_service])
    |> Ash.read_one()
  end

  @doc """
  创建客服聊天记录（带重复检查）
  """
  def create_customer_chat_with_check(attrs) do
    user_id = attrs[:user_id] || attrs["user_id"]

    # 检查是否已有活跃会话
    if has_active_chat_session?(user_id) do
      case get_active_chat_session(user_id) do
        {:ok, existing_chat} ->
          {:error, {:already_exists, existing_chat}}

        {:error, _} ->
          # 如果获取失败，仍然尝试创建新会话
          create_customer_chat(attrs)
      end
    else
      create_customer_chat(attrs)
    end
  end
end
