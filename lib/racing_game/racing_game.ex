# lib/cypridina/racing_game.ex
# Every 3 minutes
defmodule RacingGameScheduler do
  use Oban.Worker, queue: :default, max_attempts: 1
  require Logger

  @version "1.0.1"

  @impl true
  def perform(_job) do
    Logger.info("RacingGameScheduler version #{@version} running")

    # 业务逻辑 - 通过 RaceController 触发新比赛
    RacingGame.RaceController.start_new_race()
    :ok
  end
end

defmodule RacingGame do
  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  admin do
    show? false
  end

  resources do
    resource RacingGame.Race
    resource RacingGame.Bet
    resource RacingGame.Stock
  end

  # 股票玩法
  # 添加用户资产相关的辅助函数
  def get_user_stocks(user_id) do
    # 使用get_by_user_id而不是直接用Ash.get!
    user_stocks = get_or_create_user_stocks(user_id)
    user_stocks
  end

  defp get_or_create_user_stocks(user_id) do
    case RacingGame.Stock.get_user_stocks(user_id) do
      {:error, _reason} ->
        # 创建默认股票记录，每个动物0股
        animals = ["A", "B", "C", "D", "E", "F"]

        Enum.each(animals, fn racer_id ->
          RacingGame.Stock
          |> Ash.Changeset.for_create(:create, %{
            user_id: user_id,
            racer_id: racer_id,
            quantity: 0
          })
          |> Ash.create()
        end)

        # 返回用户股票
        case RacingGame.Stock.get_user_stocks(user_id) do
          {:ok, stocks} -> stocks
          {:error, _} -> []
        end

      {:ok, stock} ->
        stock
    end
  end

  # ============================================================================
  # 股票管理功能 (从 StockManagementService 合并)
  # ============================================================================
  alias RacingGame.Stock
  alias Cypridina.Ledger.Transfer
  alias Cypridina.Repo
  require Ash.Query
  require Logger

  # 强制平仓触发价格阈值
  @force_liquidation_threshold 40

  @doc """
  重置所有股票总持仓数据
  """
  def reset_all_stock_holdings do
    Logger.info("开始重置所有股票总持仓数据...")

    Repo.transaction(fn ->
      # 删除所有现有股票持仓记录
      Stock
      |> Ash.read!()
      |> Enum.each(fn stock ->
        stock |> Ash.destroy!()
      end)

      Logger.info("已删除所有股票持仓记录")
      :ok
    end)
  end

  @doc """
  根据股票买入卖出交易记录重新统计股票总持仓
  """
  def recalculate_stock_holdings_from_transactions do
    Logger.info("开始根据交易记录重新统计股票总持仓...")

    Repo.transaction(fn ->
      # 获取所有股票买入卖出交易记录
      transactions =
        Transfer
        |> Ash.Query.filter(transaction_type in [:buy_stock, :sell_stock])
        |> Ash.Query.sort(inserted_at: :asc)
        |> Ash.read!()

      Logger.info("找到 #{length(transactions)} 条股票交易记录")

      # 按用户和动物分组统计净持仓
      user_stock_map =
        Enum.reduce(transactions, %{}, fn transaction, acc ->
          metadata = transaction.metadata || %{}

          case metadata do
            %{"racer_id" => racer_id, "quantity" => quantity}
            when is_binary(racer_id) and is_integer(quantity) ->
              user_id = transaction.to_account_id
              key = {user_id, racer_id}

              current_quantity = Map.get(acc, key, 0)

              new_quantity =
                case transaction.transaction_type do
                  :buy_stock -> current_quantity + quantity
                  :sell_stock -> current_quantity - quantity
                  _ -> current_quantity
                end

              Map.put(acc, key, new_quantity)

            _ ->
              Logger.warning("交易记录格式不正确，跳过: #{inspect(transaction.id)}")
              acc
          end
        end)

      Logger.info("统计出 #{map_size(user_stock_map)} 个用户-股票组合")

      # 创建股票持仓记录
      Enum.each(user_stock_map, fn {{user_id, racer_id}, quantity} ->
        if quantity > 0 do
          case Stock.add(%{
                 user_id: user_id,
                 racer_id: racer_id,
                 amount: quantity,
                 # 重新统计时设置为0，后续会通过交易记录重新计算
                 cost: Decimal.new(0),
                 # 系统操作
                 transaction_target_id: "-1",
                 transaction_target_type: :system
               }) do
            {:ok, _stock} ->
              Logger.debug("创建股票持仓: 用户 #{user_id}, 动物 #{racer_id}, 数量 #{quantity}")

            {:error, error} ->
              Logger.error("创建股票持仓失败: #{inspect(error)}")
              Repo.rollback("创建股票持仓失败")
          end
        end
      end)

      Logger.info("股票总持仓重新统计完成")
      :ok
    end)
  end

  @doc """
  获取股票持仓统计摘要
  """
  def get_stock_holdings_summary do
    total_holdings = Stock |> Ash.read!() |> length()
    total_users = Stock |> Ash.read!() |> Enum.map(& &1.user_id) |> Enum.uniq() |> length()
    total_quantity = Stock |> Ash.read!() |> Enum.reduce(0, &(&1.quantity + &2))

    %{
      total_holdings: total_holdings,
      total_users: total_users,
      total_quantity: total_quantity
    }
  end

  @doc """
  检查股票价格并在低于阈值时触发强制平仓
  此函数在比赛结束时被调用，而不是定时检测
  """
  def check_and_trigger_force_liquidation(current_race \\ nil) do
    race_to_check = current_race || RacingGame.RaceController.get_current_race()

    case race_to_check do
      nil ->
        Logger.info("当前没有比赛进行，跳过价格检查")
        {:ok, :no_race}

      race ->
        bet_amount_map = race.bet_amount_map
        Logger.info("当前股票价格: #{inspect(bet_amount_map)}")

        # 检查是否有股票价格低于阈值
        low_price_stocks =
          bet_amount_map
          |> Enum.filter(fn {_racer_id, price} -> price < @force_liquidation_threshold end)

        if length(low_price_stocks) > 0 do
          Logger.warning("发现低价股票，触发强制平仓: #{inspect(low_price_stocks)}")

          case trigger_force_liquidation_for_low_prices(low_price_stocks, race) do
            {:ok, result} ->
              Logger.info("低价股票强制平仓完成: #{inspect(result)}")
              {:ok, result}

            {:error, reason} ->
              Logger.error("低价股票强制平仓失败: #{inspect(reason)}")
              {:error, reason}
          end
        else
          Logger.info("所有股票价格正常，无需强制平仓")
          {:ok, :no_action_needed}
        end
    end
  end

  @doc """
  强制平仓所有用户股票（管理员操作）
  """
  def force_liquidate_all_stocks(current_race \\ nil) do
    race_to_use = current_race || RacingGame.RaceController.get_current_race()

    if race_to_use do
      Logger.info("开始强制平仓所有用户股票...")

      Repo.transaction(fn ->
        # 获取所有股票持仓
        all_stocks = Stock |> Ash.read!()

        liquidation_result = %{
          total_users: 0,
          total_stocks_liquidated: 0,
          total_points_returned: 0,
          failed_liquidations: [],
          trigger_reason: "管理员手动强制平仓"
        }

        # 按用户分组处理
        stocks_by_user = Enum.group_by(all_stocks, & &1.user_id)

        result =
          Enum.reduce(stocks_by_user, liquidation_result, fn {user_id, user_stocks}, acc ->
            case liquidate_user_all_stocks(user_id, user_stocks, race_to_use) do
              {:ok, %{stocks_count: stocks_count, points_returned: points_returned}} ->
                %{
                  acc
                  | total_users: acc.total_users + 1,
                    total_stocks_liquidated: acc.total_stocks_liquidated + stocks_count,
                    total_points_returned: acc.total_points_returned + points_returned
                }

              {:error, reason} ->
                %{acc | failed_liquidations: [{user_id, reason} | acc.failed_liquidations]}
            end
          end)

        Logger.info("强制平仓完成: #{inspect(result)}")
        result
      end)
    else
      {:error, "当前没有进行中的比赛"}
    end
  end

  # 清算用户的所有股票（管理员强制平仓）
  defp liquidate_user_all_stocks(user_id, user_stocks, current_race) do
    if Enum.empty?(user_stocks) do
      {:ok, %{stocks_count: 0, points_returned: 0}}
    else
      Repo.transaction(fn ->
        # 获取用户强制平仓前的积分余额
        user_balance_before = Cypridina.Accounts.get_user_points(user_id)

        # 计算总价值并清空股票
        {total_points, total_stocks, liquidated_stocks} =
          Enum.reduce(user_stocks, {0, 0, []}, fn stock,
                                                  {acc_points, acc_stocks, acc_liquidated} ->
            if stock.quantity > 0 do
              # 获取当前价格
              current_price = Map.get(current_race.bet_amount_map, stock.racer_id, 160)
              stock_value = current_price * stock.quantity

              # 清空股票持仓
              case Stock.subtract!(%{
                     user_id: user_id,
                     racer_id: stock.racer_id,
                     amount: stock.quantity,
                     # 系统操作
                     transaction_target_id: "-1",
                     transaction_target_type: :system
                   }) do
                %{quantity: _remaining} ->
                  liquidated_stock = %{
                    racer_id: stock.racer_id,
                    quantity: stock.quantity,
                    value: stock_value,
                    price: current_price,
                    original_cost: stock.total_cost
                  }

                  Logger.info(
                    "强制平仓股票详情: 用户#{user_id} 卖出#{stock.racer_id}股票#{stock.quantity}股，单价#{current_price}，总价值#{stock_value}，原始成本#{stock.total_cost}"
                  )

                  {acc_points + stock_value, acc_stocks + stock.quantity,
                   [liquidated_stock | acc_liquidated]}

                error ->
                  Logger.error("股票持仓减少失败: #{inspect(error)}")
                  Repo.rollback("股票持仓减少失败")
              end
            else
              {acc_points, acc_stocks, acc_liquidated}
            end
          end)

        # 如果有股票被清算，返还积分
        if total_stocks > 0 do
          # 计算强制平仓后的身价
          user_balance_after = user_balance_before + total_points

          # 构建详细的强制平仓描述
          stock_details =
            Enum.map_join(liquidated_stocks, "，", fn stock ->
              racer_name = get_racer_name(stock.racer_id)
              "#{racer_name}#{stock.quantity}股@#{stock.price}"
            end)

          description = "管理员强制平仓：#{stock_details}"

          case Cypridina.Accounts.add_points(user_id, total_points,
                 transaction_type: :force_liquidation,
                 description: description,
                 metadata: %{
                   trigger_reason: "管理员手动强制平仓",
                   liquidated_stocks: liquidated_stocks,
                   race_issue: current_race.issue,
                   balance_before: user_balance_before,
                   balance_after: user_balance_after,
                   total_stock_value: total_points,
                   total_stocks_count: total_stocks,
                   racing_game: true
                 },
                 transaction_target_id: "-1",
                 transaction_target_type: :system
               ) do
            {:ok, new_points} ->
              Logger.info(
                "强制平仓完成: 用户#{user_id} 清算#{total_stocks}份股票，返还#{total_points}积分，身价从#{user_balance_before}变为#{new_points}"
              )

              Logger.info("强制平仓详情: #{stock_details}")
              %{stocks_count: total_stocks, points_returned: total_points}

            {:error, reason} ->
              Logger.error("积分返还失败: #{inspect(reason)}")
              Repo.rollback("积分返还失败: #{inspect(reason)}")
          end
        else
          # 没有股票需要清算
          %{stocks_count: 0, points_returned: 0}
        end
      end)
    end
  end

  @doc """
  为低价股票触发强制平仓
  """
  def trigger_force_liquidation_for_low_prices(low_price_stocks, current_race) do
    Logger.info("开始为低价股票执行强制平仓...")

    Repo.transaction(fn ->
      # 获取所有受影响的股票持仓
      affected_racer_ids = Enum.map(low_price_stocks, fn {racer_id, _price} -> racer_id end)

      affected_stocks =
        Stock
        |> Ash.Query.filter(racer_id in ^affected_racer_ids)
        |> Ash.Query.filter(quantity > 0)
        |> Ash.read!()

      Logger.info("找到 #{length(affected_stocks)} 个受影响的股票持仓")

      liquidation_result = %{
        total_users: 0,
        total_stocks_liquidated: 0,
        total_points_returned: 0,
        failed_liquidations: [],
        low_price_stocks: low_price_stocks,
        trigger_reason: "股票价格低于#{@force_liquidation_threshold}"
      }

      # 按用户分组处理
      stocks_by_user = Enum.group_by(affected_stocks, & &1.user_id)

      result =
        Enum.reduce(stocks_by_user, liquidation_result, fn {user_id, user_stocks}, acc ->
          case liquidate_user_low_price_stocks(
                 user_id,
                 user_stocks,
                 current_race,
                 low_price_stocks
               ) do
            {:ok, %{stocks_count: stocks_count, points_returned: points_returned}} ->
              %{
                acc
                | total_users: acc.total_users + 1,
                  total_stocks_liquidated: acc.total_stocks_liquidated + stocks_count,
                  total_points_returned: acc.total_points_returned + points_returned
              }

            {:error, reason} ->
              %{acc | failed_liquidations: [{user_id, reason} | acc.failed_liquidations]}
          end
        end)

      # 广播强制平仓事件
      Phoenix.PubSub.broadcast(
        Cypridina.PubSub,
        RacingGame.RaceController.race_topic(),
        {:game_data_update,
         %{
           event_type: :force_liquidation_completed,
           current_race: current_race,
           # 避免死锁，不获取总下注
           total_bets: %{},
           extra_data: result
         }}
      )

      Logger.info("低价股票强制平仓完成: #{inspect(result)}")
      result
    end)
  end

  # 清算用户的低价股票
  defp liquidate_user_low_price_stocks(user_id, user_stocks, current_race, low_price_stocks) do
    low_price_map = Map.new(low_price_stocks)

    # 只处理低价股票
    low_price_user_stocks =
      Enum.filter(user_stocks, fn stock ->
        Map.has_key?(low_price_map, stock.racer_id)
      end)

    if Enum.empty?(low_price_user_stocks) do
      {:ok, %{stocks_count: 0, points_returned: 0}}
    else
      Repo.transaction(fn ->
        # 获取用户强制平仓前的积分余额
        user_balance_before = Cypridina.Accounts.get_user_points(user_id)
        # 使用 subtract action 清算所有低价股票（确保成本归零）
        liquidated_stock_details =
          Enum.map(low_price_user_stocks, fn stock ->
            case Stock.subtract!(%{
                   user_id: user_id,
                   racer_id: stock.racer_id,
                   amount: stock.quantity,
                   # 系统操作
                   transaction_target_id: "-1",
                   transaction_target_type: :system
                 }) do
              %{quantity: _remaining} ->
                current_price =
                  Map.get(
                    current_race.bet_amount_map,
                    stock.racer_id,
                    @force_liquidation_threshold
                  )

                stock_value = current_price * stock.quantity

                Logger.info(
                  "低价股票清算: 用户#{user_id} 卖出#{stock.racer_id}股票#{stock.quantity}股，单价#{current_price}，总价值#{stock_value}，原始成本#{stock.total_cost}"
                )

                %{
                  racer_id: stock.racer_id,
                  quantity: stock.quantity,
                  price: current_price,
                  value: stock_value,
                  original_cost: stock.total_cost
                }

              error ->
                Logger.error("低价股票清算失败: #{inspect(error)}")
                Repo.rollback("低价股票清算失败")
            end
          end)

        # 计算返还积分
        {total_points, total_stocks} =
          Enum.reduce(liquidated_stock_details, {0, 0}, fn stock_detail,
                                                           {acc_points, acc_stocks} ->
            {acc_points + stock_detail.value, acc_stocks + stock_detail.quantity}
          end)

        if total_points > 0 do
          # 计算强制平仓后的身价
          user_balance_after = user_balance_before + total_points

          # 构建详细的强制平仓描述
          stock_details =
            Enum.map_join(liquidated_stock_details, "，", fn stock ->
              racer_name = get_racer_name(stock.racer_id)
              "#{racer_name}#{stock.quantity}股@#{stock.price}"
            end)

          description = "低价股票强制平仓：#{stock_details}"

          # 返还积分给用户并记录强制平仓的积分变动
          case Cypridina.Accounts.add_points(user_id, total_points,
                 transaction_type: :force_liquidation,
                 description: description,
                 metadata: %{
                   liquidated_stocks: liquidated_stock_details,
                   race_issue: current_race.issue,
                   trigger_reason: "股票价格低于#{@force_liquidation_threshold}",
                   balance_before: user_balance_before,
                   balance_after: user_balance_after,
                   total_stock_value: total_points,
                   total_stocks_count: total_stocks,
                   racing_game: true
                 },
                 transaction_target_id: "-1",
                 transaction_target_type: :system
               ) do
            {:ok, new_points} ->
              Logger.info(
                "低价股票强制平仓完成: 用户#{user_id} 清算#{total_stocks}份股票，返还#{total_points}积分，身价从#{user_balance_before}变为#{new_points}"
              )

              Logger.info("低价股票强制平仓详情: #{stock_details}")
              %{stocks_count: total_stocks, points_returned: total_points}

            {:error, reason} ->
              Logger.error("积分返还失败: #{inspect(reason)}")
              Repo.rollback("积分返还失败: #{inspect(reason)}")
          end
        else
          %{stocks_count: total_stocks, points_returned: 0}
        end
      end)
    end
  end

  # 获取选手名称的辅助函数
  defp get_racer_name(racer_id) do
    case racer_id do
      "A" -> "饿小宝"
      "B" -> "盒马"
      "C" -> "票票"
      "D" -> "虾仔"
      "E" -> "支小宝"
      "F" -> "欢猩"
      _ -> racer_id
    end
  end
end
