defmodule RacingGame.Live.AdminPanel.StockHoldingsComponent do
  @moduledoc """
  股票持仓组件 - 仅限管理员使用
  """
  use CypridinaWeb, :live_component

  alias CypridinaWeb.AuthHelper
  alias RacingGame.Stock
  alias RacingGame.Live.AdminPanel.PermissionFilter
  alias Cypridina.Utils.TimeHelper

  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign_defaults()
      |> load_stocks_data()

    {:ok, socket}
  end

  defp assign_defaults(socket) do
    socket
    |> assign(:search_query, "")
    |> assign(:page, 1)
    |> assign(:per_page, 20)
    |> assign(:page_info, nil)
  end

  def handle_event("refresh", _params, socket) do
    socket = load_stocks_data(socket)
    {:noreply, socket}
  end

  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    socket =
      socket
      |> assign(:search_query, query)
      |> assign(:page, 1)
      |> load_stocks_data()

    {:noreply, socket}
  end

  def handle_event("clear_search", _params, socket) do
    socket =
      socket
      |> assign(:search_query, "")
      |> assign(:page, 1)
      |> load_stocks_data()

    {:noreply, socket}
  end

  def handle_event("page_change", %{"page" => page}, socket) do
    page = String.to_integer(page)

    socket =
      socket
      |> assign(:page, page)
      |> load_stocks_data()

    {:noreply, socket}
  end

  defp load_stocks_data(socket) do
    user = socket.assigns.current_user
    search_query = socket.assigns.search_query || ""
    page = socket.assigns.page
    per_page = socket.assigns.per_page

    # 构建基础查询
    query =
      Stock
      |> Ash.Query.load([:user])

    # 应用权限过滤
    query = PermissionFilter.apply_user_filter(query, user, :user_id)

    # 添加搜索条件
    query =
      if search_query != "" do
        # 对于 Stock，我们需要通过关联的用户名进行搜索
        # 先获取匹配的用户ID，然后过滤股票记录
        import Ash.Query

        matching_user_ids =
          case Cypridina.Accounts.User
               |> filter(contains(username, ^search_query))
               |> select([:id])
               |> Ash.read() do
            {:ok, users} -> Enum.map(users, & &1.id)
            _ -> []
          end

        if length(matching_user_ids) > 0 do
          filter(query, user_id in ^matching_user_ids)
        else
          # 如果没有匹配的用户，返回空结果
          filter(query, user_id == "no-match")
        end
      else
        query
      end

    # 使用 Ash.Query.page 进行分页查询并按更新时间倒序排列
    case query
         |> Ash.Query.sort(updated_at: :desc)
         |> Ash.Query.page(count: true, limit: per_page, offset: (page - 1) * per_page)
         |> Ash.read() do
      {:ok, %Ash.Page.Offset{results: stocks, count: total_count}} ->
        socket
        |> assign(:stocks_data, stocks)
        |> assign(:page_info, %{total_count: total_count, page: page, per_page: per_page})

      {:error, error} ->
        require Logger
        Logger.error("Failed to load stock holdings data: #{inspect(error)}")

        socket
        |> assign(:stocks_data, [])
        |> assign(:page_info, %{total_count: 0, page: 1, per_page: per_page})
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">股票持仓</h2>
        <div class="text-sm text-base-content/60">
          <%= if @page_info do %>
            共 {@page_info.total_count} 条记录，第 {@page_info.page} 页
          <% else %>
            加载中...
          <% end %>
        </div>
      </div>
      
    <!-- 搜索框 -->
      <div class="flex justify-between items-center mb-4">
        <form phx-submit="search" phx-target={@myself} class="flex space-x-2">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder="按用户名搜索..."
            class="input input-bordered input-sm w-64"
          />
          <button type="submit" class="btn btn-outline btn-sm">
            <.icon name="hero-magnifying-glass" class="w-4 h-4" /> 搜索
          </button>
        </form>
        <%= if @search_query != "" do %>
          <button phx-click="clear_search" phx-target={@myself} class="btn btn-ghost btn-sm">
            <.icon name="hero-x-mark" class="w-4 h-4" /> 清除搜索
          </button>
        <% end %>
      </div>

      <%= if @stocks_data && length(@stocks_data) > 0 do %>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>用户名</th>
                <th>用户ID</th>
                <th>动物ID</th>
                <th>持仓数量</th>
                <th>总成本</th>
                <th>平均成本</th>
                <th>更新时间</th>
              </tr>
            </thead>
            <tbody>
              <%= for stock <- @stocks_data do %>
                <% average_cost = calculate_average_cost(stock) %>
                <tr>
                  <td class="font-medium">
                    <%= if stock.user do %>
                      {to_string(stock.user.username)}
                    <% else %>
                      未知用户
                    <% end %>
                  </td>
                  <td class="font-mono text-sm">{String.slice(stock.user_id, 0, 8)}...</td>
                  <td>
                    <span class="badge badge-primary">{stock.racer_id}</span>
                  </td>
                  <td class="font-medium">
                    <span class="badge badge-outline badge-lg">
                      {stock.quantity}
                    </span>
                  </td>
                  <td class="font-medium text-info">
                    <div class="flex items-center space-x-1">
                      <.icon name="hero-currency-dollar" class="w-4 h-4" />
                      <span>{format_decimal(stock.total_cost)}</span>
                    </div>
                  </td>
                  <td class="font-medium text-accent">
                    <%= if average_cost do %>
                      <div class="flex items-center space-x-1">
                        <.icon name="hero-calculator" class="w-4 h-4" />
                        <span>{format_decimal(average_cost)}</span>
                      </div>
                    <% else %>
                      <span class="text-base-content/40">-</span>
                    <% end %>
                  </td>
                  <td>{TimeHelper.format_local_datetime(stock.updated_at)}</td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
    <!-- 分页组件 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex justify-center mt-6">
            <div class="join">
              <%= if @page_info.page > 1 do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page - 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  «
                </button>
              <% end %>

              <%= for page_num <- max(1, @page_info.page - 2)..min(ceil(@page_info.total_count / @page_info.per_page), @page_info.page + 2) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  phx-target={@myself}
                  class={[
                    "join-item btn btn-sm",
                    if(page_num == @page_info.page, do: "btn-active", else: "")
                  ]}
                >
                  {page_num}
                </button>
              <% end %>

              <%= if @page_info.page < ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={@page_info.page + 1}
                  phx-target={@myself}
                  class="join-item btn btn-sm"
                >
                  »
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-12">
          <div class="text-base-content/40 mb-4">
            <.icon name="hero-chart-bar" class="w-16 h-16 mx-auto" />
          </div>
          <p class="text-base-content/60">暂无股票持仓记录</p>
        </div>
      <% end %>
    </div>
    """
  end

  # 计算平均成本
  defp calculate_average_cost(stock) do
    if stock.quantity > 0 and stock.total_cost do
      Decimal.div(stock.total_cost, stock.quantity)
    else
      nil
    end
  end

  # 格式化 Decimal 数字显示
  defp format_decimal(nil), do: "0"

  defp format_decimal(decimal) when is_struct(decimal, Decimal) do
    decimal
    |> Decimal.round(2)
    |> Decimal.to_string()
    |> format_number_with_commas()
  end

  defp format_decimal(number) when is_number(number) do
    number
    |> Decimal.new()
    |> format_decimal()
  end

  defp format_decimal(_), do: "0"

  # 添加千分位分隔符
  defp format_number_with_commas(number_string) do
    [integer_part, decimal_part] =
      case String.split(number_string, ".") do
        [integer] -> [integer, nil]
        [integer, decimal] -> [integer, decimal]
      end

    formatted_integer =
      integer_part
      |> String.reverse()
      |> String.graphemes()
      |> Enum.chunk_every(3)
      |> Enum.map(&Enum.join/1)
      |> Enum.join(",")
      |> String.reverse()

    if decimal_part && decimal_part != "00" do
      "#{formatted_integer}.#{decimal_part}"
    else
      formatted_integer
    end
  end
end
