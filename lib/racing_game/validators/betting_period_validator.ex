defmodule RacingGame.Validators.BettingPeriodValidator do
  use Ash.Resource.Validation

  @impl true
  def validate(changeset, _opts, _context) do
    race_issue = Ash.Changeset.get_attribute(changeset, :race_issue)

    # 使用 Ash.Query 查找比赛
    case RacingGame.Race
         |> Ash.Query.filter(issue == ^race_issue)
         |> Ash.read_one() do
      {:ok, race} when not is_nil(race) ->
        now = DateTime.utc_now()

        # 检查比赛是否在下注阶段
        if race.status != 0 || DateTime.compare(now, race.order_end_time) == :gt do
          Ash.Changeset.add_error(changeset, :race_issue, "下注阶段已结束")
        else
          changeset
        end

      _ ->
        Ash.Changeset.add_error(changeset, :race_issue, "找不到比赛")
    end
  end
end

# filepath: /app/cypridina/lib/cypridina/racing_game/validators/sufficient_points_validator.ex
defmodule RacingGame.Validators.SufficientPointsValidator do
  use Ash.Resource.Validation

  def validate(changeset, _opts) do
    user = Ash.Changeset.get_argument(changeset, :user)
    amount = Ash.Changeset.get_argument(changeset, :amount)

    if user.points >= amount do
      :ok
    else
      {:error, "积分不足，需要 #{amount} 点，当前只有 #{user.points} 点"}
    end
  end
end
