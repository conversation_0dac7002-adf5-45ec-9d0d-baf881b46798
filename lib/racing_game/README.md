#### 设计
请帮我用phoenix和ash framewokr3.0 设计一个小游戏的服务端部分，要求如下
1. 由固定1号饿小宝、2号盒马、3号票票、4号虾仔、5号支小宝、6号欢猩6个选手竞速的时时彩游戏，每3分钟进行一次（2分40秒竞猜+20秒比赛+5秒颁奖）
<!-- 2. 期号参照202504280701 -->
2. 客户端使用以下4个接口向服务端请求数据。
    1. 获取最终结果 /getsettle 返回示例如下: 
    ```json
    {"api":"getsettle","data":{"bonusAmount":0,"bonusStatus":3,"endTime":"16.95,15.46,17.92,16.26,17.15,17.73","extPackageInfo":null,"extPrizeInfo":null,"issue":"202504210440","issueId":"202504210440","lastIssue":"************","lastLotteryCode":"4,2,6,5,3,1","lotteryCode":["A","E","F","D","B","C"],"nextIssue":"202504210441","nextIssueId":"202504210441","nextIssueStatus":0,"nextOrderEndTime":1745243983000,"serverTime":1745243696694,"speed":"5.9,6.47,5.58,6.15,5.83,5.64"},"ret":["SUCCESS::接口调用成功"],"v":""}
    ```
    2. 获取游戏信息 /getinfo 返回示例如下: 
    ```json
    {"api":"getinfo","data":{"betItem":{"1":[{"item":"A","odds":0},{"item":"B","odds":0},{"item":"C","odds":0},{"item":"D","odds":0},{"item":"E","odds":0},{"item":"F","odds":0}]},"betNumber":["20"],"countdown":28,"countdown1":48,"fsdf":"ydh3fpk6","issue":"************","issueDesc":"猜中冠军，限量得现金红包","issueEndTime":1745243640000,"issueId":"************","issueStatus":0,"leaderboard":["E_24","A_18","B_16","F_16","C_14","D_12"],"orderEndTime":1745243623000,"props":{"totalCount":0},"serverTime":1745243582923},"ret":["SUCCESS::接口调用成功"],"v":""}
    ```
    3. 获取最近一场的数据 /getlatestdata 返回示例如下: 
    ```json
    {"api":"getlatestdata","data":{"histories":[{"issue":"************","issueTime":"2025-04-21T21:54:00+08:00","result":"4,2,6,5,3,1"}],"record":[{"issue":"************","issueId":"************","result":["D","B","F","E","C","A"]}],"total":["E_24","A_18","B_16","F_16","C_14","D_12"]},"ret":["SUCCESS::接口调用成功"],"v":""}
    ```
    4. 竞速过程中请求实况的接口 /rank，返回示例如下: 
    ```json
    {"gid":"1","uuid":"1745642576238-7f345225-c63b-4e43-8076-89c11230b99d","data":[{"speed":630,"dist":7631,"idx":1},{"speed":670,"dist":7606,"idx":3},{"speed":620,"dist":7584,"idx":5},{"speed":625,"dist":7506,"idx":6},{"speed":635,"dist":7497,"idx":4},{"speed":615,"dist":7386,"idx":2}]}
    ```
3. 数据模型用mix ash.gen.resource生成，参照如下例子:
```bash
mix ash.gen.resource RacingGame.Race \
--uuid-primary-key id \
--attribute issue:string:required \
--attribute issue_id:string \
--attribute issue_status:integer \
--attribute issue_end_time:utc_datetime_usec \
--attribute order_end_time:utc_datetime_usec \
--timestamps \
--extend postgres

# 比赛模型
mix ash.gen.resource RacingGame.Race \
--uuid-primary-key id \
--attribute issue:string:required \
--attribute issue_id:string \
--attribute positions:array\
--attribute speeds:array \
--attribute end_times:array \
--attribute status:integer \
--attribute start_time:utc_datetime_usec \
--attribute end_time:utc_datetime_usec \
--timestamps \
--extend postgres

# 固定选手模型
mix ash.gen.resource RacingGame.Racer \
--uuid-primary-key id \
--attribute number:integer:required \
--attribute code:string:required \
--attribute name:string:required \
--attribute total_wins:integer \
--attribute current_odds:decimal \
--timestamps \
--extend postgres

# 排行榜模型
mix ash.gen.resource RacingGame.Leaderboard \
--uuid-primary-key id \
--attribute racer_number:integer:required \
--attribute win_count:integer \
--timestamps \
--extend postgres
```

mix ash.gen.resource RacingGame.racers \
  --attribute code:string name:string image_url:string:nullable description:string:nullable \
  --domain RacingGame

mix ash.gen.resource RaceEntry race_entries \
  --attribute race_id:uuid racer_id:uuid position:integer finish_time:decimal speed:decimal distance:decimal \
  --domain RacingGame

mix ash.gen.resource Bet bets \
  --attribute race_id:uuid user_id:uuid racer_id:uuid amount:decimal odds:decimal status:integer \
  --domain RacingGame![alt text](image.png)


抽水
就是猜冠军的设千分之一至千分之10
股票的设千分之一至千分之13

g个人信显示x

