defmodule RacingGame.Changes.UpdateExtraData do
  @moduledoc """
  更新extra_data字段的原子操作兼容change
  """
  use Ash.Resource.Change

  @impl true
  def change(changeset, _opts, _context) do
    processed_by = Ash.Changeset.get_argument(changeset, :processed_by)
    reject_reason = Ash.Changeset.get_argument(changeset, :reject_reason)
    extra_data = Ash.Changeset.get_attribute(changeset, :extra_data) || %{}

    new_extra_data =
      Map.merge(extra_data, %{
        "processed_by" => processed_by,
        "processed_at" => DateTime.utc_now()
      })

    # 如果有拒绝原因，添加到extra_data中
    new_extra_data =
      if reject_reason do
        Map.put(new_extra_data, "reject_reason", reject_reason)
      else
        new_extra_data
      end

    Ash.Changeset.change_attribute(changeset, :extra_data, new_extra_data)
  end

  @impl true
  def atomic(_changeset, _opts, _context) do
    # 对于原子操作，我们返回:not_atomic来表示这个change不支持原子操作
    # 这会让Ash回退到非原子操作
    :not_atomic
  end
end
