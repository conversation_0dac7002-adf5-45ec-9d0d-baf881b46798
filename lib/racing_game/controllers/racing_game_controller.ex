defmodule CypridinaWeb.RacingGameController do
  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, :controller

  alias <PERSON>Game.RaceController
  require Logger

  def getsettle(conn, _params) do
    result = RaceController.get_current_race_result()

    response = %{
      api: "getsettle",
      data: result,
      ret: ["SUCCESS::接口调用成功"],
      v: ""
    }

    json(conn, response)
  end

  def getinfo(conn, _params) do
    case RaceController.get_current_game_info() do
      {:ok, info} ->
        Logger.info("获取当前比赛信息成功: #{inspect(info)}")

        response = %{
          api: "getinfo",
          data: info,
          ret: ["SUCCESS::接口调用成功"],
          v: ""
        }

        json(conn, response)

      {:error, :no_race} ->
        # 没有比赛时返回 HTTP 状态码 300，表示服务器维护中
        response = %{
          api: "getinfo",
          data: %{},
          ret: ["MAINTENANCE::服务器维护中"],
          v: ""
        }

        conn
        |> put_status(404)
        |> json(response)
    end
  end

  def getlatestdata(conn, _params) do
    data = RaceController.get_latest_race_data()

    response = %{
      api: "getlatestdata",
      data: data,
      ret: ["SUCCESS::接口调用成功"],
      v: ""
    }

    json(conn, response)
  end

  def rank(conn, params) do
    Logger.info("rank #{inspect(params)}")

    status = RaceController.get_current_race_status()
    json(conn, status)
  end
end
