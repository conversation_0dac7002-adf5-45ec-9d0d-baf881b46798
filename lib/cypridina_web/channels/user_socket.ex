defmodule CypridinaWeb.UserSocket do
  @moduledoc """
  用户WebSocket连接处理

  处理用户的WebSocket连接，包括：
  - 用户认证
  - 聊天频道连接
  - 在线状态管理
  """

  use Phoenix.Socket

  # 聊天频道
  channel "chat_session:*", CypridinaWeb.ChatChannel
  channel "user_chat:*", CypridinaWeb.ChatChannel

  # 通用聊天系统频道
  channel "chat:*", Teen.Channels.ChatChannel

  @impl true
  def connect(%{"token" => token}, socket, _connect_info) do
    # 通过token验证用户身份
    case verify_user_token(token) do
      {:ok, user_id} ->
        socket = assign(socket, :user_id, user_id)
        {:ok, socket}

      {:error, _reason} ->
        :error
    end
  end

  def connect(_params, _socket, _connect_info) do
    :error
  end

  @impl true
  def id(socket) do
    "user_socket:#{socket.assigns.user_id}"
  end

  # 私有函数

  defp verify_user_token(token) do
    # 这里需要实现token验证逻辑
    # 可以使用Phoenix.Token或JWT等方式
    case Phoenix.Token.verify(CypridinaWeb.Endpoint, "user_socket", token, max_age: 86400) do
      {:ok, user_id} -> {:ok, user_id}
      {:error, _reason} -> {:error, :invalid_token}
    end
  end
end
