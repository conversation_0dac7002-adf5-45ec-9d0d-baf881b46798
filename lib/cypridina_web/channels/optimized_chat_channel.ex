defmodule CypridinaWeb.OptimizedChatChannel do
  @moduledoc """
  优化的聊天频道模块

  提供高性能的实时聊天功能，包括：
  - 连接池管理
  - 消息批处理
  - 错误恢复
  - 性能监控
  """

  use CypridinaWeb, :channel

  alias Cypridina.Chat.{ChatService, MessageHandler, <PERSON>t<PERSON>og<PERSON>, ErrorHandler}
  alias Phoenix.PubSub

  @impl true
  def join("chat_session:" <> session_id, payload, socket) do
    user_id = socket.assigns.user_id
    game_type = Map.get(payload, "game_type", "system") |> String.to_atom()

    ChatLogger.log_user_event(
      :joining_session,
      %{
        id: user_id,
        session_id: session_id
      },
      game_type: game_type
    )

    with {:ok, session} <- verify_and_get_session(session_id, user_id),
         {:ok, socket} <- setup_socket(socket, session_id, session, game_type),
         :ok <- subscribe_to_events(session_id),
         :ok <- broadcast_user_presence(socket, :join) do
      ChatLogger.log_user_event(
        :joined_session,
        %{
          id: user_id,
          session_id: session_id
        },
        game_type: game_type
      )

      {:ok, %{session: format_session(session)}, socket}
    else
      {:error, reason} ->
        error_response =
          ErrorHandler.handle_session_error({:error, reason}, %{
            session_id: session_id,
            user_id: user_id,
            game_type: game_type
          })

        {:error, error_response}
    end
  end

  @impl true
  def handle_in("send_message", %{"content" => content} = payload, socket) do
    %{session_id: session_id, user_id: user_id, game_type: game_type} = socket.assigns

    message_opts = build_message_options(payload, game_type)

    with {:ok, message} <- ChatService.send_message(session_id, user_id, content, message_opts),
         {:ok, enriched_message} <- enrich_message(message),
         :ok <- broadcast_message(socket, enriched_message),
         :ok <- handle_post_send_tasks(message) do
      {:reply, {:ok, %{message: format_message(enriched_message)}}, socket}
    else
      {:error, reason} ->
        error_response =
          ErrorHandler.handle_message_error({:error, reason}, %{
            session_id: session_id,
            user_id: user_id,
            game_type: game_type
          })

        {:reply, {:error, error_response}, socket}
    end
  end

  @impl true
  def handle_in("send_file", %{"file_data" => file_data} = payload, socket) do
    %{session_id: session_id, user_id: user_id, game_type: game_type} = socket.assigns

    with {:ok, file_info} <- process_file_upload(file_data, session_id, user_id),
         {:ok, message} <- send_file_message(session_id, user_id, file_info, payload),
         {:ok, enriched_message} <- enrich_message(message),
         :ok <- broadcast_message(socket, enriched_message) do
      {:reply, {:ok, %{message: format_message(enriched_message)}}, socket}
    else
      {:error, reason} ->
        error_response =
          ErrorHandler.handle_file_error({:error, reason}, %{
            session_id: session_id,
            user_id: user_id,
            game_type: game_type
          })

        {:reply, {:error, error_response}, socket}
    end
  end

  @impl true
  def handle_in("mark_messages_read", %{"message_ids" => message_ids}, socket) do
    %{session_id: session_id, user_id: user_id, game_type: game_type} = socket.assigns

    with {:ok, _receipts} <-
           MessageHandler.mark_messages_read_batch(session_id, user_id, message_ids),
         :ok <- broadcast_read_status(socket, user_id, message_ids) do
      {:reply, {:ok, %{marked_count: length(message_ids)}}, socket}
    else
      {:error, reason} ->
        error_response =
          ErrorHandler.handle_message_error({:error, reason}, %{
            session_id: session_id,
            user_id: user_id,
            game_type: game_type
          })

        {:reply, {:error, error_response}, socket}
    end
  end

  @impl true
  def handle_in("get_messages", payload, socket) do
    %{session_id: session_id, user_id: user_id, game_type: game_type} = socket.assigns

    opts = build_pagination_options(payload)

    with {:ok, messages} <- MessageHandler.get_session_messages(session_id, user_id, opts) do
      formatted_messages = Enum.map(messages, &format_message/1)
      {:reply, {:ok, %{messages: formatted_messages}}, socket}
    else
      {:error, reason} ->
        error_response =
          ErrorHandler.handle_message_error({:error, reason}, %{
            session_id: session_id,
            user_id: user_id,
            game_type: game_type
          })

        {:reply, {:error, error_response}, socket}
    end
  end

  @impl true
  def handle_in("typing", %{"typing" => typing}, socket) do
    %{session_id: session_id, user_id: user_id} = socket.assigns

    broadcast_from!(socket, "user_typing", %{
      user_id: user_id,
      typing: typing,
      timestamp: DateTime.utc_now()
    })

    {:noreply, socket}
  end

  @impl true
  def handle_info({:new_message, message}, socket) do
    if message.sender_id != socket.assigns.user_id do
      push(socket, "new_message", %{message: format_message(message)})
    end

    {:noreply, socket}
  end

  @impl true
  def handle_info({:messages_read, %{user_id: user_id, message_ids: message_ids}}, socket) do
    if user_id != socket.assigns.user_id do
      push(socket, "messages_read", %{
        user_id: user_id,
        message_ids: message_ids,
        timestamp: DateTime.utc_now()
      })
    end

    {:noreply, socket}
  end

  @impl true
  def handle_info({:user_joined, user_data}, socket) do
    push(socket, "user_joined", %{user: format_user(user_data)})
    {:noreply, socket}
  end

  @impl true
  def handle_info({:user_left, user_data}, socket) do
    push(socket, "user_left", %{user: format_user(user_data)})
    {:noreply, socket}
  end

  @impl true
  def terminate(reason, socket) do
    %{session_id: session_id, user_id: user_id, game_type: game_type} = socket.assigns

    ChatLogger.log_user_event(
      :left_session,
      %{
        id: user_id,
        session_id: session_id
      },
      game_type: game_type
    )

    broadcast_user_presence(socket, :leave)
    :ok
  end

  # 私有函数

  defp verify_and_get_session(session_id, user_id) do
    with {:ok, session} <- ChatService.get_session(session_id),
         {:ok, _participant} <- ChatService.verify_participant_in_session(session_id, user_id) do
      {:ok, session}
    end
  end

  defp setup_socket(socket, session_id, session, game_type) do
    socket =
      socket
      |> assign(:session_id, session_id)
      |> assign(:session, session)
      |> assign(:game_type, game_type)
      |> assign(:joined_at, DateTime.utc_now())

    {:ok, socket}
  end

  defp subscribe_to_events(session_id) do
    Phoenix.PubSub.subscribe(Cypridina.PubSub, "chat_session:#{session_id}")
    :ok
  end

  defp broadcast_user_presence(socket, action) do
    %{session_id: session_id, user_id: user_id} = socket.assigns

    broadcast_from!(socket, "user_presence", %{
      user_id: user_id,
      action: action,
      timestamp: DateTime.utc_now()
    })

    :ok
  end

  defp build_message_options(payload, game_type) do
    [
      message_type: Map.get(payload, "message_type", "text") |> String.to_atom(),
      reply_to_id: Map.get(payload, "reply_to_id"),
      game_type: game_type
    ]
  end

  defp enrich_message(message) do
    case Ash.load(message, [:sender, :reply_to]) do
      {:ok, enriched} -> {:ok, enriched}
      {:error, reason} -> {:error, reason}
    end
  end

  defp broadcast_message(socket, message) do
    broadcast!(socket, "new_message", %{message: format_message(message)})
    :ok
  end

  defp handle_post_send_tasks(message) do
    MessageHandler.handle_message_sent_async(message)
    :ok
  end

  defp process_file_upload(file_data, session_id, user_id) do
    # 处理文件上传逻辑
    # 这里可以支持Base64、预签名URL等多种上传方式
    # 占位符
    {:ok, %{}}
  end

  defp send_file_message(session_id, user_id, file_info, payload) do
    caption = Map.get(payload, "caption", "")
    reply_to_id = Map.get(payload, "reply_to_id")

    ChatService.send_file_message(session_id, user_id, file_info,
      caption: caption,
      reply_to_id: reply_to_id
    )
  end

  defp broadcast_read_status(socket, user_id, message_ids) do
    broadcast_from!(socket, "messages_read", %{
      user_id: user_id,
      message_ids: message_ids,
      timestamp: DateTime.utc_now()
    })

    :ok
  end

  defp build_pagination_options(payload) do
    [
      limit: Map.get(payload, "limit", 50),
      offset: Map.get(payload, "offset", 0),
      before_message_id: Map.get(payload, "before_message_id")
    ]
  end

  defp format_session(session) do
    %{
      id: session.id,
      type: session.session_type,
      title: session.title,
      participant_count: session.participant_count,
      created_at: session.inserted_at
    }
  end

  defp format_message(message) do
    %{
      id: message.id,
      content: message.content,
      type: message.message_type,
      sender: format_user(message.sender),
      reply_to: if(message.reply_to, do: format_message(message.reply_to)),
      attachments: message.attachments || [],
      created_at: message.inserted_at,
      status: message.status
    }
  end

  defp format_user(user) when is_nil(user), do: nil

  defp format_user(user) do
    %{
      id: user.id,
      nickname: user.nickname,
      avatar: user.avatar
    }
  end
end
