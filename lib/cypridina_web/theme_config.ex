defmodule CypridinaWeb.ThemeConfig do
  @moduledoc """
  主题配置模块
  统一管理所有可用主题的配置信息
  """

  @themes [
    # 基础主题
    %{
      name: "light",
      label: "浅色",
      icon: "☀️",
      description: "明亮清爽的浅色主题",
      category: :basic,
      is_dark: false
    },
    %{
      name: "dark",
      label: "深色",
      icon: "🌙",
      description: "护眼舒适的深色主题",
      category: :basic,
      is_dark: true
    },

    # 自定义主题
    %{
      name: "modern-blue",
      label: "现代蓝",
      icon: "💙",
      description: "现代感十足的蓝色主题",
      category: :custom,
      is_dark: false
    },
    %{
      name: "purple-dark",
      label: "紫色暗",
      icon: "💜",
      description: "神秘优雅的紫色暗主题",
      category: :custom,
      is_dark: true
    },
    %{
      name: "nature-green",
      label: "自然绿",
      icon: "🌿",
      description: "清新自然的绿色主题",
      category: :custom,
      is_dark: false
    },
    %{
      name: "warm-orange",
      label: "温暖橙",
      icon: "🧡",
      description: "温暖活力的橙色主题",
      category: :custom,
      is_dark: false
    },
    %{
      name: "minimal-mono",
      label: "极简",
      icon: "⚫",
      description: "简约纯净的黑白主题",
      category: :custom,
      is_dark: false
    },

    # DaisyUI 内置主题
    %{
      name: "cupcake",
      label: "纸杯蛋糕",
      icon: "🧁",
      description: "甜美可爱的粉色主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "cyberpunk",
      label: "赛博朋克",
      icon: "🤖",
      description: "未来科技感主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "forest",
      label: "森林",
      icon: "🌲",
      description: "深邃宁静的森林主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "luxury",
      label: "奢华",
      icon: "💎",
      description: "高端奢华的金色主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "business",
      label: "商务",
      icon: "💼",
      description: "专业稳重的商务主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "emerald",
      label: "翡翠",
      icon: "💚",
      description: "清雅的翡翠绿主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "corporate",
      label: "企业",
      icon: "🏢",
      description: "正式的企业级主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "synthwave",
      label: "合成波",
      icon: "🌆",
      description: "80年代复古未来主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "retro",
      label: "复古",
      icon: "📻",
      description: "怀旧复古风格主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "valentine",
      label: "情人节",
      icon: "💕",
      description: "浪漫温馨的粉红主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "garden",
      label: "花园",
      icon: "🌺",
      description: "花园般清新的主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "aqua",
      label: "水蓝",
      icon: "🌊",
      description: "清澈的水蓝色主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "lofi",
      label: "Lo-Fi",
      icon: "🎵",
      description: "舒缓放松的音乐主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "pastel",
      label: "粉彩",
      icon: "🎨",
      description: "柔和的粉彩色主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "fantasy",
      label: "幻想",
      icon: "🦄",
      description: "梦幻奇妙的幻想主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "wireframe",
      label: "线框",
      icon: "📐",
      description: "简洁的线框设计主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "black",
      label: "纯黑",
      icon: "⚫",
      description: "极致简约的纯黑主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "dracula",
      label: "德古拉",
      icon: "🧛",
      description: "神秘的德古拉主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "cmyk",
      label: "CMYK",
      icon: "🖨️",
      description: "印刷风格的CMYK主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "autumn",
      label: "秋天",
      icon: "🍂",
      description: "温暖的秋日主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "acid",
      label: "酸性",
      icon: "🟢",
      description: "鲜艳的酸性绿主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "lemonade",
      label: "柠檬水",
      icon: "🍋",
      description: "清爽的柠檬黄主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "night",
      label: "夜晚",
      icon: "🌃",
      description: "深邃的夜晚主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "coffee",
      label: "咖啡",
      icon: "☕",
      description: "温暖的咖啡色主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "winter",
      label: "冬天",
      icon: "❄️",
      description: "清冷的冬日主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "dim",
      label: "昏暗",
      icon: "🔅",
      description: "柔和的昏暗主题",
      category: :daisyui,
      is_dark: true
    },
    %{
      name: "nord",
      label: "北欧",
      icon: "🏔️",
      description: "简约的北欧风主题",
      category: :daisyui,
      is_dark: false
    },
    %{
      name: "sunset",
      label: "日落",
      icon: "🌅",
      description: "温暖的日落主题",
      category: :daisyui,
      is_dark: false
    }
  ]

  @doc """
  获取所有可用主题
  """
  def get_all_themes, do: @themes

  @doc """
  获取主题信息
  """
  def get_theme(name) do
    Enum.find(@themes, &(&1.name == name))
  end

  @doc """
  检查主题是否存在
  """
  def theme_exists?(name) do
    Enum.any?(@themes, &(&1.name == name))
  end

  @doc """
  按分类获取主题
  """
  def get_themes_by_category(category) do
    Enum.filter(@themes, &(&1.category == category))
  end

  @doc """
  获取深色主题
  """
  def get_dark_themes do
    Enum.filter(@themes, &(&1.is_dark == true))
  end

  @doc """
  获取浅色主题
  """
  def get_light_themes do
    Enum.filter(@themes, &(&1.is_dark == false))
  end

  @doc """
  格式化为 Backpex 主题选择器格式
  """
  def format_for_backpex_selector(themes \\ @themes) do
    Enum.map(themes, fn theme ->
      {"#{theme.icon} #{theme.label}", theme.name}
    end)
  end

  @doc """
  获取主题统计信息
  """
  def get_theme_stats do
    %{
      total: length(@themes),
      basic: length(get_themes_by_category(:basic)),
      custom: length(get_themes_by_category(:custom)),
      daisyui: length(get_themes_by_category(:daisyui)),
      dark: length(get_dark_themes()),
      light: length(get_light_themes())
    }
  end

  @doc """
  获取推荐主题（常用主题）
  """
  def get_recommended_themes do
    recommended_names = ["light", "dark", "modern-blue", "cyberpunk", "forest", "business"]
    Enum.filter(@themes, &(&1.name in recommended_names))
  end
end
