defmodule CypridinaWeb.Plugs.AdminAuthPlug do
  @moduledoc """
  后台管理权限控制插件

  限制只有代理和管理员才能访问 backpex_admin 后台
  使用统一的 AuthHelper 模块进行权限检查
  """

  import Plug.Conn
  import Phoenix.Controller

  alias CypridinaWeb.AuthHelper
  require Logger

  def init(opts), do: opts

  def call(conn, _opts) do
    case AuthHelper.check_admin_or_agent_access(conn) do
      {:ok, user, role} ->
        # 所有已登录用户都可以访问，继续处理
        conn
        |> assign(:current_user, user)
        |> assign(:user_role, role)

      {:error, :not_authenticated} ->
        # 未登录，重定向到登录页面
        Logger.info("User not authenticated, redirecting to sign-in")

        conn
        |> put_flash(:error, "请先登录")
        |> redirect(to: "/sign-in")
        |> halt()
    end
  end
end
