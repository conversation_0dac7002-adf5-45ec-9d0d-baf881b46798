defmodule CypridinaWeb.Components.ThemeSwitcher do
  @moduledoc """
  主题切换器组件
  提供多种主题选择和切换功能
  """

  use Phoenix.Component
  import CypridinaWeb.CoreComponents

  @themes [
    %{name: "light", label: "浅色", icon: "☀️", description: "明亮清爽的浅色主题"},
    %{name: "dark", label: "深色", icon: "🌙", description: "护眼舒适的深色主题"},
    %{name: "modern-blue", label: "现代蓝", icon: "💙", description: "现代感十足的蓝色主题"},
    %{name: "purple-dark", label: "紫色暗", icon: "💜", description: "神秘优雅的紫色暗主题"},
    %{name: "nature-green", label: "自然绿", icon: "🌿", description: "清新自然的绿色主题"},
    %{name: "warm-orange", label: "温暖橙", icon: "🧡", description: "温暖活力的橙色主题"},
    %{name: "minimal-mono", label: "极简", icon: "⚫", description: "简约纯净的黑白主题"},
    %{name: "cupcake", label: "纸杯蛋糕", icon: "🧁", description: "甜美可爱的粉色主题"},
    %{name: "cyberpunk", label: "赛博朋克", icon: "🤖", description: "未来科技感主题"},
    %{name: "forest", label: "森林", icon: "🌲", description: "深邃宁静的森林主题"},
    %{name: "luxury", label: "奢华", icon: "💎", description: "高端奢华的金色主题"},
    %{name: "business", label: "商务", icon: "💼", description: "专业稳重的商务主题"},
    %{name: "emerald", label: "翡翠", icon: "💚", description: "清雅的翡翠绿主题"},
    %{name: "corporate", label: "企业", icon: "🏢", description: "正式的企业级主题"},
    %{name: "synthwave", label: "合成波", icon: "🌆", description: "80年代复古未来主题"},
    %{name: "retro", label: "复古", icon: "📻", description: "怀旧复古风格主题"},
    %{name: "valentine", label: "情人节", icon: "💕", description: "浪漫温馨的粉红主题"},
    %{name: "garden", label: "花园", icon: "🌺", description: "花园般清新的主题"},
    %{name: "aqua", label: "水蓝", icon: "🌊", description: "清澈的水蓝色主题"},
    %{name: "lofi", label: "Lo-Fi", icon: "🎵", description: "舒缓放松的音乐主题"},
    %{name: "pastel", label: "粉彩", icon: "🎨", description: "柔和的粉彩色主题"},
    %{name: "fantasy", label: "幻想", icon: "🦄", description: "梦幻奇妙的幻想主题"},
    %{name: "wireframe", label: "线框", icon: "📐", description: "简洁的线框设计主题"},
    %{name: "black", label: "纯黑", icon: "⚫", description: "极致简约的纯黑主题"},
    %{name: "dracula", label: "德古拉", icon: "🧛", description: "神秘的德古拉主题"},
    %{name: "cmyk", label: "CMYK", icon: "🖨️", description: "印刷风格的CMYK主题"},
    %{name: "autumn", label: "秋天", icon: "🍂", description: "温暖的秋日主题"},
    %{name: "acid", label: "酸性", icon: "🟢", description: "鲜艳的酸性绿主题"},
    %{name: "lemonade", label: "柠檬水", icon: "🍋", description: "清爽的柠檬黄主题"},
    %{name: "night", label: "夜晚", icon: "🌃", description: "深邃的夜晚主题"},
    %{name: "coffee", label: "咖啡", icon: "☕", description: "温暖的咖啡色主题"},
    %{name: "winter", label: "冬天", icon: "❄️", description: "清冷的冬日主题"},
    %{name: "dim", label: "昏暗", icon: "🔅", description: "柔和的昏暗主题"},
    %{name: "nord", label: "北欧", icon: "🏔️", description: "简约的北欧风主题"},
    %{name: "sunset", label: "日落", icon: "🌅", description: "温暖的日落主题"}
  ]

  @doc """
  渲染主题切换器组件
  """
  attr :current_theme, :string, default: "light"
  attr :class, :string, default: ""
  attr :show_label, :boolean, default: false
  attr :compact, :boolean, default: false

  def theme_switcher(assigns) do
    assigns = assign(assigns, :themes, @themes)

    ~H"""
    <div class={["theme-switcher", @class]}>
      <div class="dropdown dropdown-end">
        <div
          tabindex="0"
          role="button"
          class={[
            "btn",
            if(@compact, do: "btn-sm btn-circle", else: "btn-ghost"),
            "theme-toggle-btn transition-all hover:scale-105"
          ]}
          title="切换主题"
        >
          <span class="theme-icon text-lg">
            {get_theme_icon(@current_theme)}
          </span>
          <%= if @show_label do %>
            <span class="ml-2 hidden sm:inline">
              {get_theme_label(@current_theme)}
            </span>
          <% end %>
        </div>

        <div
          tabindex="0"
          class="dropdown-content z-[1000] p-4 shadow-2xl bg-base-100 rounded-box w-96 max-h-96 overflow-y-auto border border-base-300"
        >
          <div class="mb-3">
            <h3 class="font-semibold text-base-content mb-1">选择主题</h3>
            <p class="text-sm text-base-content/70">个性化您的界面体验</p>
          </div>

          <div class="grid grid-cols-1 gap-1">
            <%= for theme <- @themes do %>
              <div
                class={[
                  "theme-option-item cursor-pointer rounded-lg p-3 transition-all hover:bg-base-200",
                  if(theme.name == @current_theme,
                    do: "bg-primary/10 border border-primary/20",
                    else: ""
                  )
                ]}
                phx-click="change_theme"
                phx-value-theme={theme.name}
              >
                <div class="flex items-center gap-3">
                  <span class="text-lg flex-shrink-0">{theme.icon}</span>
                  <div class="flex-1 min-w-0">
                    <div class={[
                      "font-medium text-sm",
                      if(theme.name == @current_theme, do: "text-primary", else: "text-base-content")
                    ]}>
                      {theme.label}
                    </div>
                    <div class="text-xs text-base-content/60 truncate">
                      {theme.description}
                    </div>
                  </div>
                  <div class="theme-preview flex-shrink-0" data-theme={theme.name}>
                    <div class="flex gap-1">
                      <div class="w-3 h-3 rounded-full bg-primary border border-base-300"></div>
                      <div class="w-3 h-3 rounded-full bg-secondary border border-base-300"></div>
                      <div class="w-3 h-3 rounded-full bg-accent border border-base-300"></div>
                    </div>
                  </div>
                  <%= if theme.name == @current_theme do %>
                    <div class="flex-shrink-0">
                      <.icon name="hero-check" class="w-4 h-4 text-primary" />
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>

          <div class="mt-4 pt-3 border-t border-base-300">
            <div class="flex items-center justify-between text-xs text-base-content/60">
              <span>快捷键: Ctrl+Shift+T</span>
              <span>{length(@themes)} 个主题</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  简化版主题切换按钮
  """
  attr :current_theme, :string, default: "light"
  attr :class, :string, default: ""

  def theme_toggle_button(assigns) do
    ~H"""
    <button
      class={["btn btn-ghost btn-circle theme-toggle-simple", @class]}
      phx-click="toggle_theme"
      title="切换主题"
    >
      <span class="text-lg">
        {get_theme_icon(@current_theme)}
      </span>
    </button>
    """
  end

  @doc """
  主题预览卡片
  """
  attr :theme, :map, required: true
  attr :current_theme, :string, default: "light"
  attr :class, :string, default: ""

  def theme_preview_card(assigns) do
    ~H"""
    <div
      class={[
        "theme-preview-card p-4 rounded-lg border-2 cursor-pointer transition-all hover:scale-105",
        if(@theme.name == @current_theme,
          do: "border-primary bg-primary/5",
          else: "border-base-300 hover:border-primary/50"
        ),
        @class
      ]}
      phx-click="change_theme"
      phx-value-theme={@theme.name}
      data-theme={@theme.name}
    >
      <div class="flex items-center gap-3 mb-3">
        <span class="text-2xl">{@theme.icon}</span>
        <div>
          <h4 class="font-semibold text-base-content">{@theme.label}</h4>
          <p class="text-sm text-base-content/70">{@theme.name}</p>
        </div>
      </div>

      <div class="space-y-2">
        <div class="flex gap-2">
          <div class="flex-1 h-2 bg-primary rounded"></div>
          <div class="flex-1 h-2 bg-secondary rounded"></div>
          <div class="flex-1 h-2 bg-accent rounded"></div>
        </div>
        <div class="flex gap-2">
          <div class="flex-1 h-2 bg-base-200 rounded"></div>
          <div class="flex-1 h-2 bg-base-300 rounded"></div>
        </div>
      </div>

      <p class="text-xs text-base-content/60 mt-3 line-clamp-2">
        {@theme.description}
      </p>

      <%= if @theme.name == @current_theme do %>
        <div class="mt-3 flex items-center gap-2 text-primary">
          <.icon name="hero-check" class="w-4 h-4" />
          <span class="text-sm font-medium">当前主题</span>
        </div>
      <% end %>
    </div>
    """
  end

  # 私有函数

  defp get_theme_icon(theme_name) do
    case Enum.find(@themes, &(&1.name == theme_name)) do
      %{icon: icon} -> icon
      _ -> "🎨"
    end
  end

  defp get_theme_label(theme_name) do
    case Enum.find(@themes, &(&1.name == theme_name)) do
      %{label: label} -> label
      _ -> theme_name
    end
  end

  @doc """
  获取所有可用主题
  """
  def get_available_themes, do: @themes

  @doc """
  获取主题信息
  """
  def get_theme_info(theme_name) do
    Enum.find(@themes, &(&1.name == theme_name))
  end

  @doc """
  检查主题是否存在
  """
  def theme_exists?(theme_name) do
    Enum.any?(@themes, &(&1.name == theme_name))
  end
end
