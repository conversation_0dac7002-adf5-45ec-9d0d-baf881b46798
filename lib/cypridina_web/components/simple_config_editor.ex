defmodule CypridinaWeb.Components.SimpleConfigEditor do
  use Phoenix.LiveComponent
  import Phoenix.HTML.Form

  @impl true
  def mount(socket) do
    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    # 获取当前配置数据
    current_config = get_current_config(assigns)

    socket =
      socket
      |> assign(assigns)
      |> assign(:config_j<PERSON>, <PERSON>.encode!(current_config, pretty: true))
      |> assign(:errors, [])

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="simple-config-editor">
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {@title}
        </label>
        <p class="text-xs text-gray-500 mb-2">{@description}</p>

        <textarea
          name={@field_name}
          rows="20"
          class="w-full px-3 py-2 font-mono text-sm border rounded focus:ring-1 focus:ring-blue-500"
          phx-blur="save_config"
          phx-target={@myself}
        ><%= @config_json %></textarea>
        
    <!-- 错误提示 -->
        <%= if @errors != [] do %>
          <div class="text-red-600 text-xs mt-1">
            <%= for error <- @errors do %>
              <div>{error}</div>
            <% end %>
          </div>
        <% end %>
        
    <!-- 成功提示 -->
        <%= if assigns[:success_message] do %>
          <div class="text-green-600 text-xs mt-1">
            {@success_message}
          </div>
        <% end %>
      </div>
      
    <!-- 操作按钮 -->
      <div class="flex gap-2">
        <button
          type="button"
          phx-click="reset_to_default"
          phx-target={@myself}
          class="px-4 py-2 text-sm font-medium bg-gray-800 text-white rounded-md border border-gray-700 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
        >
          重置为默认
        </button>

        <button
          type="button"
          phx-click="validate_json"
          phx-target={@myself}
          class="px-4 py-2 text-sm font-medium bg-blue-700 text-white rounded-md border border-blue-600 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          验证格式
        </button>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("save_config", %{"value" => json_text}, socket) do
    case Jason.decode(json_text) do
      {:ok, config_data} ->
        # 保存配置
        case save_config_to_db(socket.assigns, config_data) do
          {:ok, _} ->
            updated_socket =
              socket
              |> assign(:config_json, Jason.encode!(config_data, pretty: true))
              |> assign(:errors, [])
              |> assign(:success_message, "配置保存成功！")

            Process.send_after(self(), {:clear_success, updated_socket.assigns.myself}, 3000)
            {:noreply, updated_socket}

          {:error, reason} ->
            {:noreply, assign(socket, :errors, ["保存失败: #{inspect(reason)}"])}
        end

      {:error, _} ->
        {:noreply, assign(socket, :errors, ["JSON格式错误，请检查语法"])}
    end
  end

  def handle_event("validate_json", _params, socket) do
    case Jason.decode(socket.assigns.config_json) do
      {:ok, _} ->
        updated_socket =
          socket
          |> assign(:errors, [])
          |> assign(:success_message, "JSON格式正确！")

        Process.send_after(self(), {:clear_success, updated_socket.assigns.myself}, 2000)
        {:noreply, updated_socket}

      {:error, _} ->
        {:noreply, assign(socket, :errors, ["JSON格式错误，请检查语法"])}
    end
  end

  def handle_event("reset_to_default", _params, socket) do
    default_config = get_default_config(socket.assigns)

    {:noreply,
     socket
     |> assign(:config_json, Jason.encode!(default_config, pretty: true))
     |> assign(:errors, [])
     |> assign(:success_message, "已重置为默认配置")}
  end

  @impl true
  def handle_info({:clear_success, component_id}, socket) do
    if socket.assigns.myself == component_id do
      {:noreply, assign(socket, :success_message, nil)}
    else
      {:noreply, socket}
    end
  end

  # 获取当前配置
  defp get_current_config(assigns) do
    if assigns[:room] && assigns.room.unified_config && map_size(assigns.room.unified_config) > 0 do
      # 如果数据库中有配置，直接显示
      assigns.room.unified_config
    else
      # 如果数据库中没有配置，显示空Map，让用户从头开始编辑
      %{}
    end
  end

  # 获取默认配置
  defp get_default_config(assigns) do
    # 对于统一配置，如果没有数据就显示空Map
    %{}
  end

  # 保存配置到数据库
  defp save_config_to_db(assigns, config_data) do
    try do
      room_id = assigns[:room_id] || (assigns[:room] && assigns.room.id)

      if room_id do
        # 更新现有房间
        update_room_config(room_id, assigns.config_type, config_data)
      else
        # 创建新房间配置
        create_room_config(assigns, config_data)
      end
    rescue
      error ->
        {:error, error}
    end
  end

  # 更新房间配置
  defp update_room_config(room_id, _config_type, config_data) do
    case Teen.GameManagement.LeveRoomConfig.get_by_id(room_id) do
      {:ok, room} ->
        update_params = %{unified_config: config_data}
        Teen.GameManagement.LeveRoomConfig.update(room, update_params)

      {:error, _} ->
        {:error, "房间不存在"}
    end
  end

  # 创建房间配置
  defp create_room_config(assigns, config_data) do
    game_id = assigns[:game_id]

    if game_id do
      # 首先获取游戏配置以获取 game_config_id
      case Teen.GameManagement.ManageGameConfig.get_by_game_id(game_id: game_id) do
        {:ok, game_config} ->
          params = %{
            game_config_id: game_config.id,
            game_id: game_id,
            # 临时服务器ID
            server_id: 9999,
            server_ip: "127.0.0.1",
            port: 4000,
            order_id: 1,
            min_bet: 100,
            max_bet: 0,
            entry_fee: 200,
            max_players: 1,
            bundle_name: "slot777",
            is_enabled: true
          }

          params = Map.put(params, :unified_config, config_data)

          Teen.GameManagement.LeveRoomConfig.create(params)

        {:error, _} ->
          {:error, "游戏不存在"}
      end
    else
      {:error, "缺少游戏ID"}
    end
  end
end
