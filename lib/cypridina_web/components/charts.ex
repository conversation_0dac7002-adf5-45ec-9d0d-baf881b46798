defmodule CypridinaWeb.Components.Charts do
  @moduledoc """
  图表相关的Phoenix Components
  """
  use Phoenix.Component

  @doc """
  渲染选手名次走势图组件

  ## 示例

      <.ranking_chart
        id="ranking-chart"
        races={@recent_races}
        animals={@animals}
        title="选手名次走势图 (最近5场)"
      />
  """
  attr :id, :string, required: true, doc: "图表的唯一ID"
  attr :races, :list, required: true, doc: "近期比赛数据列表"
  attr :animals, :list, required: true, doc: "选手列表"
  attr :title, :string, default: "选手名次走势图", doc: "图表标题"
  attr :class, :string, default: "", doc: "图表容器的额外CSS类"

  def ranking_chart(assigns) do
    ~H"""
    <div class={"ranking-chart-container #{@class}"}>
      <div class="chart-title">
        <h3>{@title}</h3>
      </div>

      <div class="chart-wrapper">
        <div
          id={@id}
          phx-hook="RacingChart"
          data-chart-data={Jason.encode!(@races)}
          data-animals={Jason.encode!(@animals)}
          phx-update="ignore"
        >
        </div>
      </div>
    </div>
    """
  end

  @doc """
  渲染控制线波动图表组件

  ## 示例

      <.control_line_chart
        id="control-line-chart"
        chart_data={@control_line_data}
        time_range={@time_range}
        title="四级控制线波动图"
      />
  """
  attr :id, :string, required: true, doc: "图表的唯一ID"
  attr :chart_data, :map, required: true, doc: "控制线数据"
  attr :time_range, :atom, default: :hour, doc: "时间范围"
  attr :title, :string, default: "控制线波动图", doc: "图表标题"
  attr :class, :string, default: "", doc: "图表容器的额外CSS类"

  def control_line_chart(assigns) do
    ~H"""
    <div class={"control-line-chart-container #{@class}"}>
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">{@title}</h3>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">实时更新</span>
          <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
        </div>
      </div>

      <div class="chart-wrapper bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div
          id={@id}
          phx-hook="ControlLineChart"
          data-chart-data={Jason.encode!(@chart_data)}
          data-time-range={@time_range}
          phx-update="ignore"
          class="w-full h-[450px]"
        >
        </div>
      </div>
      
    <!-- 图表说明 -->
      <div class="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
        <div class="flex items-center space-x-2">
          <div class="w-4 h-0.5 bg-blue-500 rounded"></div>
          <span class="text-sm text-gray-600">当前库存</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-4 h-0.5 bg-green-500 rounded" style="border-bottom: 2px dashed #10B981;">
          </div>
          <span class="text-sm text-gray-600">中心线</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-4 h-0.5 bg-red-500 rounded"></div>
          <span class="text-sm text-gray-600">绝对收分线</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-4 h-0.5 bg-yellow-500 rounded" style="border-bottom: 2px dotted #F59E0B;">
          </div>
          <span class="text-sm text-gray-600">预收分线</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-4 h-0.5 bg-purple-500 rounded" style="border-bottom: 2px dotted #8B5CF6;">
          </div>
          <span class="text-sm text-gray-600">预放分线</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-4 h-0.5 bg-cyan-500 rounded"></div>
          <span class="text-sm text-gray-600">绝对放分线</span>
        </div>
      </div>
    </div>
    """
  end
end
