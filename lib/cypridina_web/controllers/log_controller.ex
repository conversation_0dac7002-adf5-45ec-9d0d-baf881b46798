defmodule CypridinaWeb.LogController do
  @moduledoc """
  日志文件下载控制器
  """

  use CypridinaWeb, :controller

  @logs_dir "logs"

  def download(conn, %{"file" => file_path}) do
    # 安全检查：确保文件路径在logs目录内
    safe_path = Path.join(@logs_dir, file_path)

    case Path.relative_to(safe_path, @logs_dir) do
      ^file_path ->
        # 路径安全，继续处理
        if File.exists?(safe_path) do
          filename = Path.basename(file_path)

          conn
          |> put_resp_content_type("application/octet-stream")
          |> put_resp_header("content-disposition", "attachment; filename=\"#{filename}\"")
          |> send_file(200, safe_path)
        else
          conn
          |> put_status(:not_found)
          |> json(%{error: "文件不存在"})
        end

      _ ->
        # 路径不安全，拒绝访问
        conn
        |> put_status(:forbidden)
        |> json(%{error: "访问被拒绝"})
    end
  end
end
