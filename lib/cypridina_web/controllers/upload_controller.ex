defmodule CypridinaWeb.UploadController do
  @moduledoc """
  处理文件上传的控制器
  """

  use <PERSON><PERSON>ridinaWeb, :controller
  alias Cypridina.Services.UploadService
  alias Cypridina.Accounts.User

  @doc """
  上传头像 - 支持base64和文件上传
  """
  def upload_avatar(conn, %{"user_id" => user_id} = params) do
    cond do
      # Base64上传
      Map.has_key?(params, "base64_data") ->
        handle_base64_upload(conn, user_id, params)

      # 文件上传
      Map.has_key?(params, "file") ->
        handle_file_upload(conn, user_id, params)

      true ->
        conn
        |> put_status(:bad_request)
        |> json(%{error: "需要提供base64_data或file参数"})
    end
  end

  @doc """
  生成预签名上传URL
  """
  def generate_upload_url(conn, %{"user_id" => user_id, "extension" => extension} = params) do
    content_type = Map.get(params, "content_type", "image/jpeg")

    case UploadService.generate_presigned_upload_url(user_id, extension, content_type) do
      {:ok, upload_url, file_path} ->
        conn
        |> json(%{
          success: true,
          upload_url: upload_url,
          file_path: file_path
        })

      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{error: "生成上传URL失败: #{inspect(reason)}"})
    end
  end

  @doc """
  获取用户头像URL
  """
  def get_avatar_url(conn, %{"user_id" => user_id}) do
    case User.get_by_id(user_id) do
      {:ok, user} ->
        avatar_urls = %{
          original: Cypridina.Uploaders.Avatar.url({user.avatar, user}, :original),
          thumb: Cypridina.Uploaders.Avatar.url({user.avatar, user}, :thumb)
        }

        conn
        |> json(%{
          success: true,
          avatar_urls: avatar_urls
        })

      {:error, _reason} ->
        conn
        |> put_status(:not_found)
        |> json(%{error: "用户不存在"})
    end
  end

  # 私有函数

  defp handle_base64_upload(conn, user_id, %{"base64_data" => base64_data} = params) do
    extension = Map.get(params, "extension", "jpg")

    case UploadService.upload_avatar_from_base64(user_id, base64_data, extension) do
      {:ok, urls} ->
        conn
        |> json(%{
          success: true,
          message: "头像上传成功",
          urls: urls
        })

      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{error: "上传失败: #{inspect(reason)}"})
    end
  end

  defp handle_file_upload(conn, user_id, %{"file" => file}) do
    case UploadService.upload_file(user_id, file) do
      {:ok, url} ->
        conn
        |> json(%{
          success: true,
          message: "文件上传成功",
          url: url
        })

      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{error: "上传失败: #{inspect(reason)}"})
    end
  end
end
