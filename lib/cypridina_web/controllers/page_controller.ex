defmodule CypridinaWeb.PageController do
  use Cy<PERSON>ridinaWeb, :controller

  def home(conn, _params) do
    render(conn, :home)
  end

  def redirect_to_racing_game(conn, _params) do
    redirect(conn, to: "/app/racing_game")
  end

  def redirect_to_admin_users(conn, _params) do
    conn
    |> Phoenix.Controller.redirect(to: ~p"/admin/users")
    |> Plug.Conn.halt()
  end

  def payment_success(conn, params) do
    order_id = Map.get(params, "order_id")

    conn
    |> put_layout(html: {CypridinaWeb.Layouts, :app})
    |> render(:payment_success, order_id: order_id)
  end
end
