defmodule CypridinaWeb.AuthOverrides do
  use AshAuthentication.Phoenix.Overrides

  # 自定义登录页面样式

  # 修改整体页面布局
  override AshAuthentication.Phoenix.Components.Layout do
    # set :class,
    # "min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-blue-300 to-blue-500"

    # set :inner_class, "w-full max-w-md px-4"
  end

  # 隐藏默认的横幅
  override AshAuthentication.Phoenix.Components.Banner do
    set :show, false
  end

  # 自定义登录表单样式
  override AshAuthentication.Phoenix.Components.SignIn do
    # 隐藏默认横幅
    set :show_banner, false

    # 添加自定义 logo 和标题
    # set :before_form, """
    # <div class="flex flex-col items-center justify-center mb-6">
    #   <img src="/images/shiba_logo.png" alt="Logo" class="w-24 h-24 mb-4">
    #   <div class="bg-orange-100 rounded-lg p-3 mb-4 w-full">
    #     <div class="flex items-center">
    #       <div class="flex-1">
    #         <img src="/images/animal_race_banner.png" alt="动物运动会" class="w-full">
    #       </div>
    #     </div>
    #   </div>
    # </div>
    # """

    # 自定义表单容器样式
    # set :form_class, "bg-white rounded-lg shadow-lg overflow-hidden"

    # 自定义表单内部样式
    # set :form_inner_class, "p-6"

    # 自定义标题样式（隐藏默认标题）
    # set :heading_class, "hidden"

    # 自定义输入框组容器样式
    # set :input_container_class, "mb-4"

    # 自定义输入框标签样式
    # set :label_class, "hidden"

    # 自定义输入框样式
    # set :input_class,
    # "w-full p-3 border rounded-lg mb-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500"

    # 自定义用户名输入框
    # set :username_input_container, """
    # <div class="relative">
    #   <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
    #     <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
    #       <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
    #     </svg>
    #   </div>
    #   <%= @input %>
    # </div>
    # """

    # 自定义密码输入框
    # set :password_input_container, """
    # <div class="relative">
    #   <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
    #     <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
    #       <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 116 0z" clip-rule="evenodd" />
    #     </svg>
    #   </div>
    #   <%= @input %>
    #   <div class="absolute inset-y-0 right-0 flex items-center pr-3">
    #     <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
    #       <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
    #       <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
    #     </svg>
    #   </div>
    # </div>
    # """

    # 自定义提交按钮样式
    # set :submit_button_class,
    # "w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition duration-300 mt-4"

    # 添加注册链接
    # set :after_form, """
    # <div class="mt-4 text-right text-blue-700">
    #   <a href="/register" class="hover:underline">没有账号，立即注册</a>
    # </div>
    # """
  end

  override AshAuthentication.Phoenix.Components.Password do
    set :register_toggle_text, nil
    set :reset_toggle_text, nil
  end

  override AshAuthentication.Phoenix.Components.Password.Input do
    # 自定义输入框占位符文本
    set :identity_input_label, "账户"
    set :identity_input_placeholder, "请输入用户名"
    set :password_input_label, "密码"

    # set :input_class, "w-full p-3 border rounded-lg mb-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500"
  end

  override AshAuthentication.Phoenix.Components.Password.SignInForm do
    # 自定义提交按钮文本
    set :button_text, "登录"

    # 移除旧的JS重定向逻辑，改为使用LiveView的push_navigate
    # 由AuthController和RedirectLive组件负责处理重定向逻辑
  end

  # 自定义注册表单样式（可选，如果你也想更新注册页面）
  override AshAuthentication.Phoenix.Components.Register do
    # set :show_banner, false
    # set :heading_text, "注册新账号"
    # set :submit_button_text, "注册"

    # set :submit_button_class,
    #     "w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition duration-300"
  end
end
