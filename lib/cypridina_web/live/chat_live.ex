defmodule CypridinaWeb.ChatLive do
  @moduledoc """
  优化的聊天LiveView组件

  提供高性能的实时聊天界面，包括：
  - 虚拟滚动
  - 消息同步
  - 离线消息处理
  - 性能优化
  """

  use CypridinaWeb, :live_view

  alias Cypridina.Chat.{ChatService, MessageHandler, ChatLogger}
  alias Phoenix.PubSub

  @impl true
  def mount(%{"session_id" => session_id}, _session, socket) do
    user_id = get_current_user_id(socket)

    if connected?(socket) do
      Phoenix.PubSub.subscribe(Cypridina.PubSub, "chat_session:#{session_id}")
      Phoenix.PubSub.subscribe(Cypridina.PubSub, "user_presence:#{user_id}")
    end

    with {:ok, session} <- ChatService.get_session(session_id),
         {:ok, _participant} <- ChatService.verify_participant_in_session(session_id, user_id),
         {:ok, initial_messages} <- load_initial_messages(session_id, user_id),
         {:ok, participants} <- load_session_participants(session_id) do
      socket =
        socket
        |> assign(:session_id, session_id)
        |> assign(:session, session)
        |> assign(:user_id, user_id)
        |> assign(:messages, initial_messages)
        |> assign(:participants, participants)
        |> assign(:message_input, "")
        |> assign(:typing_users, MapSet.new())
        |> assign(:loading_more, false)
        |> assign(:has_more_messages, true)
        |> assign(:unread_count, 0)
        |> assign(:connection_status, :connected)
        |> assign(:last_seen_message_id, get_last_message_id(initial_messages))

      ChatLogger.log_user_event(:opened_chat, %{
        id: user_id,
        session_id: session_id
      })

      {:ok, socket}
    else
      {:error, reason} ->
        {:ok, assign(socket, :error, reason)}
    end
  end

  @impl true
  def handle_event("send_message", %{"message" => message_content}, socket) do
    %{session_id: session_id, user_id: user_id} = socket.assigns

    if String.trim(message_content) != "" do
      case ChatService.send_message(session_id, user_id, message_content) do
        {:ok, message} ->
          # 消息会通过PubSub广播回来，不需要立即更新
          socket = assign(socket, :message_input, "")
          {:noreply, socket}

        {:error, reason} ->
          socket = put_flash(socket, :error, "发送消息失败: #{inspect(reason)}")
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("load_more_messages", _params, socket) do
    %{session_id: session_id, user_id: user_id, messages: current_messages} = socket.assigns

    if socket.assigns.has_more_messages and not socket.assigns.loading_more do
      socket = assign(socket, :loading_more, true)

      oldest_message_id = get_oldest_message_id(current_messages)

      case MessageHandler.get_session_messages(session_id, user_id,
             limit: 20,
             before_message_id: oldest_message_id
           ) do
        {:ok, new_messages} ->
          all_messages = new_messages ++ current_messages
          has_more = length(new_messages) == 20

          socket =
            socket
            |> assign(:messages, all_messages)
            |> assign(:has_more_messages, has_more)
            |> assign(:loading_more, false)

          {:noreply, socket}

        {:error, _reason} ->
          socket =
            socket
            |> assign(:loading_more, false)
            |> put_flash(:error, "加载历史消息失败")

          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("mark_messages_read", %{"message_ids" => message_ids}, socket) do
    %{session_id: session_id, user_id: user_id} = socket.assigns

    case MessageHandler.mark_messages_read_batch(session_id, user_id, message_ids) do
      {:ok, _receipts} ->
        socket = assign(socket, :unread_count, 0)
        {:noreply, socket}

      {:error, _reason} ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("typing", %{"typing" => typing}, socket) do
    %{session_id: session_id, user_id: user_id} = socket.assigns

    Phoenix.PubSub.broadcast(Cypridina.PubSub, "chat_session:#{session_id}", {
      :user_typing,
      %{user_id: user_id, typing: typing}
    })

    {:noreply, socket}
  end

  @impl true
  def handle_event("scroll_to_bottom", _params, socket) do
    # 标记所有可见消息为已读
    visible_message_ids = get_visible_message_ids(socket.assigns.messages)

    if length(visible_message_ids) > 0 do
      send(self(), {:mark_messages_read, visible_message_ids})
    end

    {:noreply, socket}
  end

  @impl true
  def handle_event("file_upload", %{"file" => file_data}, socket) do
    %{session_id: session_id, user_id: user_id} = socket.assigns

    case ChatService.upload_chat_file_from_base64(
           session_id,
           user_id,
           file_data["data"],
           file_data["name"]
         ) do
      {:ok, file_info} ->
        case ChatService.send_file_message(session_id, user_id, file_info) do
          {:ok, _message} ->
            {:noreply, socket}

          {:error, reason} ->
            socket = put_flash(socket, :error, "发送文件消息失败: #{inspect(reason)}")
            {:noreply, socket}
        end

      {:error, reason} ->
        socket = put_flash(socket, :error, "文件上传失败: #{inspect(reason)}")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:new_message, message}, socket) do
    %{messages: current_messages, user_id: user_id} = socket.assigns

    # 将新消息添加到列表末尾
    updated_messages = current_messages ++ [message]

    # 如果不是自己发送的消息，增加未读计数
    unread_count =
      if message.sender_id != user_id do
        socket.assigns.unread_count + 1
      else
        socket.assigns.unread_count
      end

    socket =
      socket
      |> assign(:messages, updated_messages)
      |> assign(:unread_count, unread_count)
      |> assign(:last_seen_message_id, message.id)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:user_typing, %{user_id: user_id, typing: typing}}, socket) do
    current_typing = socket.assigns.typing_users

    updated_typing =
      if typing do
        MapSet.put(current_typing, user_id)
      else
        MapSet.delete(current_typing, user_id)
      end

    socket = assign(socket, :typing_users, updated_typing)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:messages_read, %{user_id: user_id, message_ids: message_ids}}, socket) do
    # 更新消息的已读状态显示
    updated_messages = update_message_read_status(socket.assigns.messages, user_id, message_ids)

    socket = assign(socket, :messages, updated_messages)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:user_joined, user_data}, socket) do
    participants = socket.assigns.participants ++ [user_data]
    socket = assign(socket, :participants, participants)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:user_left, user_data}, socket) do
    participants = Enum.reject(socket.assigns.participants, &(&1.id == user_data.id))
    socket = assign(socket, :participants, participants)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:mark_messages_read, message_ids}, socket) do
    %{session_id: session_id, user_id: user_id} = socket.assigns

    case MessageHandler.mark_messages_read_batch(session_id, user_id, message_ids) do
      {:ok, _receipts} ->
        socket = assign(socket, :unread_count, 0)
        {:noreply, socket}

      {:error, _reason} ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:connection_status, status}, socket) do
    socket = assign(socket, :connection_status, status)
    {:noreply, socket}
  end

  # 私有函数

  defp get_current_user_id(socket) do
    # 从socket中获取当前用户ID
    # 这里需要根据实际的认证系统实现
    socket.assigns[:current_user][:id] || "anonymous"
  end

  defp load_initial_messages(session_id, user_id) do
    MessageHandler.get_session_messages(session_id, user_id, limit: 50)
  end

  defp load_session_participants(session_id) do
    ChatService.get_session_participants(session_id)
  end

  defp get_last_message_id([]), do: nil

  defp get_last_message_id(messages) do
    List.last(messages).id
  end

  defp get_oldest_message_id([]), do: nil

  defp get_oldest_message_id(messages) do
    List.first(messages).id
  end

  defp get_visible_message_ids(messages) do
    # 获取当前可见的消息ID列表
    # 这里可以根据实际的滚动位置来确定
    Enum.map(messages, & &1.id)
  end

  defp update_message_read_status(messages, reader_id, read_message_ids) do
    Enum.map(messages, fn message ->
      if message.id in read_message_ids do
        # 更新消息的已读状态
        # 这里需要根据实际的消息结构来实现
        message
      else
        message
      end
    end)
  end

  defp format_typing_users(typing_users, participants) do
    typing_users
    |> MapSet.to_list()
    |> Enum.map(fn user_id ->
      Enum.find(participants, &(&1.id == user_id))
    end)
    |> Enum.filter(& &1)
    |> Enum.map(& &1.nickname)
  end
end
