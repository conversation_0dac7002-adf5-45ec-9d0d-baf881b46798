# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Cypridina is a multi-game platform built with Elixir/Phoenix and the Ash Framework. It supports various games including Teen Patti, slots, and racing games, with integrated payment processing, chat, and rewards systems.

## Development Commands

### Server Management
```bash
make server        # Start Phoenix with interactive shell (recommended)
make dev          # Start Phoenix in development mode
make daemon       # Start Phoenix in background
make stop         # Gracefully stop Phoenix
make restart      # Restart service
make status       # Check service status
```

### Testing & Code Quality
```bash
mix test                          # Run all tests
mix test path/to/test.exs        # Run specific test file
mix test path/to/test.exs:123   # Run test at specific line
mix format                       # Format code
mix credo                        # Run static analysis
```

### Database
```bash
make setup        # Initial project setup (deps, db, migrations)
make reset        # Reset database
mix ash.migrate   # Run Ash migrations
mix ash.rollback  # Rollback migrations
```

### Logging
```bash
make logs-view              # View all log files
make logcat GAME_ID=123    # Follow specific game logs
make loglist               # List available game logs
```

## Architecture Overview

### Core Modules
- **Cypridina**: Core business domain with Ash resources
  - `Accounts`: User management and authentication
  - `Chat`: Real-time messaging
  - `Ledger`: Financial transactions
  - `RoomSystem`: Game room management

- **CypridinaWeb**: Phoenix web layer
  - Controllers, LiveViews, and components
  - API endpoints and WebSocket channels

- **Teen**: Teen Patti game implementation
  - `GameSystem`: Various game modes (Classic, Joker, AK47, etc.)
  - `PaymentSystem`: Payment gateway integration
  - `ActivitySystem`: Rewards and bonuses

- **RacingGame**: Racing game module

### Key Technologies
- **Framework**: Phoenix 1.8.0-rc.3 with Ash Framework 3.0
- **Database**: PostgreSQL via ash_postgres
- **Real-time**: Phoenix Channels and PubSub
- **Jobs**: Oban for background processing
- **Admin**: Backpex for admin panels
- **Storage**: MinIO/S3 via Waffle
- **Frontend**: LiveView with ESBuild + Tailwind CSS

## Code Style
- Follow Elixir conventions and idioms
- Use pattern matching when appropriate
- Prefer pipeline operator |> for data transformations
- Keep functions small and focused
- 习惯于精简代码来降低复杂度、修复问题
- 多用管道操作，少用case异常处理

## Ash Framework Guidelines
- Don't define `get_by_*` actions directly, use code interface with read action:
  ```elixir
  define :get_by_field, action: :read, get_by: :field
  ```
- Elixir doesn't require server restart for code changes (hot reloading)
- Resources should be in `lib/cypridina/` organized by domain
- Use policies for authorization
- Prefer calculations over custom attributes when possible

## Testing Guidelines
- Always run tests before considering a task complete
- Tests are organized by module in `test/`
- Use ExUnit test helpers in `test/support/`
- Run specific tests with line numbers: `mix test test/file.exs:42`
- Use `@tag` for test categorization

## Payment System Integration
The platform integrates multiple payment gateways:
- YidunPay (primary)
- SanQiPay
- DuoDuoPay
- QuanQiuPay
Each gateway has specific configuration in the payment system module.

## Important Notes
- Never commit unless explicitly asked
- Always check git status before making changes
- Search the codebase before implementing new features
- This is a production gaming platform - ensure security best practices
- The platform operates in "teen" mode (Indian gaming market)
- Payment and financial operations require extra care and testing

## Common Patterns
- Use Ash.Query for database queries
- Leverage Ash actions for business logic
- Use Phoenix contexts for organizing domains
- Implement proper error handling with `{:ok, result}` and `{:error, reason}`
- Use GenServer for stateful processes
- Implement supervision trees for fault tolerance

<!-- usage-rules-start -->
<!-- usage-rules-header -->
# Usage Rules

**IMPORTANT**: Consult these usage rules early and often when working with the packages listed below. 
Before attempting to use any of these packages or to discover if you should use them, review their 
usage rules to understand the correct patterns, conventions, and best practices.
<!-- usage-rules-header-end -->

<!-- ash_postgres-start -->
## ash_postgres usage
_The PostgreSQL data layer for Ash Framework_

[ash_postgres usage rules](deps/ash_postgres/usage-rules.md)
<!-- ash_postgres-end -->
<!-- ash_graphql-start -->
## ash_graphql usage
_The extension for building GraphQL APIs with Ash
_

[ash_graphql usage rules](deps/ash_graphql/usage-rules.md)
<!-- ash_graphql-end -->
<!-- ash_json_api-start -->
## ash_json_api usage
_The JSON:API extension for the Ash Framework.
_

[ash_json_api usage rules](deps/ash_json_api/usage-rules.md)
<!-- ash_json_api-end -->
<!-- ash_oban-start -->
## ash_oban usage
_The extension for integrating Ash resources with Oban._

[ash_oban usage rules](deps/ash_oban/usage-rules.md)
<!-- ash_oban-end -->
<!-- igniter-start -->
## igniter usage
_A code generation and project patching framework_

[igniter usage rules](deps/igniter/usage-rules.md)
<!-- igniter-end -->
<!-- ash_authentication-start -->
## ash_authentication usage
_Authentication extension for the Ash Framework._

[ash_authentication usage rules](deps/ash_authentication/usage-rules.md)
<!-- ash_authentication-end -->
<!-- ash-start -->
## ash usage
_A declarative, extensible framework for building Elixir applications._

[ash usage rules](deps/ash/usage-rules.md)
<!-- ash-end -->
<!-- ash_phoenix-start -->
## ash_phoenix usage
_Utilities for integrating Ash and Phoenix_

[ash_phoenix usage rules](deps/ash_phoenix/usage-rules.md)
<!-- ash_phoenix-end -->
<!-- ash_ai-start -->
## ash_ai usage
_Integrated LLM features for your Ash application._

[ash_ai usage rules](deps/ash_ai/usage-rules.md)
<!-- ash_ai-end -->
<!-- ash_backpex-start -->
## ash_backpex usage
_Integrated Backpex for your Ash application._

[ash_backpex usage rules](docs/ash_backpex_usage.md)
<!-- ash_backpex-end -->
<!-- otp-end -->
<!-- reactor-start -->
## reactor usage
_An asynchronous, graph-based execution engine_

[reactor usage rules](deps/reactor/usage-rules.md)
<!-- reactor-end -->
<!-- usage_rules-start -->
## usage_rules usage
_A dev tool for Elixir projects to gather LLM usage rules from dependencies_

## Using Usage Rules

Many packages have usage rules, which you should *thoroughly* consult before taking any
action. These usage rules contain guidelines and rules *directly from the package authors*.
They are your best source of knowledge for making decisions.

## Modules & functions in the current app and dependencies

When looking for docs for modules & functions that are dependencies of the current project,
or for Elixir itself, use `mix usage_rules.docs`

```
# Search a whole module
mix usage_rules.docs Enum

# Search a specific function
mix usage_rules.docs Enum.zip

# Search a specific function & arity
mix usage_rules.docs Enum.zip/1
```


## Searching Documentation

You should also consult the documentation of any tools you are using, early and often. The best 
way to accomplish this is to use the `usage_rules.search_docs` mix task. Once you have
found what you are looking for, use the links in the search results to get more detail. For example:

```
# Search docs for all packages in the current application, including Elixir
mix usage_rules.search_docs Enum.zip

# Search docs for specific packages
mix usage_rules.search_docs Req.get -p req

# Search docs for multi-word queries
mix usage_rules.search_docs "making requests" -p req

# Search only in titles (useful for finding specific functions/modules)
mix usage_rules.search_docs "Enum.zip" --query-by title
```


<!-- usage_rules-end -->
<!-- usage_rules:elixir-start -->
## usage_rules:elixir usage
# Elixir Core Usage Rules

## Pattern Matching
- Use pattern matching over conditional logic when possible
- Prefer to match on function heads instead of using `if`/`else` or `case` in function bodies

## Error Handling
- Use `{:ok, result}` and `{:error, reason}` tuples for operations that can fail
- Avoid raising exceptions for control flow
- Use `with` for chaining operations that return `{:ok, _}` or `{:error, _}`

## Common Mistakes to Avoid
- Elixir has no `return` statement, nor early returns. The last expression in a block is always returned.
- Don't use `Enum` functions on large collections when `Stream` is more appropriate
- Avoid nested `case` statements - refactor to a single `case`, `with` or separate functions
- Don't use `String.to_atom/1` on user input (memory leak risk)
- Lists and enumerables cannot be indexed with brackets. Use pattern matching or `Enum` functions
- Prefer `Enum` functions like `Enum.reduce` over recursion
- When recursion is necessary, prefer to use pattern matching in function heads for base case detection
- Using the process dictionary is typically a sign of unidiomatic code
- Only use macros if explicitly requested
- There are many useful standard library functions, prefer to use them where possible

## Function Design
- Use guard clauses: `when is_binary(name) and byte_size(name) > 0`
- Prefer multiple function clauses over complex conditional logic
- Name functions descriptively: `calculate_total_price/2` not `calc/2`
- Predicate function names should not start with `is` and should end in a question mark. 
- Names like `is_thing` should be reserved for guards

## Data Structures
- Use structs over maps when the shape is known: `defstruct [:name, :age]`
- Prefer keyword lists for options: `[timeout: 5000, retries: 3]`
- Use maps for dynamic key-value data
- Prefer to prepend to lists `[new | list]` not `list ++ [new]`

## Mix Tasks

- Use `mix help` to list available mix tasks
- Use `mix help task_name` to get docs for an individual task
- Read the docs and options fully before using tasks

## Testing
- Run tests in a specific file with `mix test test/my_test.exs` and a specific test with the line number `mix test path/to/test.exs:123`
- Limit the number of failed tests with `mix test --max-failures n`
- Use `@tag` to tag specific tests, and `mix test --only tag` to run only those tests
- Use `assert_raise` for testing expected exceptions: `assert_raise ArgumentError, fn -> invalid_function() end`
- Use `mix help test` to for full documentation on running tests

## Debugging

- Use `dbg/1` to print values while debugging. This will display the formatted value and other relevant information in the console.

<!-- usage_rules:elixir-end -->
<!-- usage_rules:otp-start -->
## usage_rules:otp usage
# OTP Usage Rules

## GenServer Best Practices
- Keep state simple and serializable
- Handle all expected messages explicitly
- Use `handle_continue/2` for post-init work
- Implement proper cleanup in `terminate/2` when necessary

## Process Communication
- Use `GenServer.call/3` for synchronous requests expecting replies
- Use `GenServer.cast/2` for fire-and-forget messages.
- When in doubt, us `call` over `cast`, to ensure back-pressure
- Set appropriate timeouts for `call/3` operations

## Fault Tolerance
- Set up processes such that they can handle crashing and being restarted by supervisors
- Use `:max_restarts` and `:max_seconds` to prevent restart loops

## Task and Async
- Use `Task.Supervisor` for better fault tolerance
- Handle task failures with `Task.yield/2` or `Task.shutdown/2`
- Set appropriate task timeouts
- Use `Task.async_stream/3` for concurrent enumeration with back-pressure

<!-- usage_rules:otp-end -->
<!-- usage-rules-end -->

你是Lyra，一位大师级AI提示词优化专家。你的任务是：将任何用户输入转化为精确构建的提示词，释放AI在所有平台上的最大潜力。

## 4-D 方法论

### 1. 拆解

* 提取核心意图、关键实体和上下文
* 确定输出要求和约束条件
* 映射提供的内容与缺失的内容

### 2. 诊断

* 审核清晰度差距和歧义
* 检查具体性和完整性
* 评估结构和复杂性需求

### 3. 开发

* 根据请求类型选择最佳技术：

  * 创意 → 多角度 + 语气强调
  * 技术性 → 基于约束 + 精确度聚焦
  * 教育性 → 少量示例 + 清晰结构
  * 复杂性 → 思维链条 + 系统框架
* 分配适当的AI角色/专业
* 增强上下文并实施逻辑结构

### 4. 交付

* 构建优化后的提示词
* 根据复杂性格式化
* 提供实施指导

## 优化技术

基础: 角色分配、上下文分层、输出规格、任务分解

高级: 思维链条、少量示例学习、多角度分析、约束优化

平台说明:

* ChatGPT/GPT-4: 结构化部分、对话引导
* Claude: 更长的上下文、推理框架
* Gemini: 创意任务、对比分析
* 其他: 应用普遍的最佳实践

## 操作模式

详细模式：

* 收集带智能默认值的上下文
* 提出2-3个有针对性的澄清问题
* 提供全面的优化

基础模式：

* 快速修复主要问题
* 仅应用核心技术
* 提供可直接使用的提示词

## 响应格式

简单请求：

```
您的优化提示词：
[优化后的提示词]

改进内容： [关键改进]
```

复杂请求：

```
您的优化提示词：
[优化后的提示词]

关键改进：
• [主要变动和益处]

应用的技术： [简要提及]

专家提示： [使用指导]

## 欢迎信息（必填）

启用时，显示如下：

“你好！我是Lyra，你的AI提示词优化师。我将模糊的请求转化为精确有效的提示词，以便带来更好的结果。

我需要知道的：

* 目标AI： ChatGPT、Claude、Gemini 或其他
* 提示词风格： 详细（我会先提问澄清问题）或基础（快速优化）

示例：

* “使用ChatGPT的详细模式 — 写一封营销邮件”
* “使用Claude的基础模式 — 帮我修改简历”

只需分享你的粗略提示词，我将处理优化！”

## 处理流程

1. 自动检测复杂性：

   * 简单任务 → 基础模式
   * 复杂/专业任务 → 详细模式
2. 通知用户并提供覆盖选项
3. 执行所选模式协议
4. 交付优化后的提示词

记忆说明： 不要将优化会话中的任何信息保存到记忆中
