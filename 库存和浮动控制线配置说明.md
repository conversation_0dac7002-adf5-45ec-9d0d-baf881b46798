# 库存和浮动控制线配置说明

## 概述

本文档详细说明了游戏平台中库存控制系统的配置参数和运作机制，帮助运维人员理解如何通过调整相关参数来控制系统的收分和放分行为。

## 基础概念

### 1. 当前库存 (Current Inventory)
- **定义**: 所有玩家总输赢值的累计总和
- **正值**: 玩家总体输钱，平台盈利状态
- **负值**: 玩家总体赢钱，平台亏损状态
- **零值**: 盈亏平衡状态
- **数据源**: 从钱包控制系统实时获取

### 2. 中心线 (Center Line)
- **初始值**: 默认为 0
- **更新规则**: 中心线 = 上一局中心线 + 本局暗税
- **作用**: 所有收分/放分线的基准线，反映系统的整体趋势
- **影响**: 直接影响所有控制线的位置

### 3. 暗税比例 (Dark Tax Rate)
- **正值**: 中心线逐步上升，系统偏向收分
- **负值**: 中心线逐步下降，系统偏向放分
- **单位**: 千分比（如 100 = 10%，-50 = -5%）
- **计算**: 暗税 = 明税 × 暗税比例

## 核心配置参数

### 基础配置 (GameControlConfig)
```elixir
%{
  base_inventory: 1_000_000,      # 基础库存（默认100万）
  control_weight: 500,            # 控制权重（1-1000）
  dark_tax_rate: 10,              # 暗税比例（10 = 1%）
  winner_tax_rate: 0.05           # 明税比例（5%）
}
```

### 收分线浮动配置
```elixir
%{
  collect_line_max: 30_000,       # 收分线浮动最大值
  collect_line_min: 5_000,        # 收分线浮动最小值
  collect_line_ratio: 0.2,        # 收分线浮动比例（20%）
  pre_collect_line_ratio: 0.7     # 前置收分线比例（70%）
}
```

### 放分线浮动配置
```elixir
%{
  release_line_max: 30_000,       # 放分线浮动最大值
  release_line_min: 5_000,        # 放分线浮动最小值
  release_line_ratio: 0.2,        # 放分线浮动比例（20%）
  pre_release_line_ratio: 0.7     # 前置放分线比例（70%）
}
```

## 控制线计算逻辑

### 收分线计算
```
步骤1: 浮动值 = clamp(中心线 × 收分线比例, 收分线最小值, 收分线最大值)
步骤2: 绝对收分线 = 中心线 - 浮动值
步骤3: 前置收分线 = 中心线 - (浮动值 × 前置收分线比例)
```

### 放分线计算
```
步骤1: 浮动值 = clamp(中心线 × 放分线比例, 放分线最小值, 放分线最大值)
步骤2: 绝对放分线 = 中心线 + 浮动值
步骤3: 前置放分线 = 中心线 + (浮动值 × 前置放分线比例)
```

### 计算示例
**假设**: 中心线 = 10000，收分线比例 = 0.2，收分线最小值 = 5000，收分线最大值 = 30000

```
浮动值 = clamp(10000 × 0.2, 5000, 30000) = clamp(2000, 5000, 30000) = 5000
绝对收分线 = 10000 - 5000 = 5000
前置收分线 = 10000 - (5000 × 0.7) = 10000 - 3500 = 6500
```

## 控制触发条件

### 五级控制区间
游戏系统根据当前库存与控制线的关系，将控制策略分为五个级别：

1. **强力收分区间**: 库存 ≤ 绝对收分线
   - 权重: 1000 (100%触发)
   - 策略: 强制收分，确保平台盈利

2. **权重收分区间**: 绝对收分线 < 库存 ≤ 前置收分线
   - 权重: 700 (70%触发)
   - 策略: 高概率收分

3. **随机模式区间**: 前置收分线 < 库存 < 前置放分线
   - 权重: 500 (50%触发)
   - 策略: 随机结果，保持游戏平衡

4. **权重放分区间**: 前置放分线 ≤ 库存 < 绝对放分线
   - 权重: 300 (30%触发)
   - 策略: 适度放分，提升玩家体验

5. **强力放分区间**: 库存 ≥ 绝对放分线
   - 权重: 100 (10%触发)
   - 策略: 大量放分，吸引玩家继续游戏

### 权重判断机制
```elixir
# 生成1-1000的随机数
random_number = :rand.uniform(1000)

# 如果随机数 <= 权重值，执行对应操作
if random_number <= weight do
  execute_control_action()
else
  execute_random_action()
end
```

## 配置示例和场景

### 默认配置
```elixir
%{
  # 基础设置
  中心线: 0,
  暗税比例: 10,                    # 1%
  
  # 收分线设置
  收分线比例: 0.2,                 # 20%
  收分线最大值: 30000,
  收分线最小值: 5000,
  前置收分线比例: 0.7,             # 70%
  
  # 放分线设置
  放分线比例: 0.2,                 # 20%
  放分线最大值: 30000,
  放分线最小值: 5000,
  前置放分线比例: 0.7              # 70%
}
```

**计算结果（中心线=0时）**:
```
收分浮动值 = clamp(0 × 0.2, 5000, 30000) = 5000
绝对收分线 = 0 - 5000 = -5000
前置收分线 = 0 - (5000 × 0.7) = -3500
前置放分线 = 0 + (5000 × 0.7) = 3500
绝对放分线 = 0 + 5000 = 5000
```

### 保守配置（收分倾向）
```elixir
%{
  收分线比例: 0.15,               # 减少收分线浮动
  放分线比例: 0.25,               # 增加放分线浮动
  收分线最小值: 8000,             # 提高收分线最小值
  放分线最小值: 5000,             # 保持放分线最小值
  暗税比例: 50                    # 5%，正值偏向收分
}
```

### 激进配置（放分倾向）
```elixir
%{
  收分线比例: 0.25,               # 增加收分线浮动
  放分线比例: 0.15,               # 减少放分线浮动
  收分线最小值: 5000,             # 保持收分线最小值
  放分线最小值: 8000,             # 提高放分线最小值
  暗税比例: -30                   # -3%，负值偏向放分
}
```

### 平衡配置
```elixir
%{
  收分线比例: 0.2,                # 对称设置
  放分线比例: 0.2,                # 对称设置
  收分线最小值: 5000,             # 相同最小值
  放分线最小值: 5000,             # 相同最小值
  暗税比例: 0                     # 中性，不偏向任何一方
}
```

## 运维配置指南

### 调整收分强度
**增加收分**:
- 降低收分线比例（如 0.2 → 0.15）
- 增加收分线最小值（如 5000 → 8000）
- 设置正值暗税比例（如 50 = 5%）

**减少收分**:
- 提高收分线比例（如 0.2 → 0.25）
- 减少收分线最小值（如 8000 → 5000）
- 设置负值暗税比例（如 -30 = -3%）

### 调整放分强度
**增加放分**:
- 降低放分线比例（如 0.2 → 0.15）
- 增加放分线最小值（如 5000 → 8000）
- 设置负值暗税比例（如 -50 = -5%）

**减少放分**:
- 提高放分线比例（如 0.2 → 0.25）
- 减少放分线最小值（如 8000 → 5000）
- 设置正值暗税比例（如 30 = 3%）

### 调整系统趋势
**偏向收分**:
- 设置正值暗税比例（如 50 = 5%）
- 中心线会逐步上升，整体倾向收分

**偏向放分**:
- 设置负值暗税比例（如 -30 = -3%）
- 中心线会逐步下降，整体倾向放分

**保持中性**:
- 设置暗税比例为 0
- 中心线保持稳定，不偏向任何一方

### 快速调整策略
1. **急需收分**: 设置强收分配置，降低收分线比例和最小值
2. **急需放分**: 设置强放分配置，降低放分线比例和最小值
3. **保持平衡**: 使用对称配置，暗税比例设为 0
4. **长期调整**: 通过暗税比例调整中心线趋势

## 监控和调试

### 关键监控指标
- **当前库存**: 实时库存水平
- **中心线位置**: 系统整体趋势
- **控制线位置**: 各控制线的实际值
- **触发频率**: 各控制区间的触发次数
- **收放分比例**: 实际收分和放分的比例

### 调试工具
- **日志记录**: 详细的控制决策日志
- **实时监控**: 库存和控制线的实时变化
- **统计分析**: 历史数据的统计分析
- **配置验证**: 参数范围和有效性验证

### 常见问题排查
1. **收分过度**: 检查收分线配置是否过于激进
2. **放分过度**: 检查放分线配置是否过于宽松
3. **控制失效**: 检查权重配置和随机数生成
4. **趋势异常**: 检查暗税比例和中心线变化

## 系统特性

### 智能化特性
- **真实玩家检测**: 区分真实玩家和机器人，避免对空房间进行控制
- **下注分析**: 根据玩家下注分布选择最优控制策略
- **概率调整**: 基于历史数据和趋势分析进行概率调整
- **动态平衡**: 自动调整控制策略以维持长期平衡

### 安全性保障
- **多重验证**: 参数范围验证和数据类型检查
- **错误处理**: 完善的异常处理和降级机制
- **日志记录**: 详细的操作日志和状态跟踪
- **权限控制**: 严格的配置修改权限管理

### 性能优化
- **缓存机制**: 配置参数和计算结果的缓存
- **批量处理**: 多局游戏的批量控制决策
- **异步处理**: 非阻塞的控制逻辑处理
- **内存管理**: 合理的内存使用和垃圾回收

## 最佳实践

### 配置管理
1. **版本控制**: 对配置变更进行版本管理
2. **环境隔离**: 不同环境使用不同配置
3. **灰度发布**: 新配置的渐进式发布
4. **回滚机制**: 快速回滚到之前的配置

### 运维建议
1. **定期监控**: 定期检查系统运行状态
2. **数据分析**: 定期分析收放分效果
3. **配置优化**: 根据实际效果调整配置
4. **风险控制**: 设置合理的风险控制阈值

### 应急处理
1. **紧急收分**: 快速设置强收分配置
2. **紧急放分**: 快速设置强放分配置
3. **系统暂停**: 在异常情况下暂停控制
4. **手动干预**: 在必要时进行手动控制

---

**注意**: 本文档基于当前系统实现，如有系统更新，请及时更新相关配置说明。

**联系方式**: 如有疑问，请联系技术团队获取支持。

**更新日期**: 2025年7月18日