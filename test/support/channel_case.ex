defmodule CypridinaWeb.ChannelCase do
  use ExUnit.CaseTemplate

  using do
    quote do
      use Phoenix.ChannelTest

      # 导入常用的测试工具
      import Ecto
      import Ecto.Changeset
      import Ecto.Query

      # 设置默认的endpoint
      @endpoint CypridinaWeb.Endpoint

      # 导入仓库
      alias Cypridina.Repo
      alias Cypridina.Accounts.User
    end
  end

  setup tags do
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(Cypridina.Repo)

    unless tags[:async] do
      Ecto.Adapters.SQL.Sandbox.mode(Cypridina.Repo, {:shared, self()})
    end

    {:ok, []}
  end
end
