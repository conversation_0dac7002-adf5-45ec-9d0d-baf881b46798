defmodule CypridinaWeb.ThemeConfigTest do
  use ExUnit.Case, async: true
  alias CypridinaWeb.ThemeConfig

  describe "get_all_themes/0" do
    test "returns a list of theme maps" do
      themes = ThemeConfig.get_all_themes()

      assert is_list(themes)
      assert length(themes) > 0

      # 检查第一个主题的结构
      first_theme = List.first(themes)
      assert Map.has_key?(first_theme, :name)
      assert Map.has_key?(first_theme, :label)
      assert Map.has_key?(first_theme, :icon)
      assert Map.has_key?(first_theme, :description)
      assert Map.has_key?(first_theme, :category)
      assert Map.has_key?(first_theme, :is_dark)
    end
  end

  describe "get_theme/1" do
    test "returns theme info for existing theme" do
      theme = ThemeConfig.get_theme("light")

      assert theme.name == "light"
      assert theme.label == "浅色"
      assert theme.icon == "☀️"
      assert theme.is_dark == false
    end

    test "returns nil for non-existing theme" do
      theme = ThemeConfig.get_theme("non-existing")
      assert is_nil(theme)
    end
  end

  describe "theme_exists?/1" do
    test "returns true for existing theme" do
      assert ThemeConfig.theme_exists?("light")
      assert ThemeConfig.theme_exists?("dark")
      assert ThemeConfig.theme_exists?("cyberpunk")
    end

    test "returns false for non-existing theme" do
      refute ThemeConfig.theme_exists?("non-existing")
    end
  end

  describe "get_themes_by_category/1" do
    test "returns basic themes" do
      basic_themes = ThemeConfig.get_themes_by_category(:basic)

      assert length(basic_themes) >= 2
      assert Enum.all?(basic_themes, &(&1.category == :basic))

      theme_names = Enum.map(basic_themes, & &1.name)
      assert "light" in theme_names
      assert "dark" in theme_names
    end

    test "returns custom themes" do
      custom_themes = ThemeConfig.get_themes_by_category(:custom)

      assert length(custom_themes) > 0
      assert Enum.all?(custom_themes, &(&1.category == :custom))
    end

    test "returns daisyui themes" do
      daisyui_themes = ThemeConfig.get_themes_by_category(:daisyui)

      assert length(daisyui_themes) > 0
      assert Enum.all?(daisyui_themes, &(&1.category == :daisyui))
    end
  end

  describe "get_dark_themes/0" do
    test "returns only dark themes" do
      dark_themes = ThemeConfig.get_dark_themes()

      assert length(dark_themes) > 0
      assert Enum.all?(dark_themes, &(&1.is_dark == true))

      theme_names = Enum.map(dark_themes, & &1.name)
      assert "dark" in theme_names
    end
  end

  describe "get_light_themes/0" do
    test "returns only light themes" do
      light_themes = ThemeConfig.get_light_themes()

      assert length(light_themes) > 0
      assert Enum.all?(light_themes, &(&1.is_dark == false))

      theme_names = Enum.map(light_themes, & &1.name)
      assert "light" in theme_names
    end
  end

  describe "format_for_backpex_selector/1" do
    test "formats themes for Backpex selector" do
      themes = ThemeConfig.get_recommended_themes()
      formatted = ThemeConfig.format_for_backpex_selector(themes)

      assert is_list(formatted)
      assert length(formatted) > 0

      # 检查格式
      {label, value} = List.first(formatted)
      assert is_binary(label)
      assert is_binary(value)
      assert String.contains?(label, "☀️") or String.contains?(label, "🌙")
    end

    test "formats all themes by default" do
      formatted = ThemeConfig.format_for_backpex_selector()
      all_themes = ThemeConfig.get_all_themes()

      assert length(formatted) == length(all_themes)
    end
  end

  describe "get_theme_stats/0" do
    test "returns correct statistics" do
      stats = ThemeConfig.get_theme_stats()

      assert Map.has_key?(stats, :total)
      assert Map.has_key?(stats, :basic)
      assert Map.has_key?(stats, :custom)
      assert Map.has_key?(stats, :daisyui)
      assert Map.has_key?(stats, :dark)
      assert Map.has_key?(stats, :light)

      # 验证统计数据的一致性
      assert stats.total == stats.basic + stats.custom + stats.daisyui
      assert stats.total == stats.dark + stats.light
      assert stats.total > 0
    end
  end

  describe "get_recommended_themes/0" do
    test "returns recommended themes" do
      recommended = ThemeConfig.get_recommended_themes()

      assert is_list(recommended)
      assert length(recommended) > 0

      theme_names = Enum.map(recommended, & &1.name)
      assert "light" in theme_names
      assert "dark" in theme_names
    end
  end
end
